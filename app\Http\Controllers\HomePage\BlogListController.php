<?php

declare(strict_types=1);

namespace App\Http\Controllers\HomePage;

use App\Constants\UrlConstants;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class BlogListController extends Controller
{
    public function index(Request $request)
    {
        $blogTags = ['中古車', '中古車輸出', 'ニュージーランド', 'オーストラリア', '船積み', 'アフリカ', '中東', 'オセアニア'];
        $searchTag = $request->input('tags', '');

        $isValidTag = '' !== $searchTag && in_array($searchTag, $blogTags);

        $headingTitle = $isValidTag ? "{$searchTag}に関する記事一覧" : 'AUTOHUB BLOG';
        $brandName = __('home-page/blog-list.lang_autohub');
        $pageTitle = "{$headingTitle}｜{$brandName}";

        $locale = app()->getLocale();
        $url = 'ja' === $locale
            ? UrlConstants::URL_WP . 'ah-custom-blog-all.php'
            : UrlConstants::URL_WP . 'ah-custom-blog-all-en.php';

        $response = Http::asForm()->post($url, ['tags' => $searchTag]);

        $blogContent = $response->successful()
            ? $response->body()
            : '';

        return view('home-page.blog-list', compact(
            'blogContent',
            'searchTag',
            'pageTitle',
            'headingTitle',
            'blogTags',
        ));
    }
}
