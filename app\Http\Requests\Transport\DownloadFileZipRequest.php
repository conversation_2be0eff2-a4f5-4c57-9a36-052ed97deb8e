<?php

declare(strict_types=1);

namespace App\Http\Requests\Transport;

use Illuminate\Foundation\Http\FormRequest;

class DownloadFileZipRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules()
    {
        return [
            'ref_no' => 'required|string',
        ];
    }
}
