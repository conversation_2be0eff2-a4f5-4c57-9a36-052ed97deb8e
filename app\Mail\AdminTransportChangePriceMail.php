<?php

declare(strict_types=1);

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use App\Traits\MailTrait;

class AdminTransportChangePriceMail extends Mailable
{
    use MailTrait;
    use Queueable;
    use SerializesModels;

    private array $data;

    /**
     * Create a new message instance.
     */
    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function build(): self
    {
        $subject = str_replace('$car_no', Arr::get($this->data, 'data_info.car_no'), config('mail.mail_subjects.admin-transport-change-price'));
        $subject = str_replace('$customer_name', Arr::get($this->data, 'data_info.customer_name'), $subject);

        return $this
            ->from($this->data['mail_from']['mail'], $this->data['mail_from']['name'])
            ->bcc($this->transformMail($this->data['mail_bcc'] ?? ''))
            ->cc($this->transformMail($this->data['mail_cc'] ?? ''))
            ->subject($subject)
            ->text('emails.ja.admin-transport-change-price')
            ->with([
                'id' => Arr::get($this->data, 'data_info.id'),
                'ah_staff_name' => Arr::get($this->data, 'data_info.ah_staff_name'),
                'trans_name' => Arr::get($this->data, 'data_info.trans_name'),
                'transport_id' => Arr::get($this->data, 'data_info.transport_id'),
                'last_deli_price' => Arr::get($this->data, 'data_info.last_deli_price'),
                'deli_price' => Arr::get($this->data, 'data_info.deli_price'),
                'customer_name' => Arr::get($this->data, 'data_info.customer_name'),
                'fr_aa_place_id' => Arr::get($this->data, 'data_info.fr_aa_place_id'),
                'fr_name' => Arr::get($this->data, 'data_info.fr_name'),
                'fr_addr' => Arr::get($this->data, 'data_info.fr_addr'),
                'fr_tel' => Arr::get($this->data, 'data_info.fr_tel'),
                'to_name' => Arr::get($this->data, 'data_info.to_name'),
                'to_addr' => Arr::get($this->data, 'data_info.to_addr'),
                'to_tel' => Arr::get($this->data, 'data_info.to_tel'),
                'pos_no' => Arr::get($this->data, 'data_info.pos_no'),
                'aa_no' => Arr::get($this->data, 'data_info.aa_no'),
                'date_of_payment' => Arr::get($this->data, 'data_info.date_of_payment'),
                'car_name' => Arr::get($this->data, 'data_info.car_name'),
                'car_no' => Arr::get($this->data, 'data_info.car_no'),
                'undrivable_flg' => Arr::get($this->data, 'data_info.undrivable_flg'),
                'tall_flg' => Arr::get($this->data, 'data_info.tall_flg'),
                'lowdown_flg' => Arr::get($this->data, 'data_info.lowdown_flg'),
                'long_flg' => Arr::get($this->data, 'data_info.long_flg'),
                'plate_cut_flg' => Arr::get($this->data, 'data_info.plate_cut_flg'),
                'plate_no' => Arr::get($this->data, 'data_info.plate_no'),
                'country' => Arr::get($this->data, 'data_info.country'),
                'note' => Arr::get($this->data, 'data_info.note'),
                'odr_date' => Arr::get($this->data, 'data_info.odr_date'),
            ]);
    }
}
