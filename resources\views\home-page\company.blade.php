@extends('app')

@section('title', __('home-page/company.page_title'))

@push('styles')
    @vite('resources/css/company.css')
    @vite('resources/css/common/tab.css')
@endpush

@section('content')
    <div class="aos-init" data-aos="zoom-in">
        <div class="h-[50px] w-full lg:h-20"></div>
        <div
            class="absolute -z-1 min-h-screen w-full !bg-center"
            style="background: url('{{ asset('images/company/ah_company_background01.avif') }}')"
        >
            <div id="company-bg-01" class="h-[62px] w-full md:h-[92px] 2xl:bg-white"></div>
            <div id="company-bg-02" class="h-[730px] w-full bg-white md:h-[485px]"></div>
            <div id="company-bg-03" class="h-[272px] w-full"></div>
            <div id="company-bg-04" class="h-[805px] w-full bg-white md:h-[718px]"></div>
            <div id="company-bg-05" class="h-[327px] w-full md:h-[224px]"></div>
        </div>
    </div>
    <div class="mx-auto w-full overflow-hidden px-[15px]">
        <div class="company_01 mx-auto mt-[85px] flex max-w-6xl flex-wrap md:mt-[127px]">
            <div
                class="2md:max-w-[42%] aos-init z-9 order-2 max-w-full px-[15px] md:order-1 md:max-w-1/2"
                data-aos="zoom-in-right"
                data-aos-duration="1000"
            >
                <h1
                    class="2sm:text-27 2md:text-[35px] 2md:mr-[-100px] 2md:pl-0 text-25 mt-5 pl-[10%] font-bold break-all md:mr-[-70px] md:text-[30px]"
                >
                    {{ __('home-page/company.title') }}
                </h1>
                @if ($isJa)
                    <p
                        class="2sm:leading-7 2md:mr-0 mb-4 text-sm leading-[26px] md:mr-5 md:leading-[32px] lg:mt-5 lg:leading-[35px]"
                    >
                        {{ __('home-page/company.content_1') }}
                        <br />
                        {{ __('home-page/company.content_2') }}
                    </p>
                @else
                    <p
                        class="2sm:leading-[23px] 2md:mr-0 mb-4 text-sm leading-5 md:-mr-5 md:text-base md:leading-[23px] lg:mt-5 lg:leading-7"
                    >
                        {{ __('home-page/company.content_1') }}
                        <br />
                        {{ __('home-page/company.content_2') }}
                    </p>
                @endif
            </div>
            <div
                class="2md:ml-[16%] 2md:max-w-[42%] aos-init relative order-1 w-full max-w-full px-[15px] md:order-2 md:max-w-1/2"
                data-aos="zoom-in-left"
                data-aos-duration="1000"
            >
                <img
                    src="{{ asset('images/company/ah_top_image_autohub05_1.webp') }}"
                    class="company_top_bg 2sm:w-[780px] mt-0 2sm:mt-[-30px] 2md:mt-[30px] 2md:ml-[-30px] absolute -top-12 left-[-108px] sm-460:mt-5 w-[500px] sm-460:w-[690px] md:mt-[35px] md:ml-5 md:w-[150%] lg:mt-0"
                    alt="ah_top_image_autohub05_1"
                />
                <br />
                <img
                    src="{{ asset('images/company/ah_top_image_autohub05.webp') }}"
                    class="company_top_img 2sm:ml-[55px] 2sm:mt-[-25px] 2md:mt-[30px] 2md:ml-[-30px] mt-[-50px] ml-[33px] w-[370px] sm-460:w-100 max-w-[445px] md:mt-[35px] md:ml-5 md:w-full lg:mt-0"
                    alt="ah_top_image_autohub05"
                />
            </div>
        </div>
        <div id="cloud_btn01"></div>
        <div class="row kigyorinen absolute top-[925px] mx-[-15px] flex w-full flex-wrap md:top-[700px]">
            <div class="flex w-full max-w-full flex-col items-center px-[15px] text-center">
                <h1 class="2md:text-[35px] text-23 font-bold text-white [text-shadow:1px_1px_1px_black] md:text-[30px]">
                    {{ __('home-page/company.management_philosophy') }}
                </h1>
                <img
                    src="{{ asset('images/company/' . ($isJa ? 'yattemimasu.webp' : 'yattemimasu_en.webp')) }}"
                    class="w-[290px] md:w-[380px]"
                    alt="yattemimasu"
                />
                <p class="2md:text-25 md:text-23 text-17 leading-[33px] text-white [text-shadow:1px_1px_1px_black]">
                    {{ __('home-page/company.content_philosophy') }}
                </p>
            </div>
        </div>
        <div class="slogans mx-auto mt-[350px] flex max-w-6xl flex-wrap">
            <div class="z-10 w-full">
                <h1 class="2sm:text-27 2md:text-33 text-22 text-center font-bold break-all md:text-3xl lg:text-[35px]">
                    {{ __('home-page/company.slogans') }}
                </h1>
            </div>
            @foreach ($slogans as $index => $content)
                <x-company.slogan :index="$index" :content="$content" />
            @endforeach
        </div>
        <div class="staff mx-auto mt-[45px] flex max-w-6xl flex-wrap">
            <div class="z-10 mb-[30px] w-full">
                <h1
                    class="word-break: break-all; 2sm:text-27 2md:text-33 text-22 text-center font-bold md:text-[30px] lg:text-[35px]"
                >
                    {{ __('home-page/company.staff_title') }}
                </h1>
            </div>
            @foreach ($staffBoxes as $box)
                <x-company.staff-box :enTitle="$box['en']" :jaTitle="$box['ja']" :content="$box['content']" />
            @endforeach

            <div class="2md:px-[15px] max-w-1/2 flex-none basis-1/2 px-[5px] md:max-w-1/4 md:basis-1/4">
                <div
                    class="aos-init aos-animate 2md:py-5 2md:mb-[30px] 2md:h-[230px] mb-[10px] h-[180px] rounded-sm px-[10px] py-[15px] text-center md:h-[225px] lg:px-[15px]"
                    data-aos="zoom-in"
                    data-aos-delay="1500"
                    data-aos-duration="800"
                >
                    <img
                        src="{{ asset('images/company/Autohub-logo-red.png') }}"
                        class="2md:mt-[140px] mt-[55px] w-full md:mt-[90px]"
                        alt="Autohub-logo-red"
                    />
                </div>
            </div>
        </div>
        <div id="cloud_btn02"></div>
        <div id="cloud_btn03"></div>
        <div class="tab-container">
            <div class="rocker-toggle col-span-10 col-start-2 mt-[80px] mb-[-140px] md:col-span-4 md:col-start-5">
                <div class="flex justify-center">
                    <label class="rocker z-10">
                        <input class="rocker-switch" type="checkbox" checked />
                        <span class="switch-left leading-[1.2]">{{ __('home-page/company.company_profile') }}</span>
                        <span class="switch-right leading-normal">{{ __('home-page/company.offices') }}</span>
                    </label>
                </div>
            </div>
            <div id="company_profile01" class="tab-1 mx-auto mt-[45px] max-w-6xl">
                <div class="mt-15 flex flex-wrap">
                    <h1
                        class="2md:text-[35px] 2md:max-w-5/6 2md:text-left text-25 md:text-28 mx-auto !my-[25px] w-full text-center font-bold lg:max-w-2/3"
                    >
                        {{ __('home-page/company.company_profile') }}
                    </h1>
                    <div class="2md:max-w-5/6 relative mx-auto w-full lg:max-w-2/3">
                        <table class="w-full text-[#444]">
                            <tbody class="2sm:text-[0.9em] text-left text-[0.8em] md:text-base">
                                <tr class="border-t border-dashed border-[#adb5bd]">
                                    <th>{{ __('home-page/company.company_name') }}</th>
                                    <td class="p-[10px]">{{ __('home-page/company.company_name_content') }}</td>
                                </tr>
                                <tr class="border-t border-dashed border-[#adb5bd]">
                                    <th>{{ __('home-page/company.business') }}</th>
                                    <td class="p-[10px]">
                                        <div class="mx-[-15px] flex flex-wrap">
                                            <div class="relative w-full px-[15px]">
                                                {{ __('home-page/company.business_content_title') }}
                                            </div>
                                        </div>
                                        @foreach (array_chunk(__('home-page/company.business_contents'), 2) as $pair)
                                            <div class="mx-[-15px] flex flex-wrap">
                                                @foreach ($pair as $item)
                                                    <div
                                                        class="2sm:basis-1/2 2sm:max-w-1/2 2sm:pr-[15px] w-full pl-[15px] text-[0.9em]"
                                                    >
                                                        {!! $item !!}
                                                    </div>
                                                @endforeach
                                            </div>
                                        @endforeach
                                    </td>
                                </tr>
                                <tr class="border-t border-dashed border-[#adb5bd]">
                                    <th>{{ __('home-page/company.established') }}</th>
                                    <td class="p-[10px]">{{ __('home-page/company.established_content') }}</td>
                                </tr>
                                <tr class="border-t border-dashed border-[#adb5bd]">
                                    <th>{{ __('home-page/company.capital_found') }}</th>
                                    <td class="p-[10px]">{{ __('home-page/company.capital_found_content') }}</td>
                                </tr>
                                <tr class="border-t border-dashed border-[#adb5bd]">
                                    <th>{{ __('home-page/company.directors') }}</th>
                                    <td class="p-[10px]">
                                        @foreach (__('home-page/company.director_list') as $item)
                                            <div class="relative w-full">
                                                {!! $item !!}
                                            </div>
                                        @endforeach
                                    </td>
                                </tr>
                                <tr class="border-t border-dashed border-[#adb5bd]">
                                    <th>{{ __('home-page/company.number_staff') }}</th>
                                    <td class="p-[10px]">{{ __('home-page/company.number_staff_content') }}</td>
                                </tr>
                                <tr class="border-t border-dashed border-[#adb5bd]">
                                    <th>{{ __('home-page/company.amount_sales') }}</th>
                                    <td class="p-[10px]">{{ __('home-page/company.amount_sales_content') }}</td>
                                </tr>
                                <tr class="border-t border-dashed border-[#adb5bd]">
                                    <th>{{ __('home-page/company.main_banks') }}</th>
                                    <td class="p-[10px]">
                                        {{ __('home-page/company.main_banks_content') }}
                                    </td>
                                </tr>
                                <tr class="border-t border-dashed border-[#adb5bd]">
                                    <th>{{ __('home-page/company.business_lincence') }}</th>
                                    <td class="p-[10px]">{!! __('home-page/company.business_lincence_content') !!}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div id="company_profile02" class="tab-2 2sm:text-base mx-auto mt-[45px] hidden max-w-6xl text-[0.9em]">
                <div class="mt-15 flex flex-wrap">
                    <h1
                        class="2md:text-[35px] 2md:max-w-5/6 2md:text-left text-25 md:text-28 mx-auto !my-[25px] w-full text-center font-bold lg:max-w-2/3"
                    >
                        {{ __('home-page/company.offices') }}
                    </h1>
                    <div class="2md:max-w-5/6 relative mx-auto w-full lg:max-w-2/3">
                        @foreach ($offices as $index => $office)
                            <x-company.office
                                :index="$index"
                                :title="$office['title']"
                                :office="$office['office']"
                                :address="$office['address']"
                                :url="$office['url']"
                            />
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@once
    @push('scripts')
        <script>
            $('.tab-container .rocker-toggle').on('click', function (event) {
                event.stopPropagation()
                const tabWrapper = $(this).closest('.tab-container')
                const tab1 = $(tabWrapper).find('.tab-1')
                const tab2 = $(tabWrapper).find('.tab-2')
                const checkbox = $(tabWrapper).find('.rocker-switch')
                const isChecked = checkbox.prop('checked')

                $(tab2).css('display', isChecked ? 'none' : 'block')
                $(tab1).css('display', isChecked ? 'block' : 'none')
            })

            $('.tab-container .rocker-switch').on('change', function (event) {
                event.stopPropagation()
            })
        </script>
    @endpush
@endonce
