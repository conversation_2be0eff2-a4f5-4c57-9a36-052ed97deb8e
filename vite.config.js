import {defineConfig} from 'vite';
import laravel from 'laravel-vite-plugin';
import tailwindcss from '@tailwindcss/vite'

export default defineConfig({
    plugins: [
        laravel({
            input: [
                /* resource css */
                'resources/css/app.css',
                'resources/css/home.css',
                'resources/css/blog.css',
                'resources/css/blog-list.css',
                'resources/css/company.css',
                'resources/css/news-list.css',
                'resources/css/question.css',
                'resources/css/recruit.css',
                'resources/css/recruit2.css',
                'resources/css/services.css',
                'resources/css/schedule.css',
                'resources/css/map.css',
                'resources/css/contact.css',
                'resources/css/hubnet_intro.css',
                'resources/css/yards/yard-area.css',
                'resources/css/common/tab.css',
                /* resource js */
                'resources/js/app.js',
                'resources/js/service.js',
                'resources/js/schedule.js',
                'resources/js/animation.js',
            ],
            refresh: true,
        }),
        tailwindcss(),
    ],
});
