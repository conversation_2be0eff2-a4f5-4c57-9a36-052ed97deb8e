<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Mail\TransportFormBackendMail;
use App\Traits\MailTrait;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Mail;

class SendMailTransportFromBackend implements ShouldQueue
{
    use MailTrait;
    use Queueable;

    private array $data;
    private array $mailAddresses;

    /**
     * Create a new job instance.
     */
    public function __construct(array $mailAddresses, array $data)
    {
        $this->data = $data;
        $this->mailAddresses = $mailAddresses;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $mailAddresses = array_filter($this->mailAddresses, fn ($mailAddress) => $this->checkMailValid($mailAddress));

        if (!count($mailAddresses)) {
            return;
        }

        Mail::to($mailAddresses)
            ->send(new TransportFormBackendMail($this->data));
    }
}
