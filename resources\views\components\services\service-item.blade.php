@props([
    'index' => null,
    'title' => '',
    'description' => '',
    'link' => '',
    'bgImage' => '',
    'bgBack' => '',
    'bgContainer' => '',
])

@php
    $classTitle = '';
    $classItem1 = '';
    $classItem2 = '';
    $classImage = '';
    if ($index % 2 === 0) {
        $classImage = 'md:!left-[-30px]';
        $classItem1 = 'md:order-2';
        $classItem2 = 'md:order-1';
    }

    $isJa = app()->getLocale() === 'ja';
    $localizedSchedule = fn ($country) => $isJa ? route('schedule', ['country' => $country]) : route('localized.schedule', ['locale' => 'en', 'country' => $country]);
@endphp

@if ($index !== 1)
    <div id="page{{ $index }}"></div>
@endif

<div class="2sm:mt-50 2sm:text-base mt-35 bg-white text-sm" id="section{{ $index }}">
    <div class="mx-auto mt-[45px] flex max-w-6xl flex-wrap px-4">
        <div class="{{ $classItem1 }} z-10 mx-auto w-5/6 px-[15px] md:w-1/2">
            <h2
                class="{{ $classTitle }} 2sm:text-[37px] 2md:text-[42px] text-28 mt-[35px] leading-[1.2] font-bold lg:text-[46px]"
            >
                {!! $title !!}
            </h2>
            {!! $description !!}
            <br />
            <br />
            <div class="flex justify-between">
                <a
                    href="{{ $link }}"
                    id="detail_open_btn01"
                    class="group 2sm:mb-0 2sm:h-12 2sm:w-40 2sm:pt-3 2sm:pb-[6px] 2sm:text-sm 2sm:leading-6 2sm:px-[1px] relative mb-[10px] flex h-[35px] w-[130px] max-w-7/12 items-center justify-center rounded-[25px] bg-[repeating-linear-gradient(45deg,_#ffffff,_#ffffff_3px,_#e7e7e7_3px,_#e7e7e7_30px)] pt-[5px] text-xs leading-[23px] font-bold text-gray-900"
                >
                    <span
                        class="2sm:h-12 2sm:text-sm absolute top-[-6px] left-[-6px] flex h-[35px] w-full items-center justify-center rounded-[25px] border border-black bg-white text-xs leading-[23px] duration-200 group-hover:top-[-1px] group-hover:left-[-1px]"
                    >
                        {{ __('home-page/services.detail_info') }}
                    </span>
                </a>
                @if ($index === 5)
                    <a href="https://lin.ee/ua6qQrL" class="line_add_friend">
                        <img
                            alt="LINE_addbtn_160"
                            class="LINE_addbtn_160 hidden h-11 translate-y-[-6px] transform md:block"
                            src="{{ asset(app()->getLocale() === 'ja' ? 'images/services/LINE_addbtn_135.png' : 'images/services/LINE_addbtn_185_en.png') }}"
                        />
                    </a>
                    <a href="https://lin.ee/ua6qQrL" class="line_add_friend">
                        <img
                            alt="LINE_addbtn_120"
                            class="LINE_addbtn_120 2sm:mb-0 mb-[10px] h-[37px] translate-y-[-6px] transform md:hidden"
                            src="{{ asset(app()->getLocale() === 'ja' ? 'images/services/LINE_addbtn_120.png' : 'images/services/LINE_addbtn_120_en.png') }}"
                        />
                    </a>
                @endif
            </div>
            @if ($index === 2)
                <br />
                <div class="flex flex-wrap md:mx-[-15px]">
                    <div class="2sm:px-[5px] relative w-1/4 px-[3px]">
                        <a
                            href="{{ $localizedSchedule(6400) }}"
                            id="schedule_dl01"
                            class="2sm:p 2sm:px-[0.5em] 2sm:pt-[0.5em] 2sm:pb-[0.3em] 2sm:text-15 2sm:mt-0 2sm:mb-0 text-13 mt-[-5px] mb-[10px] inline-block w-full rounded-[15px] bg-[#ff8600] px-[0.2em] pt-[0.2em] pb-[0.1em] text-center leading-[1.1em] font-normal text-white hover:opacity-80"
                        >
                            <i class="fas fa-ship"></i>
                            <br />
                            NZ
                            <br />
                            <span class="2sm:text-11 text-10 2md:text-xs">
                                {{ __('home-page/services.schedule') }}
                            </span>
                        </a>
                    </div>
                    <div class="2sm:px-[5px] relative w-1/4 px-[3px]">
                        <a
                            href="{{ $localizedSchedule(6100) }}"
                            id="schedule_dl02"
                            class="2sm:p 2sm:px-[0.5em] 2sm:pt-[0.5em] 2sm:pb-[0.3em] 2sm:text-15 2sm:mt-0 2sm:mb-0 text-13 mt-[-5] mb-[10px] inline-block w-full rounded-[15px] bg-[#40992d] px-[0.2em] pt-[0.2em] pb-[0.1em] text-center leading-[1.1em] font-normal text-white hover:opacity-80"
                        >
                            <i class="fas fa-ship"></i>
                            <br />
                            AUS
                            <br />
                            <span class="2sm:text-11 text-10 2md:text-xs">
                                {{ __('home-page/services.schedule') }}
                            </span>
                        </a>
                    </div>
                    <div class="2sm:px-[5px] relative w-1/4 px-[3px]">
                        <a
                            href="{{ $localizedSchedule(4400) }}"
                            id="schedule_dl03"
                            class="2sm:p 2sm:px-[0.5em] 2sm:pt-[0.5em] 2sm:pb-[0.3em] 2sm:text-15 2sm:mt-0 2sm:mb-0 text-13 mt-[-5] mb-[10px] inline-block w-full rounded-[15px] bg-[#27B2C5] px-[0.2em] pt-[0.2em] pb-[0.1em] text-center leading-[1.1em] font-normal text-white hover:opacity-80"
                        >
                            <i class="fas fa-ship"></i>
                            <br />
                            UK
                            <br />
                            <span class="2sm:text-11 text-10 2md:text-xs">
                                {{ __('home-page/services.schedule') }}
                            </span>
                        </a>
                    </div>
                    <div class="2sm:px-[5px] relative w-1/4 px-[3px]">
                        <a
                            href="{{ $localizedSchedule(1202) }}"
                            id="schedule_dl01"
                            class="2sm:p 2sm:px-[0.5em] 2sm:pt-[0.5em] 2sm:pb-[0.3em] 2sm:text-15 2sm:mt-0 2sm:mb-0 text-13 bg-red-450 mt-[-5] mb-[10px] inline-block w-full rounded-[15px] px-[0.2em] pt-[0.2em] pb-[0.1em] text-center leading-[1.1em] font-normal text-white hover:opacity-80"
                        >
                            <i class="fas fa-ship"></i>
                            <br />
                            USA
                            <br />
                            <span class="2sm:text-11 text-10 2md:text-xs">
                                {{ __('home-page/services.schedule') }}
                            </span>
                        </a>
                    </div>
                </div>
            @endif
        </div>
        <div
            class="{{ $classItem2 }} relative mx-auto flex w-5/6 items-start justify-center px-[15px] md:w-1/2 lg:justify-start"
        >
            <img
                alt="ah_service_image02"
                src="{{ asset('images/services/' . $bgImage) }}"
                class="relative z-1 mt-[15px] w-[90%] !max-w-[450px] object-contain md:w-full lg:mt-0"
            />
            <div
                class="{{ $classImage }} 2sm:top-[25px] 2sm:left-16 2md:max-h-[282px] absolute top-2 left-4 h-full max-h-[282px] w-[90%] max-w-[450px] md:top-10 md:left-15 md:h-full md:max-h-[220px] md:w-full"
                style="background-image: url('{{ asset('images/services/' . $bgBack) }}')"
            ></div>
        </div>
    </div>
</div>
