<?php

declare(strict_types=1);

namespace App\Services\LoadingOrder;

use App\Repositories\sqlServer\LoadingOrderRepository;

class DetailLoadingOrderService
{
    private LoadingOrderRepository $loadingOrderRepository;

    public function __construct(LoadingOrderRepository $loadingOrderRepository)
    {
        $this->loadingOrderRepository = $loadingOrderRepository;
    }

    public function call($id)
    {
        return $this->loadingOrderRepository->getDetailById($id);
    }
}
