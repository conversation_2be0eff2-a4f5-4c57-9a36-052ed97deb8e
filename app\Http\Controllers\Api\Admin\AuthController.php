<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\AuthorizationRequest;
use App\Http\Requests\User\LoginUserRequest;
use App\Http\Requests\User\RefreshTokenRequest;
use App\Http\Resources\User\UserResource;
use App\Services\AuthService;
use Illuminate\Http\Request;
use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Response;

class AuthController extends Controller
{
    public function __construct(private AuthService $authService)
    {
    }

    #[OA\Post(
        path: '/api/admin/auth/login',
        summary: 'Admin user login',
        description: 'Authenticate admin user with ID and password',
        requestBody: new OA\RequestBody(
            required: true,
            description: 'Login credentials',
            content: new OA\JsonContent(
                required: ['id', 'password'],
                properties: [
                    new OA\Property(property: 'id', type: 'string', example: '1000', description: 'User ID'),
                    new OA\Property(property: 'password', type: 'string', format: 'password', example: 'secret123', description: 'User password')
                ]
            )
        ),
        tags: ['Admin Authentication'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Login successful',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'code', type: 'integer', example: 200),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Success'),
                        new OA\Property(
                            property: 'data',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'accessToken', type: 'string', example: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...'),
                                new OA\Property(property: 'refreshToken', type: 'string', example: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...')
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 404,
                description: 'Invalid credentials',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 404),
                        new OA\Property(property: 'message', type: 'string', example: 'IDかパスワードが正しくありません')
                    ]
                )
            ),
            new OA\Response(
                response: 422,
                description: 'Validation error',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'message', type: 'string', example: 'The given data was invalid.'),
                        new OA\Property(
                            property: 'errors',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'id', type: 'array', items: new OA\Items(type: 'string', example: 'The id field is required.')),
                                new OA\Property(property: 'password', type: 'array', items: new OA\Items(type: 'string', example: 'The password field is required.'))
                            ]
                        )
                    ]
                )
            )
        ]
    )]
    /**
     * @throws ArrayWithMixedKeysException
     * @throws ConfigurationNotFoundException
     * @throws IncompatibleTypeException
     * @throws OAuthServerException
     */
    public function login(LoginUserRequest $request): Response
    {
        $data = $request->validated();
        $result = $this->authService->login($data);
        return $this->respond($result);
    }

    #[OA\Get(
        path: '/api/admin/profile',
        summary: 'Get admin user profile',
        description: 'Retrieve authenticated admin user profile information',
        security: [['access_token' => []]],
        tags: ['Admin Authentication'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Profile retrieved successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'code', type: 'integer', example: 200),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Success'),
                        new OA\Property(
                            property: 'data',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'id', type: 'string', example: '1000'),
                                new OA\Property(property: 'cus_Name_JP', type: 'string', example: '株式会社テスト'),
                                new OA\Property(property: 'cus_Name_kana', type: 'string', example: 'カブシキガイシャテスト'),
                                new OA\Property(property: 'cus_Name_EN', type: 'string', example: 'Test Company Ltd.'),
                                new OA\Property(property: 'tel', type: 'string', example: '03-1234-5678'),
                                new OA\Property(property: 'fax', type: 'string', example: '03-1234-5679'),
                                new OA\Property(property: 'zipcd', type: 'string', example: '123-4567'),
                                new OA\Property(property: 'address', type: 'string', example: '東京都渋谷区'),
                                new OA\Property(property: 'address2', type: 'string', example: '神南1-1-1'),
                                new OA\Property(property: 'address3', type: 'string', example: 'テストビル3F'),
                                new OA\Property(property: 'hn_cd', type: 'integer', example: 1)
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 401,
                description: 'Unauthorized',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 401),
                        new OA\Property(property: 'message', type: 'string', example: 'Unauthorized')
                    ]
                )
            )
        ]
    )]
    public function getProfile(Request $request): Response
    {
        return $this->respond(new UserResource($request->user()));
    }

    #[OA\Post(
        path: '/api/admin/auth/logout',
        summary: 'Admin user logout',
        description: 'Logout authenticated admin user and invalidate token',
        security: [['access_token' => []]],
        tags: ['Admin Authentication'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Logout successful',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'code', type: 'integer', example: 200),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Success'),
                        new OA\Property(
                            property: 'data',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'message', type: 'string', example: 'Successfully logged out')
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 401,
                description: 'Unauthorized',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 401),
                        new OA\Property(property: 'message', type: 'string', example: 'Unauthorized')
                    ]
                )
            )
        ]
    )]
    public function logout(Request $request): Response
    {
        $this->authService->logout($request->bearerToken());

        return $this->respond(['message' => 'Successfully logged out']);
    }

    #[OA\Post(
        path: '/api/admin/auth/refresh-token',
        summary: 'Refresh access token',
        description: 'Refresh expired access token using refresh token in request body',
        requestBody: new OA\RequestBody(
            required: true,
            description: 'Refresh token',
            content: new OA\JsonContent(
                required: ['token'],
                properties: [
                    new OA\Property(property: 'token', type: 'string', example: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...', description: 'Refresh token to generate new access token')
                ]
            )
        ),
        tags: ['Admin Authentication'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Token refreshed successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'code', type: 'integer', example: 200),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Success'),
                        new OA\Property(
                            property: 'data',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'accessToken', type: 'string', example: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...'),
                                new OA\Property(property: 'refreshToken', type: 'string', example: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...')
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 401,
                description: 'Invalid or expired refresh token',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 401),
                        new OA\Property(property: 'message', type: 'string', example: 'Invalid or expired refresh token')
                    ]
                )
            ),
            new OA\Response(
                response: 422,
                description: 'Validation error',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'message', type: 'string', example: 'The given data was invalid.'),
                        new OA\Property(
                            property: 'errors',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'token', type: 'array', items: new OA\Items(type: 'string', example: 'The token field is required.'))
                            ]
                        )
                    ]
                )
            )
        ]
    )]
    public function refreshToken(RefreshTokenRequest $request): Response
    {
        return $this->respond($this->authService->refreshToken($request->token));
    }

    #[OA\Post(
        path: '/api/admin/auth/authorization',
        summary: 'Process authorization request',
        description: 'Process authorization with redirect URI and secret key',
        security: [['access_token' => []]],
        requestBody: new OA\RequestBody(
            required: true,
            description: 'Authorization parameters',
            content: new OA\JsonContent(
                required: ['redirect_uri', 'secret_key'],
                properties: [
                    new OA\Property(property: 'redirect_uri', type: 'string', example: 'https://example.com/callback', description: 'Redirect URI for authorization'),
                    new OA\Property(property: 'secret_key', type: 'string', example: 'A7bK9mN2pQ8xT5wZzX2vB5hN7jK9mE1f', description: 'Secret key for authorization')
                ]
            )
        ),
        tags: ['Admin Authentication'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Authorization processed successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'code', type: 'integer', example: 200),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Success'),
                        new OA\Property(
                            property: 'data',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'authorization_code', type: 'string', example: 'auth_code_12345'),
                                new OA\Property(property: 'redirect_uri', type: 'string', example: 'https://example.com/callback')
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 401,
                description: 'Unauthorized',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 401),
                        new OA\Property(property: 'message', type: 'string', example: 'Unauthorized')
                    ]
                )
            ),
            new OA\Response(
                response: 422,
                description: 'Validation error',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'message', type: 'string', example: 'The given data was invalid.'),
                        new OA\Property(
                            property: 'errors',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'redirect_uri', type: 'array', items: new OA\Items(type: 'string', example: 'The redirect uri field is required.')),
                                new OA\Property(property: 'secret_key', type: 'array', items: new OA\Items(type: 'string', example: 'The secret key field is required.'))
                            ]
                        )
                    ]
                )
            )
        ]
    )]
    public function processAuthorization(AuthorizationRequest $request): Response
    {
        $result = $this->authService->processAuthorization(array_merge($request->validated(), ['token' => $request->bearerToken()]));
        return $this->respond($result);
    }

}
