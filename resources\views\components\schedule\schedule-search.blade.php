@use('App\Modules\SailingSchedule\Constants\PortAreaOrder')
@props([
    'routes' => [],
    'portJa' => [],
    'ports' => [],
    'countries' => [],
])

@php
    $classDropdownCommon =
        'dropdown-item text-13 flex w-full cursor-pointer items-center overflow-hidden px-6 py-1 pr-2 font-normal overflow-ellipsis whitespace-nowrap hover:bg-[#f8f9fa] hover:text-[#16181b] active:bg-[#0075a9] active:text-white';
@endphp

<div class="sailing-schedule_form-search 2md:pr-6 lg:w-[calc(100%-535px)]">
    <div
        class="relative z-10 rounded-lg border border-[#eeee] bg-white/50 shadow-[0_0_10px_0_#0000001a] backdrop-blur-[10px]"
    >
        <form action="" method="GET">
            <div class="flex w-full flex-wrap lg:pt-[30.5px]">
                <x-schedule.schedule-search-item
                    id="route"
                    :title="__('home-page/sailing_schedule.route')"
                    classContainer="order-1"
                >
                    @foreach ($routes as $route)
                        <div
                            class="{{ $classDropdownCommon }}"
                            data-code="{{ $route['route_code'] }}"
                            data-name="{{ $route['route_name'] }}"
                            data-initial-display="1"
                        >
                            <span class="text-13 ml-2 md:text-base">{{ $route['route_name'] }}</span>
                        </div>
                    @endforeach
                </x-schedule.schedule-search-item>
                <x-schedule.schedule-search-item
                    id="pol"
                    :title="__('home-page/sailing_schedule.pol')"
                    classContainer="order-3 lg:order-2"
                >
                    @foreach ($portJa as $key => $port)
                        <div
                            class="{{ $classDropdownCommon }}"
                            data-code="{{ $key }}"
                            data-name="{{ '(' . $port['area'] . ') ' . trim($port['name']) }}"
                            data-initial-display="1"
                        >
                            <img
                                src="{{ asset(sprintf('images/countries/%s.svg', $port['country_code'])) }}"
                                class="w-[30px]"
                                alt="pol"
                            />
                            <span
                                class="text-13 ml-2 md:text-base"
                                title="{{ '(' . $port['area'] . ') ' . trim($port['name']) }}"
                            >
                                {{ '(' . $port['area'] . ') ' . trim($port['name']) }}
                            </span>
                        </div>
                    @endforeach
                </x-schedule.schedule-search-item>
                <x-schedule.schedule-search-item
                    id="port"
                    :title="__('home-page/sailing_schedule.pod')"
                    classContainer="order-4 lg:order-3"
                >
                    @foreach ($ports as $key => $port)
                        @if ($port['country_code'] == PortAreaOrder::JAPAN_CODE)
                            @continue
                        @endif

                        <div
                            class="{{ $classDropdownCommon }}"
                            data-code="{{ $key }}"
                            data-name="{{ trim($port['name']) }}"
                            data-initial-display="1"
                        >
                            <img
                                src="{{ asset(sprintf('images/countries/%s.svg', $port['country_code'])) }}"
                                class="w-[30px]"
                                alt="pod"
                            />
                            <span class="text-13 ml-2 md:text-base" title="{{ trim($port['name']) }}">
                                {{ $port['name'] }}
                            </span>
                        </div>
                    @endforeach
                </x-schedule.schedule-search-item>
                <x-schedule.schedule-search-item
                    id="country"
                    :title="__('home-page/sailing_schedule.country')"
                    classContainer="order-2 lg:order-4 lg:border-none lg:pt-[30px] lg:!pb-[18px]"
                >
                    @foreach ($countries as $country)
                        <div
                            class="{{ $classDropdownCommon }}"
                            data-code="{{ trim($country['code']) }}"
                            data-name="{{ trim($country['name']) }}"
                            data-initial-display="1"
                        >
                            <img
                                class="w-[30px]"
                                src="{{ asset(sprintf('images/countries/%s.svg', trim($country['code']))) }}"
                                alt=""
                            />
                            <span class="text-13 ml-2 md:text-base">{{ trim($country['name']) }}</span>
                        </div>
                    @endforeach
                </x-schedule.schedule-search-item>
                <div
                    class="order-5 w-1/2 border-b border-[#eeee] px-4 pb-2 lg:w-1/3 lg:border-none lg:pt-[30px] lg:!pb-[18px]"
                >
                    <div class="mb-1 pt-[10px] text-sm leading-5 font-semibold text-slate-950 lg:mb-5 lg:p-0">
                        {{ __('home-page/sailing_schedule.etd') }}
                    </div>
                    <div class="flex items-center">
                        <img
                            src="{{ asset('images/schedule/date.svg') }}"
                            alt=""
                            class="relative z-2 mr-2 inline-block h-4 w-4"
                        />
                        <input
                            placeholder="ETD"
                            class="input-date input-value"
                            id="start-date"
                            name="startDate"
                            value="{{ request()->get('startDate', '') }}"
                            readonly
                        />
                    </div>
                </div>
                <div
                    class="order-5 w-1/2 border-b border-[#eeee] px-4 pb-2 lg:w-1/3 lg:border-none lg:pt-[30px] lg:!pb-[18px]"
                >
                    <div class="mb-1 pt-[10px] text-sm leading-5 font-semibold text-slate-950 lg:mb-5 lg:p-0">
                        {{ __('home-page/sailing_schedule.eta') }}
                    </div>
                    <div class="flex items-center">
                        <img
                            src="{{ asset('images/schedule/date.svg') }}"
                            alt=""
                            class="relative z-2 mr-2 inline-block h-4 w-4"
                        />
                        <input
                            placeholder="ETA"
                            class="input-date input-value"
                            id="end-date"
                            name="endDate"
                            value="{{ request()->get('endDate', '') }}"
                            readonly
                        />
                    </div>
                </div>
                <div
                    class="order-7 grid w-full cursor-pointer grid-cols-2 gap-[1px] text-base leading-[22px] font-semibold lg:pt-2"
                >
                    <div class="rounded-bl-[8px] bg-[#f4d7dd] p-[13px] text-center text-red-500" onClick="clearForm()">
                        {{ __('home-page/sailing_schedule.clear') }}
                    </div>
                    <button class="rounded-br-[8px] bg-[#b1d1ec] p-[13px] text-center text-[#4174ad] cursor-pointer" type="submit">
                        {{ __('home-page/sailing_schedule.search') }}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
