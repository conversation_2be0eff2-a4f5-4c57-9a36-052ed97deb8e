<div class="sailing-schedule_map 2md:w-auto w-full">
    <form action="" method="get">
        <input type="hidden" name="route" id="area" />
        <input type="hidden" name="startDate" id="startDate" />
        <input type="hidden" name="endDate" id="endDate" />
        <div id="map">
            <div class="sailing-schedule_map-sp">
                <img src="{{ asset('images/schedule/map_PC.svg') }}" alt="map" id="map-pc" />
                <img src="{{ asset('images/schedule/map_sp_1.svg') }}" alt="map" id="map-sp-1" />
                <img src="{{ asset('images/schedule/map_sp_2.svg') }}" alt="map" id="map-sp-2" />
                @foreach (__('home-page/sailing_schedule.maps') as $key => $map)
                    <div class="{{ $map['class'] }}" id="select-{{ $key }}">
                        <div class="area-name">
                            <button onclick="distSelect({{ $key }})">
                                <span class="cursor-pointer">{!! $map['name'] !!}</span>
                            </button>
                        </div>
                    </div>
                    @if ($key === 15)
                        <div class="other-area"></div>
                    @endif
                @endforeach
            </div>
        </div>
    </form>
</div>
