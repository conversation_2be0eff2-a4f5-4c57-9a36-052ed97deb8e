<?php

declare(strict_types=1);

namespace App\Services\LoadingOrder;

use App\Enums\ApiKey;
use App\Enums\OldHubNetApi;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CheckingFileOldHubnetService
{
    private string $apiBaseUrl;
    private string $apiEndpoint;
    private int $requestTimeout;

    public function __construct()
    {
        $this->apiBaseUrl = env('APP_URL_OLD', '');
        $this->apiEndpoint = OldHubNetApi::LOADING_ORDER_CHECK_ENDPOINT->value;
        $this->requestTimeout = (int) OldHubNetApi::TIMEOUT->value;
    }


    /**
     * Retrieve file information for transport records from old HubNet API
     *
     * Makes authenticated HTTP POST request to legacy API with loading order data.
     * Includes token in headers for authentication.
     * Validates response structure and logs both success and failure cases.
     *
     * @param array $params Array of loading order records, each containing:
     *                      - m_customer_id: Customer ID (numeric)
     *                      - loading_id: loading order ID (numeric)
     * @return array Array of file info:
     * - customer_id (int)
     * - loading_id (int)
     * - file_list (array<array{filename: string}>)
     *
     */
    public function call(array $params): array
    {
        try {
            $this->validateParams($params);

            $firstRecord = $params[0];
            $queryParams = [
                'm_customer_id' => $firstRecord['m_customer_id'],
                'loading_id' => $firstRecord['loading_id']
            ];

            $response = Http::timeout($this->requestTimeout)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'X-Token-Validate' => ApiKey::API_OLD_HUBNET->getValue()
                ])
                ->post($this->apiBaseUrl . $this->apiEndpoint . '?' . http_build_query($queryParams), $params);

            if (!$response->successful()) {
                throw new Exception("HTTP request failed with status: {$response->status()}");
            }

            $responseData = $response->json();

            return $responseData['data'] ?? [];
        } catch (Exception $exception) {
            Log::error($exception->getMessage());
            return [];
        }
    }

    /**
     * Validate transport parameters before API call
     *
     * Ensures all required fields are present and properly formatted.
     * Checks for empty arrays and validates data types.
     *
     * @param array $params Transport parameters to validate
     * @throws Exception When parameters are missing, empty, or invalid
     */
    private function validateParams(array $params): void
    {
        if (empty($params)) {
            throw new Exception("Parameters array cannot be empty");
        }

        foreach ($params as $index => $param) {
            if (!isset($param['m_customer_id']) || !isset($param['loading_id'])) {
                throw new Exception("Missing required parameters at index {$index}. Required: m_customer_id, loading_id");
            }

            if (!is_numeric($param['m_customer_id']) || !is_numeric($param['loading_id'])) {
                throw new Exception("Parameters must be numeric at index {$index}");
            }
        }
    }
}
