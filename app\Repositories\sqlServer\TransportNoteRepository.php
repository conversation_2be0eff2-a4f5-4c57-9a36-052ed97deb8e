<?php

declare(strict_types=1);

namespace App\Repositories\sqlServer;

use App\Models\sqlServer\TransportNote;
use App\Repositories\BaseRepository;
use Illuminate\Support\Facades\Auth;

class TransportNoteRepository extends BaseRepository
{
    public function model(): mixed
    {
        return TransportNote::class;
    }

    public function create(array $data): ?TransportNote
    {
        // Check if note data is empty or null
        if (empty($data['note']) || $data['note'] === null) {
            return null;
        }

        // Check if there's a change compared to the newest note
        $newestNote = $this->getNewestNoteByTransportId($data['tp_id']);
        if ($newestNote && trim($newestNote->note) === trim($data['note'])) {
            return null; // No change, don't create
        }

        // Update list_show_flg for existing notes
        $listTransportNote = $this->getByTransportId($data['tp_id']);
        foreach ($listTransportNote as $item) {
            if($item->list_show_flg > 0 && $item->list_show_flg <= 3)  {
                $item->list_show_flg++;
            }
            if($item->list_show_flg > 3) {
                $item->list_show_flg = 0;
            }
            // Use update method instead of save to avoid primary key issues
            $this->updateNote((int)$item->idx, ['list_show_flg' => $item->list_show_flg]);
        }
        
        return ($this->model)::create($data);
    }

    
    /**
     * Get notes by transport ID
     *
     * @param int $tpId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getByTransportId(int $tpId)
    {
        return ($this->model)::query()
            ->where('tp_id', $tpId)
            ->where('del_flg', 0)
            ->orderBy('list_show_flg', 'asc')
            ->get();
    }

    public function getNewestNoteByTransportId(int $tpId)
    {
        return ($this->model)::query()
            ->where('tp_id', $tpId)
            ->where('del_flg', 0)
            ->where('list_show_flg', 1)
            ->first();
    }
    /**
     * Update transport note
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateNote(int $id, array $data)
    {
        return ($this->model)::query()
            ->where('idx', $id)
            ->update($data);
    }

    /**
     * Soft delete transport note
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        return ($this->model)::query()
            ->where('idx', $id)
            ->update([
                'del_flg' => 1,
                
            ]);
    }
} 