<?php

declare(strict_types=1);

namespace App\Http\Controllers\HomePage;

use App\Http\Controllers\Controller;
use Illuminate\View\View;

class IntroductionController extends Controller
{
    public function index(): View
    {
        $lang = app()->getLocale();
        $isJa = 'ja' === $lang;
        $urlOld = env('APP_URL_OLD', '');
        return view('home-page.introduction', compact('urlOld', 'lang', 'isJa'));
    }
}
