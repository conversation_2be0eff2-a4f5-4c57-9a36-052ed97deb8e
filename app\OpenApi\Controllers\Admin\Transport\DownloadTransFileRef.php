<?php

declare(strict_types=1);

namespace App\OpenApi\Controllers\Admin\Transport;

use OpenApi\Attributes as OA;

#[OA\Get(
    path: '/api/admin/transport/download-trans-file-ref',
    description: 'Download transport file reference based on customer ID, transport ID, and filename. This endpoint allows users to download specific transport-related files or documents.',
    summary: 'Download Transport File Reference',
    security: [['access_token' => []]],
    tags: ['Transport Management'],
    parameters: [
        new OA\Parameter(
            name: 'customer_id',
            description: 'Customer ID to identify the specific customer',
            in: 'query',
            required: true,
            schema: new OA\Schema(type: 'integer', minimum: 1, example: 12345)
        ),
        new OA\Parameter(
            name: 'transport_id',
            description: 'Transport ID to identify the specific transport record',
            in: 'query',
            required: true,
            schema: new OA\Schema(type: 'integer', minimum: 1, example: 67890)
        ),
        new OA\Parameter(
            name: 'fname',
            description: 'Filename of the transport file to download',
            in: 'query',
            required: true,
            schema: new OA\Schema(type: 'string', maxLength: 255, example: 'transport_document_2024_001.pdf')
        )
    ],
    responses: [
        new OA\Response(
            response: 200,
            description: 'File download successful',
            content: new OA\MediaType(
                mediaType: 'application/octet-stream',
                schema: new OA\Schema(
                    type: 'string',
                    format: 'binary'
                )
            ),
            headers: [
                new OA\Header(
                    header: 'Content-Disposition',
                    description: 'Attachment filename',
                    schema: new OA\Schema(type: 'string', example: 'attachment; filename="transport_document_2024_001.pdf"')
                ),
                new OA\Header(
                    header: 'Content-Type',
                    description: 'MIME type of the file',
                    schema: new OA\Schema(type: 'string', example: 'application/pdf')
                ),
                new OA\Header(
                    header: 'Content-Length',
                    description: 'Size of the file in bytes',
                    schema: new OA\Schema(type: 'integer', example: 1024000)
                )
            ]
        ),
        new OA\Response(
            response: 400,
            description: 'Bad Request - Invalid parameters or file not found',
            content: new OA\JsonContent(
                properties: [
                    new OA\Property(property: 'success', type: 'boolean', example: false),
                    new OA\Property(property: 'message', type: 'string', example: 'Invalid parameters or file not found'),
                    new OA\Property(property: 'code', type: 'integer', example: 400)
                ]
            )
        ),
        new OA\Response(
            response: 401,
            description: 'Unauthorized - Invalid or missing access token',
            content: new OA\JsonContent(
                properties: [
                    new OA\Property(property: 'success', type: 'boolean', example: false),
                    new OA\Property(property: 'message', type: 'string', example: 'Unauthorized'),
                    new OA\Property(property: 'code', type: 'integer', example: 401)
                ]
            )
        ),
        new OA\Response(
            response: 403,
            description: 'Forbidden - Insufficient permissions to access the file',
            content: new OA\JsonContent(
                properties: [
                    new OA\Property(property: 'success', type: 'boolean', example: false),
                    new OA\Property(property: 'message', type: 'string', example: 'Forbidden - Insufficient permissions'),
                    new OA\Property(property: 'code', type: 'integer', example: 403)
                ]
            )
        ),
        new OA\Response(
            response: 404,
            description: 'Not Found - File does not exist or is not accessible',
            content: new OA\JsonContent(
                properties: [
                    new OA\Property(property: 'success', type: 'boolean', example: false),
                    new OA\Property(property: 'message', type: 'string', example: 'File not found'),
                    new OA\Property(property: 'code', type: 'integer', example: 404)
                ]
            )
        ),
        new OA\Response(
            response: 422,
            description: 'Validation Error - Invalid request parameters',
            content: new OA\JsonContent(
                properties: [
                    new OA\Property(
                        property: 'message',
                        type: 'string',
                        example: 'The given data was invalid.'
                    ),
                    new OA\Property(
                        property: 'errors',
                        properties: [
                            new OA\Property(
                                property: 'customer_id',
                                type: 'array',
                                items: new OA\Items(type: 'string'),
                                example: ['Customer ID is required', 'Customer ID must be a valid integer']
                            ),
                            new OA\Property(
                                property: 'transport_id',
                                type: 'array',
                                items: new OA\Items(type: 'string'),
                                example: ['Transport ID is required', 'Transport ID must be a valid integer']
                            ),
                            new OA\Property(
                                property: 'fname',
                                type: 'array',
                                items: new OA\Items(type: 'string'),
                                example: ['Filename is required', 'Filename must be a valid string']
                            )
                        ],
                        type: 'object'
                    )
                ]
            )
        ),
        new OA\Response(
            response: 500,
            description: 'Internal server error - File processing failed',
            content: new OA\JsonContent(
                properties: [
                    new OA\Property(property: 'success', type: 'boolean', example: false),
                    new OA\Property(property: 'message', type: 'string', example: 'Internal server error'),
                    new OA\Property(property: 'code', type: 'integer', example: 500)
                ]
            )
        )
    ]
)]
class DownloadTransFileRef
{
}
