<?php

declare(strict_types=1);

namespace App\Http\Controllers\HomePage;

use App\Constants\UrlConstants;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class PostViewController extends Controller
{
    public function index(Request $request)
    {
        $postId = $request->query('postid', '');
        $url = UrlConstants::URL_WP . ('ja' === app()->getLocale() ? "ah-custom-post-view.php?postid=" : "ah-custom-post-view-en.php?postid=");
        $url = $url . $postId;
        $response = Http::get($url);

        $content = $response->successful()
            ? $response->body()
            : '';

        preg_match('/<h1 class="blog_view_title">(.*?)<\/h1>/s', $content, $matches);
        $h1_title = $matches[1] ?? '';

        $title = $h1_title . '｜' . __('home-page/blog-list.lang_autohub');

        return view('home-page.post-view', compact('title', 'content'));
    }
}
