<?php

declare(strict_types=1);

namespace App\Services\Transport;

use App\Enums\OldHubNetApi;
use Exception;
use Illuminate\Support\Facades\Http;

class StoreFileTransportService
{
    private string $apiBaseUrl;
    private string $apiEndpoint;
    private int $requestTimeout;

    /**
     * Initialize service with API configuration
     *
     * Loads base URL from environment and endpoint/timeout from enum constants
     */
    public function __construct()
    {
        $this->apiBaseUrl = env('APP_URL_OLD', '');
        $this->apiEndpoint = OldHubNetApi::STORE_FILE_IMAGE_HUBNET_ENDPOINT->value;
        $this->requestTimeout = (int) OldHubNetApi::TIMEOUT->value;
    }

    /**
     * Retrieve file information for transport records from old HubNet API
     *
     * Makes authenticated HTTP POST request to legacy API with transport data.
     * Includes login session ID in headers for authentication.
     * Validates response structure and logs both success and failure cases.
     *
     * @param array $params Array of transport records, each containing:
     *                      - m_customer_id: Customer ID (numeric)
     *                      - transport_id: Transport ID (numeric)
     * @return array Array of file info:
     * - customer_id (int)
     * - customer_id (int)
     * - transport_id (int)
     * - name_files (array<string>)
     * - name_files_new (array<string>)
     *
     * @throws Exception When parameters are invalid, request fails, or response is malformed
     */
    public function call(array $params): array
    {
        $this->validateParams($params);

        $http = Http::acceptJson()->timeout(120)->retry(3, 300);

        foreach ($params['files'] as $i => $f) {
            $http = $http->attach(
                "trans_file[]",
                file_get_contents($f->getRealPath()),
                $f->getClientOriginalName(),
                ['Content-Type' => $f->getClientMimeType() ?? 'application/octet-stream']
            );
        }

        // dd($http);

        $response = $http->post($this->apiBaseUrl . $this->apiEndpoint, [
            'm_customer_id' => $params['m_customer_id'] ?? null,
            'hn_cd_chk' => 1
        ]);

        dd($response->body());

        if (!$response->successful()) {
            throw new Exception("HTTP request failed with status: {$response->status()}");
        }

        $responseData = $response->json();

        if (!$this->isValidResponse($responseData)) {
            throw new Exception("Invalid response structure from old HubNet API");
        }

        return $responseData['data'] ?? [];
    }

    /**
     * Validate transport parameters before API call
     *
     * Ensures all required fields are present and properly formatted.
     * Checks for empty arrays and validates data types.
     *
     * @param array $params Transport parameters to validate
     * @throws Exception When parameters are missing, empty, or invalid
     */
    private function validateParams(array $params): void
    {
        if (empty($params)) {
            throw new Exception("Parameters array cannot be empty");
        }
    }

    /**
     * Validate old HubNet API response structure
     *
     * Ensures response contains all required fields and proper data structure.
     * Validates both top-level response and individual file data items.
     *
     * @param array|null $response Raw API response to validate
     * @return bool True if response structure is valid, false otherwise
     */
    private function isValidResponse(?array $response): bool
    {
        return ! (!$response)



        ;
    }
}
