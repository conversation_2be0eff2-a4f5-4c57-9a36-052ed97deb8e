<?php

declare(strict_types=1);

namespace App\Http\Requests\Transport;

use Illuminate\Foundation\Http\FormRequest;

class ShippingReportMonthRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules()
    {
        return [
            'limit' => ['nullable', 'integer', 'min:1'], // default 20
            'page' => ['nullable', 'integer', 'min:1'],
            'month' => [
                'required',
                'integer',
                'min:1',
                'max:12',
            ],
        ];
    }
}
