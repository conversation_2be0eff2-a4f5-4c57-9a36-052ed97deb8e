<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Transport\ConfirmTransportEditRequest;
use App\Http\Requests\Transport\GetListBulkEditTransportRequest;
use App\Http\Requests\Transport\TransportBulkUpdateRequest;
use App\Http\Resources\Transport\TransportResource;
use App\Services\Transport\ConfirmEditTransportService;
use App\Services\Transport\GetListBulkEditTransportService;
use Illuminate\Http\JsonResponse;
use OpenApi\Attributes as OA;

/**
 * ConfirmEditTransportController
 *
 * Handles transport edit confirmation API endpoints
 */
class ConfirmEditTransportController extends Controller
{
    public function __construct(
        private readonly ConfirmEditTransportService $confirmEditTransportService,
        private readonly GetListBulkEditTransportService $getListBulkEditTransportService
    ) {
    }

    #[OA\Put(
        path: '/api/admin/transport/{id}/confirm-update',
        summary: 'Confirm transport update',
        description: 'Confirm the update of a transport record with comprehensive field updates',
        security: [['access_token' => []]],
        tags: ['Transport Management'],
        parameters: [
            new OA\Parameter(
                name: 'id',
                in: 'path',
                required: true,
                description: 'Transport ID',
                schema: new OA\Schema(type: 'integer', example: 1001)
            )
        ],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(
                properties: [
                    // System fields
                    new OA\Property(property: 'customer_name', type: 'string', description: 'Customer name', example: '株式会社テスト', nullable: true),

                    // Basic info
                    new OA\Property(property: 'fr_aa_place_id', type: 'integer', description: 'From AA place ID', example: 1, nullable: true),
                    new OA\Property(property: 'fr_name', type: 'string', description: 'From name', example: '送り主名', nullable: true),
                    new OA\Property(property: 'fr_addr', type: 'string', description: 'From address', example: '東京都渋谷区', nullable: true),
                    new OA\Property(property: 'fr_tel1', type: 'string', description: 'From telephone 1', example: '03', nullable: true),
                    new OA\Property(property: 'fr_tel2', type: 'string', description: 'From telephone 2', example: '1234', nullable: true),
                    new OA\Property(property: 'fr_tel3', type: 'string', description: 'From telephone 3', example: '5678', nullable: true),
                    new OA\Property(property: 'fr_tel', type: 'string', description: 'From telephone (combined)', example: '03-1234-5678', nullable: true),
                    new OA\Property(property: 'to_name', type: 'string', description: 'To name', example: '受け取り人名', nullable: true),
                    new OA\Property(property: 'to_addr', type: 'string', description: 'To address', example: '大阪府大阪市', nullable: true),
                    new OA\Property(property: 'to_tel1', type: 'string', description: 'To telephone 1', example: '06', nullable: true),
                    new OA\Property(property: 'to_tel2', type: 'string', description: 'To telephone 2', example: '1234', nullable: true),
                    new OA\Property(property: 'to_tel3', type: 'string', description: 'To telephone 3', example: '5678', nullable: true),
                    new OA\Property(property: 'to_tel', type: 'string', description: 'To telephone (combined)', example: '06-1234-5678', nullable: true),

                    // Car info
                    new OA\Property(property: 'car_name', type: 'string', description: 'Car name', example: 'トヨタ カローラ', nullable: true),
                    new OA\Property(property: 'car_no', type: 'string', description: 'Car number', example: 'ABC123', nullable: true),
                    new OA\Property(property: 'car_color', type: 'string', description: 'Car color', example: '白', nullable: true),
                    new OA\Property(property: 'plate_cut_flg', type: 'integer', description: 'Plate cut flag (0:なし, 1:あり, 2:その他)', example: 1, nullable: true),
                    new OA\Property(property: 'plate_no', type: 'string', description: 'Plate number', example: '品川500 あ 1234', nullable: true),
                    new OA\Property(property: 'luxury_money', type: 'integer', description: 'Luxury money', example: 50000, nullable: true),
                    new OA\Property(property: 'package_fee', type: 'integer', description: 'Package fee', example: 10000, nullable: true),

                    // Dates
                    new OA\Property(property: 'from_plan_date', type: 'string', description: 'From plan date', example: '2024-01-15', nullable: true),
                    new OA\Property(property: 'to_plan_date', type: 'string', description: 'To plan date', example: '2024-01-20', nullable: true),
                    new OA\Property(property: 'to_date', type: 'string', description: 'To date', example: '2024-01-18', nullable: true),
                    new OA\Property(property: 'plate_send_date', type: 'string', description: 'Plate send date', example: '2024-01-16', nullable: true),
                    new OA\Property(property: 'date_of_payment', type: 'string', description: 'Date of payment', example: '2024-01-17', nullable: true),
                    new OA\Property(property: 'date_of_payment_time', type: 'integer', description: 'Date of payment time (1:午前, 2:午後, 3:入金前搬出可能)', example: 1, nullable: true, enum: [1, 2, 3]),
                    new OA\Property(property: 'auction_date', type: 'string', description: 'Auction date', example: '2024-01-10', nullable: true),

                    // Status and flags
                    new OA\Property(property: 'st_cd', type: 'integer', description: 'Status code (0:確認中, 1:受付済, 2:下見完了, 3:保留中, 8:下見できず, 9:キャンセル)', example: 9, nullable: true),
                    new OA\Property(property: 'optn1_flg', type: 'integer', description: 'Option 1 flag (0:なし, 1:あり, 2:その他)', example: 1, nullable: true),
                    new OA\Property(property: 'undrivable_flg', type: 'integer', description: 'Undrivable flag (0:なし, 1:あり, 2:その他)', example: 0, nullable: true),
                    new OA\Property(property: 'tall_flg', type: 'integer', description: 'Tall flag (0:なし, 1:あり, 2:その他)', example: 0, nullable: true),
                    new OA\Property(property: 'lowdown_flg', type: 'integer', description: 'Lowdown flag (0:なし, 1:あり, 2:その他)', example: 0, nullable: true),
                    new OA\Property(property: 'long_flg', type: 'integer', description: 'Long flag (0:なし, 1:あり, 2:その他)', example: 0, nullable: true),
                    new OA\Property(property: 'old_flg', type: 'integer', description: 'Old flag (0:なし, 1:あり, 2:その他)', example: 0, nullable: true),
                    new OA\Property(property: 'luxury_flg', type: 'integer', description: 'Luxury flag (0:なし, 1:あり, 2:その他)', example: 1, nullable: true),
                    new OA\Property(property: 'luxury_flg_insrance', type: 'integer', description: 'Luxury flag insurance (0:なし, 1:あり, 2:その他)', example: 1, nullable: true),
                    new OA\Property(property: 'other_flg', type: 'integer', description: 'Other flag (0:なし, 1:あり, 2:その他)', example: 0, nullable: true),
                    new OA\Property(property: 'tick_no_flg', type: 'integer', description: 'Ticket no flag (0:なし, 1:あり, 2:その他)', example: 0, nullable: true),
                    new OA\Property(property: 'auction_chk', type: 'integer', description: 'Auction check (0:なし, 1:あり, 2:その他)', example: 1, nullable: true),
                    new OA\Property(property: 'pos_chk', type: 'integer', description: 'POS check (0:なし, 1:あり, 2:その他)', example: 0, nullable: true),

                    // Prices
                    new OA\Property(property: 'deli_price', type: 'number', format: 'float', description: 'Delivery price', example: 50000, nullable: true),
                    new OA\Property(property: 'sales_price', type: 'number', format: 'float', description: 'Sales price', example: 60000, nullable: true),

                    // References
                    new OA\Property(property: 'pos_no', type: 'string', description: 'POS number', example: 'POS123', nullable: true),
                    new OA\Property(property: 'aa_no', type: 'string', description: 'AA number', example: 'AA456', nullable: true),
                    new OA\Property(property: 'ref_no', type: 'integer', description: 'Reference number', example: 789, nullable: true),
                    new OA\Property(property: 'agent_ref_no', type: 'string', description: 'Agent reference number', example: 'AGENT123', nullable: true),
                    new OA\Property(property: 'plate_send_no', type: 'string', description: 'Plate send number', example: 'PLATE456', nullable: true),
                    new OA\Property(property: 'plate_send_co', type: 'string', description: 'Plate send company', example: 'ヤマト運輸', nullable: true),
                    new OA\Property(property: 'other_flg_txt', type: 'string', description: 'Other flag text', example: 'その他備考', nullable: true),
                    new OA\Property(property: 'pos_chk_text', type: 'string', description: 'POS check text', example: 'POS確認事項', nullable: true),
                    new OA\Property(property: 'auction_txt', type: 'string', description: 'Auction text', example: 'オークション備考', nullable: true),

                    // Places
                    new OA\Property(property: 'fr_place_id', type: 'integer', description: 'From place ID', example: 1, nullable: true),
                    new OA\Property(property: 'to_place_id', type: 'integer', description: 'To place ID', example: 2, nullable: true),
                    new OA\Property(property: 'to_etc_place_id', type: 'integer', description: 'To etc place ID', example: 3, nullable: true),
                    new OA\Property(property: 'to_aa_place_id', type: 'integer', description: 'To AA place ID', example: 4, nullable: true),
                    new OA\Property(property: 'm_trans_id', type: 'integer', description: 'Transport ID', example: 5, nullable: true),

                    // Country and shipping
                    new OA\Property(property: 'country', type: 'string', description: 'Country', example: 'Japan', nullable: true),
                    new OA\Property(property: 'country_free', type: 'string', description: 'Country free', example: '日本', nullable: true),
                    new OA\Property(property: 'country_cd', type: 'integer', description: 'Country code', example: 1, nullable: true),
                    new OA\Property(property: 'country_area', type: 'integer', description: 'Country area', example: 1, nullable: true),
                    new OA\Property(property: 'port_cd', type: 'integer', description: 'Port code', example: 1, nullable: true),
                    new OA\Property(property: 'port', type: 'string', description: 'Port', example: '東京港', nullable: true),
                    new OA\Property(property: 'service_name', type: 'string', description: 'Service name', example: 'サービス名', nullable: true),
                    new OA\Property(property: 'vessel_voy', type: 'string', description: 'Vessel voyage', example: 'VESSEL001', nullable: true),
                    new OA\Property(property: 'etd', type: 'string', description: 'ETD', example: '2024-01-15', nullable: true),
                    new OA\Property(property: 'eta', type: 'string', description: 'ETA', example: '2024-01-20', nullable: true),
                    new OA\Property(property: 'fin_ship', type: 'string', description: 'Final ship', example: 'FINAL001', nullable: true),
                    new OA\Property(property: 'service_kbn', type: 'string', description: 'Service category', example: 'S1', nullable: true),

                    // Auction info
                    new OA\Property(property: 'auc_name', type: 'string', description: 'Auction name', example: 'オークション名', nullable: true),
                    new OA\Property(property: 'auc_addr', type: 'string', description: 'Auction address', example: 'オークション住所', nullable: true),
                    new OA\Property(property: 'auc_tel', type: 'string', description: 'Auction telephone', example: '03-1234-5678', nullable: true),

                    // Plate sending
                    new OA\Property(property: 'plate_send_name', type: 'string', description: 'Plate send name', example: '送付先名', nullable: true),
                    new OA\Property(property: 'plate_send_zipcd', type: 'string', description: 'Plate send zip code', example: '123-4567', nullable: true),
                    new OA\Property(property: 'plate_send_address', type: 'string', description: 'Plate send address', example: '送付先住所', nullable: true),
                    new OA\Property(property: 'plate_send_tel', type: 'string', description: 'Plate send telephone', example: '03-1234-5678', nullable: true),

                    // Notes and additional fields
                    new OA\Property(property: 'note', type: 'string', description: 'Note', example: 'メモ', nullable: true),
                    new OA\Property(property: 'comment', type: 'string', description: 'Comment', example: 'コメント', nullable: true),
                    new OA\Property(property: 'commt_biko_reporter', type: 'string', description: 'Comment reporter', example: '管理者', nullable: true),
                    new OA\Property(property: 'tp_id', type: 'integer', description: 'Transport note ID', example: 1, nullable: true),
                    new OA\Property(property: 'reg_date', type: 'string', description: 'Registration date', example: '2024-01-15 10:30:00', nullable: true),
                    new OA\Property(property: 'name', type: 'string', description: 'Name for notes', example: '登録者名', nullable: true),
                    new OA\Property(property: 'list_show_flg', type: 'integer', description: 'List show flag', example: 1, nullable: true),

                    // Status fields
                    new OA\Property(property: 'inp_ah_name', type: 'string', description: 'Input AH name', example: 'admin_user', nullable: true),
                    new OA\Property(property: 'ah_id', type: 'integer', description: 'AH ID', example: 1, nullable: true),
                    new OA\Property(property: 'odr_date', type: 'string', description: 'Order date', example: '2024-01-01', nullable: true),

                    // File attachment fields
                    new OA\Property(property: 'm_customer_id', type: 'integer', description: 'Customer ID for files', example: 1, nullable: true),
                    new OA\Property(
                        property: 'trans_file',
                        type: 'array',
                        description: 'Transport files array',
                        items: new OA\Items(type: 'string', example: 'temp_file_001.pdf'),
                        nullable: true
                    ),
                ]
            )
        ),
        responses: [
            new OA\Response(
                response: 200,
                description: 'Transport edit confirmed successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'code', type: 'integer', example: 200),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Success'),
                        new OA\Property(
                            property: 'data',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'transport', type: 'object'),
                                new OA\Property(
                                    property: 'trans_file_name',
                                    type: 'array',
                                    description: 'Transport file names',
                                    items: new OA\Items(type: 'string', example: 'invoice_001.pdf'),
                                    nullable: true
                                ),
                                new OA\Property(
                                    property: 'trans_file_name_new',
                                    type: 'array',
                                    description: 'New transport file names',
                                    items: new OA\Items(type: 'string', example: 'new_invoice_001.pdf'),
                                    nullable: true
                                ),
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 400,
                description: 'Bad request - Invalid input data',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 400),
                        new OA\Property(property: 'message', type: 'string', example: 'Failed to confirm transport edit')
                    ]
                )
            ),
            new OA\Response(
                response: 404,
                description: 'Transport not found',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 404),
                        new OA\Property(property: 'message', type: 'string', example: 'Transport not found')
                    ]
                )
            ),
            new OA\Response(
                response: 403,
                description: 'Forbidden - Insufficient permissions',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 403),
                        new OA\Property(property: 'message', type: 'string', example: 'Insufficient permissions')
                    ]
                )
            ),
            new OA\Response(
                response: 500,
                description: 'Internal server error',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 500),
                        new OA\Property(property: 'message', type: 'string', example: 'Failed to confirm transport edit')
                    ]
                )
            )
        ]
    )]
    public function confirmUpdate(ConfirmTransportEditRequest $request, int $id): JsonResponse
    {
        $data = $request->validated();

        // Process the validated data
        $filePath = $this->confirmEditTransportService->confirmUpdate($id, $data);

        return $this->respond([
            'trans_file_name' => [],
            'trans_file_name_new' => []
        ]);
    }

    #[OA\Put(
        path: '/api/admin/transport/{id}/update',
        summary: 'Confirm transport update',
        description: 'Confirm the update of a transport record with comprehensive field updates',
        security: [['access_token' => []]],
        tags: ['Transport Management'],
        parameters: [
            new OA\Parameter(
                name: 'id',
                in: 'path',
                required: true,
                description: 'Transport ID',
                schema: new OA\Schema(type: 'integer', example: 1001)
            )
        ],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(
                properties: [
                    // System fields
                    new OA\Property(property: 'customer_name', type: 'string', description: 'Customer name', example: '株式会社テスト', nullable: true),

                    // Basic info
                    new OA\Property(property: 'fr_aa_place_id', type: 'integer', description: 'From AA place ID', example: 1, nullable: true),
                    new OA\Property(property: 'fr_name', type: 'string', description: 'From name', example: '送り主名', nullable: true),
                    new OA\Property(property: 'fr_addr', type: 'string', description: 'From address', example: '東京都渋谷区', nullable: true),
                    new OA\Property(property: 'fr_tel1', type: 'string', description: 'From telephone 1', example: '03', nullable: true),
                    new OA\Property(property: 'fr_tel2', type: 'string', description: 'From telephone 2', example: '1234', nullable: true),
                    new OA\Property(property: 'fr_tel3', type: 'string', description: 'From telephone 3', example: '5678', nullable: true),
                    new OA\Property(property: 'fr_tel', type: 'string', description: 'From telephone (combined)', example: '03-1234-5678', nullable: true),
                    new OA\Property(property: 'to_name', type: 'string', description: 'To name', example: '受け取り人名', nullable: true),
                    new OA\Property(property: 'to_addr', type: 'string', description: 'To address', example: '大阪府大阪市', nullable: true),
                    new OA\Property(property: 'to_tel1', type: 'string', description: 'To telephone 1', example: '06', nullable: true),
                    new OA\Property(property: 'to_tel2', type: 'string', description: 'To telephone 2', example: '1234', nullable: true),
                    new OA\Property(property: 'to_tel3', type: 'string', description: 'To telephone 3', example: '5678', nullable: true),
                    new OA\Property(property: 'to_tel', type: 'string', description: 'To telephone (combined)', example: '06-1234-5678', nullable: true),

                    // Car info
                    new OA\Property(property: 'car_name', type: 'string', description: 'Car name', example: 'トヨタ カローラ', nullable: true),
                    new OA\Property(property: 'car_no', type: 'string', description: 'Car number', example: 'ABC123', nullable: true),
                    new OA\Property(property: 'car_color', type: 'string', description: 'Car color', example: '白', nullable: true),
                    new OA\Property(property: 'plate_cut_flg', type: 'integer', description: 'Plate cut flag (0:なし, 1:あり, 2:その他)', example: 1, nullable: true),
                    new OA\Property(property: 'plate_no', type: 'string', description: 'Plate number', example: '品川500 あ 1234', nullable: true),
                    new OA\Property(property: 'luxury_money', type: 'integer', description: 'Luxury money', example: 50000, nullable: true),
                    new OA\Property(property: 'package_fee', type: 'integer', description: 'Package fee', example: 10000, nullable: true),

                    // Dates
                    new OA\Property(property: 'from_plan_date', type: 'string', description: 'From plan date', example: '2024-01-15', nullable: true),
                    new OA\Property(property: 'to_plan_date', type: 'string', description: 'To plan date', example: '2024-01-20', nullable: true),
                    new OA\Property(property: 'to_date', type: 'string', description: 'To date', example: '2024-01-18', nullable: true),
                    new OA\Property(property: 'plate_send_date', type: 'string', description: 'Plate send date', example: '2024-01-16', nullable: true),
                    new OA\Property(property: 'date_of_payment', type: 'string', description: 'Date of payment', example: '2024-01-17', nullable: true),
                    new OA\Property(property: 'date_of_payment_time', type: 'integer', description: 'Date of payment time (1:午前, 2:午後, 3:入金前搬出可能)', example: 1, nullable: true, enum: [1, 2, 3]),
                    new OA\Property(property: 'auction_date', type: 'string', description: 'Auction date', example: '2024-01-10', nullable: true),

                    // Status and flags
                    new OA\Property(property: 'st_cd', type: 'integer', description: 'Status code (0:確認中, 1:受付済, 2:下見完了, 3:保留中, 8:下見できず, 9:キャンセル)', example: 9, nullable: true),
                    new OA\Property(property: 'optn1_flg', type: 'integer', description: 'Option 1 flag (0:なし, 1:あり, 2:その他)', example: 1, nullable: true),
                    new OA\Property(property: 'undrivable_flg', type: 'integer', description: 'Undrivable flag (0:なし, 1:あり, 2:その他)', example: 0, nullable: true),
                    new OA\Property(property: 'tall_flg', type: 'integer', description: 'Tall flag (0:なし, 1:あり, 2:その他)', example: 0, nullable: true),
                    new OA\Property(property: 'lowdown_flg', type: 'integer', description: 'Lowdown flag (0:なし, 1:あり, 2:その他)', example: 0, nullable: true),
                    new OA\Property(property: 'long_flg', type: 'integer', description: 'Long flag (0:なし, 1:あり, 2:その他)', example: 0, nullable: true),
                    new OA\Property(property: 'old_flg', type: 'integer', description: 'Old flag (0:なし, 1:あり, 2:その他)', example: 0, nullable: true),
                    new OA\Property(property: 'luxury_flg', type: 'integer', description: 'Luxury flag (0:なし, 1:あり, 2:その他)', example: 1, nullable: true),
                    new OA\Property(property: 'luxury_flg_insrance', type: 'integer', description: 'Luxury flag insurance (0:なし, 1:あり, 2:その他)', example: 1, nullable: true),
                    new OA\Property(property: 'other_flg', type: 'integer', description: 'Other flag (0:なし, 1:あり, 2:その他)', example: 0, nullable: true),
                    new OA\Property(property: 'tick_no_flg', type: 'integer', description: 'Ticket no flag (0:なし, 1:あり, 2:その他)', example: 0, nullable: true),
                    new OA\Property(property: 'auction_chk', type: 'integer', description: 'Auction check (0:なし, 1:あり, 2:その他)', example: 1, nullable: true),
                    new OA\Property(property: 'pos_chk', type: 'integer', description: 'POS check (0:なし, 1:あり, 2:その他)', example: 0, nullable: true),

                    // Prices
                    new OA\Property(property: 'deli_price', type: 'number', format: 'float', description: 'Delivery price', example: 50000, nullable: true),
                    new OA\Property(property: 'sales_price', type: 'number', format: 'float', description: 'Sales price', example: 60000, nullable: true),

                    // References
                    new OA\Property(property: 'pos_no', type: 'string', description: 'POS number', example: 'POS123', nullable: true),
                    new OA\Property(property: 'aa_no', type: 'string', description: 'AA number', example: 'AA456', nullable: true),
                    new OA\Property(property: 'ref_no', type: 'integer', description: 'Reference number', example: 789, nullable: true),
                    new OA\Property(property: 'agent_ref_no', type: 'string', description: 'Agent reference number', example: 'AGENT123', nullable: true),
                    new OA\Property(property: 'plate_send_no', type: 'string', description: 'Plate send number', example: 'PLATE456', nullable: true),
                    new OA\Property(property: 'plate_send_co', type: 'string', description: 'Plate send company', example: 'ヤマト運輸', nullable: true),
                    new OA\Property(property: 'other_flg_txt', type: 'string', description: 'Other flag text', example: 'その他備考', nullable: true),
                    new OA\Property(property: 'pos_chk_text', type: 'string', description: 'POS check text', example: 'POS確認事項', nullable: true),
                    new OA\Property(property: 'auction_txt', type: 'string', description: 'Auction text', example: 'オークション備考', nullable: true),

                    // Places
                    new OA\Property(property: 'fr_place_id', type: 'integer', description: 'From place ID', example: 1, nullable: true),
                    new OA\Property(property: 'to_place_id', type: 'integer', description: 'To place ID', example: 2, nullable: true),
                    new OA\Property(property: 'to_etc_place_id', type: 'integer', description: 'To etc place ID', example: 3, nullable: true),
                    new OA\Property(property: 'to_aa_place_id', type: 'integer', description: 'To AA place ID', example: 4, nullable: true),
                    new OA\Property(property: 'm_trans_id', type: 'integer', description: 'Transport ID', example: 5, nullable: true),

                    // Country and shipping
                    new OA\Property(property: 'country', type: 'string', description: 'Country', example: 'Japan', nullable: true),
                    new OA\Property(property: 'country_free', type: 'string', description: 'Country free', example: '日本', nullable: true),
                    new OA\Property(property: 'country_cd', type: 'integer', description: 'Country code', example: 1, nullable: true),
                    new OA\Property(property: 'country_area', type: 'integer', description: 'Country area', example: 1, nullable: true),
                    new OA\Property(property: 'port_cd', type: 'integer', description: 'Port code', example: 1, nullable: true),
                    new OA\Property(property: 'port', type: 'string', description: 'Port', example: '東京港', nullable: true),
                    new OA\Property(property: 'service_name', type: 'string', description: 'Service name', example: 'サービス名', nullable: true),
                    new OA\Property(property: 'vessel_voy', type: 'string', description: 'Vessel voyage', example: 'VESSEL001', nullable: true),
                    new OA\Property(property: 'etd', type: 'string', description: 'ETD', example: '2024-01-15', nullable: true),
                    new OA\Property(property: 'eta', type: 'string', description: 'ETA', example: '2024-01-20', nullable: true),
                    new OA\Property(property: 'fin_ship', type: 'string', description: 'Final ship', example: 'FINAL001', nullable: true),
                    new OA\Property(property: 'service_kbn', type: 'string', description: 'Service category', example: 'S1', nullable: true),

                    // Auction info
                    new OA\Property(property: 'auc_name', type: 'string', description: 'Auction name', example: 'オークション名', nullable: true),
                    new OA\Property(property: 'auc_addr', type: 'string', description: 'Auction address', example: 'オークション住所', nullable: true),
                    new OA\Property(property: 'auc_tel', type: 'string', description: 'Auction telephone', example: '03-1234-5678', nullable: true),

                    // Plate sending
                    new OA\Property(property: 'plate_send_name', type: 'string', description: 'Plate send name', example: '送付先名', nullable: true),
                    new OA\Property(property: 'plate_send_zipcd', type: 'string', description: 'Plate send zip code', example: '123-4567', nullable: true),
                    new OA\Property(property: 'plate_send_address', type: 'string', description: 'Plate send address', example: '送付先住所', nullable: true),
                    new OA\Property(property: 'plate_send_tel', type: 'string', description: 'Plate send telephone', example: '03-1234-5678', nullable: true),

                    // Notes and additional fields
                    new OA\Property(property: 'note', type: 'string', description: 'Note', example: 'メモ', nullable: true),
                    new OA\Property(property: 'comment', type: 'string', description: 'Comment', example: 'コメント', nullable: true),
                    new OA\Property(property: 'commt_biko_reporter', type: 'string', description: 'Comment reporter', example: '管理者', nullable: true),
                    new OA\Property(property: 'tp_id', type: 'integer', description: 'Transport note ID', example: 1, nullable: true),
                    new OA\Property(property: 'reg_date', type: 'string', description: 'Registration date', example: '2024-01-15 10:30:00', nullable: true),
                    new OA\Property(property: 'name', type: 'string', description: 'Name for notes', example: '登録者名', nullable: true),
                    new OA\Property(property: 'list_show_flg', type: 'integer', description: 'List show flag', example: 1, nullable: true),

                    // Status fields
                    new OA\Property(property: 'inp_ah_name', type: 'string', description: 'Input AH name', example: 'admin_user', nullable: true),
                    new OA\Property(property: 'ah_id', type: 'integer', description: 'AH ID', example: 1, nullable: true),
                    new OA\Property(property: 'odr_date', type: 'string', description: 'Order date', example: '2024-01-01', nullable: true),

                    // File attachment fields
                    new OA\Property(property: 'm_customer_id', type: 'integer', description: 'Customer ID for files', example: 1, nullable: true),
                    new OA\Property(
                        property: 'name_files',
                        type: 'array',
                        description: 'Transport files array get api confirm',
                        items: new OA\Items(type: 'string', example: 'temp_file_001.pdf'),
                        nullable: true
                    ),
                    new OA\Property(
                        property: 'name_files_new',
                        type: 'array',
                        description: 'New transport files array',
                        items: new OA\Items(type: 'string', example: 'temp_file_001.pdf'),
                        nullable: true
                    ),
                ]
            )
        ),
        responses: [
            new OA\Response(
                response: 200,
                description: 'Transport edit confirmed successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'code', type: 'integer', example: 200),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Success'),
                        new OA\Property(
                            property: 'data',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'transport', type: 'object'),
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 400,
                description: 'Bad request - Invalid input data',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 400),
                        new OA\Property(property: 'message', type: 'string', example: 'Failed to confirm transport edit')
                    ]
                )
            ),
            new OA\Response(
                response: 404,
                description: 'Transport not found',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 404),
                        new OA\Property(property: 'message', type: 'string', example: 'Transport not found')
                    ]
                )
            ),
            new OA\Response(
                response: 403,
                description: 'Forbidden - Insufficient permissions',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 403),
                        new OA\Property(property: 'message', type: 'string', example: 'Insufficient permissions')
                    ]
                )
            ),
            new OA\Response(
                response: 500,
                description: 'Internal server error',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 500),
                        new OA\Property(property: 'message', type: 'string', example: 'Failed to confirm transport edit')
                    ]
                )
            )
        ]
    )]
    public function updateTransport(ConfirmTransportEditRequest $request, int $id): JsonResponse
    {
        $result = $this->confirmEditTransportService->updateTransport($id, $request->validated());

        return $this->respond(new TransportResource($result));
    }

    #[OA\Put(
        path: '/api/admin/transport/bulk-edit',
        summary: 'Bulk edit transport records',
        description: 'Update multiple transport records with specified fields and optional email notifications. Fields are automatically separated into transport_data and transport_notes_data.',
        security: [['access_token' => []]],
        tags: ['Transport Management'],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(
                required: ['id_list'],
                properties: [
                    new OA\Property(
                        property: 'id_list',
                        type: 'string',
                        description: 'Comma-separated list of transport IDs to update. Non-numeric values will be filtered out.',
                        example: '1001,abc,1002,1003,xyz'
                    ),
                    new OA\Property(property: 'update_all', type: 'boolean', description: 'Update all fields flag', example: false, nullable: true),
                    new OA\Property(property: 'st_cd', type: 'string', description: 'Status code', example: '9', nullable: true),
                    new OA\Property(property: 'from_plan_date', type: 'string', description: 'Pickup plan date (Y-m-d format)', example: '2024-01-15', nullable: true),
                    new OA\Property(property: 'to_plan_date', type: 'string', description: 'Delivery plan date (Y-m-d format)', example: '2024-01-20', nullable: true),
                    new OA\Property(property: 'to_date', type: 'string', description: 'Delivery date (Y-m-d format)', example: '2024-01-18', nullable: true),
                    new OA\Property(property: 'plate_send_date', type: 'string', description: 'Plate send date (Y-m-d format)', example: '2024-01-16', nullable: true),
                    new OA\Property(property: 'plate_send_co', type: 'string', description: 'Plate send company', example: 'Yamato Transport', nullable: true),
                    new OA\Property(property: 'plate_send_no', type: 'string', description: 'Plate send tracking number', example: 'YT123456789', nullable: true),
                    new OA\Property(property: 'deli_price', type: 'string', description: 'Delivery price', example: '50000', nullable: true),
                    new OA\Property(property: 'sales_price', type: 'string', description: 'Sales price', example: '60000', nullable: true),
                    new OA\Property(property: 'comment', type: 'string', description: 'Transport note comment', example: 'Updated delivery information', nullable: true),
                    new OA\Property(property: 'commt_biko_reporter', type: 'string', description: 'Comment reporter', example: 'Admin User', nullable: true),
                ]
            )
        ),
        responses: [
            new OA\Response(
                response: 200,
                description: 'Transport records updated successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'code', type: 'integer', example: 200),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Transport records updated successfully'),
                        new OA\Property(
                            property: 'data',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'updated_count', type: 'integer', example: 3, description: 'Number of records updated'),
                                new OA\Property(property: 'transport_ids', type: 'array', items: new OA\Items(type: 'integer'), description: 'Array of transport IDs (when update_all=false)'),
                                new OA\Property(property: 'total_records', type: 'integer', example: 150, description: 'Total number of records (when update_all=true)'),
                                new OA\Property(property: 'mail_sent', type: 'boolean', example: true),
                                new OA\Property(property: 'mail_sent_count', type: 'integer', example: 2),
                                new OA\Property(property: 'notes_created', type: 'integer', example: 2)
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 400,
                description: 'Bad request - Invalid input data',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 400),
                        new OA\Property(property: 'message', type: 'string', example: 'Invalid input data'),
                        new OA\Property(
                            property: 'errors',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'id_list', type: 'array', items: new OA\Items(type: 'string'), example: ['The id list field is invalid.']),
                                new OA\Property(property: 'deli_price', type: 'array', items: new OA\Items(type: 'string'), example: ['The deli price must be an integer.'])
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 403,
                description: 'Forbidden - Insufficient permissions',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 403),
                        new OA\Property(property: 'message', type: 'string', example: 'Insufficient permissions')
                    ]
                )
            ),
            new OA\Response(
                response: 404,
                description: 'One or more transport records not found',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 404),
                        new OA\Property(property: 'message', type: 'string', example: 'One or more transport records not found')
                    ]
                )
            ),
            new OA\Response(
                response: 500,
                description: 'Internal server error',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 500),
                        new OA\Property(property: 'message', type: 'string', example: 'Failed to update transport records')
                    ]
                )
            )
        ]
    )]
    public function bulkEdit(TransportBulkUpdateRequest $request): JsonResponse
    {
        $validated = $request->validated();
        if ($request->input('update_all', false)) {
            $result = $this->confirmEditTransportService->bulkUpdateAll($validated);
        } else {
            $result = $this->confirmEditTransportService->bulkEditTransport($validated);
        }
        return $this->respond($result);
    }
    #[OA\Get(
        path: '/api/admin/transport/bulk-edit-list',
        summary: 'Get transport list for bulk edit',
        description: 'Get paginated transport list with specific fields for bulk edit operation. Returns transport records with essential fields needed for bulk editing.',
        security: [['access_token' => []]],
        tags: ['Transport Management'],
        parameters: [
            new OA\Parameter(
                name: 'limit',
                description: 'Number of items per page (default: 20, max: 100)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'integer', minimum: 1, maximum: 100, example: 20)
            ),
            new OA\Parameter(
                name: 'page',
                description: 'Page number for pagination (default: 1)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'integer', minimum: 1, example: 1)
            ),
            new OA\Parameter(
                name: 'id_list',
                description: 'Comma-separated list of transport IDs to filter. Non-numeric values will be filtered out. Empty or null returns all records.',
                in: 'query',
                required: false,
                schema: new OA\Schema(
                    type: 'string',
                    example: '1001,abc,1002,1003,xyz'
                )
            ),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful operation',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'code', type: 'integer', example: 200),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Success'),
                        new OA\Property(
                            property: 'data',
                            type: 'object',
                            properties: [
                                new OA\Property(
                                    property: 'items',
                                    type: 'array',
                                    items: new OA\Items(
                                        type: 'object',
                                        properties: [
                                            new OA\Property(property: 'id', type: 'integer', example: 1, description: 'Transport ID'),
                                            new OA\Property(property: 'm_trans_id', type: 'string', example: 'TR001', description: 'Transport ID from master table'),
                                            new OA\Property(property: 'fr_name', type: 'string', example: 'Tokyo', description: 'From location name'),
                                            new OA\Property(property: 'to_name', type: 'string', example: 'Osaka', description: 'To location name'),
                                            new OA\Property(property: 'st_cd', type: 'string', example: 'ACTIVE', description: 'Status code'),
                                            new OA\Property(property: 'ref_no', type: 'string', example: 'REF123', description: 'Reference number'),
                                            new OA\Property(property: 'cus_name_JP', type: 'string', example: '田中太郎', description: 'Customer name in Japanese'),
                                            new OA\Property(property: 'car_no', type: 'string', example: 'ABC123', description: 'Car number'),
                                            new OA\Property(property: 'car_name', type: 'string', example: 'Toyota Camry', description: 'Car name'),
                                        ]
                                    )
                                ),
                                new OA\Property(
                                    property: 'meta',
                                    type: 'object',
                                    properties: [
                                        new OA\Property(property: 'current_page', type: 'integer', example: 1, description: 'Current page number'),
                                        new OA\Property(property: 'last_page', type: 'integer', example: 5, description: 'Last page number'),
                                        new OA\Property(property: 'per_page', type: 'integer', example: 20, description: 'Items per page'),
                                        new OA\Property(property: 'total', type: 'integer', example: 75, description: 'Total number of records'),
                                        new OA\Property(property: 'from', type: 'integer', example: 1, description: 'First item number on current page'),
                                        new OA\Property(property: 'to', type: 'integer', example: 20, description: 'Last item number on current page')
                                    ]
                                )
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 401,
                description: 'Unauthorized - Invalid or missing access token',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 401),
                        new OA\Property(property: 'message', type: 'string', example: 'Unauthorized')
                    ]
                )
            ),
            new OA\Response(
                response: 403,
                description: 'Forbidden - Insufficient transport admin permissions',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 403),
                        new OA\Property(property: 'message', type: 'string', example: 'Insufficient permissions')
                    ]
                )
            ),
            new OA\Response(
                response: 422,
                description: 'Validation Error - Invalid request parameters',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 422),
                        new OA\Property(property: 'message', type: 'string', example: 'The given data was invalid.'),
                        new OA\Property(
                            property: 'errors',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'limit', type: 'array', items: new OA\Items(type: 'string'), example: ['The limit must be between 1 and 100.']),
                                new OA\Property(property: 'page', type: 'array', items: new OA\Items(type: 'string'), example: ['The page must be at least 1.']),
                                new OA\Property(property: 'id_list', type: 'array', items: new OA\Items(type: 'string'), example: ['The id list field is invalid.'])
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 500,
                description: 'Internal server error',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 500),
                        new OA\Property(property: 'message', type: 'string', example: 'Internal server error')
                    ]
                )
            )
        ]
    )]
    public function getListBulkEditTransport(GetListBulkEditTransportRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $result = $this->getListBulkEditTransportService->call($validated);

        return $this->respond($result);
    }

}
