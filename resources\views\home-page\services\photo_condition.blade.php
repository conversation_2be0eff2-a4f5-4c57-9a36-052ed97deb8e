@extends('app')

@section('title', __('home-page/photo_condition.page_title'))

@section('content')
    <x-services.container>
        <x-services.title-page
            titlePage="{{ __('home-page/photo_condition.title') }}"
            description1="{{ __('home-page/photo_condition.page_description1') }}"
            description2="{{ __('home-page/photo_condition.page_description2') }}"
        />
        <x-services.title subTitle="{{ __('home-page/photo_condition.service_descriptions') }}" />
        <x-services.good-content :content="__('home-page/photo_condition.good_content')" />
        <div class="mx-auto mt-[45px] w-full px-4">
            <x-services.how-to-use
                :content1="__('home-page/photo_condition.how_to_use_content_1')"
                :content2="__('home-page/photo_condition.how_to_use_content_2')"
                icon1="fas fa-globe"
                icon2="fas fa-fax"
            />
            <div class="mx-[-15px] flex flex-wrap">
                <div class="2md:w-1/2 relative w-full px-[15px]">
                    <img
                        src="{{ asset('images/services/ah_service_image10_02.png') }}"
                        class="mx-auto block w-full !max-w-max"
                        alt=""
                    />
                </div>
                <div class="2md:w-1/2 2md:mt-0 relative mt-6 w-full px-[15px]">
                    <p
                        class="2sm:text-xl md:text-25 text-17 absolute top-1/2 left-1/2 m-0 w-full -translate-x-1/2 -translate-y-1/2 transform bg-white/70 p-[1em] text-center font-bold text-slate-400 text-shadow-gray-100"
                    >
                        {{ __('home-page/photo_condition.condition_check_sheet') }}
                    </p>
                    <img
                        src="{{ asset('images/services/condition_sheet.jpg') }}"
                        class="mx-auto block w-full !max-w-max"
                        alt=""
                    />
                </div>
            </div>
            <div class="mt-20 w-full md:mt-24">
                <div class="mb-4">
                    <h2
                        class="border-l-solid 2sm:text-25 md:text-32 border-l-5 border-l-sky-300 pl-[0.5em] text-xl font-medium md:pr-[15px] md:pl-[25px]"
                    >
                        {{ __('home-page/photo_condition.plan_contents') }}
                    </h2>
                </div>
                <div class="mt-8 mb-20 md:mt-12 md:mb-28">
                    @foreach ($plans as $index => $plan)
                        <x-services.photo-condition-plan-content
                            :index="$index"
                            :title="$plan['title']"
                            :content1="$plan['content1']"
                            :content2="$plan['content2']"
                            :icon2="$plan['icon2']"
                        />
                    @endforeach
                </div>
            </div>
            <x-services.content-item title="{{ __('common.functions') }}">
                <x-services.service-function-item
                    :index="1"
                    :content="__('home-page/photo_condition.function_1')"
                    icon="fas fa-envelope"
                    image="photo_condition_01.jpg"
                />
                <x-services.service-function-item
                    :index="2"
                    :content="__('home-page/photo_condition.function_2')"
                    icon="fas fa-globe"
                    image="photo_condition_02.jpg"
                />
            </x-services.content-item>
        </div>
        <x-services.service-options
            :description="__('home-page/photo_condition.hubnet_order')"
            iconRight="icon_menu_09.png"
            :titleRight="__('home-page/photo_condition.car_information')"
            :contentRight="__('home-page/photo_condition.track_trace')"
            linkRight="{{ env('APP_URL_OLD', '') }}/hn/tat/list_81_.php?lan={{ app()->getLocale() }}"
        />
    </x-services.container>
@endsection
