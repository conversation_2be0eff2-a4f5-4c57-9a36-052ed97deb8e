<?php

declare(strict_types=1);

namespace App\Models\sqlServer;

use Illuminate\Database\Eloquent\Model;

class ToPlace extends Model
{
    public const CREATED_AT = 'reg_date';
    public const UPDATED_AT = 'up_date';

    protected $fillable = [
        'id',
        'del_flg',
        'reg_date',
        'up_date',
        'up_owner',
        'name',
        'addr',
        'tel',
        'area_cd',
        'coop_flg',
        'nz_flg',
        'aus_flg',
        'pre_flg',
        'royal_flg',
        'custom_flg',
        'support_flg',
        'tsukan_flg',
        'comp_name',
        'zip',
        'paper_addr',
        'paper_person',
        'fax',
        'yard_name',
        'consignee_country_text',
        'sharing_flg',
        'sharing_sp_worker_id',
        'sort_no',
        'map_tag',
        'check_optn_flg',
        'sp_flg',
        'as400_cd',
        'as400_cd2',
        'lat_up_l',
        'lng_up_l',
        'lat_btm_r',
        'lng_btm_r',
        'sp_yard_flg',
        'sp_ichinen_flg',
        'container_flg',
        'sp_agent_flg',
        'sp_trans_flg',
        'usa_flg',
        'uk_flg',
        'delete_flg',
    ];

    protected $table = 't_to_place';
}
