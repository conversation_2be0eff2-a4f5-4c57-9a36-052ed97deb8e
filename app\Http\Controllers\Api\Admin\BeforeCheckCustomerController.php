<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\BeforeCheckCustomer\ListBeforeCheckCustomer;
use App\Http\Resources\BeforeCheckCustomer\ListBeforeCheckCustomerResource;
use App\Services\BeforeCheckCustomer\GetListBeforeCheckCustomerService;
use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Response;

class BeforeCheckCustomerController extends Controller
{
    private GetListBeforeCheckCustomerService $getListBeforeCheckCustomerService;

    public function __construct(GetListBeforeCheckCustomerService $getListBeforeCheckCustomerService)
    {
        $this->getListBeforeCheckCustomerService = $getListBeforeCheckCustomerService;
    }

    #[OA\Get(
        path: '/api/admin/before-check-customers',
        description: 'Retrieve a list of customers that need to be checked before processing. When update=go_update is provided, the system will reload data from the main tables. Filters by specific auction places (45, 25, 77), date range (last 5 years), and excludes customer ID 1001.',
        summary: 'Get list of before check customers',
        security: [['access_token' => []]],
        tags: ['Before check customers'],
        parameters: [
            new OA\Parameter(
                name: 'update',
                description: 'Trigger data reload from main tables. Use "go_update" to refresh the before check customer data.',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'go_update')
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'List retrieved successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'code', type: 'integer', example: 200),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Success'),
                        new OA\Property(
                            property: 'data',
                            type: 'array',
                            items: new OA\Items(
                                properties: [
                                    new OA\Property(property: 'm_customer_id', description: 'Customer ID', type: 'string', example: 'CUST001'),
                                    new OA\Property(property: 'cus_Name_JP', description: 'Customer name in Japanese', type: 'string', example: '株式会社テスト'),
                                    new OA\Property(property: 'cus_Name_EN', description: 'Customer name in English', type: 'string', example: 'Test Company Ltd.'),
                                    new OA\Property(property: 'odr_person', description: 'Order person name', type: 'string', example: '田中太郎'),
                                    new OA\Property(property: 'odr_tel', description: 'Order person telephone', type: 'string', example: '03-1234-5678'),
                                    new OA\Property(property: 'odr_mail', description: 'Order person email', type: 'string', example: '<EMAIL>')
                                ],
                                type: 'object'
                            )
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 401,
                description: 'Unauthorized - Invalid or missing authentication token',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 401),
                        new OA\Property(property: 'message', type: 'string', example: 'Unauthorized')
                    ]
                )
            ),
            new OA\Response(
                response: 403,
                description: 'Forbidden - Insufficient permissions or role restrictions',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 403),
                        new OA\Property(property: 'message', type: 'string', example: 'Access denied')
                    ]
                )
            ),
            new OA\Response(
                response: 500,
                description: 'Internal server error - Database transaction failed during data reload',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 500),
                        new OA\Property(property: 'message', type: 'string', example: 'Internal server error'),
                        new OA\Property(property: 'error', type: 'string', example: 'Database transaction failed')
                    ]
                )
            )
        ]
    )]
    public function getList(ListBeforeCheckCustomer $request): Response
    {
        return $this->respond(
            ListBeforeCheckCustomerResource::collection(
                $this->getListBeforeCheckCustomerService->call($request->validated())
            )
        );
    }
}
