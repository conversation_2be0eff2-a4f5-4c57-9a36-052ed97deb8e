<?php

declare(strict_types=1);

namespace App\Http\Requests\ToPlace;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Class GetDataDropdownToPlaceRequest
 *
 * Request used to validate data for fetching "To Place" dropdown list.
 */
class GetDataDropdownToPlaceRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            't_to_place-sp_flg-equal' => ['nullable', 'numeric'],

            'sort' => [
                'nullable',
                'string',
                'regex:/^([a-zA-Z0-9_]+\\|(asc|desc))(,\\s*[a-zA-Z0-9_]+\\|(asc|desc))*$/i',
            ]
        ];
    }

    public function messages(): array
    {
        return [
            't_to_place-sp_flg-equal.numeric' => 'The special flag must be a number (0 or 1).',
            't_to_place-sp_flg-equal.in' => 'The special flag must be either 0 (inactive) or 1 (active).',
            'sort.string' => 'The sort parameter must be a string.',
            'sort.regex' => 'The sort parameter must be in the format "column|direction". Examples: "sort_no|asc", "name|desc", "sort_no|asc,name|desc".',
        ];
    }
}
