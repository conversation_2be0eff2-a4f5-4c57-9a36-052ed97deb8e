<?php

declare(strict_types=1);

namespace App\Enums\CommonCode;

/**
 * CommonCodeCategory Enum
 *
 * Defines all cd_kbn categories used in t_com table
 * Based on system-wide analysis of constants and query usage
 */
enum CommonCodeCategory: string
{
    case PREFECTURE = '0001';           // Prefecture/ChikuRegion Codes - Geographic
    case INSPECTION_STATUS = '0002';    // Inspection Status Codes - Core Business
    case DESTINATION = '0007';          // Destination/Area Codes - Logistics
    case SERVICE_FEATURE = '0009';      // Service Feature Flags - Configuration
    case WORK_STATUS = '0011';          // Work Request Status - Operations
    case WORK_CATEGORY = '0012';        // Work Categories - Classification
    case EXHIBITION_TYPE = '0013';      // Exhibition Types - Events
    case EXHIBITION_SUBTYPE = '0014';   // Exhibition Subtypes - Events
    case EXHIBITION_STATUS = '0015';    // Exhibition Status - Events
    case EXHIBITION_FLAG = '0016';      // Exhibition Flags - Events
    case SERVICE_TYPE = '0018';         // Service Types (odr_kbn) - Loading System
}
