.required{
	color:#f00;font-size:14px;font-weight:bold;margin-left:5px;
}
#contact_form {
    background-color: #fff;padding: 3em 5em 4em 5em;position: relative;z-index: 1;color: #444;border-radius:5px;margin-top: 120px;
}
#contact_form::before,#contact_form::after {
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, .3);
    content: "";
    height: 100%;
    position: absolute;
    width: 100%;
}
#contact_form::before {
    background-color: rgba(255, 255, 255, .5);
    left: 0;top: 0;z-index: -1;
}
#contact_form::after {
    background: linear-gradient(-135deg, #dddfe0,#eee,#fff,#fff, #eee,#dddfe0);
    top: 5px;left: 5px;z-index: -2;
}
.bg-image01{
	background-image: url(../../public/images/home/<USER>
	position:absolute;z-index:-1;
}
.bg-fixed{
	width:100%;min-height: 100vh;background-attachment: fixed;background-size: auto;background-position:right top;
}
.contact_bg01{
	height:92px;width:100%;
}
.contact_bg02{
	background:rgb(255,255,255,1);
	height:300px;width:100%;
	box-shadow: 1px 1px 10px 0px grey, -1px -1px 10px 0px grey;
	opacity:0.8;
}
.contact_bg03{
	background-image: url(../../public/images/home/<USER>
    height:962px;
    width:100%;
}
.contact_header_div{
	height:80px;width:100%;
}
.btn-contact{
    cursor: pointer;
	background: linear-gradient(45deg, #ff6b00, #ff8c00);
    border: solid 2px #f9925b;
}
.btn-back{
    cursor: pointer;
	background: linear-gradient(45deg, #028cfd,#31a0fb,#028cfd);
    border: solid 2px #2199fb;
}
.btn-confirm{
    cursor: pointer;
	background: linear-gradient(45deg, #f9874b,#fda06f,#f9874b);
    border: solid 2px #f9925b;
}
.btn-confirm:hover {
    background: linear-gradient(45deg, #fda06f, #f9874b, #fda06f);
    color: #fff;
}
.btn-back:hover {
    background: linear-gradient(45deg, #31a0fb, #028cfd, #31a0fb);
    color: #fff;
}
.btn-return{
    cursor: pointer;
	background: linear-gradient(45deg, #f9874b,#fda06f,#f9874b);
    border: solid 2px #f9925b;
}
.btn-return:hover {
    background: linear-gradient(45deg, #fda06f,#f9874b,#fda06f);
    color: #fff;
}
input::placeholder,
textarea::placeholder {
    color: grey;
    opacity: 1;
}
@media screen and (max-width:576px) {
	.contact_header_div{
		height:60px;width:100%;
	}
	#contact_form{
		padding: 3em 1em 2em 1em;
	}
	.contact_bg03{
		height:1050px;background-size: 1000px;
	}
	.contact_bg02{
		height:250px;
	}
	.bg-fixed{
		background-size: 1000px;
	}
	.contact_bg01{
		height:40px;
	}

	#contact_form{
		margin-top:50px;
	}
}
@media screen and (min-width:576px) and (max-width:767px) {
	.contact_header_div{
		height:60px;width:100%;
	}
	#contact_form{
		padding: 3em 1em 2em 1em;
	}
	.contact_bg03{
		height:945px;background-size: 1500px;
	}
	.bg-fixed{
		background-size: 1000px;
	}
	.contact_bg01{
		height:40px;
	}

}
@media screen and (min-width: 768px) and (max-width: 991px){
	.contact_header_div{
		height:60px;width:100%;
	}
	#contact_form{
		padding: 3em 1em 2em 1em;
	}
	.contact_bg03{
		height:871px;
	}

}
@media screen and (min-width: 1920px){
	.bg-image01 {
		background-repeat: no-repeat;
		background-size: 1920px;
		background-position: left;
	}
}