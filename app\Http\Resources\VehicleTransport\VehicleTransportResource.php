<?php

declare(strict_types=1);

namespace App\Http\Resources\VehicleTransport;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VehicleTransportResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'HNCC03' => trim($this->HNCC03),
            'HNCC54' => trim($this->HNCC54),
            'HNNC25' => trim($this->HNNC25),
            'HNTD14' => trim($this->HNTD14),
            'HNTD19' => trim($this->HNTD19),
        ];
    }
}
