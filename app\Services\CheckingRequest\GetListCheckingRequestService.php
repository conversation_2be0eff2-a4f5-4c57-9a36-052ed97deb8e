<?php

declare(strict_types=1);

namespace App\Services\CheckingRequest;

use App\Constants\PageMetaData;
use App\Http\Resources\CheckingRequest\CheckingRequestResource;
use App\Repositories\sqlServer\CheckingRequestRepository;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Arr;

class GetListCheckingRequestService
{
    private CheckingRequestRepository $checkingRequestRepository;

    public function __construct(CheckingRequestRepository $checkingRequestRepository)
    {
        $this->checkingRequestRepository = $checkingRequestRepository;
    }

    public function call(array $params): AnonymousResourceCollection|array
    {
        $relations = ['customer', 'aaDate.aaPlace', 'inspector'];

        if (Arr::has($params, ['limit','page'])) {
            $pagination = $this->checkingRequestRepository->paginate($params, $relations);

            return [
                'items' => CheckingRequestResource::collection($pagination),
                'meta' => (new PageMetaData($pagination))->toArray()
            ];
        }

        $checkingRequests = $this->checkingRequestRepository->getList($params, $relations);

        return CheckingRequestResource::collection($checkingRequests);
    }
}
