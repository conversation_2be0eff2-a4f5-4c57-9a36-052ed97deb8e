<?php

declare(strict_types=1);

namespace App\Repositories\as400;

use App\Models\as400\VehicleTransport;
use App\Repositories\BaseRepository;
use Exception;
use Illuminate\Support\Arr;
use App\Enums\Country\CountryCode;

class VehicleTransportRepository extends BaseRepository
{
    public function model(): mixed
    {
        return VehicleTransport::class;
    }

    public function getPaginate($params = null, array $relations = [], $columns = ['*'])
    {
        $query = ($this->model)::query();

        $params = $params ?: request()->toArray();
        $limit = Arr::get($params, 'limit', 20);
        $page = Arr::get($params, 'page', 1);

        $columns = [
            'HNCC03',
            'HNCC54',
            'HNNC25',
            'HNTD14',
            'HNTD19',
        ];

        $query = $query->where('HNYCKB', '<>', '1')
            ->where('HNKSKB', 'AH')
            ->whereIn('HNCS74', [CountryCode::NZ->value, CountryCode::AUS->value])
            ->where('HND2DK', '2')
            ->where('HNNN47', '<>', '')
            ->where('HNTD14', '>=', $params['from_date'])
            ->where('HNTD14', '<=', $params['to_date'])
            ->where('HNTD19', '>=', $params['from_date'])
            ->where('HNTD19', '<=', $params['to_date']);

        $query = $this->applyFilterScope($query, $params);

        if (isset($params['sort'])) {
            $this->buildSortQuery($query, $params);
        } else {
            $query->orderBy('HNTD14', 'ASC');
        }

        // Use custom AS400 pagination instead of standard Laravel paginate()
        try {
            // Try with count first (LengthAwarePaginator)
            return $this->customsPaginateAs400($query, $limit, $page, $columns, 'page', true);
        } catch (Exception $e) {
            // If that fails, try simple pagination without count
            return $this->simplePaginateAs400($query, $limit, $page, $columns);
        }
    }
}
