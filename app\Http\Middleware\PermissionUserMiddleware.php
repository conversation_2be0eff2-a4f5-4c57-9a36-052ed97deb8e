<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Exceptions\AuthException;
use App\Services\Jwt\JwtService;
use Closure;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response as HttpResponse;

readonly class PermissionUserMiddleware
{
    public function __construct(private JwtService $jwtService)
    {
    }

    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     * @throws AuthException
     */
    public function handle(Request $request, Closure $next, ...$permissions): mixed
    {
        $permissions = array_map('intval', $permissions);
        try {
            $user = Auth::user();

            if (in_array($user->hn_cd, $permissions)) {
                return $next($request);
            }

            throw new AuthException(code: HttpResponse::HTTP_FORBIDDEN);
        } catch (Exception $e) {
            Log::error('Error middleware permission for customer', [
                'method' => __METHOD__,
                'message' => $e->getMessage()
            ]);
            throw new AuthException(code: HttpResponse::HTTP_FORBIDDEN);
        }

    }
}
