@php
    /**
     * @var string $content
     * @var string $image
     * @var string $icon
     * @var int $index
     */
    $classContainer = '2sm:my-[5em] 2sm:px-[1em] 2md:my-[6em] relative my-[3em] flex flex-wrap items-center md:flex-nowrap ';
    $classIcon = '2sm:text-[200px] 2md:text-[300px] absolute top-0 transform text-[120px] text-[#F7F7B2] md:text-[250px] ';
    $classContent = '2sm:text-3xl 2md:w-3/5 2md:text-38 z-5 w-full text-center text-xl font-bold text-gray-200 opacity-80 md:w-[54%] ';
    $classImage = '2md:h-[350px] relative z-3 mx-auto my-[1em] !bg-cover !bg-center shadow-[0_0_10px_#aaa] md:m-0 ';
    if ($index % 2 !== 0) {
        $classIcon = $classIcon . '2md:right-75 right-[270px] left-0 -rotate-30 md:left-auto';
        $classContent = $classContent . 'md:mb-4';
        $classImage = $classImage . '2sm:w-75 2sm:h-75 2md:w-[350px] h-[230px] w-[230px] rounded-full md:ml-auto';
    } else {
        $classContainer = $classContainer . 'md:flex-row-reverse';
        $classIcon = $classIcon . '2md:top-10 right-5 rotate-30 md:right-0';
        $classImage = $classImage . '2md:w-150 2sm:w-[350px] 2sm:h-[233px] h-50 w-75 rounded-xl md:mr-auto md:h-[250px] md:w-[395px] md:rounded-4xl';
    }
@endphp

<div class="{{ $classContainer }}">
    <i class="{{ $icon . ' ' . $classIcon }}"></i>
    <p class="{{ $classContent }}">
        {!! $content !!}
    </p>
    <div class="{{ $classImage }}" style="background: url('{{ asset('images/services/' . $image) }}')"></div>
</div>
