<?php

declare(strict_types=1);

namespace App\Http\Controllers\HomePage;

use App\Http\Controllers\Controller;
use App\Modules\SailingSchedule\Services\SailingScheduleService;
use Illuminate\View\View;
use Throwable;

class SailingScheduleController extends Controller
{
    private SailingScheduleService $sailingScheduleService;

    public function __construct(SailingScheduleService $sailingScheduleService)
    {
        $this->sailingScheduleService = $sailingScheduleService;
    }

    public function index(): View
    {
        $params = request()->query();

        $routes = $this->sailingScheduleService->getRouteSchedule()->toArray(request());

        $portsAll = $this->sailingScheduleService->getPort();
        $ports = $portsAll['ports'];
        $portJa = $portsAll['japanese_ports'];

        $countries = $this->sailingScheduleService->getCountries()->toArray(request());

        $schedules = [];
        try {
            $schedules = $this->sailingScheduleService->getSchedule($params);
        } catch (Throwable $th) {
            $schedules = [];
        }

        return view('home-page.sailing-schedule', [
            'routes' => $routes,
            'ports' => $ports,
            'portJa' => $portJa,
            'countries' => $countries,
            'schedules' => $schedules,
        ]);
    }
}
