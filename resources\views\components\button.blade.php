@props([
    'type' => 'button',
    'variant' => 'primary',
    'size' => 'md',
    'href' => null,
    'disabled' => false,
    'class' => '',
])

@php
    $baseClasses = 'inline-flex w-80 cursor-pointer items-center justify-center rounded-md font-medium transition-colors focus:outline-none';

    $variants = [
        'primary' => 'bg-red-500 text-white hover:bg-[#c82333]',
        'secondary' => 'bg-gray-200 text-gray-900 hover:bg-gray-300',
        'danger' => 'bg-red-500 text-white hover:bg-red-600',
    ];

    $sizes = [
        'sm' => 'px-3 py-2 text-sm',
        'md' => 'px-4 py-2 text-base',
        'lg' => 'px-6 py-3 text-lg',
    ];

    $classes = $baseClasses . ' ' . $variants[$variant] . ' ' . $sizes[$size] . ' ' . ($disabled ? 'opacity-50 cursor-not-allowed' : '') . ' ' . $class;
@endphp

@if ($href)
    <a href="{{ $disabled ? '#' : $href }}" {{ $attributes->merge(['class' => $classes]) }}>
        {{ $slot }}
    </a>
@else
    <button type="{{ $type }}" {{ $attributes->merge(['class' => $classes]) }} {{ $disabled ? 'disabled' : '' }}>
        {{ $slot }}
    </button>
@endif
