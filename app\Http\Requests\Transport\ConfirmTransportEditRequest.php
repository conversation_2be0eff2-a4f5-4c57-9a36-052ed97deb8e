<?php

declare(strict_types=1);

namespace App\Http\Requests\Transport;

use App\Enums\Transport\DatePaymentTime;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ConfirmTransportEditRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Basic info - Integer fields
            'fr_aa_place_id' => ['nullable', 'integer'],
            'fr_place_id' => ['nullable', 'integer'],
            'to_place_id' => ['nullable', 'integer'],
            'to_etc_place_id' => ['nullable', 'integer'],
            'to_aa_place_id' => ['nullable', 'integer'],
            'm_trans_id' => ['nullable', 'integer'],

            // Basic info - String fields
            'fr_name' => ['nullable', 'string', 'max:100'],
            'fr_addr' => ['nullable', 'string', 'max:100'],
            'fr_tel' => ['nullable', 'string', 'max:15'],
            'to_name' => ['nullable', 'string', 'max:100'],
            'to_addr' => ['nullable', 'string', 'max:100'],
            'to_tel' => ['nullable', 'string', 'max:15'],

            // Car info - String fields
            'car_name' => ['nullable', 'string', 'max:100'],
            'car_no' => ['nullable', 'string', 'max:50'],
            'car_color' => ['nullable', 'string', 'max:50'],
            'plate_no' => ['nullable', 'string', 'max:60'],

            // Car info - Integer fields
            'luxury_money' => ['nullable', 'integer'],
            'package_fee' => ['nullable', 'integer'],

            // Status and flags - Integer fields (database int/tinyint)
            'plate_cut_flg' => ['nullable', 'integer'],
            'st_cd' => ['nullable', 'integer'],
            'optn1_flg' => ['nullable', 'integer'],
            'undrivable_flg' => ['nullable', 'integer'],
            'tall_flg' => ['nullable', 'integer'],
            'lowdown_flg' => ['nullable', 'integer'],
            'long_flg' => ['nullable', 'integer'],
            'old_flg' => ['nullable', 'integer'],
            'luxury_flg' => ['nullable', 'integer'],
            'luxury_flg_insrance' => ['nullable', 'integer'],
            'other_flg' => ['nullable', 'integer'],
            'tick_no_flg' => ['nullable', 'integer'],
            'auction_chk' => ['nullable', 'integer'],
            'pos_chk' => ['nullable', 'integer'],

            // Prices - Float fields (cast to float in model)
            'deli_price' => ['nullable', 'numeric'],
            'sales_price' => ['nullable', 'numeric'],

            // Dates - Date fields
            'from_plan_date' => ['nullable', 'date_format:Y-m-d'],
            'to_plan_date' => ['nullable', 'date_format:Y-m-d'],
            'to_date' => ['nullable', 'date_format:Y-m-d'],
            'plate_send_date' => ['nullable', 'date_format:Y-m-d'],
            'date_of_payment' => ['nullable', 'date_format:Y-m-d'],
            'auction_date' => ['nullable', 'date_format:Y-m-d'],
            'date_of_payment_time' => ['nullable', Rule::in(DatePaymentTime::getAllValues())],

            // References - String fields
            'pos_no' => ['nullable', 'string', 'max:20'],
            'aa_no' => ['nullable', 'string', 'max:20'],
            'ref_no' => ['nullable', 'integer'],
            'agent_ref_no' => ['nullable', 'string', 'max:50'],
            'plate_send_no' => ['nullable', 'string', 'max:50'],
            'plate_send_co' => ['nullable', 'string', 'max:200'],

            // Text fields
            'other_flg_txt' => ['nullable', 'string', 'max:255'],
            'pos_chk_text' => ['nullable', 'string', 'max:100'],
            'auction_txt' => ['nullable', 'string', 'max:200'],

            // Country and shipping - String fields
            'country' => ['nullable', 'string', 'max:255'],
            'country_free' => ['nullable', 'string', 'max:100'],
            'port' => ['nullable', 'string', 'max:100'],
            'service_name' => ['nullable', 'string', 'max:255'],
            'vessel_voy' => ['nullable', 'string', 'max:255'],
            'etd' => ['nullable', 'string', 'max:50'],
            'eta' => ['nullable', 'string', 'max:50'],
            'fin_ship' => ['nullable', 'string', 'max:50'],
            'service_kbn' => ['nullable', 'string', 'max:2'],

            // Country and shipping - Integer fields
            'country_cd' => ['nullable', 'integer'],
            'country_area' => ['nullable', 'integer'],
            'port_cd' => ['nullable', 'integer'],

            // Auction info - String fields
            'auc_name' => ['nullable', 'string', 'max:200'],
            'auc_addr' => ['nullable', 'string', 'max:255'],
            'auc_tel' => ['nullable', 'string', 'max:15'],

            // Plate sending - String fields
            'plate_send_name' => ['nullable', 'string', 'max:100'],
            'plate_send_zipcd' => ['nullable', 'string', 'max:10'],
            'plate_send_address' => ['nullable', 'string', 'max:255'],
            'plate_send_tel' => ['nullable', 'string', 'max:255'],

            // Notes fields
            'tp_id' => ['nullable', 'integer'],
            'reg_date' => ['nullable', 'date_format:Y-m-d H:i:s'],
            'name' => ['nullable', 'string', 'max:100'],
            'list_show_flg' => ['nullable', 'integer'],
            'comment' => ['nullable', 'string', 'max:1000'],
            'commt_biko_reporter' => ['nullable', 'string', 'max:20'],

            // Image fields
            'name_files_new' => ['nullable', 'array'],
            'name_files' => ['nullable', 'array'],
            'trans_file' => [
                'nullable', 'array',
            ],
            'trans_file.*' => ['file'],
            'm_customer_id' => ['nullable', 'integer'],
        ];
    }

}
