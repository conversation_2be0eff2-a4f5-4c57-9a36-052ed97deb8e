<?php

declare(strict_types=1);

namespace App\Services\Jwt;

use Exception;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\SignatureInvalidException;
use UnexpectedValueException;

class JwtService
{
    /**
     * Generate a JWT token with the provided payload and expiration time
     *
     * @param array $payload
     * @param int $expiresInSeconds Time in seconds until the token expires
     * @return string
     */
    public function generateToken(array $payload, int $expiresInSeconds): string
    {
        $issuedAt = time();
        $expiration = $issuedAt + $expiresInSeconds;

        $tokenPayload = array_merge($payload, [
            'iat' => $issuedAt,    // Issued at
            'exp' => $expiration,  // Expiration time
        ]);

        return $this->encodeToken($tokenPayload);
    }

    public function generateTokenWithTimeExpired(array $payload, int $issuedAt, int $timeExpired): string
    {
        $tokenPayload = array_merge($payload, [
            'iat' => $issuedAt,    // Issued at
            'exp' => $timeExpired,  // Expiration time
        ]);

        return $this->encodeToken($tokenPayload);
    }

    /**
     * Encode an array payload into a JWT token
     *
     * @param array $payload
     * @return string
     */
    public function encodeToken(array $payload): string
    {
        return JWT::encode($payload, env('JWT_SECRET'), 'HS256');
    }

    /**
     * Decode a JWT token into an array
     *
     * @param string $token
     * @return array
     */
    public function decodeToken(string $token): array
    {
        $decoded = JWT::decode($token, new Key(env('JWT_SECRET'), 'HS256'));
        return (array) $decoded;
    }

    /**
     * Verify if a token is valid
     *
     * @param string $token
     * @return array|false Returns the decoded payload if valid, false otherwise
     */
    public function verifyToken(string $token): array|bool
    {
        try {
            return $this->decodeToken($token);
        } catch (ExpiredException $e) {
            // Token has expired
            return false;
        } catch (SignatureInvalidException $e) {
            // Invalid token signature
            return false;
        } catch (UnexpectedValueException $e) {
            // Invalid token structure
            return false;
        } catch (Exception $e) {
            // Any other errors
            return false;
        }
    }
}
