<?php

declare(strict_types=1);

namespace App\Models\as400;

use Illuminate\Database\Eloquent\Model;

class RouteSchedule extends Model
{
    public $timestamps = false;
    protected $connection = 'as400';
    protected $table = 'HCDLIB.MKRORP';

    protected $fillable = [
        'KRKRCD',    // Route Code
        'KRKRNM',    // Route Name
        'KRCRDT',    // Created Date (YYYYMMDD)
        'KRCRTM',    // Created Time (HHMMSS)
        'KRUPDT',    // Updated Date
        'KRUPTM',    // Updated Time
    ];
}
