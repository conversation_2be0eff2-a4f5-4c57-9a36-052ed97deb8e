<?php

declare(strict_types=1);

namespace App\Services\LoadingOrder;

use App\Constants\ApiCodes;
use App\Enums\ApiKey;
use App\Exceptions\ApiException;
use Illuminate\Http\Client\Response as HttpResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use RuntimeException;

class BulkDownloadFileZipLoadingOrderService
{
    private const API_ENDPOINT = 'backend/order/get_file_Download.php';
    private const HTTP_TIMEOUT = 90;
    private const USER_AGENT = 'Laravel-Transport-API/1.0';

    /**
     * Download zip file for given loading order IDs
     *
     * @param array $params ['ids' => array<int>]
     * @return Response
     * @throws ApiException
     */
    public function call(array $params): Response
    {
        $ids = $params['ids'] ?? [];
        $chkParam = implode('/', $ids);

        $apiUrl = $this->buildApiUrl();
        $response = $this->makeApiRequest($apiUrl, ['chk' => rtrim($chkParam, '/') . '/']);

        if ($response->successful()) {
            $filename = $this->extractFilenameFromHeaders($response) ?? $this->getZipFileName();

            $contentLength = $response->header('Content-Length');
            $headers = [
                'Content-Type'        => 'application/zip',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ];
            if (null !== $contentLength) {
                $headers['Content-Length'] = $contentLength;
            } else {
                $headers['Content-Length'] = strlen($response->body());
            }

            return response($response->body(), 200, $headers);
        }

        $this->logApiError($apiUrl, $params, $response);
        throw new ApiException(ApiCodes::FILE_DOWN_LOAD_ERROR, 'Could not download file ZIP');
    }

    /**
     * Build the complete API URL
     */
    private function buildApiUrl(): string
    {
        $oldApiUrl = config('app.APP_URL_OLD', env('APP_URL_OLD'));
        if (empty($oldApiUrl)) {
            Log::error('APP_URL_OLD is not configured');
            throw new RuntimeException('External API configuration error');
        }
        return rtrim($oldApiUrl, '/') . '/' . self::API_ENDPOINT;
    }

    /**
     * Make HTTP request to external API
     */
    private function makeApiRequest(string $apiUrl, array $params): HttpResponse
    {
        return Http::timeout(self::HTTP_TIMEOUT)
            ->withHeaders([
                'Accept'            => 'application/zip',
                'User-Agent'        => self::USER_AGENT,
                'X-Token-Validate'  => ApiKey::API_OLD_HUBNET->value,
            ])
            ->get($apiUrl, $params);
    }

    /**
     * Extract filename from Content-Disposition header
     */
    private function extractFilenameFromHeaders(HttpResponse $response): ?string
    {
        $disposition = $response->header('Content-Disposition');
        if ($disposition && preg_match('/filename="?([^"]+)"?/', $disposition, $m)) {
            return $m[1] ?? null;
        }
        return null;
    }

    private function getZipFileName(): string
    {
        return 'file_' . date('mds') . '.zip';
    }

    private function logApiError(string $url, array $params, HttpResponse $response): void
    {
        Log::error('Download ZIP failed', [
            'url'    => $url,
            'params' => $params,
            'status' => $response->status(),
            'body'   => $response->body(),
        ]);
    }
}
