.height80 {
  height: 80px;
}
.bg-image01 {
  background-image: url(../img/ah_recruit_background01.jpg);
  position: absolute;
  z-index: -1;
}
.bg-fixed {
  width: 100%;
  min-height: 100vh;
  background-attachment: fixed;
  background-size: auto;
  background-position: center;
}
.container-width {
  margin: 45px auto 0px;
}
h1.main_title {
  font-size: 35px;
  margin-top: 30px;
}
.recruit_top_p {
  line-height: 40px;
  font-size: 19px;
}
.recruit_main_title {
  font-size: 50px;
  font-weight: bold;
  font-family: "M PLUS Rounded 1c", sans-serif;
  margin-top: 100px;
}
.recruit_top_margin {
  margin-top: 127px;
}
.company_profile01_top {
  margin-top: 125px;
}
.contact_us {
  color: #495057;
  height: 320px;
  padding: 50px;
  background-image: url("../img/contact_image.jpg");
  background-size: 100%;
  box-shadow: -1px -1px 7px grey;
}
.btn-danger.detail-btn {
  height: 45px;
  line-height: 33px;
  font-size: 12px;
  margin-top: -19px;
}
#animation01 {
  top: -113px;
  left: 124px;
}
#animation02 {
  top: 240px;
  left: 260px;
}
#animation03 {
  top: 210px;
  left: 1150px;
}
#animation04 {
  top: 375px;
  left: 740px;
}
#animation05 {
  top: 25px;
  left: 816px;
}
#animation06 {
  top: 1000px;
  left: 100px;
}
#animation07 {
  top: 500px;
  left: 1600px;
}
#animation08 {
  top: 1200px;
  left: 90px;
}
#animation09 {
  top: 745px;
  left: 1790px;
}
#animation10 {
  top: 604px;
  left: -50px;
}
#animation11 {
  top: 1000px;
  left: 1600px;
}
#animation12 {
  top: 1500px;
  left: 230px;
}
#animation13 {
  top: 1500px;
  left: 1757px;
}
#animation14 {
  top: 480px;
  left: 900px;
}
#animation15 {
  top: 1280px;
  left: 1800px;
}
#animation16 {
  top: 930px;
  left: 1500px;
}
#animation17 {
  top: 512px;
  left: 1624px;
}
#animation18 {
  top: 900px;
  left: 90px;
}
#animation19 {
  top: 1200px;
  left: 1500px;
}
#animation20 {
  top: 1000px;
  left: 200px;
}
#animation21 {
  top: -45px;
  left: 1600px;
}
#animation22 {
  top: 1300px;
  left: 190px;
}
#animation23 {
  top: 1030px;
  left: 800px;
}
#animation24 {
  top: 1400px;
  left: 800px;
}
#animation25 {
  top: 50px;
  left: 1740px;
}
.bx-wrapper .bx-pager.bx-default-pager a {
  display: none;
}
.no-slider {
  webkit-box-shadow: 0 0 5px #ccc;
  box-shadow: 0 0 5px #ccc;
  border: 5px solid #fff;
  background: #fff;
  margin-bottom: 60px;
}
.br-1200 {
  display: none;
  line-height: 0px;
}
.br-576 {
  display: none;
  line-height: 0px;
}

.cp01 {
  position: absolute;
  top: 100%;
  left: 100%;
  transform: translate(-50%, 0%);
}

@media screen and (max-width: 576px) {
  h1.main_title {
    font-size: 24px;
    margin-top: 0px;
  }
  .recruit_top_p {
    line-height: 25px;
    font-size: 13px;
  }
  .recruit_top_margin {
    margin-top: 70px;
  }
  .recruit_main_title {
    font-size: 36px;
    margin-top: 0px;
  }
  .bg-fixed {
    background-size: 800px;
    background-position: top;
  }
  .rocker {
    transform: scale(0.7);
  }
  .company_profile01_top {
    margin-top: 70px;
  }
  .contact_us {
    color: #495057;
    height: 260px;
    padding: 50px;
    background-image: url("../img/contact_image.jpg");
    background-size: 165%;
    box-shadow: -1px -1px 7px grey;
    background-position: right -95px center;
  }
  .btn-danger.detail-btn {
    height: 45px;
    line-height: 33px;
    font-size: 12px;
    margin-top: -19px;
  }
  #animation07 {
    top: 105;
    left: 15;
  }
  .height80 {
    height: 50px;
  }
  .main_title.mt01,
  .main_title.mt02 {
    font-size: 18px;
  }
  .br-576 {
    display: block;
  }

  .cp01 {
    position: absolute;
    top: 100%;
    left: 100%;
    transform: translate(-75%, 0%);
  }
}
@media screen and (min-width: 576px) and (max-width: 767px) {
  h1.main_title {
    font-size: 26px;
    margin-top: 0px;
  }
  .recruit_top_p {
    line-height: 25px;
    font-size: 15px;
  }
  .recruit_top_margin {
    margin-top: 85px;
  }
  .recruit_main_title {
    font-size: 36px;
    margin-top: 0px;
  }
  .bg-fixed {
    background-size: 800px;
    background-position: top;
  }
  .rocker {
    transform: scale(0.8);
  }
  .company_profile01_top {
    margin-top: 70px;
  }
  .contact_us {
    background-size: 165%;
    background-position: right -95px center;
  }
  .btn-danger.detail-btn {
    height: 45px;
    line-height: 33px;
    font-size: 12px;
    margin-top: -19px;
  }
  #animation22 {
    top: 1500;
    left: 364;
  }
  .height80 {
    height: 50px;
  }
  .main_title.mt01,
  .main_title.mt02 {
    font-size: 18px;
  }

  .cp01 {
    position: absolute;
    top: 100%;
    left: 100%;
    transform: translate(-100%, 0%);
  }
}
@media screen and (min-width: 768px) and (max-width: 991px) {
  h1.main_title {
    font-size: 30px;
    margin-top: 30px;
  }
  .recruit_top_p {
    line-height: 25px;
    font-size: 19px;
  }
  .rocker {
    transform: scale(0.9);
  }
  .recruit_main_title {
    margin-top: 80px;
    font-size: 45px;
  }
  .height80 {
    height: 50px;
  }
  .main_title.mt01,
  .main_title.mt02 {
    font-size: 20px;
  }

  .cp01 {
    position: absolute;
    top: 100%;
    left: 100%;
    transform: translate(-30%, 0%);
  }
}
@media screen and (min-width: 992px) and (max-width: 1199px) {
  .recruit_main_title {
    margin-top: 50px;
    font-size: 45px;
  }
  .cp01 {
    position: absolute;
    top: 100%;
    left: 100%;
    transform: translate(-30%, 0%);
  }
}
@media screen and (max-width: 1200px) {
  .br-1200 {
    display: block;
  }

  .cp01 {
    position: absolute;
    top: 100%;
    left: 75%;
    transform: translate(auto, 0%);
  }
}

.btn--orange,
a.btn--orange {
  color: #fff;
  background-color: #fda06f;
  height: 55px;
  padding: 12px;
  font-size: 18px;
  width: 120px;
  border-radius: 0.25rem;
  font-weight: bold;
}

.btn--orange:hover,
a.btn--orange:hover {
  color: #fff;
  background: #fbba5f;
  height: 55px;
  padding: 12px;
  font-size: 18px;
  width: 120px;
  border-radius: 0.25rem;
  font-weight: bold;
}

.contact_div_04 {
  display: flex;
  justify-content: center;
}

.btn--apply_btn,
a.btn--apply {
  color: #fff;
  background-color: #fda06f;
  height: 40px;
  padding: 8px;
  margin-top: 5px;
  font-size: 15px;
  width: 120px;
  border-radius: 0.25rem;
  font-weight: bold;
}
.btn--apply_btn:hover,
a.btn--apply:hover {
  color: #fff;
  background: #fbba5f;
  height: 40px;
  padding: 8px;
  margin-top: 5px;
  font-size: 15px;
  width: 120px;
  border-radius: 0.25rem;
  font-weight: bold;
}

.contact_div_apply_btn {
  display: flex;
  justify-content: center;
}
