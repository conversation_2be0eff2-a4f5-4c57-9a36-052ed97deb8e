<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\Admin;

use App\Constants\ApiCodes;
use App\Http\Controllers\Controller;
use App\Http\Requests\Inspector\CreateInspectorRequest;
use App\Http\Requests\Inspector\GetListInspectorActiveRequest;
use App\Http\Requests\Inspector\ListInspectorRequest;
use App\Http\Resources\Inspector\InspectorNameResource;
use App\Http\Resources\Inspector\InspectorResource;
use App\Services\Inspector\CreateInspectorService;
use App\Services\Inspector\DeleteInspectorService;
use App\Services\Inspector\DetailInspectorService;
use App\Services\Inspector\GetListInspectorService;
use App\Services\Inspector\InspectorService;
use App\Services\Inspector\UpdateInspectorService;
use OpenApi\Attributes as OA;

class InspectorController extends Controller
{
    public function __construct(
        private CreateInspectorService $createInspectorService,
        private UpdateInspectorService $updateInspectorService,
        private DeleteInspectorService $deleteInspectorService,
        private GetListInspectorService $getListInspectorService,
        private DetailInspectorService $detailInspectorService,
        private InspectorService $inspectorService
    ) {
        $this->createInspectorService = $createInspectorService;
        $this->updateInspectorService = $updateInspectorService;
        $this->deleteInspectorService = $deleteInspectorService;
        $this->getListInspectorService = $getListInspectorService;
        $this->detailInspectorService = $detailInspectorService;
        $this->inspectorService = $inspectorService;
    }



    #[OA\Post(
        path: '/api/admin/inspectors/register',
        summary: 'Create inspector',
        description: 'Create a new inspector',
        security: [['access_token' => []]],
        tags: ['Inspector Management'],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(
                required: ['name', 'name_kana','tel','mail'],
                properties: [
                    new OA\Property(property: 'name', type: 'string', example: 'ピカチュウ'),
                    new OA\Property(property: 'name_kana', type: 'string', example: 'ピカチュウ'),
                    new OA\Property(property: 'tel', type: 'string', example: '0123456789'),
                    new OA\Property(property: 'mail', type: 'string', format: 'email', example: '<EMAIL>'),
                ]
            )
        ),
        responses: [
            new OA\Response(
                response: 201,
                description: 'Created',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'code', type: 'integer', example: 201),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Inspector created successfully'),
                        new OA\Property(
                            property: 'data',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'id', type: 'integer', example: 1),
                                new OA\Property(property: 'name', type: 'string', example: '田中 太郎'),
                                new OA\Property(property: 'name_kana', type: 'string', example: 'タナカ タロウ'),
                                new OA\Property(property: 'tel', type: 'string', example: '0123456789'),
                                new OA\Property(property: 'email', type: 'string', example: '<EMAIL>'),
                                new OA\Property(property: 'sort', type: 'string', example: '1'),
                                new OA\Property(property: 'del_flg', type: 'boolean', example: false),
                                new OA\Property(property: 'reg_date', type: 'string', format: 'date-time', example: '2024-01-01T00:00:00Z'),
                                new OA\Property(property: 'up_date', type: 'string', format: 'date-time', example: '2024-01-01T00:00:00Z')
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(response: 400, description: 'Bad request'),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function registerInspector(CreateInspectorRequest $request)
    {
        $data = $request->validated();
        $inspector = $this->createInspectorService->call($data);
        return $this->respond(new InspectorResource($inspector));
    }

    #[OA\Get(
        path: '/api/admin/inspectors/{id}',
        summary: 'Get inspector from id',
        description: 'Retrieve inspector information by ID',
        security: [['access_token' => []]],
        tags: ['Inspector Management'],
        parameters: [
            new OA\Parameter(
                name: 'id',
                description: 'Inspector ID',
                in: 'path',
                required: true,
                schema: new OA\Schema(
                    type: 'string',
                    minimum: 1,
                    example: 1
                )
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful operation',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'code', type: 'integer', example: 200),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Success'),
                        new OA\Property(
                            property: 'data',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'id', type: 'integer', example: 1),
                                new OA\Property(property: 'name', type: 'string', example: '田中 太郎'),
                                new OA\Property(property: 'name_kana', type: 'string', example: 'タナカ タロウ'),
                                new OA\Property(property: 'tel', type: 'string', example: '0123456789'),
                                new OA\Property(property: 'email', type: 'string', example: '<EMAIL>'),
                                new OA\Property(property: 'sort', type: 'integer', example: 1),
                                new OA\Property(property: 'del_flg', type: 'boolean', example: false),
                                new OA\Property(property: 'reg_date', type: 'string', format: 'date-time', example: '2024-01-01T00:00:00Z'),
                                new OA\Property(property: 'up_date', type: 'string', format: 'date-time', example: '2024-01-01T00:00:00Z')
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(response: 400, description: 'Bad request'),
            new OA\Response(response: 404, description: 'Inspector not found'),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function getInspector(string $id)
    {
        $inspector = $this->detailInspectorService->call((int) $id);

        if (!$inspector) {
            return $this->respondWithError(ApiCodes::HTTP_NOT_FOUND, 404, 'Inspector not found');
        }

        return $this->respond(new InspectorResource($inspector));
    }

    #[OA\Delete(
        path: '/api/admin/inspectors/delete/{id}',
        summary: 'Delete inspector',
        description: 'Delete inspector by ID (soft delete)',
        security: [['access_token' => []]],
        tags: ['Inspector Management'],
        parameters: [
            new OA\Parameter(
                name: 'id',
                description: 'Inspector ID to delete',
                in: 'path',
                required: true,
                schema: new OA\Schema(
                    type: 'integer',
                    minimum: 1,
                    example: 1
                )
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful operation',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'code', type: 'integer', example: 200),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Inspector deleted successfully'),
                        new OA\Property(
                            property: 'data',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'id', type: 'integer', example: 1),
                                new OA\Property(property: 'name', type: 'string', example: '田中 太郎'),
                                new OA\Property(property: 'name_kana', type: 'string', example: 'タナカ タロウ'),
                                new OA\Property(property: 'tel', type: 'string', example: '0123456789'),
                                new OA\Property(property: 'email', type: 'string', example: '<EMAIL>'),
                                new OA\Property(property: 'sort', type: 'integer', example: 1),
                                new OA\Property(property: 'del_flg', type: 'boolean', example: false),
                                new OA\Property(property: 'reg_date', type: 'string', format: 'date-time', example: '2024-01-01T00:00:00Z'),
                                new OA\Property(property: 'up_date', type: 'string', format: 'date-time', example: '2024-01-01T00:00:00Z')
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(response: 400, description: 'Bad request'),
            new OA\Response(response: 404, description: 'Inspector not found'),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function deleteInspector($id)
    {
        $inspector = $this->deleteInspectorService->call((int) $id);

        if (!$inspector) {
            return $this->respondWithError(ApiCodes::HTTP_NOT_FOUND, 404, 'Inspector not found');
        }

        return $this->respond(new InspectorResource($inspector));
    }

    #[OA\Put(
        path: '/api/admin/inspectors/{id}',
        summary: 'Update inspector',
        description: 'Update inspector information by ID',
        security: [['access_token' => []]],
        tags: ['Inspector Management'],
        parameters: [
            new OA\Parameter(
                name: 'id',
                description: 'Inspector ID',
                in: 'path',
                required: true,
                schema: new OA\Schema(
                    type: 'integer',
                    minimum: 1,
                    example: 1
                )
            )
        ],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(
                required: ['name', 'name_kana', 'tel', 'mail', 'sort'],
                properties: [
                    new OA\Property(property: 'name', type: 'string', example: '田中 太郎'),
                    new OA\Property(property: 'name_kana', type: 'string', example: 'タナカ タロウ'),
                    new OA\Property(property: 'tel', type: 'string', example: '0123456789'),
                    new OA\Property(property: 'mail', type: 'string', format: 'email', example: '<EMAIL>'),
                ]
            )
        ),
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful operation',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'code', type: 'integer', example: 200),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Inspector updated successfully'),
                        new OA\Property(
                            property: 'data',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'id', type: 'integer', example: 1),
                                new OA\Property(property: 'name', type: 'string', example: '田中 太郎'),
                                new OA\Property(property: 'name_kana', type: 'string', example: 'タナカ タロウ'),
                                new OA\Property(property: 'tel', type: 'string', example: '0123456789'),
                                new OA\Property(property: 'email', type: 'string', example: '<EMAIL>'),
                                new OA\Property(property: 'sort', type: 'integer', example: 1),
                                new OA\Property(property: 'del_flg', type: 'boolean', example: false),
                                new OA\Property(property: 'reg_date', type: 'string', format: 'date-time', example: '2024-01-01T00:00:00Z'),
                                new OA\Property(property: 'up_date', type: 'string', format: 'date-time', example: '2024-01-01T00:00:00Z')
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(response: 400, description: 'Bad request'),
            new OA\Response(response: 404, description: 'Inspector not found'),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function updateInspector($id, CreateInspectorRequest $request)
    {
        $data = $request->all();
        $inspector = $this->updateInspectorService->call((int) $id, $data);

        if (!$inspector) {
            return $this->respondWithError(ApiCodes::HTTP_NOT_FOUND, 404, 'Inspector not found');
        }

        return $this->respond(new InspectorResource($inspector));
    }

    #[OA\Get(
        path: '/api/admin/inspectors',
        summary: 'Get all active inspectors',
        security: [['access_token' => []]],
        description: 'Retrieve a list of all active inspectors (del_flg = 0) with full information',
        parameters: [
            new OA\Parameter(
                name: 'limit',
                description: 'Limit number of items per page',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'integer', example: 10)
            ),
            new OA\Parameter(
                name: 'srchName',
                description: 'Search term for name',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'ピカチュウ')
            ),
            new OA\Parameter(
                name: 'srchKana',
                description: 'Search term for name_kana',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'カナ')
            ),
            new OA\Parameter(
                name: 'page',
                description: 'Page number for pagination',
                in: 'query',
                required: false,
                schema: new OA\Schema(
                    type: 'integer',
                    minimum: 1,
                    example: 1
                )
            ),
            new OA\Parameter(
                name: 'sortType',
                description: 'Sort order',
                in: 'query',
                required: false,
                schema: new OA\Schema(
                    type: 'string',
                    enum: ['asc', 'desc'],
                    example: 'asc'
                )
            )
        ],
        tags: ['Inspector Management'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful operation',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'code', type: 'integer', example: 200),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Success'),
                        new OA\Property(
                            property: 'data',
                            type: 'array',
                            items: new OA\Items(
                                type: 'object',
                                properties: [
                                    new OA\Property(property: 'id', type: 'integer', example: 1),
                                    new OA\Property(property: 'name', type: 'string', example: '検査１'),
                                    new OA\Property(property: 'name_kana', type: 'string', example: 'かな'),
                                    new OA\Property(property: 'tel', type: 'string', example: '0123456789'),
                                    new OA\Property(property: 'email', type: 'string', example: '<EMAIL>'),
                                    new OA\Property(property: 'sort', type: 'integer', example: 1),
                                    new OA\Property(property: 'del_flg', type: 'boolean', example: false),
                                    new OA\Property(property: 'reg_date', type: 'string', format: 'date-time', example: '2024-01-01T00:00:00Z'),
                                    new OA\Property(property: 'up_date', type: 'string', format: 'date-time', example: '2024-01-01T00:00:00Z')
                                ]
                            )
                        ),
                        new OA\Property(
                            property: 'meta',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'current_page', type: 'integer', example: 1),
                                new OA\Property(property: 'last_page', type: 'integer', example: 5),
                                new OA\Property(property: 'per_page', type: 'integer', example: 20),
                                new OA\Property(property: 'total', type: 'integer', example: 75)
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function getAllInspectorsFilter(ListInspectorRequest $request)
    {
        $filters = $request->validated();

        $result = $this->getListInspectorService->call($filters);
        return $this->respond($result);
    }

    #[OA\Get(
        path: '/api/admin/inspectors/active',
        summary: 'Get list of active inspectors',
        description: 'Retrieve a list of all active inspectors (del_flg = false)',
        security: [['access_token' => []]],
        tags: ['Inspector Management'],
        parameters : [
            new OA\Parameter(
                name: 'sort',
                description: 'Option sort = "name|asc"',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'sort|asc')
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful operation',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(
                            property: 'success',
                            type: 'boolean',
                            example: true
                        ),
                        new OA\Property(
                            property: 'code',
                            type: 'integer',
                            example: 200
                        ),
                        new OA\Property(
                            property: 'locale',
                            type: 'string',
                            example: 'en'
                        ),
                        new OA\Property(
                            property: 'message',
                            type: 'string',
                            example: 'Success'
                        ),
                        new OA\Property(
                            property: 'data',
                            type: 'array',
                            items: new OA\Items(
                                properties: [
                                    new OA\Property(
                                        property: 'id',
                                        type: 'integer',
                                        example: 1
                                    ),
                                    new OA\Property(
                                        property: 'name',
                                        type: 'string',
                                        example: 'John Doe'
                                    ),
                                    new OA\Property(
                                        property: 'name_kana',
                                        type: 'string',
                                        example: 'ジョン・ドウ'
                                    )
                                ],
                                type: 'object'
                            )
                        )
                    ],
                    type: 'object'
                )
            ),
            new OA\Response(
                response: 401,
                description: 'Unauthorized'
            ),
            new OA\Response(
                response: 500,
                description: 'Internal server error'
            )
        ]
    )]
    public function getListInspectorActive(GetListInspectorActiveRequest $request)
    {
        $validated = $request->validated();

        $data = $this->inspectorService->getList([
            't_inspector-del_flg-equal' => false,
            ... (isset($validated['sort']) ? ['sort' => $validated['sort']] : []),
        ]);

        return $this->respond(InspectorNameResource::collection($data));
    }
}
