<?php

declare(strict_types=1);

namespace App\Repositories\sqlServer;

use App\Enums\SortType;
use App\Enums\Transport\OrderOptions;
use App\Enums\Transport\Statuses;
use App\Enums\UserRole;
use App\Models\sqlServer\Transport;
use App\Repositories\BaseRepository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class TransportRepository extends BaseRepository
{
    public function model(): mixed
    {
        return Transport::class;
    }

    // check duplicate ref no when export csv
    public function builderQueryCheckDuplicateRefNo($params, array $relations = [], $columns = ['*']): Builder
    {
        $query = ($this->model)::query();

        $params = $params ?: request()->toArray();
        $query = $query->from('t_transport')
            ->leftJoin('m_customer', 't_transport.m_customer_id', '=', 'm_customer.id')
            ->where('t_transport.del_flg', '=', 0)
            ->where('t_transport.odr_flg', '=', 1)
            ->whereRaw('(SELECT COUNT(vehicle_num) FROM t_vehicle WHERE del_flg = 0 AND vehicle_num = t_transport.car_no GROUP BY vehicle_num) > 1');

        $query = $this->applyFilterScope($query, $params)->with($relations);

        $this->buildSortQuery($query, $params);

        return $query;
    }

    public function builderQueryExportCSV($params = null, array $relations = [], $columns = ['*']): Builder
    {
        $query = ($this->model)::query();
        $columns = [
            't_transport.*',
            'm_customer.cus_name_JP',
            'm_customer.ah_sales_name',
            DB::raw('(SELECT COUNT(vehicle_num) FROM t_vehicle WHERE del_flg = 0 AND vehicle_num = t_transport.car_no GROUP BY vehicle_num) AS tv_reg_cnt'),
            DB::raw("STUFF((SELECT '　|　' + ttn.note + ' ' + CONVERT(NVARCHAR(16), reg_date, 120) + ' ' + name FROM t_transport_notes ttn WHERE t_transport.id = ttn.tp_id AND ttn.del_flg = 0 ORDER BY ttn.reg_date FOR XML PATH('')), 1, 3, '') AS notes")
        ];
        $params = $params ?: request()->toArray();
        $query = $query->select($columns)->from('t_transport')
            ->leftJoin('m_customer', 't_transport.m_customer_id', '=', 'm_customer.id')
            ->where('t_transport.del_flg', '=', 0)
            ->where('t_transport.odr_flg', '=', 1);

        $query = $this->applyFilterScope($query, $params)->with($relations);

        $this->buildSortQuery($query, $params, true);
        return $query;
    }

    public function getPaginate($params = null, array $relations = [], $columns = ['*'])
    {
        $query = ($this->model)::query();

        $params = $params ?: request()->toArray();
        $limit = Arr::get($params, 'limit', 20);
        $page = Arr::get($params, 'page', 1);

        $columns = [
            't_transport.*',
            'm_customer.cus_name_JP',
            // Subquery for vehicle count
            DB::raw('(SELECT COUNT(vehicle_num) FROM t_vehicle WHERE del_flg = 0 AND vehicle_num = t_transport.car_no GROUP BY vehicle_num) AS tv_reg_cnt'),
            // Note 1 fields
            'ttn1.note AS note1',
            'ttn1.name AS name1',
            'ttn1.reg_date AS notedate1',
            // Note 2 fields
            'ttn2.note AS note2',
            'ttn2.name AS name2',
            'ttn2.reg_date AS notedate2',
            // Note 3 fields
            'ttn3.note AS note3',
            'ttn3.name AS name3',
            'ttn3.reg_date AS notedate3'
        ];

        $query = $query->from('t_transport')
            ->leftJoin('m_customer as m_customer', 't_transport.m_customer_id', '=', 'm_customer.id')
            ->leftJoin('t_transport_notes as ttn1', function ($join): void {
                $join->on('t_transport.id', '=', 'ttn1.tp_id')
                    ->where('ttn1.list_show_flg', '=', 1);
            })
            ->leftJoin('t_transport_notes as ttn2', function ($join): void {
                $join->on('t_transport.id', '=', 'ttn2.tp_id')
                    ->where('ttn2.list_show_flg', '=', 2);
            })
            ->leftJoin('t_transport_notes as ttn3', function ($join): void {
                $join->on('t_transport.id', '=', 'ttn3.tp_id')
                    ->where('ttn3.list_show_flg', '=', 3);
            })
            ->where('t_transport.del_flg', '=', 0)
            ->where('t_transport.odr_flg', '=', 1);


        $query = $this->applyFilterScope($query, $params)->with($relations);

        $this->buildSortQuery($query, $params);

        return $query->paginate($limit, $columns, 'page', $page);
    }

    public function buildSortQuery(Builder $query, $params): void
    {
        $params = $params ?: request()->toArray();

        $orderOption = $params['order_option'] ?? '';
        $sortType = $params['sort_type'] ?? SortType::DESC->value;

        $this->applySorting($query, $orderOption, $sortType);
    }

    public function updateCarNoHasSpace(string $regDate = '2025-01-01')
    {
        return ($this->model)::query()
            ->whereNotNull('car_no')
            ->where('reg_date', '>=', $regDate)
            ->whereRaw('car_no != TRIM(car_no)')
            ->update(['car_no' => DB::raw('TRIM(car_no)')]);
    }

    public function getListTransportNotRefNo(string $regDate = '2023-01-01')
    {
        return ($this->model)::query()
            ->select('id', 'car_no')
            ->whereNull('ref_no')
            ->where('reg_date', '>', $regDate)
            ->where('st_cd', '<>', Statuses::CANCELED->value)
            ->get();
    }

    public function updateOneByTransportId(int $transportId, array $attributes)
    {
        return ($this->model)::query()
            ->where('id', $transportId)
            ->update($attributes);
    }

    public function getTransportsByIdList(array $idList)
    {
        return ($this->model)::query()
            ->when(Auth::user()->hn_cd == UserRole::admin->value, function ($query) use ($idList): void {
                $query->whereIn('id', $idList);
            }, function ($query) use ($idList): void {
                $query->whereIn('tp_id', $idList);
            })
            ->get();
    }

    /**
     * Get all transport IDs from the entire dataset (not paginated)
     *
     * @param array $params
     * @return array
     */
    public function getAllTransportIds(array $params = []): array
    {
        $query = ($this->model)::query();
        
        // Apply the same filters as getPaginate but without pagination
        $query = $this->applyFilterScope($query, $params);
        
        // Get IDs based on user role
        if (Auth::user()->hn_cd == UserRole::admin->value) {
            return $query->pluck('id')->toArray();
        } else {
            return $query->pluck('tp_id')->toArray();
        }
    }

    public function getInfoDetailTransport(int $id, array $leftJoins = [], string $selectRaw = '*')
    {
        $query = ($this->model)::query()->where('t_transport.del_flg', 0);
        if ($selectRaw) {
            $query = $query->selectRaw($selectRaw);
        }
        if (in_array('m_customer', $leftJoins)) {
            $query = $query->leftJoin('m_customer', 't_transport.m_customer_id', '=', 'm_customer.id');
        }
        $query = Auth::user()->hn_cd == UserRole::admin->value ? $query->where('t_transport.id', $id) : $query->where('t_transport.tp_id', $id);

        return $query->first();
    }

    public function updateByTpId(mixed $tpId, array $params = [])
    {
        $transport = $tpId instanceof Transport ? $tpId : ($this->model)::query()->where('tp_id', $tpId)->first();
        $transport->update($params);
        return $transport;
    }

    public function getInfoTransportCustomer(array $conditions)
    {
        return ($this->model)::query()
            ->select('t_transport.*', 'm_customer.cus_name_JP')
            ->leftJoin('m_customer', 't_transport.m_customer_id', '=', 'm_customer.id')
            ->where($conditions)
            ->first();
    }

    public function updateRecord(int $id, array $params = [])
    {
        return ($this->model)::query()
            ->where('id', $id)
            ->update($params);
    }

    public function updateStatusTransports(array $idList, array $dataUpdate)
    {
        return ($this->model)::query()
            ->when(Auth::user()->hn_cd == UserRole::admin->value, function ($query) use ($idList): void {
                $query->whereIn('id', $idList);
            }, function ($query) use ($idList): void {
                $query->whereIn('tp_id', $idList);
            })
            ->update($dataUpdate);
    }

    public function getInfoTransportByIdList(array $idList)
    {
        return ($this->model)::query()
            ->when(Auth::user()->hn_cd == UserRole::admin->value, function ($query) use ($idList): void {
                $query->whereIn('id', $idList);
            }, function ($query) use ($idList): void {
                $query->whereIn('tp_id', $idList);
            })
            ->get();
    }

    private function applySorting(Builder $query, string $orderOption, string $direction, $isExportCSV = false): Builder
    {
        $colSortDefault = 't_transport.odr_date';
        if ($isExportCSV) {
            $colSortDefault = 't_transport.id';
        }
        return match ($orderOption) {
            OrderOptions::CUS_NAME_JP->value => $query->orderBy('m_customer.cus_Name_JP', $direction),
            OrderOptions::POS_NO->value => $query->orderBy('t_transport.pos_no', $direction),
            OrderOptions::AA_NO->value => $query->orderBy('t_transport.aa_no', $direction),
            OrderOptions::CAR_NO->value => $query->orderBy('t_transport.car_no', $direction),
            OrderOptions::ORD_DATE->value => $query->orderBy('t_transport.odr_date', $direction),
            OrderOptions::TO_PLAN_DATE->value => $query->orderBy('t_transport.to_plan_date', $direction),
            OrderOptions::TO_DATE->value => $query->orderBy('t_transport.to_date', $direction),
            OrderOptions::FROM_PLAN_DATE->value => $query->orderBy('t_transport.from_plan_date', $direction),
            default => $query->orderBy($colSortDefault, 'DESC'),
        };
    }
}
