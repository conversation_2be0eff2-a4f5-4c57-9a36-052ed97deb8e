<?php

declare(strict_types=1);

namespace App\Repositories\sqlServer;

use App\Models\sqlServer\ProcessLock;
use App\Repositories\BaseRepository;

class ProcessLockRepository extends BaseRepository
{
    public function model(): mixed
    {
        return ProcessLock::class;
    }

    public function getExistLockActiveByLockName(string $lockName): bool
    {
        return ($this->model)::query()
            ->where('lock_name', $lockName)
            ->where('del_flg', 0)
            ->exists();
    }

    public function store(array $attributes)
    {
        return ($this->model)::query()->create($attributes);
    }

    public function updateOneByLockId(string $lockId, array $attributes)
    {
        return ($this->model)::query()
            ->where('lock_id', $lockId)
            ->where('del_flg', 0)
            ->update($attributes);
    }
}
