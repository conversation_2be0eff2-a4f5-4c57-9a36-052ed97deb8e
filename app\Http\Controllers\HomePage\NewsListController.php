<?php

declare(strict_types=1);

namespace App\Http\Controllers\HomePage;

use App\Constants\UrlConstants;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;

class NewsListController extends Controller
{
    public function index()
    {
        $isJa = 'ja' === app()->getLocale();
        $url = $isJa ? UrlConstants::URL_WP . 'ah-custom-top-all.php'
            : UrlConstants::URL_WP . 'ah-custom-top-all-en.php';
        $response = Http::asForm()->post($url);
        $newsContent = $response->successful()
            ? $response->body()
            : '';
        $showFooter = false;
        $isCustomMetaTag = true;
        return view('home-page.news-list', compact('newsContent', 'showFooter', 'isCustomMetaTag'));
    }
}
