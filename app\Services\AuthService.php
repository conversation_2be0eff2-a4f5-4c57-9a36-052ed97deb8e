<?php

declare(strict_types=1);

namespace App\Services;

use App\Constants\JwtPayloadDTO;
use App\Enums\UserRole;
use App\Exceptions\ApiException;
use App\Exceptions\AuthException;
use App\Http\Resources\User\UserResource;
use App\Jobs\StoreActionLogJob;
use App\Models\sqlServer\LoginSession;
use App\Repositories\sqlServer\UserRepository;
use App\Services\Jwt\JwtService;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Random\RandomException;
use Symfony\Component\HttpFoundation\Response as HttpResponse;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class AuthService
{
    private JwtService $jwtService;
    private UserRepository $userRepository;

    public function __construct(JwtService $jwtService, UserRepository $userRepository)
    {
        $this->jwtService = $jwtService;
        $this->userRepository = $userRepository;
    }

    /**
     * @throws ApiException
     * @throws RandomException
     * @throws NotFoundHttpException
     * @param array $body
     * @param array $body['id']
     * @param array $body['password']
     * @return array
     */
    public function login(array $body): array
    {
        try {
            DB::beginTransaction();
            $userId = trim($body['id']);
            $password = trim($body['password']);

            $user = $this->userRepository->getOneBy([
                'm_customer-id-equal' => $userId,
                'm_customer-del_flg-equal' => 0,
                'm_customer-login_flg-equal' => 0,
                'm_customer-exchange_flg-equal' => 0,
                'm_customer-pwd-equal' => $password,
                'm_customer-hn_cd-in' => json_encode(
                    // List role allow login admin
                    UserRole::getPermissionAdmin()
                ),
            ]);

            if (!$user) {
                throw new AuthException(
                    'IDかパスワードが正しくありません',
                    code: HttpResponse::HTTP_BAD_REQUEST
                );
            }

            $accessTokenExpired = (int)config('auth.jwt.ttl');
            $refreshTokenExpired = (int)config('auth.jwt.refresh_ttl');
            $currentDate = time();

            $expiredAt = $currentDate + $accessTokenExpired + $refreshTokenExpired;

            // Create login session
            $loginSession = LoginSession::create([
                'customer_id' => $user->idx,
                'expired_at' => $expiredAt
            ]);

            // update login_date in user table
            $user->update([
                'login_date' => now(),
            ]);

            // Store action log
            dispatch(new StoreActionLogJob(
                pageName: 'LOGIN',
                dateTime: now(),
                userId: (int) ($user->id),
                ipAddress: request()->ip()
            ));

            $jwtId = bin2hex(random_bytes(16));
            $jwtPayload = (new JwtPayloadDTO((string)$user->idx, $loginSession->id, $jwtId))->toArray();
            $accessToken = $this->jwtService->generateTokenWithTimeExpired($jwtPayload, $currentDate, $currentDate + $accessTokenExpired);
            $refreshToken = $this->jwtService->generateTokenWithTimeExpired($jwtPayload, $currentDate, $currentDate + $refreshTokenExpired);

            DB::commit();
            return [
                'access_token' => $accessToken,
                'refresh_token' => $refreshToken,
                'expired_at' => $expiredAt
            ];
        } catch (Exception $e) {
            DB::rollBack();
            Log::info([
                'error' => 'login',
                'message' => $e->getMessage(),
            ]);
            throw new AuthException(code: HttpResponse::HTTP_BAD_REQUEST, message: $e->getMessage());
        }
    }

    public function logout(string $token): void
    {
        $payload = $this->jwtService->decodeToken($token);
        $loginSessionId = $payload['login_session_id'];
        $loginSession = LoginSession::where('id', $loginSessionId)->where('expired_at', '>', now())->first();
        if (!$loginSession) {
            throw new AuthException(code: HttpResponse::HTTP_FORBIDDEN);
        }

        $loginSession->delete();
    }

    public function refreshToken(string $token): array
    {
        $payload = $this->jwtService->decodeToken($token);
        $userId = $payload['sub'];
        $loginSessionId = $payload['login_session_id'];

        $loginSession = LoginSession::where('id', $loginSessionId)->where('expired_at', '>', now())->first();
        if (!$loginSession) {
            throw new AuthException(code: HttpResponse::HTTP_BAD_REQUEST);
        }

        $jwtId = bin2hex(random_bytes(16));
        $jwtPayload = (new JwtPayloadDTO((string)$userId, $loginSessionId, $jwtId))->toArray();
        $accessTokenExpired = (int)config('auth.jwt.ttl');
        $refreshTokenExpired = (int)config('auth.jwt.refresh_ttl');
        $currentDate = time();

        $accessToken = $this->jwtService->generateTokenWithTimeExpired($jwtPayload, $currentDate, $currentDate + $accessTokenExpired);
        $refreshToken = $this->jwtService->generateTokenWithTimeExpired($jwtPayload, $currentDate, $currentDate + $refreshTokenExpired);

        $expiredAt = $currentDate + $accessTokenExpired + $refreshTokenExpired;
        $loginSession->update([
            'expired_at' => $expiredAt
        ]);

        return [
            'access_token' => $accessToken,
            'refresh_token' => $refreshToken,
            'expired_at' => $expiredAt
        ];
    }

    public function getProfile(): UserResource
    {
        $user = UserResource::make(Auth::user());

        return $user;
    }

    public function processAuthorization(array $body): array
    {
        $payload = $this->jwtService->decodeToken($body['token']);
        $keySecretAuthorize = config('auth.key_secret_authorize');
        if ($keySecretAuthorize !== $body['secret_key'] || null === $keySecretAuthorize) {
            throw new AuthException(code: HttpResponse::HTTP_BAD_REQUEST);
        }

        return [
            'redirect_uri' => $body['redirect_uri'] . '?login_session_id=' . $payload['login_session_id'] . "&code=" . $keySecretAuthorize,
        ];
    }
}
