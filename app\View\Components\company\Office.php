<?php

declare(strict_types=1);

namespace App\View\Components\company;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class Office extends Component
{
    /**
     * Create a new component instance.
     */
    public function __construct(public int $index, public string $title, public string $office, public string $address, public string $url)
    {

    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.company.office');
    }
}
