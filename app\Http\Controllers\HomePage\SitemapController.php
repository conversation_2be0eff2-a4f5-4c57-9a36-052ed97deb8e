<?php

declare(strict_types=1);

namespace App\Http\Controllers\HomePage;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;

class SitemapController extends Controller
{
    public function index(Request $request)
    {
        $sitemap = Sitemap::create();

        $locales = ['en', 'ja'];

        foreach ($locales as $locale) {
            $sitemap->add(Url::create("/{$locale}")->setLastModificationDate(Carbon::now()));
            $sitemap->add(Url::create("/{$locale}/question"));
            $sitemap->add(Url::create("/{$locale}/recruit"));
            $sitemap->add(Url::create("/{$locale}/hubnet-introduction"));
            $sitemap->add(Url::create("/{$locale}/service"));
            $sitemap->add(Url::create("/{$locale}/service/shipping-export-import"));
            $sitemap->add(Url::create("/{$locale}/service/d2d-package"));
            $sitemap->add(Url::create("/{$locale}/service/inland-transport"));
            $sitemap->add(Url::create("/{$locale}/service/insurance"));
            $sitemap->add(Url::create("/{$locale}/service/car-shipping-info"));
            $sitemap->add(Url::create("/{$locale}/service/overseas-documents"));
            $sitemap->add(Url::create("/{$locale}/service/recall-repair"));
            $sitemap->add(Url::create("/{$locale}/service/auction-vehicle-check"));
            $sitemap->add(Url::create("/{$locale}/service/photo-condition"));
            $sitemap->add(Url::create("/{$locale}/service/repair-easywork-inspection"));
            $sitemap->add(Url::create("/{$locale}/service/document-file"));
            $sitemap->add(Url::create("/{$locale}/sailing-schedule"));
            $sitemap->add(Url::create("/{$locale}/autohub-yard-list"));
            $sitemap->add(Url::create("/{$locale}/company"));
            $sitemap->add(Url::create("/{$locale}/autohub-blog-list"));
            $sitemap->add(Url::create("/{$locale}/autohub-news-list"));
            $sitemap->add(Url::create("/{$locale}/autohub-post-view"));
            $sitemap->add(Url::create("/{$locale}/contact"));
        }

        return $sitemap->toResponse($request);
    }
}
