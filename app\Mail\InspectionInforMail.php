<?php

declare(strict_types=1);

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;

class InspectionInforMail extends Mailable
{
    use Queueable;
    use SerializesModels;

    private array $data;

    /**
     * Create a new message instance.
     */
    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function build(): self
    {
        $mailInfoConfig = Arr::get(config('mail.mail_info'), config('app.env'));

        return $this
            ->from($this->data['mail_from'])
            ->bcc($this->transformMail($mailInfoConfig['bcc']))
            ->subject($this->data['aa_no'] . '：' . $this->data['car_name'])
            ->text('emails.ja.inspection-info')
            ->with([
                'aa_no' => $this->data['aa_no'],
                'car_name' => $this->data['car_name'],
                'photo_flg' => $this->data['photo_flg'],
                'optn2_flg' => $this->data['optn2_flg'],
                'note' => $this->data['note'],
                'cc_note' => $this->data['cc_note'],
                'm_customer_name' => $this->data['m_customer_name'],
                'm_customer_tel' => $this->data['m_customer_tel'],
                'odr_person' => $this->data['odr_person'],
                'odr_tel' => $this->data['odr_tel'],
                'odr_mail' => $this->data['odr_mail'],
                'open_date' => $this->data['open_date'],
                't_aa_place_name' => $this->data['t_aa_place_name'],
            ]);
    }
}
