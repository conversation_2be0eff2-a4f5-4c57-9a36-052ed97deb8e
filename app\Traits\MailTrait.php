<?php

declare(strict_types=1);

namespace App\Traits;

use Illuminate\Support\Arr;

trait MailTrait
{
    /**
     * Check if the mail is valid
     *
     * @param string $mail
     * @return bool
     */
    public function checkMailValid(string $email): bool
    {
        // Check env_app, always verify true if env_app is production
        if ('production' == config('app.env')) {
            return true;
        }

        // Validate email format first
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return false;
        }

        // Allowed domains for email sending
        $allowedDomains = explode(',', config('mail.mail_domain_allow'));
        $email = strtolower(trim($email));

        // Use str_ends_with instead of str_contains for accurate domain matching
        foreach ($allowedDomains as $domain) {
            if (str_ends_with($email, strtolower($domain))) {
                return true;
            }
        }

        return false;
    }

    /**
     * Transform mail for testing
     *
     * @param array|string $emails
     * @example
     * [
     *  '<EMAIL>',
     *  '<EMAIL>',
     *  [
     *      'email' => '<EMAIL>',
     *      'name' => 'Test 3',
     *  ],
     * ]
     * 
     * @example
     * '<EMAIL>'
     * @return array|string
     * @example
     * [
     *  '<EMAIL>',
     *  '<EMAIL>',
     *  [
     *      'email' => '<EMAIL>',
     *      'name' => 'Test 3',
     *  ],
     * ]
     * @example
     * '<EMAIL>'
     * @return array|string
     */
    public function transformMail(array | string $emails): array | string
    {
        $endDomainMailTest = '@kaopiz.test.com';

        if (is_array($emails)) {
            // if mail is array
            return collect($emails)->map(function ($email) use ($endDomainMailTest) {
                if ($this->checkMailValid(Arr::get($email, 'email') ?? '')) return $email;
                
                // if mail has name and email
                if (is_string(Arr::get($email, 'email'))) {
                    // replace mail from character @ to $endDomainMailTest
                    $email['email'] = preg_replace('/@.*/', $endDomainMailTest, trim(Arr::get($email, 'email')));
                }
                // if mail is string
                else {
                    // replace mail from character @ to $endDomainMailTest
                    $email = preg_replace('/@.*/', $endDomainMailTest, trim($email));
                }
                return $email;
            })->toArray();
        }

        // if mail is string
        if (is_string($emails) && !$this->checkMailValid($emails)) {
            return trim($emails) ? preg_replace('/@.*/', $endDomainMailTest, trim($emails)) : '';
        }

        return $emails;
    }
}
