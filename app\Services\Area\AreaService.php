<?php

declare(strict_types=1);

namespace App\Services\Area;

use App\Http\Resources\Area\AreaDropdownResource;
use App\Repositories\sqlServer\AreaRepository;

class AreaService
{
    public function __construct(private AreaRepository $areaRepository)
    {
    }

    public function getDataDropdown(array $params)
    {
        $params['select_raw'] = match ($params['distinct'] ?? null) {
            'port' => 'DISTINCT m_area.pcd, m_area.port',
            'country' => 'DISTINCT m_area.cd, m_area.country',
            'area' => 'DISTINCT m_area.acd, m_area.area',
        };
        $areas = $this->areaRepository->getList($params, []);
        return AreaDropdownResource::collection($areas);
    }
}
