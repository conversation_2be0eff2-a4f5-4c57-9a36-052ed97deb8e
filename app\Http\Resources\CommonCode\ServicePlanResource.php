<?php

declare(strict_types=1);

namespace App\Http\Resources\CommonCode;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ServicePlanResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'cd' => $this->cd,
            'value1' => $this->value1,
        ];
    }
}
