@props([
    'id' => '',
    'idFirstArea' => '',
    'title' => '',
])

@once
    @push('styles')
        @vite('resources/css/yards/yard-area.css')
    @endpush
@endonce

@if ($id)
    <div id="{{ $id }}"></div>
@endif

<div class="section -mb-[45px] bg-[#eef3f9] pt-[25px] pb-[70px]">
    <div class="mx-auto mt-[45px] max-w-6xl px-4">
        <div class="mx-auto bg-white shadow-[0_2px_10px_rgba(0,0,0,0.3)]">
            <div class="flex w-full flex-wrap">
                <h2 class="area_title 2sm:text-28 text-xl lg:text-[2rem] font-medium">{{ $title }}</h2>
                @if ($idFirstArea)
                    <div id="{{ $idFirstArea }}"></div>
                @endif
            </div>
            {{ $slot }}
        </div>
    </div>
</div>
