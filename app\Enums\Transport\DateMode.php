<?php

declare(strict_types=1);

namespace App\Enums\Transport;

enum DateMode: int
{
    case ORD_DATE = 1;
    case TO_PLAN_DATE = 2;
    case TO_DATE = 3;
    case FROM_PLAN_DATE = 4;

    public static function getAllValues(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function getName(): string
    {
        return match ($this) {
            self::ORD_DATE => '受注日',
            self::TO_PLAN_DATE => '搬入予定',
            self::TO_DATE => '搬入実績',
            self::FROM_PLAN_DATE => '搬出予定',
        };
    }
}
