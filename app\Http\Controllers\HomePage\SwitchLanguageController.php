<?php

declare(strict_types=1);

namespace App\Http\Controllers\HomePage;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class SwitchLanguageController extends Controller
{
    public function __invoke(Request $request)
    {
        $locale = '';
        switch ($request->input('locale')) {
            case 'en':
                $locale = 'ja';
                break;
            case 'ja':
                $locale = 'en';
                break;
            default:
                abort(404);
        }
        $supportedLocales = ['en', 'ja'];

        $previousUrl = url()->previous();
        $parsedUrl = parse_url($previousUrl);
        $path = $parsedUrl['path'] ?? '';

        $segments = explode('/', ltrim($path, '/'));

        if (in_array($segments[0] ?? '', $supportedLocales)) {
            array_shift($segments);
        }

        $newPath = implode('/', $segments);

        $newUrl = ('ja' !== $locale ? '/' . $locale : '') . '/' . $newPath;
        $newUrl = rtrim($newUrl, '/');
        if (isset($parsedUrl['query'])) {
            $newUrl .= '?' . $parsedUrl['query'];
        }
        return redirect($newUrl);
    }
}
