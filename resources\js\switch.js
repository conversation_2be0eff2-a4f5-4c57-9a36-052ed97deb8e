// document.addEventListener('DOMContentLoaded', function() {
//     console.log('test');
//
// 	// Toggle switch functionality
// 	const rockerSwitch = document.querySelector('.rocker');
// 	if (rockerSwitch) {
// 		rockerSwitch.addEventListener('click', function() {
// 			const checkbox = document.querySelector('#rocker-switch');
// 			const profile01 = document.querySelector('#company_profile01');
// 			const profile02 = document.querySelector('#company_profile02');
//
// 			if (checkbox.checked) {
// 				profile02.style.display = 'none';
// 				profile01.style.display = 'block';
// 			} else {
// 				profile01.style.display = 'none';
// 				profile02.style.display = 'block';
// 			}
// 		});
// 	}
// });

// Handle hash change for cloud button
// window.onload = function() {
// 	if (location.hash === '#cloud_btn03') {
// 		const rockerSwitch = document.querySelector('#rocker-switch');
// 		const profile01 = document.querySelector('#company_profile01');
// 		const profile02 = document.querySelector('#company_profile02');
//
// 		if (rockerSwitch) {
// 			rockerSwitch.checked = false;
// 			profile01.style.display = 'none';
// 			profile02.style.display = 'block';
// 		}
// 	}
// };
