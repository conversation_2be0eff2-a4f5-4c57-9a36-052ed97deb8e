/*@import url('https://fonts.googleapis.com/css?family=M+PLUS+Rounded+1c');*/
body {
    --white_color: #fff;
    --button_red_color: #dc3545;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue',
    <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
    'Segoe UI Symbol', 'Noto Color Emoji';
    font-size: 16px;
    max-width: 1920px;
}

.news-list-content a:hover {
    color: #0056b3;
    text-decoration: underline;
}

.news-list-content a {
    color: #007bff;
    text-decoration: none;
    background-color: transparent;
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 500;
}

.news-list-content a:has(img) {
    display: inline-block;
    width: fit-content;
}

.news-list-content ul {
    list-style: inside;
    padding-left: 24px;
}

.container-width {
    margin: 45px auto 0px;
}

.contact_us {
    color: #495057;
    height: 320px;
    padding: 50px;
    background-image: url('../img/contact_image.webp');
    background-size: 100%;
    box-shadow: -1px -1px 7px grey;
}

.contact_title01 {
    font-size: 48px;
    font-weight: bold;
}

.contact_title02 {
    line-height: 33px;
    font-size: 23px;
    font-weight: bold;
}

.contact_div_01 {
    margin-top: 15px;
}

.contact_div_02 {
    margin-top: 0px;
}

.contact_div_03 {
    margin-top: 20px;
}

.btn.contact_detail_btn {
    height: 55px;
    padding: 11px;
    font-size: 20px;
    width: 100%;
    background: linear-gradient(-10deg, #dc3545, #f93b4d, #dc3545);
    color: #fff;
    border: solid 2px #dc3545;
    border-radius: .25rem;
    font-weight: bold;
}

.btn.contact_detail_btn:hover {
    font-size: 22px;
    padding: 9px;
    color: #fff;
    border: solid 2px #f93b4d;
    background: linear-gradient(10deg, #dc3545, #f93b4d, #dc3545);
}

.slogan {
    position: relative;
    background: linear-gradient(-135deg, #fdf6f7, #fff, #fdf6f7);
    padding: 20px 25px;
    border-left: solid 77px #dc3545;
    font-size: 20px;
    font-weight: bold;
    margin: 10px;
    box-shadow: 5px 5px 10px -2px grey;
    border-top: solid 1px #dc3545;
    border-bottom: solid 1px #dc3545;
    border-right: solid 8px #dc3545;
}

.slogan:before {
    position: absolute;
    color: white;
    left: -52px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    font-size: 28px;
}

.slogan.slogan_1:before {
    content: "１";
}

.slogan.slogan_2:before {
    content: "２";
}

.slogan.slogan_3:before {
    content: "３";
}

.slogan.slogan_4:before {
    content: "４";
}

.staff_box {
    border: solid 1px #aaa;
    padding: 20px 15px;
    height: 230px;
    margin-bottom: 30px;
    box-shadow: 7px 7px 10px -2px grey;
    border-top: solid 1px #dc3545;
    border-bottom: solid 1px #dc3545;
    background: linear-gradient(-135deg, #fff, #f2f2f2, #fff);
    border-radius: 5px;
}

.staff_box .main_title {
    font-size: 25px;
    line-height: 40px;
}

.staff_box .main_title .red {
    font-size: 45px;
    color: #f00;
}

.staff_box p {
    font-size: 17px;
}

.staff_box img {
    margin-top: 140px;
}

#company-bg-01 {
    height: 92px;
}

#company-bg-02 {
    height: 485px;
}

#company-bg-03 {
    height: 272px;
}

#company-bg-04 {
    height: 718px;
}

#company-bg-05 {
    height: 224px;
}

.company_01 p {
    line-height: 32px;
}

.kigyorinen div img {
    width: 380px;
}

#company_profile02 iframe {
    width: 100%;
    max-width: 480px;
    height: 290px;
}

.partner {
    padding: 25px;
    text-align: center;
    height: 160px;
}

.mid {
    align-items: center;
    justify-content: center;
    padding-top: 1em;
    text-align: center;
}

.rocker {
    display: inline-block;
    position: relative;
    font-size: 1.2em;
    font-weight: bold;
    text-align: center;
    text-transform: uppercase;
    color: #888;
    width: 13em;
    height: 4em;
    border-bottom: 0.5em solid #eee;
}

.rocker::before {
    content: "";
    position: absolute;
    top: 0.5em;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #999;
    border: 0.5em solid #eee;
    border-bottom: 0;
}

.rocker input {
    opacity: 0;
    width: 0;
    height: 0;
}

.switch-left, .switch-right {
    cursor: pointer;
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 2.5em;
    width: 6em;
    transition: 0.2s;
}

.switch-left {
    height: 2.4em;
    width: 5.75em;
    left: 0.85em;
    bottom: 0.8em;
    background-color: #ddd;
    transform: rotate(15deg) skewX(15deg);
}

.switch-right {
    right: 0.5em;
    bottom: 0;
    background-color: #bd5757;
    color: #fff;
}

.switch-left::before, .switch-right::before {
    content: "";
    position: absolute;
    width: 0.4em;
    height: 2.45em;
    bottom: -0.45em;
    background-color: #ccc;
    transform: skewY(-65deg);
}

.switch-left::before {
    left: -0.4em;
}

.switch-right::before {
    right: -0.375em;
    background-color: transparent;
    transform: skewY(65deg);
}

input:checked + .switch-left {
    background-color: #0084d0;
    color: #fff;
    bottom: 0px;
    left: 0.5em;
    height: 2.5em;
    width: 6em;
    transform: rotate(0deg) skewX(0deg);
}

input:checked + .switch-left::before {
    background-color: transparent;
    width: 3.0833em;
}

input:checked + .switch-left + .switch-right {
    background-color: #ddd;
    color: #888;
    bottom: 0.8em;
    right: 0.8em;
    height: 2.4em;
    width: 5.75em;
    transform: rotate(-15deg) skewX(-15deg);
}

input:checked + .switch-left + .switch-right::before {
    background-color: #ccc;
}

input:focus + .switch-left {
    color: #333;
}

input:checked:focus + .switch-left {
    color: #fff;
}

input:focus + .switch-left + .switch-right {
    color: #fff;
}

input:checked:focus + .switch-left + .switch-right {
    color: #333;
}

.accordion_one {
    max-width: 1024px;
    margin: 0 auto;
}

.accordion_one .accordion_header {
    background-color: #fda06f;
    color: #fff;
    font-size: 18px;
    font-weight: bold;
    padding: 20px 5%;
    position: relative;
    z-index: +1;
    cursor: pointer;
    transition-duration: 0.2s;
}

.accordion_one:nth-child(2n) .accordion_header {
    background-color: #fbba5f;
}

.accordion_one .accordion_header:hover {
    opacity: .8;
}

.accordion_one .accordion_header .i_box {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 50%;
    right: 5%;
    width: 40px;
    height: 40px;
    border: 1px solid #fff;
    margin-top: -20px;
    box-sizing: border-box;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    transform-origin: center center;
    transition-duration: 0.2s;
}

.accordion_one .accordion_header .i_box .one_i {
    display: block;
    width: 18px;
    height: 18px;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    transform-origin: center center;
    transition-duration: 0.2s;
    position: relative;
}

.accordion_one .accordion_header.open .i_box {
    -webkit-transform: rotate(-360deg);
    transform: rotate(-360deg);
}

.accordion_one .accordion_header .i_box .one_i:before,
.accordion_one .accordion_header .i_box .one_i:after {
    display: flex;
    content: '';
    background-color: #fff;
    border-radius: 10px;
    width: 18px;
    height: 4px;
    position: absolute;
    top: 7px;
    left: 0;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
    transform-origin: center center;
}

.accordion_one .accordion_header .i_box .one_i:before {
    width: 4px;
    height: 18px;
    top: 0;
    left: 7px;
}

.accordion_one .accordion_header.open .i_box .one_i:before {
    content: none;
}

.accordion_one .accordion_header.open .i_box .one_i:after {
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
}

.accordion_one .accordion_inner {
    display: none;
    padding: 30px 30px;
    border-left: 2px solid #fda06f;
    border-right: 2px solid #fda06f;
    border-bottom: 2px solid #fda06f;
    box-sizing: border-box;
}

.accordion_one:nth-child(2n) .accordion_inner {
    border-left: 2px solid #fbba5f;
    border-right: 2px solid #fbba5f;
    border-bottom: 2px solid #fbba5f;
}

.accordion_one .accordion_inner p.txt_a_ac {
    margin: 0;
}

.accordion_one .accordion_inner.stay {
    display: block;
}

@media screen and (max-width: 1024px) {
    .accordion_one .accordion_header {
        font-size: 15px;
    }

    .accordion_one .accordion_header .i_box {
        width: 30px;
        height: 30px;
        margin-top: -15px;
    }
}

@media screen and (max-width: 767px) {
    .accordion_one .accordion_header {
        font-size: 13px;
        text-align: left;
        padding: 15px 60px 15px 15px;
    }
}

#company_profile01 {
    background-color: #fff;
    padding: 2em 2em 4em 2em;
    position: relative;
    z-index: 1;
    color: #444;
    border-radius: 5px;
}

#company_profile01::before, #company_profile01::after {
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, .3);
    content: "";
    height: 100%;
    position: absolute;
    width: 100%;
}

#company_profile01::before {
    background-color: rgba(255, 255, 255, .5);
    left: 0;
    top: 0;
    z-index: -1;
}

#company_profile01::after {
    background: linear-gradient(-135deg, #dddfe0, #eee, #fff, #fff, #eee, #dddfe0);
    top: 5px;
    left: 5px;
    z-index: -2;
}

#company_profile02 {
    background-color: #fff;
    padding: 2em 2em 4em 2em;
    position: relative;
    z-index: 1;
    color: #444;
}

#company_profile02::before, #company_profile02::after {
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, .3);
    content: "";
    height: 100%;
    position: absolute;
    width: 100%;
}

#company_profile02::before {
    background-color: rgba(255, 255, 255, .5);
    left: 0;
    top: 0;
    z-index: -1;
}

#company_profile02::after {
    background: linear-gradient(-135deg, #dddfe0, #eee, #fff, #fff, #eee, #dddfe0);
    top: 5px;
    left: 5px;
    z-index: -2;
}

#company_profile03 {
    background-color: #fff;
    padding: 2em;
    position: relative;
    z-index: 1;
    color: #444;
    border-radius: 50%;
    width: 400px;
    height: 400px;
}

#company_profile03::before, #company_profile03::after {
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, .3);
    content: "";
    height: 100%;
    position: absolute;
    border-radius: 50%;
    width: 400px;
    height: 400px;
}

#company_profile03::before {
    background-color: rgba(255, 255, 255, .5);
    left: 0;
    top: 0;
    z-index: -1;
}

#company_profile03::after {
    background: linear-gradient(-135deg, #94bdea, #b0d0f3, #d0e4fb, #b0d0f3, #94bdea);
    top: 5px;
    left: 5px;
    z-index: -2;
}

.recruit_table {
    width: 100%;
    color: #555;
}

.recruit_table th {
    padding: 0px;
    text-align: center;
    width: 25%;
}

.recruit_table tr {
    border-top: dashed 1px grey;
}

.recruit_table td {
    padding: 10px;
}

.recruit_bg_01 {
    height: 92px;
    width: 100%;
}

.recruit_bg_02 {
    background: rgb(255, 255, 255, 1);
    height: 322px;
    width: 100%;
    transform: skew(0deg, 2deg);
    box-shadow: 1px 1px 10px 0px grey, -1px -1px 10px 0px grey;
    opacity: 0.8;
}

.recruit_bg_03 {
    height: 165px;
    width: 100%;
}

.recruit_bg_04 {
    background: linear-gradient(45deg, #fdf9f4, #f5feff, #fff, #fff5ff, #f5feff, #f6f9eb);
    height: 2372px;
    width: 100%;
}

.recruit_rocker_top {
    margin-top: 70px;
}

.company_profile_title {
    font-size: 30px;
    font-weight: bold;
    margin: 25px auto;
}

#company_profile03 h2.main_title.mt01 {
    margin-top: 105px;
}

#company_profile03 h2.main_title.mt02 {
    margin-top: 105px;
}

#company_profile02 .row {
    margin-top: 30px;
}

.top_title01 {
    position: absolute;
    top: 100;
    left: 200px;
    width: 70%;
    max-width: 1325px;
}

.top_title02 {
    display: none;
}

@media screen and (max-width: 576px) {
    .container-width {
        max-width: 540px;
    }

    .recruit_bg_01 {
        height: 50px;
    }

    .recruit_bg_02 {
        height: 300px;
    }

    .recruit_bg_03 {
        height: 75px;
    }

    .recruit_rocker_top {
        margin-top: 0px;
    }

    .company_profile_title {
        font-size: 25px;
        font-weight: bold;
        margin: 15px auto;
    }

    #company_profile03 {
        width: 350px;
        height: 350px;
    }

    #company_profile03::before,
    #company_profile03::after {
        width: 350px;
        height: 350px;
    }

    #company_profile03 h2.main_title.mt01 {
        margin-top: 75px;
    }

    #company_profile03 h2.main_title.mt02 {
        margin-top: 75px;
    }

    #company_profile02 .row {
        margin-top: 10px;
    }

    .recruit_table {
        font-size: 0.9em;
    }

    .accordion_one .accordion_inner {
        padding: 10px;
    }

    .recruit_bg_04 {
        height: 3050px;
    }

    .top_title01 {
        display: none;
    }

    .top_title02 {
        display: block;
        position: absolute;
        top: 100px;
        left: 0%;
        width: 100%;
        max-width: 1325px;
    }

    #footer {
        font-size: 0.9em;
    }

    .contact_us {
        color: #495057;
        height: 260px;
        padding: 50px;
        background-image: url('../img/contact_image.webp');
        background-size: 165%;
        box-shadow: -1px -1px 7px grey;
        background-position: right -95px center;
    }

    .contact_div_01 {
        margin-top: -15px;
    }

    .contact_div_03 {
        margin-top: 10px;
    }

    .contact_title01 {
        font-size: 35px;
    }

    .contact_title02 {
        font-size: 16px;
        line-height: 28px;
    }

    .btn.contact_detail_btn {
        padding: 14px;
        font-size: 15px;
    }

    .btn.contact_detail_btn:hover {
        padding: 14px;
        font-size: 16px;
    }
}

@media screen and (min-width: 576px) and (max-width: 767px) {
    .container-width {
        max-width: 720px;
    }

    .recruit_bg_01 {
        height: 60px;
    }

    .recruit_bg_02 {
        height: 235px;
    }

    .recruit_bg_03 {
        height: 75px;
    }

    .recruit_rocker_top {
        margin-top: 0px;
    }

    .company_profile_title {
        font-size: 25px;
        font-weight: bold;
        margin: 15px auto;
    }

    #company_profile03 {
        width: 350px;
        height: 350px;
    }

    #company_profile03::before,
    #company_profile03::after {
        width: 350px;
        height: 350px;
    }

    #company_profile03 h2.main_title.mt01 {
        margin-top: 75px;
    }

    #company_profile03 h2.main_title.mt02 {
        margin-top: 75px;
    }

    #company_profile02 .row {
        margin-top: 20px;
    }

    .recruit_table {
        font-size: 0.9em;
    }

    .accordion_one .accordion_inner {
        padding: 10px;
    }

    #company_profile03.cp02 {
        margin-left: 163px;
    }

    .recruit_bg_04 {
        height: 2900px;
    }

    /*.top_title01{
      position: absolute;top: 115;left: 1%;width: 98%;max-width: 1325px;
    }*/
    .top_title01 {
        display: none;
    }

    .top_title02 {
        display: block;
        position: absolute;
        top: 85px;
        left: 5%;
        width: 90%;
        max-width: 1325px;
    }

    /*.contact_us{
      color:#495057;height:320px;padding:50px;background-image: url('../img/contact_image.jpg');background-size:130%;box-shadow: -1px -1px 7px grey;background-position:right;
    }*/
    .contact_us {
        background-size: 165%;
        background-position: right -95px center;
    }

    .contact_title01 {
        font-size: 40px;
    }

    .contact_title02 {
        font-size: 20px;
    }

    .btn.contact_detail_btn {
        padding: 13px;
        font-size: 18px;
    }

    .btn.contact_detail_btn:hover {
        padding: 10px;
        font-size: 20px;
    }
}

@media screen and (min-width: 768px) and (max-width: 991px) {
    .container-width {
        max-width: 960px;
    }

    .recruit_bg_04 {
        height: 2110px;
    }

    .top_title01 {
        position: absolute;
        top: 110;
        left: 5%;
        width: 90%;
        max-width: 1325px;
    }

    .contact_title01 {
        font-size: 40px;
    }

    .contact_title02 {
        font-size: 20px;
    }

    .btn.contact_detail_btn {
        width: 115%;
        padding: 13px;
        font-size: 18px;
    }

    .btn.contact_detail_btn:hover {
        width: 115%;
        padding: 10px;
        font-size: 20px;
    }
}

@media screen and (min-width: 992px) and (max-width: 1199px) {
    .container-width {
        max-width: 960px;
    }

    .recruit_bg_04 {
        height: 2267px;
    }

    .top_title01 {
        position: absolute;
        top: 100px;
        left: 5%;
        width: 90%;
        max-width: 1325px;
    }

    .contact_title01 {
        font-size: 43px;
    }

    .contact_title02 {
        font-size: 21px;
    }

    .btn.contact_detail_btn {
        width: 145%;
        padding: 13px;
        font-size: 18px;
    }

    .btn.contact_detail_btn:hover {
        padding: 10px;
        font-size: 20px;
    }
}

@media screen and (min-width: 1200px) and (max-width: 1499px) {
    .container-width {
        max-width: 1140px;
    }

    .btn.contact_detail_btn {
        width: 135%;
    }
}

@media screen and (min-width: 1500px) and (max-width: 1799px) {
    .container-width {
        max-width: 1140px;
    }
}

@media screen and (min-width: 1800px) {
    .container-width {
        max-width: 1140px;
    }
}

h1.main_title {
    font-size: 40px;
    font-weight: bold;
}

/*  追加 20230810 Y.Numahata */
div.main_title {
    font-size: 40px;
    font-weight: bold;
}

h2.main_title {
    font-size: 20px;
    font-weight: bold;
}

.btn-danger.detail-btn {
    width: 100%;
    height: 60px;
    line-height: 47px;
    font-size: 19px;
    font-weight: bold;
}

.btn-danger.detail-btn2 {
    width: 25%;
    height: 50px;
    line-height: 37px;
    font-size: 19px;
    font-weight: bold;
}

.margin-45 {
    margin-top: 45px;
}

#scroll_button {
    position: absolute;
    color: white;
    top: 85%;
    font-weight: bold;
    text-shadow: 1px 1px 5px #6c757d, -1px -1px 5px #6c757d;
    -ms-transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    margin: 0;
    padding: 15px 25px 3px;
    border: none;
    line-height: 20px;
    position: fixed;
    font-size: 25px;
}

#scroll_button:hover {
    background: none;
    font-size: 27px;
}

h3.sub_title {
    font-size: 25px;
    margin-top: 10px;
}

h1.main_title2 {
    position: relative;
    display: inline-block;
    padding: 0 55px;
    font-size: 30px;
    color: #555;
    margin: -25px 0 25px;
}

h1.main_title2:before, h1.main_title2:after {
    content: '';
    position: absolute;
    top: 50%;
    display: inline-block;
    width: 45px;
    height: 1px;
    background-color: black;
}

h1.main_title2:before {
    left: 0;
}

h1.main_title2:after {
    right: 0;
}

.blink {
    animation: blinkAnime 1s infinite alternate;
}

@keyframes blinkAnime {
    0% {
        color: #000000
    }
    100% {
        color: #ffffff
    }
}

.shutter {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #1e1e1e;
    z-index: 9999;
    font-size: 90px;
    color: #fff;
    text-align: center;
    margin: 0px auto;
    padding-top: 200px;
    font-weight: bold;
}

.shutter::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    margin: auto;
    background-color: #fff;
    width: 0;
    height: 1px;
}

.shutter::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    margin: auto;
    background: linear-gradient(to top, #fff, #f4f8fb, #eef7fd, #b7e0fd, #92d0fb, #b7e0fd, #eef7fd, #f4f8fb, #fff, transparent);
    width: 120%;
    height: 0px;
}

.shutter {
    -webkit-animation: byeShutter 2.6s forwards;
    animation: byeShutter 2.6s forwards;
}

.shutter::before {
    -webkit-animation: shutterOpen 2.6s forwards;
    animation: shutterOpen 2.6s forwards;
}

.shutter::after {
    -webkit-animation: shutterOpen2 2.6s forwards;
    animation: shutterOpen2 2.6s forwards;
}

.content {
    -webkit-animation: contentScale 2.6s forwards;
    animation: contentScale 2.6s forwards;
}

@keyframes byeShutter {
    70% {
        opacity: 1;
    }
    100% {
        opacity: 0;
        display: none;
        z-index: -1;
    }
}

@keyframes shutterOpen {
    0% {
        width: 0;
        height: 1px;
    }
    50% {
        width: 100%;
        height: 1px;
    }
    90% {
        width: 100%;
        height: 100%;
    }
    100% {
        width: 100%;
        height: 100%;
    }
}

@keyframes shutterOpen2 {
    60% {
        width: 120%;
        height: 0;
        transform: rotate(5deg);
    }
    90% {
        width: 120%;
        height: 100%;
        transform: rotate(-5deg);
    }
    100% {
        width: 120%;
        height: 100%;
        transform: rotate(-5deg);
    }
}

@keyframes contentScale {
    70% {
        -webkit-transform: perspective(800px) scale(0.9) rotateX(15deg);
        transform: perspective(800px) scale(0.9) rotateX(15deg);
    }
    100% {
        -webkit-transform: perspective(800px) scale(1) rotateX(0);
        transform: perspective(800px) scale(1) rotateX(0);
    }
}

.shutter {
    background: #fff;
}

.service_image {
    width: 300px;
    margin: 0 auto;
    overflow: hidden;
    cursor: pointer;
}

.service_image img {
    width: 100%;
    transition-duration: 0.5s;
}

.service_image:hover img {
    transform: rotateY(180deg);
    transition-duration: 0.5s;
}

@media screen and (max-width: 991px) {
    body .news-list-content {
        font-size: 0.9em;
    }

    .navbar-menu {
        font-size: 16px;
    }

    nav.navbar {
        height: 60px;
        padding: 0.7rem 1rem;
    }

    /*  padding-left追加 20230810 Y.Numahata */
    h1.main_title {
        font-size: 25px;
        padding-left: 10%;
    }

    /* 追加 20230810 Y.Numahata */
    h1.main_title span {
        display: block;
        /* padding-left: 10%; */
        text-align: right;
    }

    /* 追加 20230810 Y.Numahata */
    div.main_title {
        font-size: 25px;
    }

    h2.main_title {
        font-size: 15px;
    }

    .btn-danger.detail-btn {
        height: 55px;
        line-height: 42px;
        font-size: 14px;
        width: 100%;
    }

    .btn-danger.detail-btn2 {
        width: 35%;
        height: 38px;
        line-height: 28px;
        font-size: 14px;
    }

    .margin-45 {
        margin-top: 25px;
    }

    #scroll_button {
        font-size: 0.8em;
        padding: 14px 16px 0px;
        line-height: 5px;
    }

    .shutter {
        font-size: 45px;
        padding-top: 150px;
        background-size: 240%;
    }
}

.head_br {
    display: none;
}

.css-badge-s {
    display: none;
}

.css-badge-l {
    display: block;
    position: absolute;
    right: 10px;
}

.css-badge-l a img {
    max-width: 58px;
}

#footer > div.row {
    background: linear-gradient(-135deg, #495057, #545b62, #6c757d, #495057, #545b62, #6c757d);
    color: #fff;
    font-weight: bold;
    height: 70px;
    line-height: 70px;
    position: relative;
}

@media screen and (min-width: 1501px) {
    .navbar-nav.mx-auto {
        gap: 15px;
    }
}

@media screen and (min-width: 1200px) {
    .css-badge-s {
        display: none;
    }

    .css-badge-l {
        display: block;
    }

    .navbar-expand-xl .navbar-nav .nav-link:not(.menu-hubnet a, .menu-pas a) {
        padding-right: .7rem;
        padding-left: .7rem;
        font-size: 15px;
    }

    .nav-item {
        height: 80px;
        text-align: center;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    nav.navbar {
        height: 80px;
        padding-right: 0px;
    }

    .menu-hubnet, .menu-hubnet a {
        width: 135px;
        background-color: var(--button_red_color) !important;
        color: var(--white_color) !important;
        padding-top: 4px;
        font-size: 13px;
        display: flex;
        flex-direction: column;
        gap: 3px;
    }

    .menu-contact {
        width: 100px;
        background-color: var(--white_color);
        /* background-color: #f8c239; */
        /* border: 2px solid var(--button_red_color); */
        border-left: none;
        border-right: none;
        /* background-color: #666; */
        background-color: #fbe8e8;
        background: #F5CACF;
    }

    .menu-contact a {
        color: var(--button_red_color) !important;
        /* color: var(--white_color) !important; */
        /* color: #343a40 !important; */
    }

    .hubnet_small01 {
        font-size: 15px;
        line-height: 1.2em;
        letter-spacing: 5px;
    }

    .hubnet_small02 {
        font-size: 11px;
        letter-spacing: 3px;
    }

    .menu-pas, .menu-pas a {
        width: 120px;
        background-color: #000000 !important;
        color: #F8C239 !important;
        padding-top: 4px;
        font-size: 13px;
        display: flex;
        flex-direction: column;
        gap: 3px;
    }

    .pas_small01 {
        font-size: 15px;
        line-height: 1.2em;
    }

    .pas_small02 {
        font-size: 11px;
        font-style: italic;
        letter-spacing: 3px;
    }

    .menu-language, .menu-language a {
        width: 90px;
        background-color: #343a40 !important;
        color: #fff !important;
        line-height: 20px;
        padding-top: 4px;
    }
}

@media screen and (min-width: 1200px) and (max-width: 1500px) {
    .head_br {
        display: block;
    }

    nav.navbar {
        padding-left: 10px;
    }

    .navbar-nav.mx-auto {
        width: 100%;
        justify-content: center;
        gap: 8px;
    }

    .nav-link .space {
        display: inline-block;
        width: 10px;
    }
}

@media screen and (max-width: 1199px) {
    #navbarNavDropdown {
        background: rgba(255, 255, 255, 0.8);
        border: solid 1px #bd0a0a;
        margin-top: 8px;
        box-shadow: 0 10px 25px 0 rgba(0, 0, 0, .5);
        border-radius: 5px;
        z-index: 9999;
    }

    #navbarNavDropdown ul li a {
        border-bottom: solid 1px #ddd;
        background: rgba(255, 255, 255, 0.8);
        z-index: 5;
        color: #343a40 !important;
        margin-right: 10px;
        margin-left: 10px;
        font-weight: bold;
        text-align: center;
    }

    .css-badge-s {
        display: block;
    }

    .css-badge-l {
        display: none;
    }

    .navbar-toggler {
        border: 1px solid #bd0a0a;
        background: #bd0a0a !important;
    }

    .navbar-toggler:focus {
        outline: none;
    }

    .navbar-toggler-icon {
        background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30'	xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(255, 255, 255, 1)' stroke-width='3' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E") !important;
    }

    .hubnet-small02 {
        font-size: 1.3em;
    }

    .hubnet_small02, .pas_small02 {
        margin-left: 10px;
    }

    .menu-language, .menu-language a {
        font-size: 14px;
    }
}

p {
    margin-top: 0;
    margin-bottom: 1rem;
}

figure {
    margin: 0 0 1rem;
}

.navbar-toggler {
    border: 1px solid #bd0a0a;
    background: #bd0a0a;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(255,255,255,1)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 8h24M4 16h24M4 24h24'/%3E%3C/svg%3E");
}

.header {
    background: rgba(255, 255, 255, 1.0);
}

@media screen and (min-width: 992px) {
    .navbar-expand-lg .navbar-nav .nav-link {
        padding-right: .7rem;
        padding-left: .7rem;
        font-size: 15px;
    }
}

@media screen and (min-width: 992px) and (max-width: 1170px) {
    .nav-link {
        padding-right: .8rem;
        padding-left: .8rem;
        line-height: 24px;
        padding-top: 17px;
    }

    .menu-hubnet,
    .menu-hubnet a {
        padding-top: 14px;
    }

    .menu-language,
    .menu-language a {
        line-height: 20px;
    }
}

/* news-list in file css */
.navbar-toggler {
    border: 1px solid #bd0a0a;
    background: #bd0a0a;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(255,255,255,1)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 8h24M4 16h24M4 24h24'/%3E%3C/svg%3E");
}

.header {
    background: rgba(255, 255, 255, 1.0);
}

@media screen and (min-width: 992px) {
    .navbar-expand-lg .navbar-nav .nav-link {
        padding-right: .7rem;
        padding-left: .7rem;
        font-size: 15px;
    }
}

@media screen and (min-width: 992px) and (max-width: 1170px) {
    .nav-link {
        padding-right: .8rem;
        padding-left: .8rem;
        line-height: 24px;
        padding-top: 17px;
    }

    .menu-hubnet,
    .menu-hubnet a {
        padding-top: 14px;
    }

    .menu-language,
    .menu-language a {
        line-height: 20px;
    }
}

img {
    max-width: 100%;
}

.container-width {
    margin: 45px auto 0px;
}

.contact_header_div {
    height: 80px;
    width: 100%;
}

.news_date {
    font-size: 30px;
    color: #dc3545;
    margin-top: 50px;
}

.news_title {
    font-size: 24px;
    border-top: dotted 4px #dc3545;
    color: #000;
    padding: 10px;
    font-weight: bold;
}

.news_body {
    font-size: 16px;
}

@media screen and (max-width: 576px) {
    .container-width {
        max-width: 540px;
    }

    .contact_header_div {
        height: 60px;
    }

    .news_date {
        font-size: 25px;
        margin-top: 30px;
    }

    .news_title {
        font-size: 20px;
    }

    .news_body {
        font-size: 12px;
    }

    .pdf01,
    .pdf02 {
        width: 100%;
    }

    .pdf03,
    .pdf04,
    .pdf05 {
        width: 80%;
    }
}

@media screen and (min-width: 576px) and (max-width: 767px) {
    .container-width {
        max-width: 720px;
    }

    .contact_header_div {
        height: 60px;
    }

    .news_date {
        font-size: 26px;
        margin-top: 30px;
    }

    .news_title {
        font-size: 21px;
    }

    .news_body {
        font-size: 14px;
    }
}

@media screen and (min-width: 768px) and (max-width: 991px) {
    .container-width {
        max-width: 960px;
    }

    .contact_header_div {
        height: 60px;
    }

    .news_date {
        font-size: 28px;
        margin-top: 30px;
    }

    .news_title {
        font-size: 23px;
    }

    .news_body {
        font-size: 15px;
    }
}

@media screen and (min-width: 992px) and (max-width: 1199px) {
    .container-width {
        max-width: 1140px;
    }
}

@media screen and (min-width: 1200px) {
}
