<?php

declare(strict_types=1);

namespace App\Services\CheckingRequest;

use App\Enums\CheckingRequest\Statuses;
use App\Repositories\sqlServer\CheckingRequestRepository;
use Exception;
use Illuminate\Support\Carbon;

class CreateCheckingRequestService
{
    private CheckingRequestRepository $checkingRequestRepository;

    public function __construct(CheckingRequestRepository $checkingRequestRepository)
    {
        $this->checkingRequestRepository = $checkingRequestRepository;
    }

    /**
     * @throws Exception
     */
    public function call(array $body): void
    {
        $prepareData = $this->prepareData($body);

        $this->checkingRequestRepository->createOneWithRelations($prepareData);
    }

    private function prepareData(array $body): array
    {
        $now = Carbon::now();

        return [
            'reg_date' => $now->format('Y-m-d H:i:s'),
            'odr_date' => $now->format('Y-m-d H:i:s'),

            't_aa_date_id' => $body['t_aa_date_id'],
            'm_customer_id' => $body['m_customer_id'],
            'odr_person' => $body['odr_person'],
            'odr_tel' => $body['odr_tel'] ?? '',
            'odr_mail' => $body['odr_mail'] ?? '',
            'aa_no' => $body['aa_no'],
            'car_name' => $body['car_name'],
            'note' => $body['note'] ?? '',
            'cc_note' => $body['cc_note'] ?? '',
            'photo_flg' => $body['photo_flg'],
            'optn1_flg' => $body['optn1_flg'],
            'optn2_flg' => $body['optn2_flg'],

            'up_owner' => auth()->user()->id,
            'output_flg' => 0,
            'st_cd' => Statuses::INSPECTED,
            'del_flg' => 0
        ];
    }
}
