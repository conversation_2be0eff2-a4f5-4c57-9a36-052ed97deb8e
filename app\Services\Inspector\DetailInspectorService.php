<?php

declare(strict_types=1);

namespace App\Services\Inspector;

use App\Repositories\sqlServer\InspectorRepository;
use Exception;

class DetailInspectorService
{
    private InspectorRepository $inspectorRepository;

    public function __construct(InspectorRepository $inspectorRepository)
    {
        $this->inspectorRepository = $inspectorRepository;
    }

    /**
     * @throws Exception
     */
    public function call(int $id): mixed
    {
        return $this->inspectorRepository->findDetailById($id);
    }
}
