<?php

declare(strict_types=1);

namespace App\Guards;

use App\Services\Jwt\JwtService;
use Illuminate\Auth\GuardHelpers;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Auth\UserProvider;
use Illuminate\Http\Request;

class JwtGuard implements Guard
{
    use GuardHelpers;

    /**
     * @var Request
     */
    protected Request $request;

    /**
     * @var JwtService
     */
    protected JwtService $jwtService;

    /**
     * @var string|null
     */
    protected ?string $token = null;

    /**
     * @param UserProvider $provider
     * @param Request $request
     * @param JwtService $jwtService
     */
    public function __construct(UserProvider $provider, Request $request, JwtService $jwtService)
    {
        $this->provider = $provider;
        $this->request = $request;
        $this->jwtService = $jwtService;
    }

    /**
     * Get the currently authenticated user.
     *
     * @return Authenticatable|null
     */
    public function user(): ?Authenticatable
    {
        if (null !== $this->user) {
            return $this->user;
        }

        $token = $this->getTokenFromRequest();

        if (!$token) {
            return null;
        }

        $payload = $this->jwtService->verifyToken($token);

        if (!$payload) {
            return null;
        }

        $userId = $payload['sub'];

        $this->user = $this->provider->retrieveById($userId);

        return $this->user;
    }

    /**
     * Validate a user's credentials.
     *
     * @param array $credentials
     * @return bool
     */
    public function validate(array $credentials = []): bool
    {
        return false;
    }

    /**
     * Get the token from the request.
     *
     * @return string|null
     */
    protected function getTokenFromRequest(): ?string
    {
        $bearer = $this->request->header('Authorization');

        if (!$bearer) {
            return null;
        }

        if (str_starts_with($bearer, 'Bearer ')) {
            return mb_substr($bearer, 7);
        }

        return $bearer;
    }
}
