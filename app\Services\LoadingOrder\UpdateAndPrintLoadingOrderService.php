<?php

declare(strict_types=1);

namespace App\Services\LoadingOrder;

use App\Enums\LoadingOrder\LoadingOrderStatuses;
use App\Repositories\sqlServer\LoadingOrderRepository;
use Illuminate\Database\Eloquent\Collection;

class UpdateAndPrintLoadingOrderService
{
    private LoadingOrderRepository $loadingOrderRepository;

    public function __construct(LoadingOrderRepository $loadingOrderRepository)
    {
        $this->loadingOrderRepository = $loadingOrderRepository;
    }

    public function call(array $body): Collection
    {
        $ids = $body['ids'];

        $this->update($ids);

        return $this->print($ids);
    }

    private function update(array $ids): void
    {
        $dataUpdate = $this->prepareData();

        $this->loadingOrderRepository->bulkUpdateStatus($ids, $dataUpdate);
    }

    private function prepareData(): array
    {
        return [
            'up_date' => now(),
            'up_owner' => auth()->user()->id,
            'st_cd' => LoadingOrderStatuses::RECEIVED->value
        ];
    }

    private function print(array $ids = []): Collection
    {
        return $this->loadingOrderRepository->getLoadingOrderPrint($ids);
    }
}
