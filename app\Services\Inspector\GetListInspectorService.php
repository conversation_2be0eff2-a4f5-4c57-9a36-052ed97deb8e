<?php

declare(strict_types=1);

namespace App\Services\Inspector;

use App\Constants\PageMetaData;
use App\Http\Resources\Inspector\InspectorResource;
use App\Repositories\sqlServer\InspectorRepository;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Arr;

class GetListInspectorService
{
    private InspectorRepository $inspectorRepository;

    public function __construct(InspectorRepository $inspectorRepository)
    {
        $this->inspectorRepository = $inspectorRepository;
    }

    public function call(array $params): AnonymousResourceCollection|array
    {
        $relations = [];
        $pagination = $this->inspectorRepository->paginate($params, $relations);

        return [
            'items' => InspectorResource::collection($pagination),
            'meta' => (new PageMetaData($pagination))->toArray()
        ];
    }
}
