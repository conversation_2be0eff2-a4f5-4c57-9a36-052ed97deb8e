# Quy tắc định dạng code

Dự án sử dụng [<PERSON><PERSON>](https://laravel.com/docs/11.x/pint) để đảm bảo tính nhất quán trong định dạng code.


## Cấu hình

Cấu hình Laravel Pint được định nghĩa trong file `pint.json` ở thư mục gốc của dự án. Dự án sử dụng preset Laravel với một số quy tắc tùy chỉnh.

## Sử dụng

### Kiểm tra định dạng code

Để kiểm tra xem code có tuân thủ quy tắc định dạng với những file PHP có thay đổi đổi:

```bash
composer format-test
```

### Tự động định dạng code
Để  tự động định dạng code tuân thủ theo quy tắc với những file PHP có thay đổi đổi:

```bash
composer format
```

<PERSON><PERSON>  tự động định dạng code tuân thủ theo quy tắc mà không thực hiện thay đổi:

```bash
composer format-all
```

### Định dạng một file hoặc thư mục cụ thể

```bash
./vendor/bin/pint app/Http/Controllers/Api/User/AuthController.php
```

## Tích hợp với Git (sử dụng Husky)

Dự án sử dụng [Husky](https://typicode.github.io/husky/) để quản lý Git hooks. Husky giúp đảm bảo rằng tất cả thành viên trong dự án đều sử dụng cùng một bộ Git hooks.

### Cài đặt Husky

Khi bạn chạy `npm install husky --save-dev`, Husky sẽ được tự động cài đặt và cấu hình. Nếu bạn cần cài đặt lại, hãy chạy:

```bash
npm install husky --save-dev
```

### Cách hoạt động của pre-commit hook

Pre-commit hook được cấu hình để chạy Laravel Pint trước khi commit. Nếu code không đạt chuẩn định dạng, commit sẽ bị từ chối.

Quy trình hoạt động:
1. Khi bạn chạy `git commit`, Husky sẽ tự động kích hoạt pre-commit hook
2. Hook sẽ chạy Laravel Pint để kiểm tra định dạng code đã được add
3. Nếu có vấn đề về định dạng, commit sẽ bị từ chối và bạn sẽ thấy thông báo lỗi
4. Bạn cần sửa các vấn đề về định dạng (có thể chạy `composer format` để tự động sửa) và thử commit lại

### Bỏ qua kiểm tra pre-commit (trong trường hợp khẩn cấp)

Trong trường hợp cần commit gấp mà không thể sửa tất cả các vấn đề về định dạng code, bạn có thể sử dụng tùy chọn `--no-verify`:

```bash
git commit -m "Your commit message" --no-verify
```

Tuy nhiên, hãy cố gắng tránh sử dụng tùy chọn này và luôn đảm bảo code của bạn tuân thủ quy tắc định dạng.
