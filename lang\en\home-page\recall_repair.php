<?php

declare(strict_types=1);

return [
    'page_title' => 'Recall Repair｜AUTOHUB Co.,Ltd.',
    'title' => 'Recall Repair',
    'page_description1' => 'Recall',
    'page_description2' => 'Repair',
    'service_descriptions' => 'It is difficult for recall repair in overseas so please do it in Japan.',
    'good_content' => [
        'Dealers often refuse to repair cars for exporting but we normally get repaired because of good connections and trust with dealers.',
        'We will do what is necessary to make complicated and error-free arrangements.',
        'We will make sure that the recall repair is done.'
    ],
    'how_to_use_content_1' => 'Online Order with HUBNET',
    'how_to_use_content_2' => 'Email or TEL / FAX to your Autohub staff.',
    'details' => 'Details',
    'detail_list' => [
        [
            'title' => 'Reservation for repair to the dealer',
            'description' => 'We will organise the nearest dealr from the car in our yard.',
        ],
        [
            'title' => 'Temporary Gate-out arrangement contact.',
            'description' => 'We will request yard staff to prepare for gate-out.',
        ],
        [
            'title' => 'Transportation arrangement from our yard to the dealer. Transportation arrangement from dealer to the yard.',
            'description' => 'We will arrange round-trip land transportation from the AUTOHUB yard to the dealer.',
        ],
        [
            'title' => 'We will make sure the repair done or not.',
            'description' => 'We will make sure if the car has been repaired and notify you that the repair has been completed.',
        ],
    ],
    'how_to_request' => 'How to request on HUBNET',
    'hubnet_order' => 'Hubnet member Log-in then you order here.',
    'support_service' => 'Additional Support service',
    'support_service_content' => 'Click here if you use additional support service <span class="text-[red]">(charged)</span>',
    'support_service_title' => 'Click on Additional<br>Support service',
    'check_work' => 'Check for Easy work',
    'write_recall_repair' => 'Please write "Recall Repair" in the remarks column.',
];
