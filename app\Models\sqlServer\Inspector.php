<?php

declare(strict_types=1);

namespace App\Models\sqlServer;

use Illuminate\Database\Eloquent\Model;

/**
 * Inspector Model
 *
 * Handles list of inspectors/inspection staff
 * Maps to database table: t_inspector
 */
class Inspector extends Model
{
    public const CREATED_AT = 'reg_date';
    public const UPDATED_AT = 'up_date';

    protected $fillable = [
        'del_flg',
        'reg_date',
        'up_date',
        'up_owner',
        'id',
        'name',
        'name_kana',
        'tel',
        'mail',
    ];
    protected $table = 't_inspector';
    protected $casts = [
        'del_flg' => 'boolean',
    ];
}
