<?php

declare(strict_types=1);

namespace App\Http\Requests\Transport;

use Illuminate\Foundation\Http\FormRequest;

class GetAaToTransConfigRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'aa_place_id' => [
                'required',
                'integer',
            ],
            'to_place_id' => [
                'required',
                'integer',
            ]
        ];
    }
}
