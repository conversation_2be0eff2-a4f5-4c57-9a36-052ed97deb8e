<div>
    @php
        $i = 0;
    @endphp

    <table class="w-full border-collapse">
        @foreach ($data as $key => $item)
            <tr class="border-gray {{ $i == 0 ? '!border-none' : '' }} border-t border-dashed">
                <td class="w-1/4 p-1 text-center text-xs font-bold md:p-3 md:text-sm">{{ __($item['title']) }}</td>
                <td class="p-1 text-xs md:p-3 md:text-sm">
                    {!! __($item['content']) !!}
                </td>
            </tr>
            @php
                $i++;
            @endphp
        @endforeach

        <tr class="border-gray border-t border-dashed hidden md:table-row">
            <td class="w-1/4 p-1 text-center md:p-3"></td>
            <td class="p-1 text-center md:p-3">
                <iframe
                    class="h-[315px] w-full max-w-[560px]"
                    src="https://www.youtube.com/embed/nLGv73a45-U"
                    title="YouTube video player"
                    frameborder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowfullscreen
                ></iframe>
            </td>
        </tr>
    </table>

    <div class="border-gray mx-auto mt-2 mb-2 block border-t border-dashed pt-2 md:hidden">
        <iframe
            class="h-[315px] w-full max-w-[560px]"
            src="https://www.youtube.com/embed/nLGv73a45-U"
            title="YouTube video player"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowfullscreen
        ></iframe>
    </div>

    <div class="mt-2 flex justify-center">
        <a
            href="https://en-gage.net/autohub_career/"
            class="rounded bg-orange-500 px-6 py-3 font-bold text-white transition-colors duration-300 hover:bg-orange-200"
        >
            {{ __('button.apply') }}
        </a>
    </div>
</div>
