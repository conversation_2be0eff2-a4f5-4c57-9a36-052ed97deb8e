<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\GetDataDropdownUserRequest;
use App\Services\User\UserService;
use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Response;

class CustomerController extends Controller
{
    private UserService $userService;

    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }

    #[OA\Get(
        path: '/api/admin/customers/dropdown',
        summary: 'Get customer dropdown data',
        description: 'Retrieve customer data for dropdown selection. At least one search parameter is required.',
        security: [['access_token' => []]],
        tags: ['Customer Management'],
        parameters: [
            new OA\Parameter(
                name: 'customer_name',
                description: 'Search by customer name (Japanese or English)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '株式会社サンプル')
            ),
            new OA\Parameter(
                name: 'm_customer-cus_Name_EN-equal',
                description: 'Search by customer English name (exact match)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'Sample Corporation')
            ),
            new OA\Parameter(
                name: 'm_customer-id-equal',
                description: 'Search by customer ID (exact match)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '12345')
            ),
            new OA\Parameter(
                name: 'm_customer-ch_charge_sale-equal',
                description: 'Search by charge sale code (exact match)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'SALE001')
            ),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Customer dropdown data retrieved successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(
                            property: 'status',
                            type: 'boolean',
                            example: true
                        ),
                        new OA\Property(
                            property: 'data',
                            type: 'array',
                            items: new OA\Items(
                                properties: [
                                    new OA\Property(
                                        property: 'id',
                                        description: 'Customer ID',
                                        type: 'integer',
                                        example: 12345
                                    ),
                                    new OA\Property(
                                        property: 'cus_Name_JP',
                                        description: 'Customer name in Japanese',
                                        type: 'string',
                                        example: '株式会社サンプル'
                                    ),
                                ],
                                type: 'object'
                            )
                        ),
                        new OA\Property(
                            property: 'message',
                            type: 'string',
                            example: 'Customer dropdown data retrieved successfully',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'locale',
                            type: 'string',
                            example: null,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'code',
                            type: 'integer',
                            example: null,
                            nullable: true
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 400,
                description: 'Bad Request - No search parameters provided',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(
                            property: 'status',
                            type: 'boolean',
                            example: false
                        ),
                        new OA\Property(
                            property: 'message',
                            type: 'string',
                            example: 'At least one search parameter is required'
                        ),
                        new OA\Property(
                            property: 'errors',
                            properties: [
                                new OA\Property(
                                    property: 'customer_name',
                                    type: 'array',
                                    items: new OA\Items(type: 'string'),
                                    example: ['At least one search parameter is required']
                                ),
                                new OA\Property(
                                    property: 'm_customer-cus_Name_EN-equal',
                                    type: 'array',
                                    items: new OA\Items(type: 'string'),
                                    example: ['At least one search parameter is required']
                                ),
                                new OA\Property(
                                    property: 'm_customer-id-equal',
                                    type: 'array',
                                    items: new OA\Items(type: 'string'),
                                    example: ['At least one search parameter is required']
                                ),
                                new OA\Property(
                                    property: 'ch_charge_sale',
                                    type: 'array',
                                    items: new OA\Items(type: 'string'),
                                    example: ['At least one search parameter is required']
                                ),
                            ],
                            type: 'object'
                        )
                    ]
                )
            ),
            new OA\Response(response: 401, description: 'Unauthorized - Invalid or missing access token'),
            new OA\Response(response: 403, description: 'Forbidden - Insufficient permissions'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function getDataDropdown(GetDataDropdownUserRequest $request): Response
    {
        $customers = $this->userService->getDataDropdown($request->validated());
        return $this->respond($customers);
    }
}
