<?php

declare(strict_types=1);

namespace App\Http\Resources\Transport;

use App\Traits\CommonTrait;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TransportDetailResource extends JsonResource
{
    use CommonTrait;
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'tp_id' => $this->tp_id,
            'm_trans_id' => $this->m_trans_id,
            'st_cd' => $this->st_cd,
            'deli_price' => $this->deli_price,
            'sales_price' => $this->sales_price,
            'plate_send_date' => $this->plate_send_date ? Carbon::parse($this->plate_send_date)->format('Y-m-d H:i:s') : null,
            'plate_send_no' => $this->plate_send_no,
            'plate_send_co' => $this->plate_send_co,
            'to_plan_date' => $this->to_plan_date ? Carbon::parse($this->to_plan_date)->format('Y-m-d H:i:s') : null,
            'to_date' => $this->to_date ? Carbon::parse($this->to_date)->format('Y-m-d H:i:s') : null,
            'car_no' => $this->car_no,
            'from_plan_date' => $this->from_plan_date ? Carbon::parse($this->from_plan_date)->format('Y-m-d H:i:s') : null,
            'fr_name' => $this->fr_name,
            'fr_addr' => $this->fr_addr,
            'fr_tel' => trim($this->fr_tel),
            'to_name' => $this->to_name,
            'to_addr' => $this->to_addr,
            'to_tel' => trim($this->to_tel),
        ];
    }
}
