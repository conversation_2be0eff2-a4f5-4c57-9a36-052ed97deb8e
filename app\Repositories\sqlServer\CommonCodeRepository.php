<?php

declare(strict_types=1);

namespace App\Repositories\sqlServer;

use App\Enums\CommonCode\CommonCodeCategory;
use App\Enums\CommonCode\ExcludeLabel;
use App\Models\sqlServer\CommonCode;
use App\Repositories\BaseRepository;

class CommonCodeRepository extends BaseRepository
{
    public function model(): string
    {
        return CommonCode::class;
    }

    public function getInspectionStatuses(): \Illuminate\Database\Eloquent\Collection
    {
        $query = ($this->model)::query();

        return $query
            ->where('cd_kbn', '=', CommonCodeCategory::INSPECTION_STATUS->value)
            ->where('cd', '<>', ExcludeLabel::LABEL_CODE->value)
            ->where('del_flg', '=', 0)
            ->get();
    }

    public function getServicePlans(): \Illuminate\Database\Eloquent\Collection
    {
        $query = ($this->model)::query();

        return $query
            ->where('cd_kbn', '=', CommonCodeCategory::SERVICE_TYPE->value)
            ->where('cd', '<>', ExcludeLabel::LABEL_CODE->value)
            ->where('del_flg', '=', 0)
            ->orderBy('sort_no', 'asc')
            ->get();
    }
}
