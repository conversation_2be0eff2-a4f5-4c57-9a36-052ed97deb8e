<?php

declare(strict_types=1);

namespace App\Services\VehicleTransport;

use App\Constants\PageMetaData;
use App\Http\Resources\VehicleTransport\VehicleTransportResource;
use App\Repositories\as400\VehicleTransportRepository;
use App\Traits\CommonTrait;
use Carbon\Carbon;

class VehicleTransportService
{
    use CommonTrait;

    public function __construct(
        private VehicleTransportRepository $vehicleTransportRepository
    ) {
    }

    public function transportVehicleReportMonth(array $params = [])
    {
        $camp_date = $params['month'];
        $year = now()->year;
        $params['from_date'] = Carbon::createFromDate($year, $camp_date, 1)->format('Ymd');
        $params['to_date'] = Carbon::createFromDate($year, $camp_date, 1)->endOfMonth()->format('Ymd');

        $pagination = $this->vehicleTransportRepository->getPaginate($params);

        return [
            'items' => VehicleTransportResource::collection($pagination),
            'meta' => (new PageMetaData($pagination))->toArray()
        ];
    }
}
