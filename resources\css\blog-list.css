body {
    background: #f9f9f9;
}

label {
    display: inline-block;
    margin-bottom: 0.5rem;
}

.blog_title {
    font-size: 40px;
    font-weight: bold;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.blog_view_title {
    font-size: 37px;
    font-weight: bold;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

#add_line_close {
    position: absolute;
    top: 15px;
    right: 32px;
    width: 50px;
    z-index: 10;
}

#add_line_close_768 {
    display: none;
    position: absolute;
    width: 35px;
    z-index: 10;
}

.hubnet-btn {
    width: 300px;
    position: fixed;
    right: 15px;
    top: 100px;
    z-index: 99;
    text-align: right;
}

.h-btn {
    width: 300px;
    height: auto;
    filter: drop-shadow(2px 3px 7px black);
    opacity: 1;
    position: absolute;
    right: 0;
}

.h-btn:hover {
    opacity: 0;
}

.h-btn-hover {
    width: 300px;
    height: auto;
    filter: drop-shadow(2px 3px 7px black);
    opacity: 0;
    position: absolute;
    right: 0;
}

.h-btn-hover:hover {
    opacity: 1;
}

.add_line_close_768, .add_line_close {
    position: absolute;
    right: 60px;
    top: 10px;
}

.add_line_close_768 {
    display: none;
}

@media screen and (max-width: 1199px) {
    .blog_title {
        font-size: 35px;
    }

    .blog_view_title {
        font-size: 32px;
    }

    .h-btn {
        width: 250px;
    }

    .h-btn-hover {
        width: 250px;
    }

    .add_line_close_768, .add_line_close {
        right: 40px;
    }
}

@media screen and (max-width: 991px) {
    .h-btn {
        width: 230px;
    }

    .h-btn-hover {
        width: 230px;
    }
}

@media screen and (max-width: 768px) {
    .blog_title {
        font-size: 30px;
    }

    .blog_view_title {
        font-size: 27px;
    }

    .add_line_close {
        display: none;
    }

    .add_line_close_768 {
        display: block;
    }

    .h-btn {
        width: 200px;
    }

    .h-btn-hover {
        width: 200px;
    }

    #add_line_close {
        display: none;
        top: 15px;
        right: 25px;
    }

    #add_line_close_768 {
        display: block;
        top: 15px;
        right: 3px;
        width: 35px;
    }
}

@media screen and (max-width: 500px) {
    .add_line_close_768, .add_line_close {
        right: auto !important;
        top: auto !important;
        bottom: 100px;
        left: 150px;
    }

    .h-btn {
        width: 180px;
        bottom: 0;
        left: 15px;
    }

    .h-btn-hover {
        width: 180px;
        bottom: 0;
        left: 15px;
    }

    .hubnet-btn {
        right: auto;
        top: auto;
        bottom: 15px;
        left: 0;
    }
}

.br-480 {
    display: none;
}

@media screen and (max-width: 480px) {
    .br-480 {
        display: block;
    }

    .blog_title {
        font-size: 27px;
    }

    .blog_view_title {
        font-size: 25px;
    }
}

/*Category*/
.blog-categories {
    display: flex;
    gap: 15px;
    margin: 20px auto 40px;
    width: 100%;
    justify-content: space-between;
}

.blog-categories input[type="radio"] {
    display: none;
}

.blog-categories label {
    padding: 15px 20px;
    cursor: pointer;
    width: calc(33.3% - 15px);
    text-align: center;
    font-weight: bold;
    position: relative;
}

.blog-categories label::after {
    content: "";
    position: absolute;
    bottom: -50px;
    left: 50%;
    transform: translateX(-50%);
    border-width: 25px;
    border-style: solid;
    display: none;
}

.blog-categories input[type="radio"]:checked + label::after {
    display: block;
}

.blog-categories label:nth-child(2)::after {
    border-color: #dc3545 transparent transparent transparent;
}

.blog-categories label:nth-child(4)::after {
    border-color: #036eb8 transparent transparent transparent;
}

.blog-categories label:nth-child(6)::after {
    border-color: #eb4d2a transparent transparent transparent;
}

.blog-categories label:nth-child(2) {
    background-color: #fff;
    border: 1px solid #dc3545;
    color: #dc3545;
}

.blog-categories input[type="radio"]:checked + label:nth-child(2) {
    background-color: #dc3545;
    color: #fff;
}

.blog-categories label:nth-child(4) {
    background-color: #fff;
    border: 1px solid #036eb8;
    color: #036eb8;
}

.blog-categories input[type="radio"]:checked + label:nth-child(4) {
    background-color: #036eb8;
    color: #fff;
}

.blog-categories label:nth-child(6) {
    background-color: #fff;
    border: 1px solid #eb4d2a;
    color: #eb4d2a;
}

.blog-categories input[type="radio"]:checked + label:nth-child(6) {
    background-color: #eb4d2a;
    color: #fff;
}

.blog-contents {
    width: 100%;
    display: flex;
    gap: 50px;
}

.contents_info {
    width: 70%;
    padding-top: 20px;
}

.blog-tags {
    width: 30%;
}

.contents_info .news-item {
    /* border:solid 3px #036eb8; */
    border: none;
    background: #fff;
    padding: 20px 25px;
    margin-bottom: 30px;
    cursor: pointer;
}

.contents_info .news-item:hover {
    -webkit-box-shadow: 3px 3px 12px -2px rgba(0, 0, 0, .1);
    box-shadow: 3px 3px 12px -2px rgba(0, 0, 0, .1);
    -webkit-transform: translateY(-4px);
    transform: translateY(-4px);
    -webkit-transition: 0.3s;
    transition: 0.3s;
}

.contents_info .news-item .news_title {
    margin-bottom: 20px;
}

.contents_info .news-item .news_title a {
    color: #343a40;
    font-weight: bold;
    font-size: 1.2em;
    line-height: 1.2em;
}

.contents_info .news-item .news_title a:hover {
    text-decoration: none;
    opacity: .8;
}

.contents_info .news-item .news-body {
    display: flex;
    flex-direction: row;
    gap: 30px;
}

.contents_info .news-item .news-body .news-left {
    width: 40%;
    height: auto;
    max-height: 180px;
    overflow: hidden;
}

.contents_info .news-item .news-body .news-left img {
    width: 100%;
}

.contents_info .news-item .news-body .news-right {
    width: 60%;
    line-height: 2em;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.contents_info .news-item .news-body .news-right ul {
    display: flex;
    flex-direction: row;
    list-style: none;
    padding: 0;
    gap: 30px;
    margin: 0;
}

.contents_info .news-item .news-body .news-right ul li::before {
    content: "\f017";
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    font-size: 18px;
    margin-right: 5px;
    color: #aaa;
}

.contents_info .news-item .news-body .news-right label {
    width: 100px;
    background: #036eb8;
    color: #fff;
    text-align: center;
    padding: 3px;
    font-size: 10px;
    line-height: 20px;
}

.blog-tags input[type="text"] {
    border-radius: 5px;
    border: solid 1px #aaa;
    padding: 15px;
    width: 100%;
}

.blog-tags input[type="text"]:focus-visible {
    outline: none;
}

.blog-tags button#blog-searchBtn {
    background: transparent;
    border: none;
    margin-left: -30px;
    color: #aaa;
    position: absolute;
    font-size: 1.5em;
    top: 16px;
    right: 15px;
    cursor: pointer;
}

.blog-tags button#blog-searchBtn:focus {
    outline: none;
}

.blog-tags .srch_area {
    margin: 20px auto 30px;
    position: relative;
}

.blog-tags ul {
    display: flex;
    gap: 10px;
    list-style: none;
    padding: 0;
    flex-wrap: wrap;
}

.blog-tags ul li {
    background-color: #fff;
    position: relative;
    border: solid 1px #aaa;
    border-radius: 5px;
    padding: 5px 22px;
}

.blog-tags ul li a {
    text-decoration: none;
    color: #555;
    display: flex;
    align-items: center;
    justify-content: center;
}

.blog-tags ul li a::before {
    content: "\f02b";
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    font-size: 22px;
    margin-right: 5px;
    color: #aaa;
}

.blog-tags ul li:hover {
    cursor: pointer;
    background: #f2f2f2;
}

.blog-tags ul li.active {
    background: #a7a7a7;
    border: solid 1px #f2f2f2;
}

.blog-tags ul li.active a {
    color: #fff;
}

.blog-tags ul li.active a::before {
    color: #fff;
}

.blog-tags ul li.tag-clear {
    background: transparent;
    border: none;
    margin-left: 0;
    padding: 5px;
    width: 100%;
}

.blog-tags ul li.tag-clear a {
    justify-content: flex-start;
}

.blog-tags ul li.tag-clear a::before {
    content: "\f057";
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    font-size: 25px;
    margin-right: 5px;
    color: #aaa;
}

@media screen and (max-width: 1200px) {
    .blog-tags ul li {
        padding: 5px 20px;
        font-size: .9em;
    }
}

@media screen and (max-width: 991px) {
    .blog-contents {
        gap: 30px;
    }

    .contents_info .news-item {
        padding: 15px 10px;
    }

    .contents_info .news-item .news-body {
        gap: 15px;
    }

    .contents_info .news-item .news_title a {
        font-size: 1.0em;
    }

    .blog-categories {
        margin: 20px auto 20px;
    }

    .blog-categories label {
        padding: 10px 15px;
        font-size: .9em;
    }

    .blog-categories label::after {
        bottom: -30px;
        border-width: 15px;
    }

    .blog-tags .srch_area {
        margin: 20px auto 20px;
    }

    .blog-tags ul li {
        padding: 5px 15px;
        font-size: .8em;
    }

    .blog-tags input[type="text"] {
        font-size: .9em;
        padding: 10px;
    }

    .blog-tags button#blog-searchBtn {
        font-size: 1.3em;
        top: 12px;
    }

    .contents_info .news-item .news-body .news-right {
        line-height: 1.5em;
    }

    .contents_info .news-item .news-body .news-right ul {
        flex-direction: column;
        gap: 0;
    }
}

@media screen and (max-width: 768px) {
    .blog-contents {
        flex-direction: column;
        gap: 0;
    }

    .contents_info, .blog-tags {
        width: 100%;
    }

    .contents_info .news-item {
        margin-bottom: 20px;
    }

    .contents_info .news-item .news-body .news-right ul {
        font-size: .8em;
    }

    .contents_info .news-item .news-body .news-right ul li::before {
        font-size: 15px;
    }
}

@media screen and (max-width: 500px) {
    .blog-categories {
        gap: 10px;
    }

    .blog-categories label {
        padding: 10px;
        font-size: .8em;
        width: calc(33.3% - 5px);
    }

    .contents_info .news-item .news_title a {
        font-size: .9em;
    }

    .contents_info .news-item .news-body .news-right label {
        font-size: 9px;
        padding: 1px;
        width: 80px;
    }

    .blog-tags ul li {
        padding: 4px 10px;
        font-size: .7em;
    }

    .blog-tags ul li a::before {
        font-size: 18px;
    }

    .blog-tags input[type="text"] {
        font-size: .8em;
    }

    .blog-tags button#blog-searchBtn {
        top: 10px;
    }
}

.blog-view-area {
    width: 100%;
    background: #fff;
    padding: 50px;
    box-shadow: 3px 3px 12px -2px rgba(0, 0, 0, .1);
    margin-bottom: 50px;
}

h2.wp-block-heading {
    margin: 60px 0 20px;
    padding: 10px 15px;
    border-left: solid 10px #dc3545;
}

.wp-block-image img {
    max-width: 80%;
}

@media screen and (max-width: 991px) {
    .wp-block-image img {
        max-width: 100%;
    }

    .blog-view-area {
        padding: 30px;
    }

    h2.wp-block-heading {
        font-size: 1.8em;
    }

    h3.wp-block-heading {
        font-size: 1.7em;
    }
}

@media screen and (max-width: 768px) {
    .blog-view-area {
        padding: 25px 15px;
    }

    h2.wp-block-heading {
        font-size: 1.5em;
    }

    h3.wp-block-heading {
        font-size: 1.4em;
    }
}
