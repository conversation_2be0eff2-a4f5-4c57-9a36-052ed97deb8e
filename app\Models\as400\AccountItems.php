<?php

declare(strict_types=1);

namespace App\Models\as400;

use Illuminate\Database\Eloquent\Model;

class AccountItems extends Model
{
    public $timestamps = false;
    public $incrementing = false;
    protected $connection = 'as400';
    protected $table = 'ABDLIB.MCONTP';
    protected $primaryKey = null;

    protected $fillable = [
        'CRCC51',  // Company/Account Code
        'CRNN34',  // Item Type / Classification
        'CRCC52',  // Item Code
        'CRNN35',  // Item Name (e.g. description)
        'CRNN36',  // Transaction Type / Detail
        'CRNN37',  // Remarks / Notes
        'CRNN38',  // Amount
        'CRCC55',  // Reference Code / Link Code
    ];
}
