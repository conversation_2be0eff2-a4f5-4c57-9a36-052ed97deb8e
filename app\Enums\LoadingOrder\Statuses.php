<?php

declare(strict_types=1);

namespace App\Enums\LoadingOrder;

enum Statuses: string
{
    case CONFIRMING = '0';          // 確認中
    case ACCEPTED = '1';            // 受付済
    case COMPLETED = '2';           // 下見完了
    case ON_HOLD = '3';             // 保留中
    case CANNOT_INSPECT = '8';      // 下見できず
    case CANCELLED = '9';           // キャンセル

    /**
     * Get status name by code
     */
    public static function getStatusName(string $code): string
    {
        $status = self::tryFrom($code);
        return $status?->getJapaneseName() ?? $code;
    }

    /**
     * Get all statuses as array for dropdown/select options
     */
    public static function getAllStatuses(): array
    {
        $statuses = [];
        foreach (self::cases() as $status) {
            $statuses[$status->value] = $status->getJapaneseName();
        }
        return $statuses;
    }

    /**
     * Get the Japanese display name for this status
     */
    public function getJapaneseName(): string
    {
        return match($this) {
            self::CONFIRMING => '確認中',
            self::ACCEPTED => '受付済',
            self::COMPLETED => '下見完了',
            self::ON_HOLD => '保留中',
            self::CANNOT_INSPECT => '下見できず',
            self::CANCELLED => 'キャンセル',
        };
    }
}
