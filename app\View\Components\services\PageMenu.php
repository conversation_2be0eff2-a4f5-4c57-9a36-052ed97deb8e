<?php

declare(strict_types=1);

namespace App\View\Components\services;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class PageMenu extends Component
{
    /**
     * Create a new component instance.
     */
    public function __construct(public ?string $content, public ?string $href, public ?string $classColor)
    {

    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.services.page-menu');
    }
}
