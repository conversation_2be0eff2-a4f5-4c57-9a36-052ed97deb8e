<?php

declare(strict_types=1);

namespace App\Http\Requests\Transport;

use Illuminate\Foundation\Http\FormRequest;

class GetListBulkEditTransportRequest extends FormRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 */
	public function authorize(): bool
	{
		return true;
	}
	protected function prepareForValidation()
    {
        $idList = $this->input('id_list');
		if ($idList === null || trim($idList) === '') {
			return;
		}
		$items = explode(',', $idList);

		$items = array_filter(array_map(function ($item) {
			$item = trim($item);
			return is_numeric($item) ? (int) $item : null;
		}, $items));

		$this->merge([
			'id_list' => array_values($items),
		]);
    }

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
	 */
	public function rules(): array
	{
		return [
			'id_list' => ['sometimes', 'array'],
			'id_list.*' => ['integer', 'min:1'],
			'limit' => ['sometimes', 'integer', 'min:1', 'max:100'],
			'page' => ['sometimes', 'integer', 'min:1'],
		];
	}
} 