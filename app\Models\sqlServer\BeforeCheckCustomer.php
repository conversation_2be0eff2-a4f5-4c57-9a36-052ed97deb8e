<?php

declare(strict_types=1);

namespace App\Models\sqlServer;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * BeforeCheckCustomer Model
 *
 * Handles list of customers requesting inspection
 * Maps to database table: t_before_check_cust
 */
class BeforeCheckCustomer extends Model
{
    public $timestamps = false;
    protected $table = 't_before_check_cust';
    protected $guarded = [];

    protected $casts = [
        'del_flg' => 'boolean',
    ];

    /**
     * Get the customer associated with this inspection request
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'm_customer_id');
    }
}
