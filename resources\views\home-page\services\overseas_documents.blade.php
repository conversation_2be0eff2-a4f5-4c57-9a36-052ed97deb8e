@extends('app')

@section('title', __('home-page/overseas_documents.page_title'))

@section('content')
    <x-services.container>
        <x-services.title-page
            titlePage="{!! __('home-page/overseas_documents.title') !!}"
            description1="{!! __('home-page/overseas_documents.page_description1') !!}"
            description2="{{ __('home-page/overseas_documents.page_description2') }}"
        />
        <x-services.title subTitle="{{ __('home-page/overseas_documents.service_descriptions') }}" />
        <x-services.good-content :content="__('home-page/overseas_documents.good_content')" />
        <div class="mx-auto mt-[45px] w-full px-4">
            <x-services.how-to-use
                :content1="__('home-page/overseas_documents.how_to_use_content_1')"
                :content2="__('home-page/overseas_documents.how_to_use_content_2')"
                icon1="fas fa-phone"
                icon2="fas fa-file-alt"
            />
            <div class="my-4 w-full">
                <img src="{{ asset('images/services/overseas_map.png') }}" alt="" class="2sm:block hidden w-full" />
                <img src="{{ asset('images/services/overseas_map_02.png') }}" alt="" class="2sm:hidden block w-full" />
            </div>
        </div>
        <div class="mt-[45px] w-full">
            <div class="2sm:mt-4 2sm:mb-20 2sm:flex w-full justify-between md:mt-8 md:mb-24">
                <ul
                    class="2sm:py-[0.4em] 2md:mt-12 2md:mb-40 2md:w-1/2 flex w-full flex-col justify-between font-bold text-gray-900"
                >
                    @foreach (__('home-page/overseas_documents.how_to_use_list') as $item)
                        <li
                            class="2sm:my-[0.5em] 2sm:border 2sm:border-gray-50 2sm:border-l-40 2sm:border-l-cyan-100 2sm:rounded-[50%] 2sm:text-center 2sm:py-[1em] text-15 px-[1.5em] py-[0.5em] text-left md:text-xl"
                        >
                            <span class="2sm:bg-none bg-[linear-gradient(transparent_80%,_#d1eef6_0%)]">
                                {!! $item !!}
                            </span>
                        </li>
                    @endforeach
                </ul>
                <div class="2sm:w-2/5 2md:w-1/2 relative w-full">
                    <img
                        src="{{ asset('images/services/overseas_documents_01.jpg') }}"
                        alt=""
                        class="2sm:w-[70%] 2sm:absolute 2sm:bottom-[10%] 2sm:left-[8%] 2sm:m-0 2md:w-1/2 mr-2 ml-auto block w-[35%] shadow-cyan-100"
                    />
                    <img
                        src="{{ asset('images/services/overseas_documents_02.jpg') }}"
                        alt=""
                        class="2md:w-[30%] 2sm:w-1/2 2sm:absolute 2sm:right-0 2sm:m-0 2sm:translate-x-0 2sm:translate-y-0 2sm:transform-none block w-[30%] translate-x-[70%] -translate-y-1/2 transform shadow-lime-200"
                    />
                </div>
            </div>
        </div>
    </x-services.container>
@endsection
