body {
    overflow-x: hidden;
    background-color: #f8f8fa;
    font-family: "Segoe UI", <PERSON><PERSON>, sans-serif;
}

.schedule-result {
    transition: transform 0.5s ease, opacity 0.5s ease;
}

.sailing-schedule-result {
    max-height: 200px;
    border-radius: 8px;
    background-color: #edf3fb;
    padding: 16px 12px 0;
    break-inside: avoid;
    margin-bottom: 12px;
    transition: max-height 0.2s linear;
    cursor: pointer;
}

.sailing-schedule-result:not(.open) .sailing-schedule_ship-name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sailing-schedule-result {
    cursor: pointer;
}

.sailing-schedule-result {
    max-height: 200px;
    border-radius: 8px;
    background-color: #edf3fb;
    padding: 16px 12px 0;
    break-inside: avoid;
    margin-bottom: 12px;
    transition: max-height 0.2s linear;
}

.sailing-schedule-result_content {
    padding-left: 27px;
    position: relative;
}

.sailing-schedule-result_detail .sailing-schedule-result_content:not(:last-child) {
    padding-bottom: 22px;
}

.sailing-schedule-result_detail {
    margin-top: 8px;
    max-height: 85px;
    overflow: hidden;
    transition: max-height 0.2s linear;
}

.sailing-schedule-result_content:not(:last-child):after {
    content: "";
    width: 2px;
    height: 50%;
    background-color: #b7cae3;
    position: absolute;
    top: 26px;
    left: 6px;
}

@media screen and (min-width: 768px) {
    .sailing-schdule__details:not(:last-child) {
        padding: 16.5px 24px 8px;
    }

    .search-row {
        border-color: #eeee !important;
    }

    .sailing-schdule__details:nth-child(2) {
        padding-bottom: 28.5px !important;
    }

    .search-row {
        border-bottom: 1px solid #eeee;
    }

    .sailing-schdule__details:nth-child(2) {
        padding-bottom: 20px !important;
    }

    h1.main_title {
        font-size: 36px !important;
    }

    .sailing-schedule-result {
        max-height: 320px;
        height: 320px;
    }

    .sailing-schedule-result_detail {
        max-height: 205px;
    }
}

.sailing-schedule_ship-name {
    font-size: 18px;
    font-weight: 700;
    line-height: 23.94px;
    padding: 0 8px;
}

.sailing-schedule_name-company {
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: #4c545c;
    padding-left: 46px;
}

h5 {
    margin-bottom: 0;
}

.sailing-schedule-port-calendar {
    display: flex;
}

.sailing-schedule-port-calendar img {
    width: 24px;
    height: 24px;
    padding-right: 5px;
}

.sailing-schedule_name-port {
    font-size: 14px;
    font-weight: 600;
    line-height: 22px;
}

.sailing-schedule_date {
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: #4c545c;
}

.schedule-result {
    background-color: #c4d0de99;
    margin-bottom: 24px;
    padding: 12px;
    overflow-y: scroll;
    overflow-x: hidden;
    height: 500px;
    resize: vertical;
}

.btn-load {
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: #0075a9;
    padding: 8px 0 16px;
}

.custom-col {
    flex: 0 0 20%;
    max-width: 20%;
}

.custom-col:not(:last-child) {
    padding-right: 12px;
}

@media screen and (max-width: 575px) {
    .schedule-result {
        margin-bottom: 24px;
    }
}

@media screen and (min-width: 1200px) {
    .schedule-result {
        margin-top: 24px;
        height: calc(100vh - 490px);
    }
}

@media screen and (min-width: 1400px) {
    .container {
        max-width: 1200px;
    }
}

@media screen and (min-width: 1650px) {
    .schedule-result_details {
        padding: 0 12px;
    }

    .container {
        max-width: 1600px;
    }
}

.schedule-result:has(.sailing-schedule_note-empty) {
    overflow-y: unset;
}

.schedule-result:not(:has(.schedule-result_details #result-schedule-no-data)) {
    transform: scale(0.9);
    opacity: 0;
    transition: transform 0.5s ease, opacity 0.5s ease;
}

.input-value {
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    text-align: left;
    color: #212529;
    width: 100%;
    cursor: pointer;
    background: unset;
}

.dropdown-item,
.schedule_content-names,
.input-value,
.sailing-schedule-result:not(.open) .sailing-schedule_ship-name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

input:focus,
input {
    border: none;
    outline: none;
}

@media screen and (max-width: 991px) {
    .input-value,
    .input-date,
    .input-date::placeholder {
        font-size: 16px;
        line-height: 22px;
    }
}

.sailing-schedule-port-calendar img {
    width: 24px;
    height: 24px;
    padding-right: 5px;
}

#ui-datepicker-div {
    z-index: 10;
    background-color: #edf3fb;
}

.dropdown-item-active {
    background: #0075a9;
    color: white;
}

.schedule-result:has(.sailing-schedule_note-empty) {
    overflow-y: unset;
}

.sailing-schedule_note-empty {
    width: 100%;
    margin: 0 auto;
    padding: 250px 10px 0 10px;
}

.btn {
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}
