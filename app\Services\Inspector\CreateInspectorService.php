<?php

declare(strict_types=1);

namespace App\Services\Inspector;

use App\Repositories\sqlServer\InspectorRepository;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;

class CreateInspectorService
{
    private InspectorRepository $inspectorRepository;

    public function __construct(InspectorRepository $inspectorRepository)
    {
        $this->inspectorRepository = $inspectorRepository;
    }

    /**
     * @throws Exception
     */
    public function call(array $body): mixed
    {
        $prepareData = $this->prepareData($body);

        return $this->inspectorRepository->createOneWithRelations($prepareData);
    }

    private function prepareData(array $body): array
    {
        $now = Carbon::now();
        $user = Auth::user();
        $upOwner = $user && $user->id ? $user->id : 1;

        return [
            'reg_date' => $now->format('Y-m-d H:i:s'),
            'up_date' => $now->format('Y-m-d H:i:s'),
            'up_owner' => $upOwner,
            'name' => $body['name'],
            'name_kana' => $body['name_kana'],
            'tel' => $body['tel'] ?? '',
            'mail' => $body['mail'] ?? '',
            'del_flg' => false
        ];
    }
}
