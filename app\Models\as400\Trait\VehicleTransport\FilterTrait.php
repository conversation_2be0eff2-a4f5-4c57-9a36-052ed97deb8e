<?php

declare(strict_types=1);

namespace App\Models\as400\Trait\VehicleTransport;

use Illuminate\Database\Eloquent\Builder;

trait FilterTrait
{
    // filter start_date, end_date where col HNTD14
    public function scopeFilterStart(Builder $query, array $params): Builder
    {
        return $query->where('HNTD14', '>=', $params['from_date']);
    }

    // filter start_date, end_date where col HNTD19
    public function scopeFilterEnd(Builder $query, array $params): Builder
    {
        return $query
            ->where('HNTD19', '<=', $params['to_date']);
    }
}
