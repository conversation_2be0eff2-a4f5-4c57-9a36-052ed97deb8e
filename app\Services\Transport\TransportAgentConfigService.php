<?php

declare(strict_types=1);

namespace App\Services\Transport;

use App\Enums\Transport\TransIdDefault;
use App\Enums\Transport\UserIdSpecial;
use App\Repositories\sqlServer\TransAgentConfRepository;

class TransportAgentConfigService
{
    protected $transAgentId = TransIdDefault::SHIPPING_EAST_AND_WEST->value;
    public function __construct(
        private TransAgentConfRepository $transAgentConfRepository
    ) {
    }

    /**
     * Get transport agent configuration for AA place to To place transport
     * Migrated from ASP function getAaToTransConfig
     *
     * @param string $aaPlaceId AA place ID
     * @param string $toPlaceId To place ID
     * @param int $customerId Customer ID
     * @return array
     */
    public function getAaToTransConfig(string $aaPlaceId, string $toPlaceId, int $customerId): array
    {
        // Default is 東西海運 (Tozai Kaiun)
        $result = [
            'status' => 'OK',
            'message' => '',
            'trans_agent_id' => $this->transAgentId,
        ];

        // Get configuration from database
        $config = $this->transAgentConfRepository->getConfigByPlaceIds($aaPlaceId, $toPlaceId);

        if ($config) {
            $result['trans_agent_id'] = $config->agent_id;
        } else {
            $result['status'] = 'NG';
            $result['message'] = 'Failed to get transAgentId.';
        }

        // Special settings - override configuration based on customer
        $result['trans_agent_id'] = $this->applySpecialCustomerSettings($result['trans_agent_id'], $customerId);

        return $result;
    }

    /**
     * Apply special customer settings
     * Migrated from ASP special settings logic
     */
    private function applySpecialCustomerSettings(string $transAgentId, int $customerId): string
    {
        // Special setting: Max Future [2259] - always use Tozai Kaiun
        if (UserIdSpecial::TOZAI_KAIMUN->value === (string) $customerId) {
            return $this->transAgentId;
        }

        return $transAgentId;
    }
}
