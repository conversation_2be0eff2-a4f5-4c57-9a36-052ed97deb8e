<?php

declare(strict_types=1);

namespace App\Services\ToPlace;

use App\Http\Resources\ToPlace\ToPlaceDropdownResource;
use App\Repositories\sqlServer\ToPlaceRepositories;

class ToPlaceService
{
    private ToPlaceRepositories $toPlaceRepositories;

    public function __construct(ToPlaceRepositories $toPlaceRepositories)
    {
        $this->toPlaceRepositories = $toPlaceRepositories;
    }

    public function getToPlaceActive()
    {
        return $this->toPlaceRepositories->getToPlaceActive();
    }

    public function getDataDropdown(array $params)
    {
        $columns = ['id', 'name', 'addr', 'tel'];
        $toPlaces = $this->toPlaceRepositories->getList($params, [], $columns);

        return ToPlaceDropdownResource::collection($toPlaces);
    }
}
