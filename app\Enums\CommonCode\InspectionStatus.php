<?php

declare(strict_types=1);

namespace App\Enums\CommonCode;

/**
 * InspectionStatusResource Enum
 *
 * Defines inspection request status codes for cd_kbn = '0002'
 * Represents the core business workflow states for vehicle inspection requests
 * Used extensively across the inspection management system
 */
enum InspectionStatus: string
{
    case PENDING = '0';              // Confirmation in progress - Initial review state
    case APPROVED = '1';             // Request accepted - Ready for inspection scheduling
    case COMPLETED = '2';            // Inspection completed - Final inspection finished
    case ON_HOLD = '3';              // On hold - Temporarily suspended or paused
    case INSPECTION_IMPOSSIBLE = '8'; // Inspection impossible - Cannot perform inspection
    case CANCELLED = '9';            // Cancelled - Request terminated or aborted
    case ALL_FILTER = '999';         // All statuses - Used for search/filter dropdown only

    /**
     * Get the Japanese display name for this inspection status
     *
     * @return string Japanese status name
     */
    public function getJapaneseName(): string
    {
        return match($this) {
            self::PENDING => '確認中',                    // Confirming
            self::APPROVED => '受付済',                   // Accepted
            self::COMPLETED => '下見完了',                // Inspection Complete
            self::ON_HOLD => '保留中',                    // On Hold
            self::INSPECTION_IMPOSSIBLE => '下見できず',   // Inspection Impossible
            self::CANCELLED => 'キャンセル',              // Cancelled
            self::ALL_FILTER => '---',                   // All (filter option)
        };
    }

    /**
     * Get English display name for this inspection status
     * Used in English UI components and international reports
     *
     * @return string English status name
     */
    public function getEnglishName(): string
    {
        return match($this) {
            self::PENDING => 'Pending',                      // Awaiting confirmation
            self::APPROVED => 'Approved',                    // Request accepted
            self::COMPLETED => 'Inspection Completed',       // Inspection finished
            self::ON_HOLD => 'On Hold',                      // Temporarily paused
            self::INSPECTION_IMPOSSIBLE => 'Cannot Inspect', // Inspection not possible
            self::CANCELLED => 'Cancelled',                  // Request cancelled
            self::ALL_FILTER => 'All Statuses',             // Filter option for all
        };
    }
}
