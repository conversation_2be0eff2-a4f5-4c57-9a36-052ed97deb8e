@php
    $isEnglish = app()->getLocale() === 'en';
@endphp


@once
    @push('styles')
        @vite(['resources/css/common/tab.css', 'resources/css/common/tab.css'])
    @endpush
@endonce

<div class="common-tab relative mx-auto max-w-6xl py-1 md:py-6">
    <div class="rocker-toggle absolute top-2 left-1/2 z-10 grid -translate-x-1/2 md:top-8">
        <label class="rocker">
            <input class="rocker-switch" type="checkbox" checked />
            <span class="switch-left">AUTOHUB</span>
            <span class="switch-right">
                <span class="{{ $isEnglish ? 'recruit_switch_right' : '' }}">
                    {{ __('home-page/recruit.application_requirements') }}
                </span>
            </span>
        </label>
    </div>
    <div class="tab-1">
        {{ $slot }}
    </div>
    <div class="tab-2 hidden">
        {{ $tab2 }}
    </div>
</div>

@once
    @push('scripts')
        <script>
            $('.common-tab .rocker-toggle').on('click', function (event) {
                event.stopPropagation()
                const tabWrapper = $(this).closest('.common-tab')
                const tab1 = $(tabWrapper).find('.tab-1')
                const tab2 = $(tabWrapper).find('.tab-2')
                const checkbox = $(tabWrapper).find('.rocker-switch')
                const isChecked = checkbox.prop('checked')

                $(tab2).css('display', isChecked ? 'none' : 'block')
                $(tab1).css('display', isChecked ? 'block' : 'none')
            })

            $('.common-tab .rocker-switch').on('change', function (event) {
                event.stopPropagation()
            })
        </script>
    @endpush
@endonce
