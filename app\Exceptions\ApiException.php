<?php

declare(strict_types=1);

namespace App\Exceptions;

use App\Constants\ApiCodes;
use Exception;

class ApiException extends Exception
{
    protected string $errorCode;
    protected int $httpStatus;
    protected array $details;
    protected string|null $messageCustom;
    /**
     * ApiException constructor.
     *
     * @param string | int $errorCode
     * @param int $httpStatus
     * @param array $details
     */
    public function __construct(string | int $errorCode, int $httpStatus = 422, array $details = [], string|null $messageCustom = null)
    {
        parent::__construct($errorCode, $httpStatus);
        $this->errorCode = $errorCode;
        $this->httpStatus = $httpStatus;
        $this->details = $details;
        $this->messageCustom = $messageCustom;
    }

    public function getErrorCode(): string
    {
        return $this->errorCode;
    }

    public function getDetails(): array
    {
        return $this->details;
    }

    public function getMessageCustom(): string|null
    {
        return $this->messageCustom;
    }

    /**
     * @param string | int $errorCode
     * @return string
     */
    private function getErrorMessage(string | int $errorCode): string
    {
        return ApiCodes::convertToReadable($errorCode);
    }
}
