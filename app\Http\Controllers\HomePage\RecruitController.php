<?php

declare(strict_types=1);

namespace App\Http\Controllers\HomePage;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Lang;
use Illuminate\View\View;

class RecruitController extends Controller
{
    public function index(): View
    {
        $bubbleWidths = [150, 90, 160, 170, 120, 165, 90, 150, 200, 150, 200, 90, 250, 120 , 150, 170, 120 , 170, 130, 100, 115, 150, 120, 150, 90];
        $steps = Lang::get('home-page/recruit.step');
        $sales = Lang::get('home-page/recruit.sales');
        $engineer = Lang::get('home-page/recruit.engineer');
        return view('home-page.recruit', compact('bubbleWidths', 'steps', 'sales', 'engineer'));
    }
}
