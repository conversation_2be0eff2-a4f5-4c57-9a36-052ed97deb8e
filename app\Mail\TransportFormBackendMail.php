<?php

declare(strict_types=1);

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use App\Traits\MailTrait;

class TransportFormBackendMail extends Mailable
{
    use MailTrait;
    use Queueable;
    use SerializesModels;

    private array $data;

    /**
     * Create a new message instance.
     */
    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function build(): self
    {
        $subject = config('mail.mail_subjects.transport-from-backend');

        return $this
            ->from($this->data['mail_from']['mail'], $this->data['mail_from']['name'])
            ->bcc($this->transformMail($this->data['mail_bcc']))
            ->cc($this->transformMail($this->data['mail_cc'] ?? ''))
            ->subject($subject)
            ->view('emails.ja.transport-from-backend')
            ->with([
                "id" => Arr::get($this->data, 'data_info.id'),
                "customer_name" => Arr::get($this->data, 'data_info.customer_name'),
                "fr_aa_place_id" => Arr::get($this->data, 'data_info.fr_aa_place_id'),
                "fr_name" => Arr::get($this->data, 'data_info.fr_name'),
                "fr_addr" => Arr::get($this->data, 'data_info.fr_addr'),
                "fr_tel" => Arr::get($this->data, 'data_info.fr_tel'),
                "auction_date" => Arr::get($this->data, 'data_info.auction_date'),
                "auc_mail" => Arr::get($this->data, 'data_info.auc_mail'),
                "to_name" => Arr::get($this->data, 'data_info.to_name'),
                "to_addr" => Arr::get($this->data, 'data_info.to_addr'),
                "to_tel" => Arr::get($this->data, 'data_info.to_tel'),
                "pos_no" => Arr::get($this->data, 'data_info.pos_no'),
                "aa_no" => Arr::get($this->data, 'data_info.aa_no'),
                "date_of_payment" => Arr::get($this->data, 'data_info.date_of_payment'),
                "date_of_time" => Arr::get($this->data, 'data_info.date_of_time'),
                "car_name" => Arr::get($this->data, 'data_info.car_name'),
                "car_no" => Arr::get($this->data, 'data_info.car_no'),
                "tickitems_mail" => Arr::get($this->data, 'data_info.tickitems_mail'),
                "plate_cut_flg" => Arr::get($this->data, 'data_info.plate_cut_flg'),
                "plate_no" => Arr::get($this->data, 'data_info.plate_no'),
                "plate_address_mail" => Arr::get($this->data, 'data_info.plate_address_mail'),
                "country" => Arr::get($this->data, 'data_info.country'),
                "port" => Arr::get($this->data, 'data_info.port'),
                "odr_date" => Arr::get($this->data, 'data_info.odr_date'),
                "note" => Arr::get($this->data, 'data_info.note'),
            ]);
    }
}
