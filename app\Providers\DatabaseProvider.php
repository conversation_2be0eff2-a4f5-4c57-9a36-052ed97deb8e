<?php

declare(strict_types=1);

namespace App\Providers;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\{BelongsTo, BelongsToMany, HasMany, HasOne, Relation};
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\ServiceProvider;
use InvalidArgumentException;

/**
 * Service provider for database-related functionality and query builder macros.
 *
 * This provider extends Laravel's functionality by adding custom join operations
 * for different types of Eloquent relationships (BelongsTo, HasOne, HasMany, BelongsToMany).
 * It provides a macro 'leftJoinCustom' that allows for easy left joining of related tables
 * while respecting the relationship definitions and soft deletes.
 */
class DatabaseProvider extends ServiceProvider
{
    /**
     * Join a relation based on its type.
     *
     * @param Builder $query
     * @param Relation $relation
     * @param string $tableName
     * @param array $alreadyJoined
     * @param bool $withTrashed
     * @return void
     * @throws InvalidArgumentException
     */
    public static function joinRelation(
        Builder  $query,
        Relation $relation,
        string   $tableName,
        array    $alreadyJoined,
        bool     $withTrashed
    ): void {
        switch (true) {
            case $relation instanceof BelongsTo:
                if (!in_array($tableName, $alreadyJoined, true)) {
                    self::joinBelongsTo($query, $relation, $tableName, $withTrashed);
                }
                break;

            case $relation instanceof HasOne:
                if (!in_array($tableName, $alreadyJoined, true)) {
                    self::joinHasOne($query, $relation, $tableName, $withTrashed);
                }
                break;

            case $relation instanceof HasMany:
                if (!in_array($tableName, $alreadyJoined, true)) {
                    self::joinHasMany($query, $relation, $tableName, $withTrashed);
                }
                break;

            case $relation instanceof BelongsToMany:
                $pivotTable = $relation->getTable();
                if (!in_array($pivotTable, $alreadyJoined, true)) {
                    self::joinBelongsToMany($query, $relation, $pivotTable, $tableName, $withTrashed);
                }
                break;

            default:
                throw new InvalidArgumentException('Unsupported relation: ' . get_class($relation));
        }
    }

    /**
     * Join for BelongsTo relation.
     *
     * @param Builder $query The query builder instance
     * @param BelongsTo $relation The belongsTo relationship instance
     * @param string $table The table name to join
     * @param bool $withTrashed Whether to include soft deleted records
     * @return void
     */
    public static function joinBelongsTo(
        Builder   $query,
        BelongsTo $relation,
        string    $table,
        bool      $withTrashed
    ): void {
        $query->leftJoin($table, function (JoinClause $join) use ($relation): void {
            $join->on(
                $relation->getQualifiedForeignKeyName(),
                '=',
                $relation->getQualifiedOwnerKeyName()
            );
        });
    }

    /**
     * Join for HasOne relation.
     *
     * @param Builder $query The query builder instance
     * @param HasOne $relation The hasOne relationship instance
     * @param string $table The table name to join
     * @param bool $withTrashed Whether to include soft deleted records
     * @return void
     */
    public static function joinHasOne(
        Builder $query,
        HasOne  $relation,
        string  $table,
        bool    $withTrashed
    ): void {
        $query->leftJoin($table, function (JoinClause $join) use ($relation): void {
            $join->on(
                $relation->getQualifiedForeignKeyName(),
                '=',
                $relation->getQualifiedParentKeyName()
            );
        });
    }

    /**
     * Join for HasMany relation.
     *
     * @param Builder $query The query builder instance
     * @param HasMany $relation The hasMany relationship instance
     * @param string $table The table name to join
     * @param bool $withTrashed Whether to include soft deleted records
     * @return void
     */
    public static function joinHasMany(
        Builder $query,
        HasMany $relation,
        string  $table,
        bool    $withTrashed
    ): void {
        $query->leftJoin($table, function (JoinClause $join) use ($relation): void {
            $join->on(
                $relation->getQualifiedForeignKeyName(),
                '=',
                $relation->getQualifiedParentKeyName()
            );
        });
    }

    /**
     * Join for BelongsToMany relation.
     *
     * @param Builder $query The query builder instance
     * @param BelongsToMany $relation The belongsToMany relationship instance
     * @param string $pivotTable The intermediate pivot table name
     * @param string $relatedTable The related table name to join
     * @param bool $withTrashed Whether to include soft deleted records
     * @return void
     */
    public static function joinBelongsToMany(
        Builder       $query,
        BelongsToMany $relation,
        string        $pivotTable,
        string        $relatedTable,
        bool          $withTrashed
    ): void {
        // Join pivot table
        $query->leftJoin($pivotTable, function (JoinClause $join) use ($relation): void {
            $join->on(
                $relation->getQualifiedParentKeyName(),
                '=',
                $relation->getQualifiedForeignPivotKeyName()
            );
        });

        // Join related table
        $query->leftJoin($relatedTable, function (JoinClause $join) use ($relation, $pivotTable, $relatedTable): void {
            $join->on(
                "{$pivotTable}.{$relation->getRelatedPivotKeyName()}",
                '=',
                "{$relatedTable}.{$relation->getRelatedKeyName()}"
            );
        });
    }
    /**
     * Bootstrap the database services and register custom macros.
     *
     * @return void
     */
    public function boot(): void
    {
        $this->registerLeftJoinCustomMacro();
    }

    /**
     * Registers the 'leftJoinCustom' macro on the Query Builder which enables
     * joining related tables through dot notation relationship paths.
     *
     * @return void
     */
    protected function registerLeftJoinCustomMacro(): void
    {
        Builder::macro('leftJoinCustom', function (string $relationPath, bool $withTrashed = false) {
            /** @var Builder $query */
            $query = $this;

            // Prepare SELECT DISTINCT and eager load
            $baseTable = $query->getModel()->getTable();
            $query->select("{$baseTable}.*")->with($relationPath);

            // Process nested relations segments
            $segments = explode('.', $relationPath);
            $model = $query->getModel();

            foreach ($segments as $segment) {
                if (!method_exists($model, $segment)) {
                    throw new InvalidArgumentException(
                        "Relation [{$segment}] does not exist on model " . get_class($model)
                    );
                }

                /** @var Relation $relation */
                $relation = $model->{$segment}();
                $tableName = $relation->getRelated()->getTable();

                // Check if the table is already joined
                $joins = $query->getQuery()->joins ?: [];
                $alreadyJoined = array_map(fn ($join) => $join->table, $joins);

                // Handle different relation types
                DatabaseProvider::joinRelation($query, $relation, $tableName, $alreadyJoined, $withTrashed);

                // Prepare model for next segment
                $model = $relation->getRelated();
            }

            return $query;
        });
    }
}
