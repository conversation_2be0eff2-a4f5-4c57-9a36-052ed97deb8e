@php
    $lang = app()->getLocale();
    $isJa = $lang === 'ja';
@endphp

<div class="js_floating-close 2sm:bottom-55 fixed right-[15px] bottom-[130px] z-98 md:bottom-65 xl:bottom-[310px]">
    <a
        href="{{ $isJa ? route('introduction') : route('localized.introduction', ['locale' => 'en']) }}"
        class="group relative"
        target="_blank"
    >
        <img
            src="{{ asset($isJa ? 'images/home/<USER>' : 'images/home/<USER>') }}"
            alt="HUBNET"
            class="2md:w-[250px] w-40 group-hover:hidden sm:w-50 md:w-[230px] lg:w-60"
        />
        <img
            src="{{ asset($isJa ? 'images/home/<USER>' : 'images/home/<USER>') }}"
            alt="HUBNET"
            class="2md:w-[250px] hidden w-40 group-hover:block sm:w-50 md:w-[230px] lg:w-60"
        />
    </a>
    <img
        class="add_line_close absolute top-[5px] right-[5px] z-100 hidden md:block"
        src="{{ asset('images/home/<USER>') }}"
        alt=""
    />
    <img
        class="add_line_close_768 absolute top-[5px] right-[5px] z-100 md:hidden"
        src="{{ asset('images/home/<USER>') }}"
        alt=""
    />
</div>
