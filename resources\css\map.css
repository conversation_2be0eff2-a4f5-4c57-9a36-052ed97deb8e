#map-pc {
    display: none;
}

#map {
    position: relative;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1019607843);
    border-radius: 8px;
    font-family: "Segoe UI", sans-serif;
    font-weight: 700;
    background-color: rgba(255, 255, 255, 0.5);
}

#map svg path {
    fill: #AAAAAA;
    transition: 0.3s;
}

#map .area-name {
    font-size: 10px;
    position: absolute;
    text-align: center;
}

#map .area-name button span {
    color: #fff;
    text-shadow: #2B2625 0 0 2px, #2B2625 0 0 2px, #2B2625 0 0 2px, #2B2625 0 0 2px, #2B2625 0 0 2px, #2B2625 0 0 2px;
    white-space: pre-wrap;
}

#map .area-name:hover {
    cursor: pointer;
    opacity: 1;
}

#map .area-name span {
    position: relative;
    opacity: 0.7;
    padding-bottom: 15px;
    padding-top: 10px;
    white-space: nowrap;
}

#map .area-name button:hover span {
    opacity: 1;
    text-shadow: -1px -1px 0 #2B2625, 0 -1px 0 #2B2625, 1px -1px 0 #2B2625, 1px 0 0 #2B2625, 1px 1px 0 #2B2625, 0 1px 0 #2B2625, -1px 1px 0 #2B2625, -1px 0 0 #2B2625;
}

#map .area-name button:hover span:before {
    background: white;
    border: 4px solid #DC3545;
}

#map .area-name span:before {
    content: "";
    width: 12px;
    height: 12px;
    bottom: 0;
    border-radius: 50%;
    position: absolute;
    background: #DC3545;
    border: 4px solid white;
    box-sizing: border-box;
    left: 50%;
    transform: translateX(-50%);
    opacity: 1;
}

#map .area-active span {
    opacity: 1 !important;
    text-shadow: -1px -1px 0 #2B2625, 0 -1px 0 #2B2625, 1px -1px 0 #2B2625, 1px 0 0 #2B2625, 1px 1px 0 #2B2625, 0 1px 0 #2B2625, -1px 1px 0 #2B2625, -1px 0 0 #2B2625 !important;
}

#map .area-active span:before {
    background: white;
    border: 4px solid #DC3545;
}

#map .canada button span {
    position: absolute;
    top: 15%;
    left: 17%;
}

#map .canada.area-active svg path {
    fill: #3268D1 !important;
}

#map .canada .area-name {
    position: absolute;
    top: 32%;
    right: 29%;
}

#map .canada .area-name:hover ~ svg path {
    fill: #3268D1;
}

#map .carib button span {
    position: absolute;
}

#map .usa button span {
    position: absolute;
    top: -16%;
    left: 18%;
}

#map .carib.area-active svg path {
    fill: #FAA919;
}

#map .carib .area-name {
    top: -20%;
    left: -37px;
}

#map .carib .area-name span {
    white-space: nowrap;
}

#map .carib .area-name:hover ~ svg path {
    fill: #FAA919;
}

#map .south-america.area-active svg path {
    fill: #E88BBA;
}

#map .south-america .area-name {
    top: 50%;
    left: 20%;
}

#map .south-america .area-name:hover ~ svg path {
    fill: #E88BBA;
}

#map .south-america .area-name span:before {
    top: -5px;
    left: 34%;
}

#map .other-area {
    position: absolute;
    top: 21px;
    left: 4%;
}

#map .middle-asia.area-active svg path {
    fill: #F86624;
}

#map .middle-asia span {
    white-space: nowrap;
}

#map .middle-asia .area-name {
    top: -36%;
    left: 17%;
    z-index: 3;
}

#map .middle-asia .area-name:hover ~ svg path {
    fill: #F86624;
}

#map .west-africa.area-active svg path {
    fill: #8D81C7;
}

#map .west-africa .area-name {
    white-space: nowrap;
    top: 78%;
    left: -30%;
    z-index: 3;
}

#map .west-africa .area-name:hover ~ svg path {
    fill: #8D81C7;
}

#map .west-africa .area-name span:before {
    top: -5px;
    left: 34%;
}

#map .east-africa.area-active svg path {
    fill: #FF34AE;
}

#map .east-africa .area-name {
    top: 76%;
    left: -27%;
}

#map .east-africa .area-name:hover ~ svg path {
    fill: #FF34AE;
}

#map .east-africa .area-name span:before {
    top: -5px;
    left: 34%;
}

#map .south-east-asia.area-active svg path {
    fill: #89BA28;
}

#map .south-east-asia .area-name:hover ~ svg path {
    fill: #89BA28;
}

#map .south-east-asia .area-name span:before {
    left: 34%;
}

#map .europe.area-active svg path {
    fill: #A0C261;
}

#map .europe .area-name {
    top: 47%;
    left: 26%;
}

#map .europe .area-name:hover ~ svg path {
    fill: #A0C261;
}

#map .australia button span {
    position: absolute;
    top: 17px;
}

#map .australia.area-active svg path {
    fill: #DC3545;
}

#map .australia .area-name {
    top: 15%;
}

#map .australia .area-name:hover ~ svg path {
    fill: #DC3545;
}

#map .new-zealand.area-active svg path {
    fill: #662E9B;
}

#map .new-zealand .area-name {
    left: -218%;
    top: 83%;
}

#map .new-zealand .area-name:hover ~ svg path {
    fill: #662E9B;
}

#map .australia .area-name span:before {
    top: -5px;
}

.sailing-schedule_map {
    margin-top: 24px;
}

#map-pc {
    display: none;
}

#map-sp-1, #map-sp-2 {
    width: 375px;
    position: relative;
}

.sailing-schedule_map-sp {
    position: relative;
    margin: 0 auto;
}

@media screen and (min-width: 1200px) {
    .sailing-schedule_map {
        margin-top: 0;
    }
}

@media (max-width: 767px) {
    .sailing-schedule_map-sp {
        width: 375px;
        margin: 0 auto;
        position: relative;
    }

    #map {
        text-align: center;
        padding: 10px;
        height: auto;
    }

    #map .europe {
        position: absolute;
        top: 5%;
        left: 5%;
    }

    #map .europe button {
        width: 112px;
        height: 91px;
    }

    #map .west-africa {
        position: absolute;
        top: 18%;
        left: -3%;
    }

    #map .west-africa button {
        width: 87px;
        height: 137px;
    }

    #map .middle-asia {
        position: absolute;
        top: 18%;
        left: 20%;
    }

    #map .middle-asia button {
        width: 71px;
        height: 45px;
    }

    #map .east-africa {
        position: absolute;
        top: 25%;
        left: 20%;
    }

    #map .east-africa button {
        width: 71px;
        height: 87px;
    }

    #map .south-asia {
        position: absolute;
        top: 18%;
        left: 39%;
    }

    #map .south-asia button {
        width: 50px;
        height: 60px;
    }

    #map .east-asia {
        position: absolute;
        top: 16%;
        left: 53%;
    }

    #map .east-asia button {
        width: 67px;
        height: 43px;
    }

    #map .south-east-asia {
        position: absolute;
        top: calc(22% + 3px);
        left: 53%;
    }

    #map .south-east-asia button {
        width: 64px;
        height: 58px;
    }

    #map .rusian {
        position: absolute;
        top: 8%;
        left: 57%;
    }

    #map .rusian button {
        width: 88px;
        height: 48px;
    }

    #map .australia {
        position: absolute;
        top: 30%;
        left: 60%;
    }

    #map .australia button {
        width: 85px;
        height: 67px;
    }

    #map .papua-new-guinea {
        position: absolute;
        top: 25%;
        left: 71%;
    }

    #map .papua-new-guinea button {
        width: 96px;
        height: 37px;
    }

    #map .new-zealand {
        position: absolute;
        top: 34%;
        right: 16%;
    }

    #map .new-zealand button {
        width: 56px;
        height: 45px;
    }

    #map .new-zealand button span {
        position: absolute;
        top: -3px;
        left: 19px;
    }

    #map .micronesa {
        position: absolute;
        top: 19%;
        left: 71%;
    }

    #map .micronesa button {
        width: 144px;
        height: 43px;
    }

    #map .micronesa button span {
        position: absolute;
        top: -11%;
        left: 25%;
    }

    #map .hawaii {
        position: absolute;
        top: 68%;
        left: -6%;
    }

    #map .hawaii button {
        width: 98px;
        height: 57px;
    }

    #map .pacific {
        position: absolute;
        top: 78%;
        left: -10%;
    }

    #map .pacific button {
        width: 146px;
        height: 80px;
    }

    #map .canada {
        position: absolute;
        top: 50%;
        left: 94%;
    }

    #map .canada button {
        width: 280px;
        height: 74px;
    }

    #map .usa {
        position: absolute;
        top: 60%;
        left: 27%;
    }

    #map .usa button {
        width: 251px;
        height: 64px;
    }

    #map .usa button span {
        top: 1%;
    }

    #map .carib {
        position: absolute;
        top: 69%;
        left: 42%;
    }

    #map .carib button {
        width: 232px;
        height: 74px;
    }

    #map .carib button span {
        top: -15%;
    }

    #map .south-america {
        position: absolute;
        bottom: 20%;
        left: 40%;
    }

    #map .south-america button {
        width: 202px;
        height: 146px;
    }

    #map.map-ja .new-zealand button span {
        position: absolute;
        top: -20px;
        left: 2px;
    }

    #map.map-ja .papua-new-guinea button span {
        position: absolute;
        top: -38%;
        left: -27%;
    }

    #map.map-ja .south-east-asia button span {
        position: absolute;
        bottom: 51%;
        left: -6%;
    }

    #map .middle-asia button span {
        position: absolute;
        top: -53%;
        left: 11%;
    }
}

@media screen and (min-width: 768px) {
    .sailing-schedule_map-sp {
        width: 635px;
        height: 300px;
    }

    #map .carib button span {
        top: -41%;
    }

    #map .middle-asia {
        position: absolute;
        top: calc(41% + 8.5px);
        left: calc(15% - 5px);
    }

    #map .middle-asia button {
        width: 68px;
        height: 44px;
    }

    #map .usa {
        position: absolute;
        top: 35%;
        right: calc(33% - 4px);
    }

    #map .middle-asia button span {
        position: absolute;
        bottom: 19%;
        right: 10%;
    }

    #map .usa button {
        width: 167px;
        height: 44px;
    }

    #map-pc {
        display: block;
        scale: 1.28;
        padding: 30px 0 0 70px;
    }

    #map-sp-1, #map-sp-2 {
        display: none;
    }

    #map .west-africa {
        position: absolute;
        top: calc(50% - 18px);
        left: 1%;
    }

    #map .east-asia {
        position: absolute;
        top: 39%;
        left: 31%;
    }

    #map .east-asia button {
        width: 60px;
        height: 42px;
    }

    #map .south-america {
        position: absolute;
        right: 26%;
        top: 67%;
    }

    #map .south-america button {
        width: 129px;
        height: 98px;
    }

    #map .canada {
        position: absolute;
        right: 6%;
        top: 14%;
    }

    #map .canada button {
        width: 199px;
        height: 64px;
    }

    #map .west-africa button {
        width: 83px;
        height: 123px;
    }

    #map .carib {
        position: absolute;
        right: 24%;
        top: 50%;
    }

    #map .carib button {
        width: 149px;
        height: 49px;
    }

    #map .papua-new-guinea {
        position: absolute;
        top: 59%;
        left: calc(39% + 5px);
    }

    #map .papua-new-guinea button {
        width: 62px;
        height: 35px;
    }

    #map .micronesa {
        position: absolute;
        top: 47%;
        left: calc(41% - 3px);
    }

    #map .micronesa button {
        height: 36px;
        width: 91px;
    }

    #map .australia {
        position: absolute;
        top: calc(66% + 20px);
        left: calc(37% - 14.5px);
    }

    #map .australia button {
        width: 82px;
        height: 59px;
    }

    #map .europe {
        position: absolute;
        top: calc(10% + 18px);
        left: calc(3% + 14px);
    }

    #map .europe button {
        width: 103px;
        height: 84px;
    }

    #map .pacific {
        position: absolute;
        top: 59%;
        left: calc(50% - 4px);
    }

    #map .pacific button {
        width: 103px;
        height: 63px;
    }

    #map .hawaii {
        position: absolute;
        top: 41%;
        left: 55%;
    }

    #map .rusian {
        position: absolute;
        top: 20%;
        left: 32%;
    }

    #map .rusian button {
        width: 85px;
        height: 53px;
    }

    #map .hawaii button {
        width: 67px;
        height: 49px;
    }

    #map .new-zealand {
        position: absolute;
        top: 80%;
        left: 48%;
    }

    #map .new-zealand button {
        width: 60px;
        height: 58px;
    }

    #map .new-zealand button span {
        position: absolute;
        top: 50%;
        left: 25%;
    }

    #map .south-asia {
        position: absolute;
        top: 44%;
        left: 25%;
    }

    #map.map-ja .new-zealand {
        top: 76%;
        left: 46%;
    }

    #map.map-ja .new-zealand .area-name span:before {
        top: 0;
    }

    #map .south-asia button {
        height: 56px;
        width: 39px;
    }

    #map .south-east-asia {
        position: absolute;
        top: calc(49% + 14px);
        left: calc(32% - 5.5px);
    }

    #map .south-east-asia button {
        width: 55px;
        height: 52px;
    }

    #map .east-africa {
        position: absolute;
        top: calc(57% + 5.5px);
        left: calc(14% + 2px);
    }

    #map .east-africa button {
        width: 68px;
        height: 77px;
    }

    #map .new-zealand .area-name span:before {
        top: -5px;
    }

    #map.map-ja .australia button span {
        position: absolute;
        top: 25px;
        left: 10px;
    }

    #map .papua-new-guinea button span {
        position: absolute;
        top: -69%;
        left: -22%;
    }

    #map .micronesa button span {
        position: absolute;
        top: -55%;
        left: 21%;
    }

    #map .hawaii button span {
        position: absolute;
        bottom: 10%;
        left: 10%;
    }
}

@media screen and (max-width: 480px) {
    #map.map-ja .new-zealand button span {
        top: -26px;
        left: -13px;
    }

    .sailing-schedule_map-sp {
        width: 260px;
    }

    #map-sp-1, #map-sp-2 {
        width: 260px;
    }

    #map .europe button {
        height: 64px;
    }

    #map .west-africa button {
        width: 63px;
    }

    #map .middle-asia button {
        width: 56px;
        height: 33px;
    }

    #map .east-africa button {
        width: 55px;
        height: 87px;
    }

    #map .rusian button {
        width: 92px;
        height: 42px;
    }

    #map .rusian button span {
        position: absolute;
        top: -60%;
        left: 15%;
    }

    #map .south-asia button {
        width: 39px;
        height: 60px;
    }

    #map .east-asia button {
        width: 46px;
        height: 34px;
    }

    #map .east-asia button span {
        position: absolute;
        top: -95%;
        left: 9%;
    }

    #map .south-east-asia button {
        width: 40px;
        height: 58px;
    }

    #map .micronesa button {
        width: 82px;
        height: 24px;
    }

    #map .micronesa button span {
        top: -74%;
    }

    #map .papua-new-guinea button {
        width: 80px;
        height: 24px;
    }

    #map .australia button {
        width: 49px;
        height: 40px;
    }

    #map .australia {
        position: absolute;
        top: 30%;
        left: 63%;
    }

    #map .canada button {
        width: 202px;
        height: 51px;
    }

    #map .hawaii {
        top: 68%;
        left: -1%;
    }

    #map .hawaii button {
        width: 74px;
        height: 44px;
    }

    #map .pacific {
        position: absolute;
        top: 78%;
        left: -2%;
    }

    #map .pacific button {
        width: 114px;
        height: 51px;
    }

    #map .usa button {
        width: 182px;
        height: 46px;
    }

    #map .carib button {
        width: 180px;
        height: 53px;
    }

    #map .south-america button {
        width: 155px;
        height: 101px;
    }

    #map .papua-new-guinea button span {
        position: absolute;
        bottom: 10%;
        left: -39%;
    }

    #map .south-east-asia button span {
        position: absolute;
        top: 20px;
        left: 2px;
    }

    #map .south-east-asia span:before {
        top: -4px;
    }

    #map .middle-asia span:before {
        top: -1px;
    }

    #map .middle-asia button span {
        top: 31%;
        left: -8%;
    }
}

@media screen and (max-width: 1199px) {
    #map {
        margin: 0 auto;
    }

    .sailing-schedule_map {
        margin: 24px auto;
    }
}
