<?php

declare(strict_types=1);

namespace App\Modules\SailingSchedule\Services;

use App\Http\Resources\Country\CountryResource;
use App\Http\Resources\Country\PortResource;
use App\Http\Resources\RouteSchedule\RouteScheduleResource;
use App\Modules\SailingSchedule\Constants\AreaPortJapan;
use App\Modules\SailingSchedule\Constants\PortAreaOrder;
use App\Modules\SailingSchedule\Repositories\AccountItemRepository;
use App\Modules\SailingSchedule\Repositories\RouteScheduleRepository;
use App\Modules\SailingSchedule\Repositories\VesselScheduleRepository;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class SailingScheduleService
{
    private AccountItemRepository $accountItemRepository;
    private RouteScheduleRepository $routeScheduleRepository;
    private VesselScheduleRepository $vesselScheduleRepository;

    public function __construct(AccountItemRepository $accountItemRepository, RouteScheduleRepository $routeScheduleRepository, VesselScheduleRepository $vesselScheduleRepository)
    {
        $this->accountItemRepository = $accountItemRepository;
        $this->routeScheduleRepository = $routeScheduleRepository;
        $this->vesselScheduleRepository = $vesselScheduleRepository;
    }

    public function getCountries()
    {
        $countries = $this->accountItemRepository->getCountry();

        return CountryResource::collection($countries);
    }

    /**
     * @return AnonymousResourceCollection
     */
    public function getRouteSchedule(): AnonymousResourceCollection
    {
        $routeSchedule = $this->routeScheduleRepository->getRouteSchedule();
        $filterRoute = $this->filterRoutes($routeSchedule);

        return RouteScheduleResource::collection($filterRoute);
    }

    public function getPort()
    {
        $locale = app()->getLocale();
        $nameArea = 'ja' === $locale ? AreaPortJapan::AREA_MAP_JA : AreaPortJapan::AREA_MAP_EN;

        $data = $this->accountItemRepository->getPort();
        $portsFilter =  $this->getFilteredPorts(PortResource::toArrayFrom($data));
        $ports = $this->processPortNameWhenDuplicate($portsFilter);

        $japanese_ports = $this->getPortJA($nameArea, $ports, $locale);

        return [
            'ports' => $this->mapKey($ports),
            'japanese_ports' => $this->mapKey($japanese_ports)
        ];
    }

    public function getSchedule(array $params)
    {
        $queryData = $this->accountItemRepository->getPort();
        $ports =  $this->getFilteredPorts(PortResource::toArrayFrom($queryData));
        $portMap  =  $this->mapKey($ports);

        $data = $this->vesselScheduleRepository->getSchedules($params, $portMap);

        return $data;
    }

    private function getPortJA(array $nameArea, array $ports, string $lan): mixed
    {
        $ports = array_values(array_filter($ports, fn ($port) => PortAreaOrder::JAPAN_CODE === $port['country_code']));

        foreach ($ports as $key => $port) {
            $codeKey = (string)(int)$port['code'];
            if (isset($nameArea[$codeKey])) {
                $ports[$key]['area'] = $nameArea[$codeKey];
            }
        }

        uasort($ports, function ($a, $b) use ($lan) {
            $order = 'ja' === $lan ? PortAreaOrder::ORDER_JA : PortAreaOrder::ORDER_EN;

            $posA = array_search($a['area'] ?? '', $order);
            $posB = array_search($b['area'] ?? '', $order);

            if ($posA === $posB) {
                return strcmp($a['area'] ?? '', $b['area'] ?? '');
            }

            return (false !== $posA ? $posA : PHP_INT_MAX) - (false !== $posB ? $posB : PHP_INT_MAX);
        });

        return array_values($ports);
    }

    private function getFilteredPorts(array $ports): array
    {
        $portFiltered = array_filter($ports, fn ($port) => (
            !preg_match('/^\*/', $port['name'])
                && !in_array($port['code'], AreaPortJapan::HIDDEN_PORT_IDS)
        ), ARRAY_FILTER_USE_BOTH);

        return array_values($portFiltered);
    }

    private function mapKey(array $ports): array
    {
        return array_reduce($ports, function ($carry, $port) {
            if (isset($port['code'])) {
                $carry[(string)(int)$port['code']] = $port;
            }
            return $carry;
        }, []);
    }

    private function filterRoutes(array $routes): array
    {
        $filterData  = array_filter($routes, fn ($route) => (
            !preg_match('/^\*/', $route->route_name)
        ));

        return array_values($filterData);
    }

    private function processPortNameWhenDuplicate(array $ports): array
    {
        $names = [];
        foreach ($ports as $port) {
            $names[$port['name']] = ($names[$port['name']] ?? 0) + 1;
        }

        foreach ($ports as $key => $port) {
            if ($names[$port['name']] > 1) {
                $ports[$key]['name'] = trim($port['name']) . " (" . trim($port['country_name']) . ")";
            }
        }

        return $ports;
    }
}
