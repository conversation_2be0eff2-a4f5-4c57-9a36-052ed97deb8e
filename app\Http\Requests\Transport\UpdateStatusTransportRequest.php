<?php

declare(strict_types=1);

namespace App\Http\Requests\Transport;

use App\Enums\UserRole;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UpdateStatusTransportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules()
    {
        return [
            'status' => 'required',
            'id_list' => [
                'required',
                'array',
                Rule::exists('t_transport', 'id')->when(Auth::user()->hn_cd == UserRole::admin->value, function ($query): void {
                    $query->whereIn('id', $this->input('id_list', []));
                })->when(Auth::user()->hn_cd != UserRole::admin->value, function ($query): void {
                    $query->whereIn('tp_id', $this->input('id_list', []));
                }),
            ],
        ];
    }
}
