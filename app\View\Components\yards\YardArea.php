<?php

declare(strict_types=1);

namespace App\View\Components\yards;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class YardArea extends Component
{
    /**
     * Create a new component instance.
     */
    public function __construct(public ?string $id, public ?string $title, public ?string $idFirstArea)
    {

    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.yards.yard-area');
    }
}
