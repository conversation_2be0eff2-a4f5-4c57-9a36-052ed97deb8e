@php
    /**
     * @var string $description
     * @var string $iconRight
     * @var string $titleRight
     * @var string $contentRight
     * @var string $linkRight
     */

    $urlOld = env('APP_URL_OLD', '');
    $lang = app()->getLocale();
@endphp

<div class="relative mx-auto mt-4 w-full px-4">
    <div class="border-b-2 border-dotted border-gray-100 text-center text-gray-500">
        <p class="text-13 mt-[7em] mb-[0.2em] flex items-center justify-center md:text-base">
            <i class="far fa-hand-point-down mx-[1em]"></i>
            {{ $description }}
            <i class="far fa-hand-point-down mx-[1em]"></i>
        </p>
    </div>
    <div class="mt-[10px] mb-[90px] flex flex-wrap justify-between md:mt-[30px] md:mb-[150px]">
        <div class="shadow-gray-150 mb-[10px] flex w-[48%] bg-red-500 text-center">
            <a
                href="{{ $urlOld }}/hn/?lan={{ $lang }}"
                target="_blank"
                class="relative flex w-full flex-col items-center justify-center"
            >
                <i class="fas fa-sign-in-alt text-40 mb-[0.1em] font-black text-white"></i>
                <p class="2sm:text-base text-xs leading-normal font-bold text-white">
                    {!! __('home-page/inland_transport.hubnet_login') !!}
                </p>
            </a>
        </div>
        <div class="shadow-gray-150 mb-[10px] w-[48%]">
            <a
                href="{{ $linkRight }}"
                class="relative flex w-full flex-col items-center justify-center"
                target="_blank"
            >
                <div
                    class="absolute top-0 left-0 h-[30px] w-5"
                    style="
                        background: url('{{ asset('images/services/label_style08.png') }}') no-repeat;
                        background-size: 20px 30px;
                    "
                ></div>
                <img src="{{ asset('images/services/' . $iconRight) }}" alt="陸送発注" class="mt-[10px] w-[60px]" />
                <p class="2sm:text-base text-red-450 pb-[1em] text-center text-xs font-bold">
                    {!! $titleRight !!}
                    <span
                        class="2sm:text-xs text-10 block px-5 pt-[5px] text-center leading-[140%] font-normal text-gray-600"
                    >
                        {!! $contentRight !!}
                    </span>
                </p>
            </a>
        </div>
    </div>
</div>
