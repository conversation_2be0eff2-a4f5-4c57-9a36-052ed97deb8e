<?php

declare(strict_types=1);

return [
    'page_title' => 'Photo / Condition Check｜AUTOHUB Co.,Ltd.',
    'title' => 'Photo / Condition Check',
    'page_description1' => 'Photography',
    'page_description2' => 'Condition Check',
    'service_descriptions' => 'You can request each photos what you want so you could grasp the condition of the car.',
    'good_content' => ['Grasp car condition.', 'For helping your stock car sales.', 'Proof_insurance when using disaster insurance'],
    'how_to_use_content_1' => 'Online Order with HUBNET',
    'how_to_use_content_2' => 'Email or TEL / FAX to your Autohub staff.',
    'condition_check_sheet' => 'Condition check sheet',
    'plan_contents' => 'Plan contents',
    'plan_content_1' => 'Standard<br>(specified angle)',
    'plan_content_2' => 'Request<br>(full order)',
    'plan_content_3' => 'Premium<br>Condition Check',
    'plan_content_4' => 'Condition Check for<br>New Zealand Market',
    'about_15_shots' => 'About 15 shots',
    'about_15_shots_plus' => '：About 15 shots ＋ ',
    'about_30_shots' => 'About 30 shots',
    'check_sheet' => 'Check sheet',
    'report' => 'Report (accident history / rust)',
    'function_1' => 'You will be notified by email after the shooting is completed.<br><span class="text-sm mx-[1em] mt-[0.5em] 2sm:text-22 md:text-25 lg:text-28">For premium condition check, check sheet is also attached.</span>',
    'function_2' => 'You can see photos<br>1 minutes after<br>we take photos.',
    'hubnet_order' => 'If you are a HUBNET member, you can check here after login in.',
    'car_information' => 'Car & Shipping Information',
    'track_trace' => '(Track and Trace)',
];
