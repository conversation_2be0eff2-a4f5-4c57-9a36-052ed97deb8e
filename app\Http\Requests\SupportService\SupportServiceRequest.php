<?php

declare(strict_types=1);

namespace App\Http\Requests\SupportService;

use Illuminate\Foundation\Http\FormRequest;

class SupportServiceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'sort' => [
                'nullable',
                'string',
                'regex:/^([a-zA-Z0-9_]+\\|(asc|desc))(,\\s*[a-zA-Z0-9_]+\\|(asc|desc))*$/i',
            ]
        ];
    }
}
