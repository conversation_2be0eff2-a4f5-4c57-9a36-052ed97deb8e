<?php

declare(strict_types=1);

namespace App\Enums\LoadingOrder;

enum CsvHeaders: string
{
    case LOADING_ID = '船積ID';
    case ORDER_DATE = '申込日時';
    case STATUS = '状態';
    case AH_REF_NO = 'AH整理番号';
    case SALES_PERSON = '担当者';
    case CUSTOMER_ID = '会員ID';
    case COMPANY_NAME = '申込会社';
    case AGENT_REF_NO = 'AGENT整理番号';
    case CAR_YEAR = '年式';
    case CAR_NO = '車台番号';
    case CAR_NAME = '車名';
    case MILEAGE = '走行距離';
    case QUOTATION = '建値';
    case CURRENCY = '通貨';
    case FOB_PRICE = '車両(FOB)金額';
    case TO_NAME = '乙仲名';
    case DESTINATION = '仕向国';
    case PORT = '港';
    case ORDER_TYPE = '注文区分';
    case TO_PLAN_DATE = '搬入予定日';
    case CONSIGNEE_NAME = 'CONSIGNEE';
    case PHOTO_REQUEST = 'リクエスト写真のご要望';
    case NOTE = 'その他備考';
    case SPECIAL_NOTE = '特記事項';
    case EXPORT_CANCELLATION = '輸出抹消手続き';
    case ENGLISH_CANCELLATION = '英文抹消作成';
    case REGULAR_PHOTO = '写真撮影（規定アングル）';
    case REQUEST_PHOTO = 'リクエスト写真撮影';
    case PLATE_REMOVAL = 'プレート外し';
    case PLATE_NUMBER = 'プレート番号';
    case LIGHT_WORK = '軽作業';
    case CONDITION_CHECK = 'コンディションチェック';
    case AIRCON_GAS_REMOVAL = 'エアコンガス抜き';
    case EXPORT_INSPECTION = '輸出検査';
    case AH_MARINE_INSURANCE = 'AH包括海上保険';
    case REGULAR_MARINE_INSURANCE = '通常の海上保険';
    case OVERSEAS_DOCUMENT_SEND = '海外書類発送';

    /**
     * Get all headers as array
     */
    public static function getAllHeaders(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get header by case name
     */
    public static function getHeader(string $caseName): string
    {
        return constant("self::{$caseName}")->value;
    }
}
