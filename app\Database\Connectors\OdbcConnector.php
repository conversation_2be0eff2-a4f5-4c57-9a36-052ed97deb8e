<?php

declare(strict_types=1);

namespace App\Database\Connectors;

use Illuminate\Database\Connectors\Connector;
use Illuminate\Queue\Connectors\ConnectorInterface;

class OdbcConnector extends Connector implements ConnectorInterface
{
    public function connect(array $config)
    {
        $dsn = $config['dsn'] ?? '';
        $username = $config['username'] ?? null;
        $password = $config['password'] ?? null;
        $options = $this->getOptions($config);

        return $this->createConnection("odbc:" . $dsn, $config, $options);
    }
}
