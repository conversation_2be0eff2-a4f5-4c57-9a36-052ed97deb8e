<?php

declare(strict_types=1);

namespace App\Constants;

class JwtPayloadDTO
{
    private string $sub;
    private array $customClaims = [];

    //JWT ID (unique identifier for this token)
    private string $jwtId;
    private string $loginSessionId;

    /**
     * Create a new JWT payload
     *
     * @param string $sub
     * @param string $jwtId
     * @param array $customClaims
     */
    public function __construct(
        string $sub,
        string $loginSessionId,
        string $jwtId,
        array $customClaims = []
    ) {
        $this->sub = $sub;
        $this->loginSessionId = $loginSessionId;
        $this->jwtId = $jwtId;
        $this->customClaims = $customClaims;
    }

    /**
     * Convert the DTO to an array for JWT encoding
     *
     * @return array
     */
    public function toArray(): array
    {
        $payload = [
            'login_session_id' => $this->loginSessionId,
            'sub' => $this->sub,
            'jwt_id' => $this->jwtId,
        ];

        return array_merge($payload, $this->customClaims);
    }
}
