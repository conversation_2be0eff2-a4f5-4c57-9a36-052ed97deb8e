<?php

declare(strict_types=1);

namespace App\Providers;

use Illuminate\Database\Events\QueryExecuted;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;

class LogProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {


    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        if (config('app.debug') && app()->environment(['local', 'development'])) {
            DB::listen(function (QueryExecuted $query): void {
                $bindings = collect($query->bindings)->map(function ($binding) {
                    if (null === $binding) {
                        return 'NULL';
                    }
                    if (is_bool($binding)) {
                        return $binding ? 'true' : 'false';
                    }
                    if (is_numeric($binding)) {
                        return $binding;
                    }
                    return "'{$binding}'";

                })->toArray();

                $logMessage = "[{$query->time} ms] " . $query->sql . "\nBindings: " . json_encode($bindings);

                Log::channel('query')->debug($logMessage);
            });
        }
    }
}
