<?php

declare(strict_types=1);

use App\Exceptions\Helpers\ExceptionHandlerHelper;
use Illuminate\Database\QueryException;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Support\Facades\Log;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        api: __DIR__ . '/../routes/api.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(callback: function (Middleware $middleware): void {
        $middleware->alias([
            'auth.jwt' => App\Http\Middleware\JwtAuthenticationMiddleware::class,
            'permission.user' => App\Http\Middleware\PermissionUserMiddleware::class,
            'auth' => App\Http\Middleware\AuthenticateApiMiddleware::class,
        ]);

        $middleware->web(append: [
            App\Http\Middleware\SetLocale::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        $is_api_request = request()->is('api/*');
        if ($is_api_request) {
            $exceptions->renderable(fn (Throwable $e, $request) => ExceptionHandlerHelper::render($request, $e));
        }

        $exceptions->report(function (QueryException $exception): void {
            $bindings = collect($exception->getBindings())->map(function ($b) {
                if (null === $b) {
                    return 'NULL';
                }
                if (is_bool($b)) {
                    return $b ? 'true' : 'false';
                }
                if (is_numeric($b)) {
                    return $b;
                }
                return "'{$b}'";

            })->toArray();

            $logMessage = "Failed query: " . $exception->getSql() . "\nBindings: " . json_encode($bindings);

            Log::channel('query')->error($logMessage);
        });
    })
    ->withCommands()
    ->create();
