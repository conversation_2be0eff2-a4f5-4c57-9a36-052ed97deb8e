<?php

declare(strict_types=1);

namespace App\Services\Transport;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class ExportCsvTokenService
{
    public function generateCsvToken(): string
    {
        $nonce = Str::uuid()->toString();
        Cache::store('database')->put($nonce, [
            'created_at' => now()->toString(),
            'user_id' => Auth::user()->id,
        ]);

        return $nonce;
    }
}
