@extends('app')

@section('title', __('home-page/repair_easywork_inspection.page_title'))

@push('styles')
    @vite('resources/css/services.css')
@endpush

@section('content')
    <x-services.container>
        <x-services.title-page
            titlePage="{{ __('home-page/repair_easywork_inspection.title') }}"
            description1="{{ __('home-page/repair_easywork_inspection.page_description1') }}"
            description2="{{ __('home-page/repair_easywork_inspection.page_description2') }}"
        />
        <x-services.title subTitle="{{ __('home-page/repair_easywork_inspection.service_descriptions') }}" />
        <x-services.good-content :content="__('home-page/repair_easywork_inspection.good_content')" />
        <div class="mx-auto mt-[45px] w-full px-4">
            <x-services.how-to-use
                :content1="__('home-page/repair_easywork_inspection.how_to_use_content_1')"
                :content2="__('home-page/repair_easywork_inspection.how_to_use_content_2')"
                icon1="fas fa-globe"
                icon2="fas fa-fax"
            />
        </div>
        <div class="mx-auto mt-[45px] w-full">
            <div class="2md:mt-12 2md:mb-20 mt-4 mb-20 flex justify-between md:mt-8 md:mb-24">
                <ul class="2sm:py-[0.4em] 2md:w-1/2 flex w-full flex-col justify-between font-bold">
                    @foreach (__('home-page/repair_easywork_inspection.how_to_use_list') as $item)
                        <li
                            class="2sm:my-[0.5em] 2md:text-22 text-15 mb-[1em] border border-l-5 border-gray-50 border-l-cyan-100 p-[1em] text-gray-900 md:text-xl"
                        >
                            {{ $item }}
                        </li>
                    @endforeach
                </ul>
                <div class="2sm:w-3/5 2sm:block 2md:w-1/2 relative flex w-[190px] flex-col justify-between">
                    <img
                        src="{{ asset('images/services/syuri_01.jpg') }}"
                        alt=""
                        class="2sm:absolute 2sm:w-[45%] 2sm:left-[10%] 2sm:top-[3%] 2sm:my-0 2md:w-[35%] 2md:top-auto 2sm:shadow-lime-200 shadow-lime-200-min static mx-auto my-[0.2em] w-4/5"
                    />
                    <img
                        src="{{ asset('images/services/syuri_02.jpg') }}"
                        alt=""
                        class="2sm:absolute 2sm:w-2/5 2sm:right-0 2sm:top-1/4 2sm:my-0 2md:w-[38%] 2md:top-[15%] 2sm:shadow-cyan-100 shadow-cyan-100-min static mx-auto my-[0.2em] w-4/5"
                    />
                    <img
                        src="{{ asset('images/services/syuri_03.jpg') }}"
                        alt=""
                        class="2sm:absolute 2sm:w-3/5 2sm:left-[-5%] 2sm:top-[41%] 2sm:my-0 2md:w-1/2 2md:top-[45%] 2sm:shadow-lime-200 shadow-lime-200-min static mx-auto my-[0.2em] w-4/5"
                    />
                    <img
                        src="{{ asset('images/services/syuri_04.jpg') }}"
                        alt=""
                        class="2sm:absolute 2sm:w-1/2 2sm:right-0 2sm:bottom-10 2sm:my-0 2md:bottom-5 2md:w-[30%] 2sm:shadow-cyan-100 shadow-cyan-100-min static mx-auto my-[0.2em] w-4/5"
                    />
                </div>
            </div>
        </div>
        <div class="mx-auto mt-[45px] w-full">
            <x-services.work-box
                :title="__('home-page/repair_easywork_inspection.easy_work')"
                icon="pict_02.png"
                :background="'bg-[linear-gradient(120deg,_#c2e9fb_0%,_#d1eef6_100%)]'"
            >
                <div class="flex flex-wrap">
                    <ul class="2sm:list-disc 2sm:pl-10 2md:text-13 !m-0 w-full text-xs leading-normal text-slate-950">
                        @foreach (__('home-page/repair_easywork_inspection.easy_work_list') as $item)
                            <li
                                class="2sm:font-normal 2sm:border-b-0 2sm:list-item 2sm:text-slate-950 flex flex-col border-b border-dashed border-gray-100 py-[0.2em] font-bold text-gray-700"
                            >
                                <input type="checkbox" id="{{ $item['id'] }}" class="hidden" />
                                <label for="{{ $item['id'] }}" class="2sm:block flex cursor-pointer flex-wrap">
                                    {{ $item['title'] }}
                                    <span class="2sm:inline hidden font-normal">・・・</span>
                                    <i
                                        class="fas fa-angle-right 2sm:!hidden ml-auto block text-xl text-gray-500 transition duration-500"
                                    ></i>
                                    <span class="2sm:inline hidden w-full font-normal">
                                        {{ $item['desc'] }}
                                    </span>
                                </label>
                            </li>
                        @endforeach
                    </ul>
                </div>
            </x-services.work-box>
            <x-services.work-box
                :title="__('home-page/repair_easywork_inspection.repair')"
                icon="pict_03.png"
                :background="'bg-[linear-gradient(120deg,_#d4fc79_0%,_#e9f9a7_100%)]'"
            >
                <div class="2sm:flex-nowrap flex flex-wrap">
                    <ul class="2sm:list-disc 2sm:pl-[40px] !m-0 w-full md:w-auto">
                        @foreach (__('home-page/repair_easywork_inspection.repair_list_1') as $item)
                            <li
                                class="2sm:border-b-0 2sm:font-normal 2sm:text-slate-950 2md:text-13 border-b border-dashed border-gray-100 py-[0.2em] text-xs font-bold text-gray-700 md:py-0"
                            >
                                {{ $item }}
                            </li>
                        @endforeach
                    </ul>

                    <ul class="2sm:list-disc 2sm:pl-[40px] !m-0 w-full md:w-auto">
                        @foreach (__('home-page/repair_easywork_inspection.repair_list_2') as $item)
                            <li
                                class="2sm:border-b-0 2sm:font-normal 2sm:text-slate-950 2md:text-13 border-b border-dashed border-gray-100 py-[0.2em] text-xs font-bold text-gray-700 md:py-0"
                            >
                                {{ $item }}
                            </li>
                        @endforeach
                    </ul>
                </div>
            </x-services.work-box>
        </div>
    </x-services.container>
@endsection
