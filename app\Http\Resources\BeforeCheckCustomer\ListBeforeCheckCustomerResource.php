<?php

declare(strict_types=1);

namespace App\Http\Resources\BeforeCheckCustomer;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ListBeforeCheckCustomerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'm_customer_id' => $this->m_customer_id,
            'cus_Name_JP' => $this->cus_Name_JP,
            'cus_Name_EN' => $this->cus_Name_EN,
            'odr_person' => $this->odr_person,
            'odr_tel' => $this->odr_tel,
            'odr_mail' => $this->odr_mail
        ];
    }
}
