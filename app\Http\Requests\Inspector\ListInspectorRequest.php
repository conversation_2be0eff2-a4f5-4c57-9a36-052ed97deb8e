<?php

declare(strict_types=1);

namespace App\Http\Requests\Inspector;

use App\Traits\CommonTrait;
use App\Enums\SortType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ListInspectorRequest extends FormRequest
{
    use CommonTrait;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'srchName' => ['nullable','string'],
            'srchKana' => ['nullable','string'],
            'sortType' => ['nullable', 'string', Rule::in(SortType::getAllValues())],
            'page' => ['nullable', 'integer', 'min:1'],
            'limit' => ['nullable', 'integer', 'min:1'],
        ];
    }

    /**
     * Prepare the data for validation.
     * This method is called before validation rules are applied
     */
    protected function prepareForValidation(): void
    {
        $inputs = $this->all();
        
        // Define fields that need LIKE search (will be serialized for SQL Server LIKE)
        $likeFields = ['srchName', 'srchKana'];
        
        $sanitized = $this->serializeArray($inputs, $likeFields);
        $this->replace($sanitized);
    }
}
