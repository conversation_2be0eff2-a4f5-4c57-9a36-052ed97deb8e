<?php

declare(strict_types=1);

namespace App\Http\Requests\Transport;

use Illuminate\Foundation\Http\FormRequest;

class DownloadTransFileRefRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'customer_id' => 'required|integer',
            'transport_id' => 'required|integer',
            'fname' => 'required|string',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    // public function messages(): array
    // {
    //     return [
    //         'customer_id.required' => 'Customer ID is required',
    //         'customer_id.integer' => 'Customer ID must be a valid integer',
    //         'customer_id.min' => 'Customer ID must be greater than 0',
    //         'transport_id.required' => 'Transport ID is required',
    //         'transport_id.integer' => 'Transport ID must be a valid integer',
    //         'transport_id.min' => 'Transport ID must be greater than 0',
    //         'fname.required' => 'Filename is required',
    //         'fname.string' => 'Filename must be a valid string',
    //         'fname.max' => 'Filename cannot exceed 255 characters',
    //     ];
    // }
}
