@extends('app')

@section('title', __('home-page/sailing_schedule.page_title'))

@push('styles')
    <link href="https://fonts.googleapis.com/css2?family=M+PLUS+Rounded+1c:wght@900&display=swap" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" />
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css" />
    @vite(['resources/css/schedule.css', 'resources/css/map.css'])
@endpush

@php
    $locale = app()->getLocale();
@endphp

@section('content')
    <div
        class="content min-h-screen w-full bg-cover"
        style="background-image: url('{{ asset('images/schedule/schedule-bg01.jpg') }}')"
    >
        <div id="page-top"></div>
        <input type="hidden" id="lang" value="{{ $locale }}" />
        <input type="hidden" id="arrPort" value="{{ json_encode($ports) }}" />
        <div class="2md:h-20 h-[50px] w-full"></div>
        <div class="flex flex-wrap">
            <h1 class="!mb-0 w-full text-center text-2xl !leading-20 font-bold text-[#212529] md:text-4xl">
                SAILING SCHEDULE
            </h1>
            <div class="2sm:max-w-135 2md:max-w-240 container mx-auto w-full md:max-w-180 lg:max-w-285 lg:px-4">
                <div class="sailing-schedule 2sm:mx-0 mx-6 flex flex-wrap lg:flex-nowrap">
                    <x-schedule.schedule-search
                        :routes="$routes"
                        :portJa="$portJa"
                        :ports="$ports"
                        :countries="$countries"
                    />
                    <x-schedule.schedule-map />
                </div>
                @if (! empty($schedules))
                    <x-schedule.schedule-result :schedules="$schedules" />
                @else
                    <div id="modal-no-result" class="modal fixed inset-0 top-0 z-10000 flex items-center justify-center bg-black/50">
                        <div class="w-full max-w-lg rounded-xl bg-white p-6 text-center shadow-lg">
                            <p class="p-4 text-base text-slate-950">
                                {{ __('home-page/sailing_schedule.msg_not_data') }}
                            </p>
                            <div class="flex justify-center p-3">
                                <button
                                    class="btn text-gray-450 border-gray-450 hover:bg-gray-450 m-1 cursor-pointer rounded-sm border px-3 py-1.5 hover:text-white"
                                    onclick="this.closest('.modal.fixed').remove()"
                                >
                                    {{ __('home-page/sailing_schedule.close') }}
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="schedule-result 2sm:mx-0 mx-6">
                        <div class="schedule-result_details -mx-4 flex flex-wrap">
                            <div class="sailing-schedule_note-empty text-center">
                                {{ __('home-page/sailing_schedule.msg_note') }}
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection

@once
    @push('scripts')
        <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
        @vite('resources/js/schedule.js')
    @endpush
@endonce
