<?php

declare(strict_types=1);

namespace App\Http\Resources\LoadingOrder;

use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ListLoadingOrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     * @throws BindingResolutionException
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'st_cd' => $this->st_cd,
            'odr_kbn' => $this->odr_kbn,
            'customer' => $this->customer ? [
                'id' => $this->customer->id,
                'cus_Name_JP' => $this->customer->cus_Name_JP,
                'cus_Name_EN' => $this->customer->cus_Name_EN,
                'ah_sales_name' => $this->customer->ah_sales_name
            ] : null,
            'consignee_name' => $this->consignee_name,
            'to_name' => $this->to_name,
            'car_no' => $this->car_no,
            'note' => $this->note,
            'odr_date' => $this->odr_date,
            'destination' => $this->destination,
            'odr_sub_kbn' => $this->odr_sub_kbn,
            'consignee_flg' => $this->consignee_flg,
            'car_name' => $this->car_name,
            'optn1_flg' => $this->optn1_flg,
            'optn2_flg' => $this->optn2_flg,
        ];
    }
}
