<?php

declare(strict_types=1);

namespace App\Models\sqlServer;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * BeforeCheck Model
 *
 * Handles vehicle inspection registration information
 * Maps to database table: t_before_check
 */
class BeforeCheck extends Model
{
    public $timestamps = false;
    protected $table = 't_before_check';
    protected $guarded = [];

    protected $casts = [
        'del_flg' => 'boolean',
    ];

    /**
     * Get the customer associated with this inspection registration
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'm_customer_id');
    }

    /**
     * Get the auction date associated with this inspection
     */
    public function aaDate(): BelongsTo
    {
        return $this->belongsTo(AaDate::class, 't_aa_date_id');
    }

    /**
     * Get the inspector assigned to this inspection
     */
    public function inspector(): BelongsTo
    {
        return $this->belongsTo(Inspector::class, 't_inspector_id');
    }
}
