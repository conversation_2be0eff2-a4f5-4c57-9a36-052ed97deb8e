<?php

declare(strict_types=1);

namespace App\Repositories\sqlServer;

use App\Models\sqlServer\ToPlace;
use App\Repositories\BaseRepository;
use App\Repositories\common\BuilderWhereCondition;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;

class ToPlaceRepositories extends BaseRepository
{
    public function model(): string
    {
        return ToPlace::class;
    }

    public function getToPlaceActive()
    {
        $query = ($this->model)::query()->select('id', 'name')->where('del_flg', 0)->where('coop_flg', 1)->orderBy('sort_no', 'ASC');
        return $query->get();
    }

    public function paginate($params = null, array $relations = [], $columns = ['*']): LengthAwarePaginator
    {
        $query = ($this->model)::query();

        $query->with($relations);

        $params = $params ?: request()->toArray();
        $limit = Arr::pull($params, 'limit', 20);
        $page = Arr::pull($params, 'page');

        $query = BuilderWhereCondition::call($query, false, $params);
        $query = $this->addSoftDeleteCondition($query);

        $this->buildSortQuery($query, $params);

        return $query->paginate($limit, $columns, 'page', $page);
    }

    public function getList($params = null, array $relations = [], $columns = ['*']): Collection
    {
        $query = ($this->model)::query();

        $query->with($relations);

        $query = BuilderWhereCondition::call($query, false, $params);
        $query = $this->addSoftDeleteCondition($query);

        $this->buildSortQuery($query, $params);

        return $query->get(is_array($columns) ? $columns : func_get_args());
    }
}
