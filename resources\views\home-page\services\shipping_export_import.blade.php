@extends('app')

@section('title', __('home-page/shipping_export_import.page_title'))

@section('content')
    <x-services.container>
        <x-services.title-page
            titlePage="{{ __('home-page/shipping_export_import.title') }}"
            description1="{{ __('home-page/shipping_export_import.page_description1') }}"
            description2="{{ __('home-page/shipping_export_import.page_description2') }}"
        />

        <x-services.title subTitle="{{ __('home-page/shipping_export_import.service_descriptions') }}" />
        <x-services.good-content :content="__('home-page/shipping_export_import.good_content')" />
        <div class="mx-auto mt-[45px] w-full px-4">
            <x-services.how-to-use
                :content1="__('home-page/shipping_export_import.how_to_use_content_1')"
                :content2="__('home-page/shipping_export_import.how_to_use_content_2')"
                icon1="fas fa-globe"
                icon2="fas fa-fax"
            />
            <x-services.how-to-use-content page="shipping_export_import" />
            <x-services.title
                titlePage="{{ __('home-page/shipping_export_import.package_details') }}"
                subTitle="{{ __('home-page/shipping_export_import.package_details_content') }}"
            />
            <x-services.table-shipping />
            <x-services.important-notice
                :title="__('common.important_notice')"
                :contents="__('home-page/shipping_export_import.important_notice_list')"
            />
        </div>
        <div class="relative mx-auto mt-4 w-full px-4">
            <div class="border-b-2 border-dotted border-gray-100 text-center text-gray-500">
                <p class="text-13 mt-[7em] mb-[0.2em] flex items-center justify-center md:text-base">
                    <i class="far fa-hand-point-down mx-[1em]"></i>
                    {{ __('home-page/shipping_export_import.hubnet_order') }}
                    <i class="far fa-hand-point-down mx-[1em]"></i>
                </p>
            </div>
            <div class="mt-4 flex w-full flex-wrap">
                <div class="shadow-gray-150 mt-4 mb-8 w-full bg-red-500">
                    <a
                        href="{{ env('APP_URL_OLD', '') }}/hn/?lan={{ app()->getLocale() }}"
                        target="_blank"
                        class="block text-center text-xl leading-12 font-bold text-white"
                    >
                        {{ __('home-page/shipping_export_import.hubnet_login') }}
                    </a>
                </div>
                <div class="w-full">
                    <div class="mt-[10px] mb-[90px] flex flex-wrap justify-evenly">
                        @foreach ($plans as $plan)
                            <div class="2sm:w-[196px] shadow-gray-150 mb-[10px] w-[48%] bg-white">
                                <a
                                    href="{{ $plan['url'] }}"
                                    class="relative flex w-full flex-col items-center justify-center"
                                    target="_blank"
                                >
                                    <div
                                        class="absolute top-0 left-0 h-[30px] w-5"
                                        style="
                                            background: url('{{ asset('images/services/' . $plan['label']) }}')
                                                no-repeat;
                                            background-size: 20px 30px;
                                        "
                                    ></div>
                                    <img
                                        src="{{ asset('images/services/' . $plan['icon']) }}"
                                        alt="プランアイコン"
                                        class="mt-[10px] w-15"
                                    />
                                    <p class="2sm:text-base text-red-450 mb-4 text-center text-xs font-bold">
                                        {!! $plan['title'] !!}
                                    </p>
                                </a>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </x-services.container>
@endsection
