#!/usr/bin/env sh

# Get list file php staged
STAGED_PHP_FILES=$(git diff --cached --name-only --diff-filter=ACM)

# Check if there are any staged PHP files
if [ -z "$STAGED_PHP_FILES" ]; then
  exit 0
fi

# Run Pint on staged PHP files
echo "$STAGED_PHP_FILES" | xargs ./vendor/bin/pint --test

# Save result check format
PINT_RESULT=$?

# Check result
if [ $PINT_RESULT -ne 0 ]; then
  exit 1
fi

exit 0
