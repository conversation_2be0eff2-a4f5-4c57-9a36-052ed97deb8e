{"preset": "psr12", "rules": {"align_multiline_comment": true, "array_indentation": true, "array_syntax": true, "blank_line_after_namespace": true, "blank_line_after_opening_tag": true, "combine_consecutive_issets": true, "combine_consecutive_unsets": true, "concat_space": {"spacing": "one"}, "declare_parentheses": true, "declare_strict_types": true, "explicit_string_variable": true, "final_class": false, "fully_qualified_strict_types": true, "global_namespace_import": {"import_classes": true, "import_constants": true, "import_functions": true}, "is_null": true, "lambda_not_used_import": true, "logical_operators": true, "mb_str_functions": false, "method_chaining_indentation": true, "modernize_strpos": true, "new_with_braces": true, "no_empty_comment": true, "ordered_traits": true, "protected_to_private": true, "simplified_if_return": true, "strict_comparison": false, "ternary_to_null_coalescing": true, "use_arrow_functions": true, "void_return": true, "yoda_style": true, "array_push": true, "assign_null_coalescing_to_coalesce_equal": true, "explicit_indirect_variable": true, "set_type_to_cast": true, "phpdoc_to_return_type": true, "phpdoc_types": true, "modernize_types_casting": true, "no_superfluous_elseif": true, "no_useless_else": true, "nullable_type_declaration_for_default_null_value": true, "type_declaration_spaces": false, "ordered_imports": {"sort_algorithm": "alpha"}, "ordered_class_elements": {"order": ["use_trait", "case", "constant", "constant_public", "constant_protected", "constant_private", "property_public", "property_protected", "property_private", "construct", "destruct", "magic", "phpunit", "method_abstract", "method_public_static", "method_public", "method_protected_static", "method_protected", "method_private_static", "method_private"], "sort_algorithm": "none"}}}