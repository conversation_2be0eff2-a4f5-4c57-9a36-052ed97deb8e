<?php

declare(strict_types=1);

namespace App\Services\CommonCode;

use App\Repositories\sqlServer\CommonCodeRepository;
use Illuminate\Database\Eloquent\Collection;

class CommonCodeService
{
    private CommonCodeRepository $commonCodeRepository;

    public function __construct(CommonCodeRepository $commonCodeRepository)
    {
        $this->commonCodeRepository = $commonCodeRepository;
    }

    public function getInspectionStatuses(): Collection
    {
        return $this->commonCodeRepository->getInspectionStatuses();
    }

    public function getServicePlans(): Collection
    {
        return $this->commonCodeRepository->getServicePlans();
    }
}
