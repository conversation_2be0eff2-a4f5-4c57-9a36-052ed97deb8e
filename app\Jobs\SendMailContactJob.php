<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Mail\ContactMail;
use App\Traits\MailTrait;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Mail;

class SendMail<PERSON>ontactJob implements ShouldQueue
{
    use MailTrait;
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        private string $select_contact,
        private string $company_name,
        private string $name,
        private string $mail_address,
        private string $tel,
        private string $contact_message = '',
        private string $lang = 'ja'
    ) {

    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $select_contact_arr = [
            0 => "---",
            1 => __('home-page/contact.lang_contact_type02'),
            2 => __('home-page/contact.lang_contact_type03'),
            3 => __('home-page/contact.lang_contact_type04'),
            4 => __('home-page/contact.lang_contact_type05')
        ];

        $selectContact = Arr::get($select_contact_arr, $this->select_contact);
        if (!$selectContact) {
            throw new Exception('Invalid select contact');
        }

        if (!$this->checkMailValid($this->mail_address)) {
            // cancel mail sending with mail validate failed
            return;
        }

        Mail::to($this->mail_address)
            ->send(new ContactMail(
                $selectContact,
                $this->company_name,
                $this->name,
                $this->mail_address,
                $this->tel,
                $this->contact_message,
                $this->lang
            ));
    }
}
