<?php

declare(strict_types=1);

namespace App\Http\Controllers\HomePage;

use App\Http\Controllers\Controller;

class ServiceController extends Controller
{
    public function index()
    {
        $isJa = 'ja' === app()->getLocale();
        $localizedRoute = fn ($route) => $isJa ? route($route) : route("localized.{$route}", ['locale' => 'en']);
        $pageMenus = [
            ['color' => 'bg-[linear-gradient(-45deg,#fbb448,#fdca7e,#fbb448)] border-[#fdca7e] hover:border-[#f7b656] hover:bg-[linear-gradient(45deg,#fbb448,#fdca7e,#fbb448)]'],
            ['color' => 'border-[#bfdaa5] hover:border-[#b2d096] bg-[linear-gradient(-45deg,_#b4d496,_#bfdaa5,_#b4d496)] hover:bg-[linear-gradient(45deg,_#b4d496,_#bfdaa5,_#b4d496)]'],
            ['color' => 'border-[#add9fd] bg-[linear-gradient(-45deg,_#a1cbef,_#add9fd,_#a1cbef)] hover:border-[#99c8ef] hover:bg-[linear-gradient(45deg,_#a1cbef,_#add9fd,_#a1cbef)]'],
            ['color' => 'border-[#fdc2c2] bg-[linear-gradient(-45deg,_#f5a8a8,_#fdc2c2,_#f5a8a8)] hover:border-[#fdaaaa] hover:bg-[linear-gradient(45deg,_#f5a8a8,_#fdc2c2,_#f5a8a8)]'],
            ['color' => 'border-[#bcb3f9] bg-[linear-gradient(-45deg,_#a79dec,_#bcb3f9,_#a79dec)] hover:border-[#a8a0e8] hover:bg-[linear-gradient(45deg,_#a79dec,_#bcb3f9,_#a79dec)]'],
            ['color' => 'bg-[linear-gradient(-45deg,#fbb448,#fdca7e,#fbb448)] border-[#fdca7e] hover:border-[#f7b656] hover:bg-[linear-gradient(45deg,#fbb448,#fdca7e,#fbb448)]'],
            ['color' => 'border-[#bfdaa5] hover:border-[#b2d096] bg-[linear-gradient(-45deg,_#b4d496,_#bfdaa5,_#b4d496)] hover:bg-[linear-gradient(45deg,_#b4d496,_#bfdaa5,_#b4d496)]'],
            ['color' => 'border-[#add9fd] bg-[linear-gradient(-45deg,_#a1cbef,_#add9fd,_#a1cbef)] hover:border-[#99c8ef] hover:bg-[linear-gradient(45deg,_#a1cbef,_#add9fd,_#a1cbef)]'],
            ['color' => 'border-[#fdc2c2] bg-[linear-gradient(-45deg,_#f5a8a8,_#fdc2c2,_#f5a8a8)] hover:border-[#fdaaaa] hover:bg-[linear-gradient(45deg,_#f5a8a8,_#fdc2c2,_#f5a8a8)]'],
            ['color' => 'border-[#bcb3f9] bg-[linear-gradient(-45deg,_#a79dec,_#bcb3f9,_#a79dec)] hover:border-[#a8a0e8] hover:bg-[linear-gradient(45deg,_#a79dec,_#bcb3f9,_#a79dec)]'],
            ['color' => 'border-[#a1baf7] bg-[linear-gradient(-45deg,_#9aaedc,_#a1baf7,_#9aaedc)] hover:border-[#8fa5d8] hover:bg-[linear-gradient(45deg,_#9aaedc,_#a1baf7,_#9aaedc)]'],
        ];

        $services = [
            ['link' => $localizedRoute('service.shipping-export-import'), 'bgImage' => 'ah_service_image02.png', 'bgBack' => 'ah_service_image_bg06.png', 'bgContainer' => '#fdfaf1'],
            ['link' => $localizedRoute('service.d2d-package'), 'bgImage' => 'ah_service_image01.png', 'bgBack' => 'ah_service_image_bg02.png', 'bgContainer' => '#f7fdf2'],
            ['link' => $localizedRoute('service.insurance'), 'bgImage' => 'ah_service_image03.png', 'bgBack' => 'ah_service_image_bg03.png', 'bgContainer' => '#eef4f9'],
            ['link' => $localizedRoute('service.inland-transport'), 'bgImage' => 'ah_service_image04.png', 'bgBack' => 'ah_service_image_bg04.png', 'bgContainer' => '#fff7f7'],
            ['link' => $localizedRoute('service.car-shipping-info'), 'bgImage' => 'ah_service_image05.png', 'bgBack' => 'ah_service_image_bg05.png', 'bgContainer' => '#efedfd'],
            ['link' => $localizedRoute('service.overseas-documents'), 'bgImage' => 'ah_service_image06.png', 'bgBack' => 'ah_service_image_bg06.png', 'bgContainer' => '#fdfaf1'],
            ['link' => $localizedRoute('service.recall-repair'), 'bgImage' => 'ah_service_image08.png', 'bgBack' => 'ah_service_image_bg02.png', 'bgContainer' => '#f7fdf2'],
            ['link' => $localizedRoute('service.auction-vehicle-check'), 'bgImage' => 'ah_service_image09.png', 'bgBack' => 'ah_service_image_bg03.png', 'bgContainer' => '#eef4f9'],
            ['link' => $localizedRoute('service.photo-condition'), 'bgImage' => 'ah_service_image10.png', 'bgBack' => 'ah_service_image_bg04.png', 'bgContainer' => '#fff7f7'],
            ['link' => $localizedRoute('service.repair-easywork-inspection'), 'bgImage' => 'ah_service_image11.png', 'bgBack' => 'ah_service_image_bg05.png', 'bgContainer' => '#efedfd'],
            ['link' => $localizedRoute('service.document-file'), 'bgImage' => $isJa ? 'ah_service_image12.png' : 'ah_service_image12_en.png', 'bgBack' => 'ah_service_image_bg03.png', 'bgContainer' => '#eef4f9'],
        ];

        return view('home-page.services.index', compact('pageMenus', 'services'));
    }

    public function shippingExportImport()
    {
        $urlOld = env('APP_URL_OLD', '');
        $lang = app()->getLocale();
        $isJa = 'ja' === $lang;
        $plans = [
            [
                'url' => $urlOld . '/hn/load/d2d_lump.asp?scd=0&lan=' . $lang,
                'label' => 'label_style08.png',
                'icon' => 'smartplan_icon_01.png',
                'title' => __('home-page/shipping_export_import.smart_plan_link'),
            ],
            [
                'url' => $urlOld . '/hn/load/pre_lump.asp?scd=6&lan=' . $lang,
                'label' => 'label_style04.png',
                'icon' => 'icon_menu_05.png',
                'title' => __('home-page/shipping_export_import.light_plan_link'),
            ],
            [
                'url' => $urlOld . '/hn/load/pre_lump.asp?scd=7&lan=' . $lang,
                'label' => 'label_style07.png',
                'icon' => 'icon_menu_13.png',
                'title' => __('home-page/shipping_export_import.container_plan_link'),
            ],
        ];
        return view('home-page.services.shipping_export_import', compact('isJa', 'plans'));
    }

    public function d2dPackage()
    {
        return view('home-page.services.d2d_package');
    }

    public function inlandTransport()
    {
        $isJa = 'ja' === app()->getLocale();
        return view('home-page.services.inland_transport', compact('isJa'));
    }

    public function insurance()
    {
        return view('home-page.services.insurance');
    }

    public function carShippingInfo()
    {
        $isJa = 'ja' === app()->getLocale();
        return view('home-page.services.car_shipping_info', compact('isJa'));
    }

    public function overseasDocuments()
    {
        return view('home-page.services.overseas_documents');
    }

    public function recallRepair()
    {
        $isJa = 'ja' === app()->getLocale();
        return view('home-page.services.recall_repair', compact('isJa'));
    }

    public function auctionVehicleCheck()
    {
        return view('home-page.services.auction_vehicle_check');
    }

    public function photoCondition()
    {
        $plans = [
            [
                'title' => __('home-page/photo_condition.plan_content_1'),
                'content1' => __('home-page/photo_condition.about_15_shots'),
                'content2' => '',
                'icon2' => '',
            ],
            [
                'title' => __('home-page/photo_condition.plan_content_2'),
                'content1' => __('home-page/photo_condition.about_30_shots'),
                'content2' => '',
                'icon2' => '',
            ],
            [
                'title' => __('home-page/photo_condition.plan_content_3'),
                'content1' => __('home-page/photo_condition.about_15_shots_plus'),
                'content2' => __('home-page/photo_condition.check_sheet'),
                'icon2' => 'fas fa-clipboard-check',
            ],
            [
                'title' => __('home-page/photo_condition.plan_content_4'),
                'content1' => __('home-page/photo_condition.about_15_shots_plus'),
                'content2' => __('home-page/photo_condition.report'),
                'icon2' => 'fas fa-file-contract',
            ],
        ];
        return view('home-page.services.photo_condition', compact('plans'));
    }

    public function repairEasyworkInspection()
    {
        return view('home-page.services.repair_easywork_inspection');
    }

    public function documentFile()
    {
        $isJa = 'ja' === app()->getLocale();
        return view('home-page.services.document_file', compact('isJa'));
    }
}
