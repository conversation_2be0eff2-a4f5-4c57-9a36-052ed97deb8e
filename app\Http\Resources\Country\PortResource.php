<?php

declare(strict_types=1);

namespace App\Http\Resources\Country;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PortResource extends JsonResource
{
    public static function toArrayFrom($data): array
    {
        return self::collection($data)->toArray(request());
    }
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'code' => trim($this->CODE),
            'name' => trim($this->NAME),
            'country_code' => trim($this->COUNTRY_CODE),
            'country_name' => trim($this->COUNTRY_NAME),
            'route_code' => trim($this->ROUTE_CODE)
        ];
    }
}
