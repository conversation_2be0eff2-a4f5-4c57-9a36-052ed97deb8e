<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 26.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="レイヤー_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px"
	 y="0px" viewBox="0 0 170.5 128.6" style="enable-background:new 0 0 170.5 128.6;" xml:space="preserve">
<style type="text/css">
	.st0{fill:url(#SVGID_1_);stroke:#CF3743;stroke-width:2.1541;stroke-miterlimit:10;}
	.st1{fill:url(#SVGID_00000061451694135868321990000002354232362412595128_);}
	.st2{fill:url(#SVGID_00000129194559376995677730000004996945381441720754_);}
	.st3{fill:url(#SVGID_00000132056070763276849850000014379315432162413450_);}
	.st4{fill:url(#SVGID_00000038405777619074661170000010166155864705001647_);}
	.st5{fill:url(#SVGID_00000034810536321140360270000000463417984337838010_);}
	.st6{fill:url(#SVGID_00000059998607724199006700000000540947468409508225_);}
	.st7{opacity:0.75;fill:#FFFFFF;stroke:#CF3743;stroke-width:2.1541;stroke-miterlimit:10;}
	.st8{fill:#FFFFFF;}
</style>
<g>
	<g>
		<g>
			<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="13.5984" y1="64.3169" x2="156.9368" y2="64.3169">
				<stop  offset="0" style="stop-color:#CF3743"/>
				<stop  offset="0.4218" style="stop-color:#E83E4B"/>
				<stop  offset="1" style="stop-color:#CF3743"/>
			</linearGradient>
			<path class="st0" d="M155.6,69.5c1.8-3.3,1.7-7.3-0.2-10.5c-1.2-2-2.9-3.5-5-4.4v-4.4c0-3.4-1.6-6.5-4.2-8.4v-9.4v-1.5h-0.1
				c-0.7-4.7-4.8-8.4-9.8-8.4h-7c-4.9,0-9,3.6-9.8,8.4h-0.1v1.5v7.3h-10.5c-0.7,0-1.5,0.1-2.2,0.2c-0.1-4.3-2.7-7.9-6.3-9.6
				l-1.9-8.4C97,14.9,90.5,9.7,83.4,9.7H52.2c-7.1,0-13.6,5.2-15.1,12.1l-1.6,7.4H34c-6.1,0-11.1,5-11.1,11.1v5.2
				c-0.8,1.5-1.2,3.2-1.2,5v4c-2,0.7-3.7,2-4.9,3.8c-1.8,2.7-2.2,6.1-0.9,9.2l6.3,15c-0.2,0-0.4,0-0.5,0h-8.1V112h8.1
				c1.7,0,2,0.2,4,1.5c3.1,2,8.2,5.4,17,5.4c8.8,0,13.9-3.4,17-5.4c2-1.3,2.3-1.5,4-1.5c1.7,0,2,0.2,4,1.5c3.1,2,8.2,5.4,17,5.4
				c8.8,0,13.9-3.4,17-5.4c2-1.3,2.3-1.5,4-1.5c1.7,0,2,0.2,4,1.5c3.1,2,8.2,5.4,17,5.4c8.8,0,13.9-3.4,17-5.4c2-1.3,2.3-1.5,4-1.5
				h8.1V82.3h-7.3L155.6,69.5z"/>
			<g>
				<g>
					
						<linearGradient id="SVGID_00000064346668960229662500000005695726305085450428_" gradientUnits="userSpaceOnUse" x1="29.8635" y1="35.7844" x2="98.6979" y2="35.7844">
						<stop  offset="0" style="stop-color:#FFFFFF;stop-opacity:8.000000e-02"/>
						<stop  offset="0.3212" style="stop-color:#FFFFFF;stop-opacity:0.13"/>
						<stop  offset="0.6397" style="stop-color:#FFFFFF;stop-opacity:9.000000e-02"/>
						<stop  offset="0.824" style="stop-color:#FFFFFF;stop-opacity:7.000000e-02"/>
						<stop  offset="1" style="stop-color:#FFFFFF;stop-opacity:0.1"/>
					</linearGradient>
					<path style="fill:url(#SVGID_00000064346668960229662500000005695726305085450428_);" d="M45,23.6l-3.1,13.7H34
						c-1.6,0-3,1.3-3,3V48c-0.7,0.5-1.2,1.4-1.2,2.4v3.5h6.9v0c0-5,4.1-9.1,9.1-9.1c5,0,9.1,4.1,9.1,9.1v0h19.6v0
						c0-5,4.1-9.1,9.1-9.1c5,0,9.1,4.1,9.1,9.1v0h5.7V40.2c0-1.6-1.3-3-3-3h-2l-3.1-13.7c-0.7-3.2-4-5.8-7.3-5.8H52.2
						C48.9,17.7,45.7,20.3,45,23.6z M72.5,21.6h10.9c1.5,0,3.2,1.4,3.5,2.8l2.9,12.8H72.5V21.6z M68.9,37.2h-23l2.9-12.8
						c0.3-1.4,2-2.8,3.5-2.8h16.6V37.2z"/>
					
						<linearGradient id="SVGID_00000091699133571078228980000009595955669434133392_" gradientUnits="userSpaceOnUse" x1="38.6489" y1="53.8265" x2="53.2523" y2="53.8265">
						<stop  offset="0" style="stop-color:#FFFFFF;stop-opacity:8.000000e-02"/>
						<stop  offset="0.3212" style="stop-color:#FFFFFF;stop-opacity:0.13"/>
						<stop  offset="0.6397" style="stop-color:#FFFFFF;stop-opacity:9.000000e-02"/>
						<stop  offset="0.824" style="stop-color:#FFFFFF;stop-opacity:7.000000e-02"/>
						<stop  offset="1" style="stop-color:#FFFFFF;stop-opacity:0.1"/>
					</linearGradient>
					<path style="fill:url(#SVGID_00000091699133571078228980000009595955669434133392_);" d="M45.9,46.5c-4,0-7.3,3.3-7.3,7.3
						c0,4,3.3,7.3,7.3,7.3c4,0,7.3-3.3,7.3-7.3C53.3,49.8,50,46.5,45.9,46.5z"/>
					
						<linearGradient id="SVGID_00000016059082626424221890000008669612811418713476_" gradientUnits="userSpaceOnUse" x1="76.5164" y1="53.8265" x2="91.1236" y2="53.8265">
						<stop  offset="0" style="stop-color:#FFFFFF;stop-opacity:8.000000e-02"/>
						<stop  offset="0.3212" style="stop-color:#FFFFFF;stop-opacity:0.13"/>
						<stop  offset="0.6397" style="stop-color:#FFFFFF;stop-opacity:9.000000e-02"/>
						<stop  offset="0.824" style="stop-color:#FFFFFF;stop-opacity:7.000000e-02"/>
						<stop  offset="1" style="stop-color:#FFFFFF;stop-opacity:0.1"/>
					</linearGradient>
					<path style="fill:url(#SVGID_00000016059082626424221890000008669612811418713476_);" d="M83.8,46.5c-4,0-7.3,3.3-7.3,7.3
						c0,4,3.3,7.3,7.3,7.3c4,0,7.3-3.3,7.3-7.3C91.1,49.8,87.8,46.5,83.8,46.5z"/>
				</g>
				<g>
					
						<linearGradient id="SVGID_00000150798622789331027790000002066836452433920919_" gradientUnits="userSpaceOnUse" x1="23.2651" y1="66.7218" x2="148.8686" y2="66.7218">
						<stop  offset="0" style="stop-color:#FFFFFF;stop-opacity:8.000000e-02"/>
						<stop  offset="0.3212" style="stop-color:#FFFFFF;stop-opacity:0.13"/>
						<stop  offset="0.6397" style="stop-color:#FFFFFF;stop-opacity:9.000000e-02"/>
						<stop  offset="0.824" style="stop-color:#FFFFFF;stop-opacity:7.000000e-02"/>
						<stop  offset="1" style="stop-color:#FFFFFF;stop-opacity:0.1"/>
					</linearGradient>
					<path style="fill:url(#SVGID_00000150798622789331027790000002066836452433920919_);" d="M142.4,50.2c0-1.4-1.1-2.4-2.4-2.4
						h-1.7v-8.8h-10.7v8.8h-18.6c-1.4,0-2.4,1.1-2.4,2.4v11.7H24.9c-1.2,0-2,1.2-1.5,2.3L34,89.5c0.3,0.8,0.7,1.5,1.2,2.3
						c0.2,0.1,0.4,0.3,0.6,0.4c2.3,1.5,3.6,2.4,6.9,2.4c3.3,0,4.7-0.9,6.9-2.4c2.9-1.9,6.9-4.5,14-4.5c7.2,0,11.2,2.6,14.1,4.5
						c2.3,1.5,3.6,2.4,6.9,2.4c3.3,0,4.7-0.9,6.9-2.4c2.9-1.9,6.9-4.5,14-4.5c7.2,0,11.2,2.6,14.1,4.5c2.3,1.5,3.6,2.4,6.9,2.4
						s4.7-0.9,6.9-2.4c0,0,0,0,0,0l15-26.5c0.9-1.7-0.3-3.8-2.2-3.8h-4V50.2z"/>
					
						<linearGradient id="SVGID_00000148660922363475258350000007327957274316112024_" gradientUnits="userSpaceOnUse" x1="127.4791" y1="33.3418" x2="138.2141" y2="33.3418">
						<stop  offset="0" style="stop-color:#FFFFFF;stop-opacity:8.000000e-02"/>
						<stop  offset="0.3212" style="stop-color:#FFFFFF;stop-opacity:0.13"/>
						<stop  offset="0.6397" style="stop-color:#FFFFFF;stop-opacity:9.000000e-02"/>
						<stop  offset="0.824" style="stop-color:#FFFFFF;stop-opacity:7.000000e-02"/>
						<stop  offset="1" style="stop-color:#FFFFFF;stop-opacity:0.1"/>
					</linearGradient>
					<path style="fill:url(#SVGID_00000148660922363475258350000007327957274316112024_);" d="M138.2,36.2v-3.8c0-1-0.8-1.8-1.8-1.8
						h-7c-1,0-1.8,0.8-1.8,1.8v3.8H138.2z"/>
					
						<linearGradient id="SVGID_00000146465872043425902730000015006705412745348480_" gradientUnits="userSpaceOnUse" x1="21.676" y1="97.5274" x2="147.562" y2="97.5274">
						<stop  offset="0" style="stop-color:#FFFFFF;stop-opacity:8.000000e-02"/>
						<stop  offset="0.3212" style="stop-color:#FFFFFF;stop-opacity:0.13"/>
						<stop  offset="0.6397" style="stop-color:#FFFFFF;stop-opacity:9.000000e-02"/>
						<stop  offset="0.824" style="stop-color:#FFFFFF;stop-opacity:7.000000e-02"/>
						<stop  offset="1" style="stop-color:#FFFFFF;stop-opacity:0.1"/>
					</linearGradient>
					<path style="fill:url(#SVGID_00000146465872043425902730000015006705412745348480_);" d="M42.6,104.7c6.3,0,9.8-2.3,12.5-4.1
						c2.4-1.6,4.3-2.9,8.5-2.9c4.1,0,6,1.3,8.5,2.9c2.7,1.8,6.2,4.1,12.5,4.1c6.4,0,9.8-2.3,12.5-4.1c2.4-1.6,4.3-2.9,8.5-2.9
						c4.1,0,6.1,1.3,8.5,2.9c2.7,1.8,6.2,4.1,12.5,4.1s9.8-2.3,12.5-4.1c2.4-1.6,4.3-2.9,8.5-2.9v-7.3c-6.4,0-9.8,2.3-12.5,4.1
						c-2.4,1.6-4.3,2.9-8.5,2.9s-6.1-1.3-8.5-2.9c-2.7-1.8-6.2-4.1-12.5-4.1c-6.4,0-9.8,2.3-12.5,4.1c-2.4,1.6-4.3,2.9-8.5,2.9
						c-4.1,0-6-1.3-8.5-2.9c-2.7-1.8-6.2-4.1-12.5-4.1c-6.4,0-9.8,2.3-12.5,4.1c-2.4,1.6-4.3,2.9-8.5,2.9c-4.1,0-6-1.3-8.5-2.9
						c-2.7-1.8-6.2-4.1-12.5-4.1v7.3c4.1,0,6,1.3,8.5,2.9C32.9,102.4,36.3,104.7,42.6,104.7z"/>
				</g>
			</g>
			<rect x="13.6" y="82.3" class="st7" width="142" height="29.7"/>
		</g>
		<g>
			<path class="st8" d="M45.3,33.2c-0.3-2.7,1.3-4.4,3.4-4.6c1-0.1,1.9,0.3,2.5,0.8l-0.9,1.3c-0.4-0.3-0.9-0.5-1.5-0.5
				c-1,0.1-1.8,1.1-1.6,2.7c0.2,1.6,1,2.4,2.1,2.3c0.6-0.1,1.1-0.4,1.4-0.9l1.1,1.1c-0.6,0.9-1.4,1.3-2.5,1.5
				C47.4,37.1,45.6,35.9,45.3,33.2z"/>
			<path class="st8" d="M52.1,27.8l1.9-0.2l0.2,2.1l0,1.1c0.4-0.5,1-0.9,1.8-1c1.3-0.1,2,0.8,2.2,2.3l0.4,3.8L56.7,36l-0.4-3.5
				c-0.1-0.8-0.3-1.1-0.8-1c-0.4,0-0.7,0.3-1,0.7l0.4,4.1L53,36.3L52.1,27.8z"/>
			<path class="st8" d="M59.4,32.6c-0.2-2,1.1-3.3,2.6-3.5c1.8-0.2,2.7,1,2.9,2.7c0,0.3,0,0.7,0,0.8L61.3,33c0.3,0.8,0.9,1.1,1.6,1
				c0.4,0,0.8-0.2,1.2-0.5l0.8,1.1c-0.6,0.5-1.4,0.8-2.1,0.9C61.1,35.7,59.6,34.6,59.4,32.6z M63.2,31.5c-0.1-0.6-0.4-1-1.1-0.9
				c-0.5,0.1-1,0.4-1,1.2L63.2,31.5z"/>
			<path class="st8" d="M65.8,31.9c-0.2-2,1.2-3.3,2.9-3.5c0.7-0.1,1.3,0.1,1.8,0.5l-0.8,1.3c-0.3-0.2-0.5-0.3-0.8-0.2
				c-0.8,0.1-1.3,0.8-1.2,1.8c0.1,1,0.7,1.6,1.5,1.5c0.4,0,0.7-0.3,1-0.5l0.9,1.2c-0.5,0.6-1.3,0.9-1.9,0.9C67.5,35,66,34,65.8,31.9
				z"/>
			<path class="st8" d="M71.3,25.8l1.9-0.2l0.5,4.8l0.1,0l1.6-2.5l2.1-0.2l-1.9,2.8l2.6,3.3l-2.1,0.2l-1.6-2.2l-0.6,0.9l0.2,1.5
				l-1.9,0.2L71.3,25.8z"/>
			<path class="st8" d="M78.8,27.1l-0.2-1.7l1.9-0.2l0.1,1.7l0.1,3.7l-1.2,0.1L78.8,27.1z M79.1,32.6c-0.1-0.7,0.3-1.2,1-1.3
				c0.7-0.1,1.2,0.4,1.3,1.1s-0.3,1.2-1,1.3C79.7,33.7,79.2,33.2,79.1,32.6z"/>
			<path class="st8" d="M83.1,26.6l-0.2-1.7l1.9-0.2l0.1,1.7l0.1,3.7l-1.2,0.1L83.1,26.6z M83.4,32.1c-0.1-0.7,0.3-1.2,1-1.3
				c0.7-0.1,1.2,0.4,1.3,1.1s-0.3,1.2-1,1.3C84,33.3,83.5,32.8,83.4,32.1z"/>
		</g>
	</g>
	<g>
		<path class="st8" d="M51.4,60.9h3.2V66h4.4v-5.1h3.2v13.4h-3.2v-5.5h-4.4v5.5h-3.2V60.9z"/>
		<path class="st8" d="M65.3,68v-7.1h3.2v7.5c0,2.5,0.8,3.4,2.2,3.4c1.5,0,2.3-0.9,2.3-3.4v-7.5h3.1V68c0,4.5-1.8,6.6-5.4,6.6
			C67.1,74.5,65.3,72.5,65.3,68z"/>
		<path class="st8" d="M79.1,60.9h4.7c2.8,0,5,0.8,5,3.3c0,1.2-0.7,2.5-1.7,2.9v0.1c1.4,0.4,2.4,1.4,2.4,3.2c0,2.7-2.3,4-5.2,4h-5.1
			V60.9z M83.6,66.2c1.4,0,2-0.6,2-1.5c0-1-0.6-1.4-2-1.4h-1.3v2.9H83.6z M83.9,71.8c1.5,0,2.3-0.6,2.3-1.7c0-1.1-0.8-1.5-2.3-1.5
			h-1.6v3.3H83.9z"/>
		<path class="st8" d="M91.6,60.9h3.3l3.3,6.4l1.3,3h0.1c-0.2-1.4-0.4-3.3-0.4-5v-4.4h3.1v13.4H99l-3.3-6.5l-1.3-2.9h-0.1
			c0.1,1.5,0.4,3.3,0.4,5v4.4h-3.1V60.9z"/>
		<path class="st8" d="M105.3,60.9h8.5v2.7h-5.3V66h4.5v2.7h-4.5v2.8h5.5v2.7h-8.7V60.9z"/>
		<path class="st8" d="M119.3,63.6h-3.6v-2.7h10.5v2.7h-3.6v10.7h-3.2V63.6z"/>
		<path class="st8" d="M138.2,64.3c0-1.1-0.9-1.6-2.3-1.6c-1.1,0-2,0.5-2.9,1.4l-2-1.8c1.2-1.5,3.1-2.5,5.2-2.5
			c2.9,0,5.2,1.3,5.2,4.2c0,2.9-4.1,3.1-4,5.7h-3.1C134.2,66.7,138.2,66.1,138.2,64.3z M134,73.2c0-1.2,0.9-2,2-2c1.2,0,2,0.8,2,2
			c0,1.2-0.9,2-2,2C134.9,75.3,134,74.4,134,73.2z"/>
	</g>
	<g>
		<g>
			<path class="st8" d="M31.9,47.7h1.7l0.6,4c0.1,0.9,0.3,1.7,0.4,2.6h0c0.2-0.9,0.3-1.7,0.5-2.6l0.9-4h1.4l0.9,4
				c0.2,0.8,0.3,1.7,0.5,2.6H39c0.1-0.9,0.2-1.7,0.4-2.6l0.6-4h1.5L40,55.9h-2l-0.9-4c-0.1-0.6-0.3-1.3-0.4-1.9h0
				c-0.1,0.6-0.2,1.3-0.4,1.9l-0.9,4h-2L31.9,47.7z"/>
			<path class="st8" d="M42.6,47.1h1.6v2.2l-0.1,1.2c0.5-0.5,1.1-0.9,1.9-0.9c1.3,0,1.9,0.9,1.9,2.5v3.8h-1.6v-3.6
				c0-1-0.3-1.3-0.9-1.3c-0.5,0-0.8,0.2-1.3,0.7v4.2h-1.6V47.1z"/>
			<path class="st8" d="M49.3,54.2c0-1.3,1.1-2,3.6-2.3c0-0.6-0.3-1.1-1.1-1.1c-0.6,0-1.1,0.3-1.7,0.6l-0.6-1.1
				c0.7-0.5,1.6-0.8,2.6-0.8c1.6,0,2.4,0.9,2.4,2.7v3.6h-1.3l-0.1-0.7h0c-0.5,0.5-1.2,0.8-1.9,0.8C50.1,56,49.3,55.2,49.3,54.2z
				 M52.9,54.1v-1.2c-1.5,0.2-2,0.6-2,1.2c0,0.5,0.3,0.7,0.8,0.7C52.2,54.7,52.5,54.5,52.9,54.1z"/>
			<path class="st8" d="M56.4,53.7V51h-0.9v-1.2l1-0.1l0.2-1.7H58v1.7h1.5V51H58v2.7c0,0.7,0.3,1,0.8,1c0.2,0,0.4-0.1,0.6-0.1
				l0.3,1.2c-0.3,0.1-0.8,0.2-1.3,0.2C56.9,56,56.4,55.1,56.4,53.7z"/>
			<path class="st8" d="M63.1,47.8c0-0.5,0.4-0.9,0.9-0.9c0.6,0,0.9,0.4,0.9,0.9c0,0.5-0.4,0.9-0.9,0.9
				C63.5,48.7,63.1,48.3,63.1,47.8z M63.3,49.7h1.6v6.2h-1.6V49.7z"/>
			<path class="st8" d="M66,55.1l0.7-1c0.6,0.4,1.1,0.7,1.7,0.7c0.6,0,0.9-0.3,0.9-0.6c0-0.5-0.6-0.7-1.3-0.9
				c-0.8-0.3-1.7-0.8-1.7-1.8c0-1.1,0.9-1.9,2.3-1.9c0.9,0,1.6,0.4,2.1,0.8l-0.7,1c-0.4-0.3-0.9-0.5-1.3-0.5c-0.6,0-0.8,0.2-0.8,0.6
				c0,0.5,0.6,0.6,1.2,0.9c0.8,0.3,1.7,0.7,1.7,1.9c0,1.1-0.8,1.9-2.5,1.9C67.5,56,66.6,55.6,66,55.1z"/>
		</g>
	</g>
	<g>
		<g>
			<path d="M33.7,95.9l0.8-1c0.2,0.3,0.6,0.5,1,0.5c0.7,0,1.2-0.5,1.3-1.9c-0.3,0.4-0.9,0.7-1.3,0.7c-1.1,0-2-0.6-2-2
				c0-1.4,1-2.2,2.2-2.2c1.3,0,2.5,0.9,2.5,3.3c0,2.4-1.3,3.5-2.7,3.5C34.7,96.7,34.1,96.3,33.7,95.9z M35.9,93
				c0.3,0,0.7-0.2,0.9-0.6c-0.1-1-0.6-1.3-1-1.3c-0.4,0-0.8,0.3-0.8,1C35,92.8,35.4,93,35.9,93z"/>
			<path d="M39.1,93.3c0-2.2,1-3.4,2.4-3.4c1.4,0,2.4,1.1,2.4,3.4c0,2.2-1,3.4-2.4,3.4C40,96.7,39.1,95.5,39.1,93.3z M42.3,93.3
				c0-1.8-0.4-2.1-0.9-2.1c-0.5,0-0.9,0.4-0.9,2.1s0.4,2.2,0.9,2.2C41.9,95.5,42.3,95.1,42.3,93.3z"/>
			<path d="M44.5,91.9c0-1.3,0.7-2.1,1.7-2.1c1,0,1.7,0.8,1.7,2.1S47.1,94,46.1,94C45.2,94,44.5,93.2,44.5,91.9z M46.7,91.9
				c0-0.9-0.3-1.2-0.6-1.2c-0.3,0-0.6,0.3-0.6,1.2c0,0.9,0.3,1.3,0.6,1.3C46.5,93.1,46.7,92.8,46.7,91.9z M50,89.8h0.9l-3.6,7h-0.9
				L50,89.8z M49.4,94.6c0-1.3,0.7-2.1,1.7-2.1s1.7,0.8,1.7,2.1s-0.7,2.1-1.7,2.1S49.4,95.9,49.4,94.6z M51.7,94.6
				c0-0.9-0.3-1.2-0.6-1.2s-0.6,0.3-0.6,1.2c0,0.9,0.3,1.3,0.6,1.3S51.7,95.5,51.7,94.6z"/>
			<path d="M55.1,93.9c0-1.4,1-2.2,2-2.2s2,0.8,2,2.2c0,1.4-1,2.2-2,2.2S55.1,95.3,55.1,93.9z M58,93.9c0-0.8-0.3-1.3-0.9-1.3
				s-0.9,0.5-0.9,1.3c0,0.8,0.3,1.3,0.9,1.3S58,94.7,58,93.9z"/>
			<path d="M60.3,92.7h-0.6v-0.8l0.6,0v-0.3c0-0.9,0.4-1.6,1.5-1.6c0.3,0,0.6,0.1,0.8,0.1l-0.2,0.8c-0.1-0.1-0.3-0.1-0.4-0.1
				c-0.3,0-0.5,0.2-0.5,0.7v0.4h0.8v0.9h-0.8V96h-1.1V92.7z"/>
			<path d="M64.6,91.8h0.9l0.1,0.4h0c0.4-0.3,0.8-0.5,1.2-0.5c1,0,1.7,0.9,1.7,2.1c0,1.4-0.9,2.3-1.8,2.3c-0.4,0-0.7-0.2-1-0.5
				l0,0.7v1.3h-1.1V91.8z M67.4,93.9c0-0.8-0.3-1.3-0.8-1.3c-0.3,0-0.5,0.1-0.8,0.4v1.9c0.3,0.2,0.5,0.3,0.8,0.3
				C67,95.2,67.4,94.8,67.4,93.9z"/>
			<path d="M69.2,93.9c0-1.4,1-2.2,2-2.2c1.2,0,1.8,0.9,1.8,2c0,0.2,0,0.4-0.1,0.5h-2.6c0.1,0.7,0.5,1.1,1.2,1.1
				c0.3,0,0.6-0.1,0.9-0.3l0.4,0.7c-0.4,0.3-0.9,0.5-1.5,0.5C70.1,96.1,69.2,95.3,69.2,93.9z M72,93.5c0-0.6-0.3-0.9-0.8-0.9
				c-0.4,0-0.8,0.3-0.9,0.9H72z"/>
			<path d="M73.6,93.9c0-1.4,1-2.2,2-2.2s2,0.8,2,2.2c0,1.4-1,2.2-2,2.2S73.6,95.3,73.6,93.9z M76.5,93.9c0-0.8-0.3-1.3-0.9-1.3
				s-0.9,0.5-0.9,1.3c0,0.8,0.3,1.3,0.9,1.3S76.5,94.7,76.5,93.9z"/>
			<path d="M78.6,91.8h0.9l0.1,0.4h0c0.4-0.3,0.8-0.5,1.2-0.5c1,0,1.7,0.9,1.7,2.1c0,1.4-0.9,2.3-1.8,2.3c-0.4,0-0.7-0.2-1-0.5
				l0,0.7v1.3h-1.1V91.8z M81.3,93.9c0-0.8-0.3-1.3-0.8-1.3c-0.3,0-0.5,0.1-0.8,0.4v1.9c0.3,0.2,0.5,0.3,0.8,0.3
				C81,95.2,81.3,94.8,81.3,93.9z"/>
			<path d="M83.4,94.9V90h1.1v4.9c0,0.3,0.1,0.3,0.2,0.3c0,0,0.1,0,0.2,0L85,96c-0.1,0.1-0.3,0.1-0.6,0.1
				C83.7,96.1,83.4,95.6,83.4,94.9z"/>
			<path d="M85.5,93.9c0-1.4,1-2.2,2-2.2c1.2,0,1.8,0.9,1.8,2c0,0.2,0,0.4-0.1,0.5h-2.6c0.1,0.7,0.5,1.1,1.2,1.1
				c0.3,0,0.6-0.1,0.9-0.3l0.4,0.7c-0.4,0.3-1,0.5-1.5,0.5C86.5,96.1,85.5,95.3,85.5,93.9z M88.3,93.5c0-0.6-0.3-0.9-0.8-0.9
				c-0.4,0-0.8,0.3-0.9,0.9H88.3z"/>
			<path d="M91.5,91.8h1.1l0.5,2.1c0.1,0.4,0.1,0.8,0.2,1.2h0c0.1-0.4,0.2-0.8,0.3-1.2l0.5-2.1h1l0.5,2.1c0.1,0.4,0.2,0.8,0.3,1.2h0
				c0.1-0.4,0.1-0.8,0.2-1.2l0.5-2.1h1l-1,4.2h-1.3l-0.4-1.8c-0.1-0.4-0.2-0.8-0.2-1.2h0c-0.1,0.4-0.1,0.8-0.2,1.2L93.9,96h-1.3
				L91.5,91.8z"/>
			<path d="M98.4,90h1.1v1.5l-0.1,0.8c0.3-0.3,0.8-0.6,1.3-0.6c0.9,0,1.3,0.6,1.3,1.7V96H101v-2.5c0-0.7-0.2-0.9-0.6-0.9
				c-0.3,0-0.6,0.2-0.9,0.5V96h-1.1V90z"/>
			<path d="M103,93.9c0-1.4,1-2.2,2-2.2s2,0.8,2,2.2c0,1.4-1,2.2-2,2.2S103,95.3,103,93.9z M105.9,93.9c0-0.8-0.3-1.3-0.9-1.3
				s-0.9,0.5-0.9,1.3c0,0.8,0.3,1.3,0.9,1.3S105.9,94.7,105.9,93.9z"/>
			<path d="M109.6,94.5v-2.6h1.1v2.5c0,0.6,0.2,0.9,0.6,0.9c0.3,0,0.6-0.2,0.9-0.5v-2.8h1.1V96h-0.9l-0.1-0.6h0
				c-0.4,0.4-0.8,0.7-1.3,0.7C110,96.1,109.6,95.5,109.6,94.5z"/>
			<path d="M114.1,95.5l0.5-0.7c0.4,0.3,0.7,0.5,1.1,0.5c0.4,0,0.6-0.2,0.6-0.4c0-0.3-0.4-0.5-0.9-0.6c-0.5-0.2-1.1-0.5-1.1-1.2
				c0-0.8,0.6-1.3,1.6-1.3c0.6,0,1.1,0.3,1.4,0.5l-0.5,0.7c-0.3-0.2-0.6-0.4-0.9-0.4c-0.4,0-0.6,0.2-0.6,0.4c0,0.3,0.4,0.4,0.8,0.6
				c0.6,0.2,1.2,0.5,1.2,1.3c0,0.7-0.6,1.3-1.7,1.3C115.1,96.1,114.5,95.9,114.1,95.5z"/>
			<path d="M117.9,93.9c0-1.4,1-2.2,2-2.2c1.2,0,1.8,0.9,1.8,2c0,0.2,0,0.4-0.1,0.5H119c0.1,0.7,0.5,1.1,1.2,1.1
				c0.3,0,0.6-0.1,0.9-0.3l0.4,0.7c-0.4,0.3-0.9,0.5-1.5,0.5C118.8,96.1,117.9,95.3,117.9,93.9z M120.7,93.5c0-0.6-0.3-0.9-0.8-0.9
				c-0.4,0-0.8,0.3-0.9,0.9H120.7z"/>
			<path d="M122.3,93.9c0-1.4,0.9-2.2,1.8-2.2c0.5,0,0.8,0.2,1.1,0.5l0-0.7V90h1.1v6h-0.9l-0.1-0.4h0c-0.3,0.3-0.7,0.5-1.2,0.5
				C123,96.1,122.3,95.3,122.3,93.9z M125.1,94.8v-1.9c-0.3-0.2-0.5-0.3-0.8-0.3c-0.5,0-0.9,0.5-0.9,1.3c0,0.9,0.3,1.3,0.9,1.3
				C124.6,95.2,124.9,95.1,125.1,94.8z"/>
			<path d="M129,90.5c0-0.4,0.3-0.6,0.6-0.6c0.4,0,0.6,0.2,0.6,0.6c0,0.4-0.3,0.6-0.6,0.6C129.3,91.1,129,90.9,129,90.5z
				 M129.1,91.8h1.1V96h-1.1V91.8z"/>
			<path d="M131.6,94.6v-1.9H131v-0.8l0.7-0.1l0.1-1.1h0.9v1.1h1v0.9h-1v1.9c0,0.5,0.2,0.7,0.6,0.7c0.1,0,0.3,0,0.4-0.1l0.2,0.8
				c-0.2,0.1-0.5,0.2-0.9,0.2C132,96.1,131.6,95.5,131.6,94.6z"/>
			<path d="M19.7,104.2c0-0.9,0.7-1.4,2.4-1.6c0-0.4-0.2-0.7-0.7-0.7c-0.4,0-0.8,0.2-1.2,0.4l-0.4-0.7c0.5-0.3,1.1-0.6,1.8-0.6
				c1.1,0,1.6,0.6,1.6,1.9v2.5h-0.9l-0.1-0.5h0c-0.4,0.3-0.8,0.6-1.3,0.6C20.2,105.4,19.7,104.9,19.7,104.2z M22.1,104.1v-0.9
				c-1,0.1-1.4,0.4-1.4,0.8c0,0.3,0.2,0.5,0.6,0.5C21.6,104.5,21.8,104.4,22.1,104.1z"/>
			<path d="M24.3,101.1h0.9l0.1,0.6h0c0.4-0.4,0.8-0.7,1.4-0.7c0.9,0,1.3,0.6,1.3,1.7v2.6h-1.1v-2.5c0-0.7-0.2-0.9-0.6-0.9
				c-0.3,0-0.6,0.2-0.9,0.5v2.9h-1.1V101.1z"/>
			<path d="M28.8,104.8l0.5-0.7c0.4,0.3,0.7,0.5,1.1,0.5c0.4,0,0.6-0.2,0.6-0.4c0-0.3-0.4-0.5-0.9-0.6c-0.5-0.2-1.1-0.5-1.1-1.2
				c0-0.8,0.6-1.3,1.6-1.3c0.6,0,1.1,0.3,1.4,0.5l-0.5,0.7c-0.3-0.2-0.6-0.4-0.9-0.4c-0.4,0-0.6,0.2-0.6,0.4c0,0.3,0.4,0.4,0.8,0.6
				c0.6,0.2,1.2,0.5,1.2,1.3c0,0.7-0.6,1.3-1.7,1.3C29.8,105.4,29.2,105.2,28.8,104.8z"/>
			<path d="M32.5,101.1h1.1l0.5,2.1c0.1,0.4,0.1,0.8,0.2,1.2h0c0.1-0.4,0.2-0.8,0.3-1.2l0.5-2.1h1l0.5,2.1c0.1,0.4,0.2,0.8,0.3,1.2
				h0c0.1-0.4,0.1-0.8,0.2-1.2l0.5-2.1h1l-1,4.2h-1.3l-0.4-1.8c-0.1-0.4-0.2-0.8-0.2-1.2h0c-0.1,0.4-0.1,0.8-0.2,1.2l-0.4,1.8h-1.3
				L32.5,101.1z"/>
			<path d="M39.2,103.2c0-1.4,1-2.2,2-2.2c1.2,0,1.8,0.9,1.8,2c0,0.2,0,0.4-0.1,0.5h-2.6c0.1,0.7,0.5,1.1,1.2,1.1
				c0.3,0,0.6-0.1,0.9-0.3l0.4,0.7c-0.4,0.3-0.9,0.5-1.5,0.5C40.1,105.4,39.2,104.6,39.2,103.2z M41.9,102.8c0-0.6-0.3-0.9-0.8-0.9
				c-0.4,0-0.8,0.3-0.9,0.9H41.9z"/>
			<path d="M43.8,101.1h0.9l0.1,0.7h0c0.3-0.6,0.8-0.9,1.2-0.9c0.2,0,0.4,0,0.5,0.1l-0.2,1c-0.2,0-0.3-0.1-0.4-0.1
				c-0.3,0-0.7,0.2-1,0.8v2.5h-1.1V101.1z"/>
			<path d="M46.8,103.2c0-1.4,1-2.2,2-2.2c1.2,0,1.8,0.9,1.8,2c0,0.2,0,0.4-0.1,0.5h-2.6c0.1,0.7,0.5,1.1,1.2,1.1
				c0.3,0,0.6-0.1,0.9-0.3l0.4,0.7c-0.4,0.3-1,0.5-1.5,0.5C47.7,105.4,46.8,104.6,46.8,103.2z M49.6,102.8c0-0.6-0.3-0.9-0.8-0.9
				c-0.4,0-0.8,0.3-0.9,0.9H49.6z"/>
			<path d="M51.2,103.2c0-1.4,0.9-2.2,1.8-2.2c0.5,0,0.8,0.2,1.1,0.5l0-0.7v-1.5h1.1v6h-0.9l-0.1-0.4h0c-0.3,0.3-0.7,0.5-1.2,0.5
				C51.9,105.4,51.2,104.6,51.2,103.2z M54,104.1v-1.9c-0.3-0.2-0.5-0.3-0.8-0.3c-0.5,0-0.9,0.5-0.9,1.3c0,0.9,0.3,1.3,0.9,1.3
				C53.5,104.5,53.8,104.4,54,104.1z"/>
			<path d="M58.2,103.8V102h-0.6v-0.8l0.7-0.1l0.1-1.1h0.9v1.1h1v0.9h-1v1.9c0,0.5,0.2,0.7,0.6,0.7c0.1,0,0.3,0,0.4-0.1l0.2,0.8
				c-0.2,0.1-0.5,0.2-0.9,0.2C58.6,105.4,58.2,104.8,58.2,103.8z"/>
			<path d="M61.2,99.3h1.1v1.5l-0.1,0.8c0.3-0.3,0.8-0.6,1.3-0.6c0.9,0,1.3,0.6,1.3,1.7v2.6h-1.1v-2.5c0-0.7-0.2-0.9-0.6-0.9
				c-0.3,0-0.6,0.2-0.9,0.5v2.9h-1.1V99.3z"/>
			<path d="M65.8,104.2c0-0.9,0.7-1.4,2.4-1.6c0-0.4-0.2-0.7-0.7-0.7c-0.4,0-0.8,0.2-1.2,0.4l-0.4-0.7c0.5-0.3,1.1-0.6,1.8-0.6
				c1.1,0,1.6,0.6,1.6,1.9v2.5h-0.9l-0.1-0.5h0c-0.4,0.3-0.8,0.6-1.3,0.6C66.3,105.4,65.8,104.9,65.8,104.2z M68.2,104.1v-0.9
				c-1,0.1-1.4,0.4-1.4,0.8c0,0.3,0.2,0.5,0.6,0.5C67.7,104.5,68,104.4,68.2,104.1z"/>
			<path d="M70.6,103.8V102H70v-0.8l0.7-0.1l0.1-1.1h0.9v1.1h1v0.9h-1v1.9c0,0.5,0.2,0.7,0.6,0.7c0.1,0,0.3,0,0.4-0.1l0.2,0.8
				c-0.2,0.1-0.5,0.2-0.9,0.2C71,105.4,70.6,104.8,70.6,103.8z"/>
			<path d="M75.5,103.8V102h-0.6v-0.8l0.7-0.1l0.1-1.1h0.9v1.1h1v0.9h-1v1.9c0,0.5,0.2,0.7,0.6,0.7c0.1,0,0.3,0,0.4-0.1l0.2,0.8
				c-0.2,0.1-0.5,0.2-0.9,0.2C75.9,105.4,75.5,104.8,75.5,103.8z"/>
			<path d="M78.5,99.3h1.1v1.5l-0.1,0.8c0.3-0.3,0.8-0.6,1.3-0.6c0.9,0,1.3,0.6,1.3,1.7v2.6h-1.1v-2.5c0-0.7-0.2-0.9-0.6-0.9
				c-0.3,0-0.6,0.2-0.9,0.5v2.9h-1.1V99.3z"/>
			<path d="M83.1,103.2c0-1.4,1-2.2,2-2.2c1.2,0,1.8,0.9,1.8,2c0,0.2,0,0.4-0.1,0.5h-2.6c0.1,0.7,0.5,1.1,1.2,1.1
				c0.3,0,0.6-0.1,0.9-0.3l0.4,0.7c-0.4,0.3-0.9,0.5-1.5,0.5C84,105.4,83.1,104.6,83.1,103.2z M85.9,102.8c0-0.6-0.3-0.9-0.8-0.9
				c-0.4,0-0.8,0.3-0.9,0.9H85.9z"/>
			<path d="M87.5,106.9l0.2-0.9c0.1,0,0.2,0.1,0.3,0.1c0.4,0,0.7-0.3,0.8-0.6l0.1-0.2l-1.6-4.1h1.1l0.7,2c0.1,0.4,0.2,0.8,0.4,1.2h0
				c0.1-0.4,0.2-0.8,0.3-1.2l0.6-2h1.1l-1.5,4.3c-0.4,1-0.8,1.6-1.8,1.6C87.9,107,87.7,107,87.5,106.9z"/>
			<path d="M93.4,101.1h1.1l0.5,2.1c0.1,0.4,0.1,0.8,0.2,1.2h0c0.1-0.4,0.2-0.8,0.3-1.2l0.5-2.1h1l0.5,2.1c0.1,0.4,0.2,0.8,0.3,1.2
				h0c0.1-0.4,0.1-0.8,0.2-1.2l0.5-2.1h1l-1,4.2h-1.3l-0.4-1.8c-0.1-0.4-0.2-0.8-0.2-1.2h0c-0.1,0.4-0.1,0.8-0.2,1.2l-0.4,1.8h-1.3
				L93.4,101.1z"/>
			<path d="M100,103.2c0-1.4,1-2.2,2-2.2c1.2,0,1.8,0.9,1.8,2c0,0.2,0,0.4-0.1,0.5h-2.6c0.1,0.7,0.5,1.1,1.2,1.1
				c0.3,0,0.6-0.1,0.9-0.3l0.4,0.7c-0.4,0.3-0.9,0.5-1.5,0.5C100.9,105.4,100,104.6,100,103.2z M102.8,102.8c0-0.6-0.3-0.9-0.8-0.9
				c-0.4,0-0.8,0.3-0.9,0.9H102.8z"/>
			<path d="M104.7,101.1h0.9l0.1,0.7h0c0.3-0.6,0.8-0.9,1.2-0.9c0.2,0,0.4,0,0.5,0.1l-0.2,1c-0.2,0-0.3-0.1-0.4-0.1
				c-0.3,0-0.7,0.2-1,0.8v2.5h-1.1V101.1z"/>
			<path d="M107.7,103.2c0-1.4,1-2.2,2-2.2c1.2,0,1.8,0.9,1.8,2c0,0.2,0,0.4-0.1,0.5h-2.6c0.1,0.7,0.5,1.1,1.2,1.1
				c0.3,0,0.6-0.1,0.9-0.3l0.4,0.7c-0.4,0.3-1,0.5-1.5,0.5C108.6,105.4,107.7,104.6,107.7,103.2z M110.5,102.8
				c0-0.6-0.3-0.9-0.8-0.9c-0.4,0-0.8,0.3-0.9,0.9H110.5z"/>
			<path d="M113.6,104.8l0.5-0.7c0.4,0.3,0.7,0.5,1.1,0.5c0.4,0,0.6-0.2,0.6-0.4c0-0.3-0.4-0.5-0.9-0.6c-0.5-0.2-1.1-0.5-1.1-1.2
				c0-0.8,0.6-1.3,1.6-1.3c0.6,0,1.1,0.3,1.4,0.5l-0.5,0.7c-0.3-0.2-0.6-0.4-0.9-0.4c-0.4,0-0.6,0.2-0.6,0.4c0,0.3,0.4,0.4,0.8,0.6
				c0.6,0.2,1.2,0.5,1.2,1.3c0,0.7-0.6,1.3-1.7,1.3C114.7,105.4,114.1,105.2,113.6,104.8z"/>
			<path d="M117.6,104.2c0-0.9,0.7-1.4,2.4-1.6c0-0.4-0.2-0.7-0.7-0.7c-0.4,0-0.8,0.2-1.2,0.4l-0.4-0.7c0.5-0.3,1.1-0.6,1.8-0.6
				c1.1,0,1.6,0.6,1.6,1.9v2.5h-0.9l-0.1-0.5h0c-0.4,0.3-0.8,0.6-1.3,0.6C118.1,105.4,117.6,104.9,117.6,104.2z M120,104.1v-0.9
				c-1,0.1-1.4,0.4-1.4,0.8c0,0.3,0.2,0.5,0.6,0.5C119.5,104.5,119.7,104.4,120,104.1z"/>
			<path d="M122.4,103.8V102h-0.6v-0.8l0.7-0.1l0.1-1.1h0.9v1.1h1v0.9h-1v1.9c0,0.5,0.2,0.7,0.6,0.7c0.1,0,0.3,0,0.4-0.1l0.2,0.8
				c-0.2,0.1-0.5,0.2-0.9,0.2C122.8,105.4,122.4,104.8,122.4,103.8z"/>
			<path d="M125.3,99.8c0-0.4,0.3-0.6,0.6-0.6c0.4,0,0.6,0.2,0.6,0.6c0,0.4-0.3,0.6-0.6,0.6C125.6,100.4,125.3,100.2,125.3,99.8z
				 M125.4,101.1h1.1v4.2h-1.1V101.1z"/>
			<path d="M127.3,104.8l0.5-0.7c0.4,0.3,0.7,0.5,1.1,0.5c0.4,0,0.6-0.2,0.6-0.4c0-0.3-0.4-0.5-0.9-0.6c-0.5-0.2-1.1-0.5-1.1-1.2
				c0-0.8,0.6-1.3,1.6-1.3c0.6,0,1.1,0.3,1.4,0.5l-0.5,0.7c-0.3-0.2-0.6-0.4-0.9-0.4c-0.4,0-0.6,0.2-0.6,0.4c0,0.3,0.4,0.4,0.8,0.6
				c0.6,0.2,1.2,0.5,1.2,1.3c0,0.7-0.6,1.3-1.7,1.3C128.3,105.4,127.7,105.2,127.3,104.8z"/>
			<path d="M131.6,102H131v-0.8l0.6,0v-0.3c0-0.9,0.4-1.6,1.5-1.6c0.3,0,0.6,0.1,0.8,0.1l-0.2,0.8c-0.1-0.1-0.3-0.1-0.4-0.1
				c-0.3,0-0.5,0.2-0.5,0.7v0.4h0.8v0.9h-0.8v3.3h-1.1V102z M134.1,99.8c0-0.4,0.3-0.6,0.6-0.6c0.4,0,0.6,0.2,0.6,0.6
				c0,0.4-0.3,0.6-0.6,0.6C134.4,100.4,134.1,100.2,134.1,99.8z M134.2,101.1h1.1v4.2h-1.1V101.1z"/>
			<path d="M136.2,103.2c0-1.4,1-2.2,2-2.2c1.2,0,1.8,0.9,1.8,2c0,0.2,0,0.4-0.1,0.5h-2.6c0.1,0.7,0.5,1.1,1.2,1.1
				c0.3,0,0.6-0.1,0.9-0.3l0.4,0.7c-0.4,0.3-1,0.5-1.5,0.5C137.2,105.4,136.2,104.6,136.2,103.2z M139,102.8c0-0.6-0.3-0.9-0.8-0.9
				c-0.4,0-0.8,0.3-0.9,0.9H139z"/>
			<path d="M140.6,103.2c0-1.4,0.9-2.2,1.8-2.2c0.5,0,0.8,0.2,1.1,0.5l0-0.7v-1.5h1.1v6h-0.9l-0.1-0.4h0c-0.3,0.3-0.7,0.5-1.2,0.5
				C141.3,105.4,140.6,104.6,140.6,103.2z M143.5,104.1v-1.9c-0.3-0.2-0.5-0.3-0.8-0.3c-0.5,0-0.9,0.5-0.9,1.3
				c0,0.9,0.3,1.3,0.9,1.3C143,104.5,143.2,104.4,143.5,104.1z"/>
			<path d="M145.9,104.7c0-0.4,0.3-0.7,0.7-0.7s0.7,0.3,0.7,0.7c0,0.4-0.3,0.7-0.7,0.7S145.9,105.1,145.9,104.7z M146,100.6l0-1h1.1
				l0,1l-0.2,2.8h-0.7L146,100.6z"/>
		</g>
	</g>
</g>
</svg>
