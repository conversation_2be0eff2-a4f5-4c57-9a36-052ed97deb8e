<?php

declare(strict_types=1);

namespace App\Repositories\sqlServer;

use App\Models\sqlServer\Inspector;
use App\Repositories\BaseRepository;
use App\Repositories\common\BuilderWhereCondition;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class InspectorRepository extends BaseRepository
{
    public function model(): mixed
    {
        return Inspector::class;
    }

    public function paginate($params = null, array $relations = [], $columns = ['*']): LengthAwarePaginator
    {
        $query = ($this->model)::query();

        $params = $params ?: request()->toArray();
        $limit = Arr::pull($params, 'limit', 20);
        $page = Arr::pull($params, 'page', 1);

        $query = BuilderWhereCondition::call($query, false, $params);
        $this->applySearchNameFilters($query, $params);
        $query = $this->addSoftDeleteCondition($query);

        return $query->paginate($limit, $columns, 'page', $page);
    }
    // get list inspectors with params
    public function getList($params = [], $relations = [], $columns = ['*'], $withTrashed = false)
    {
        $query = ($this->model)::query();
        $query = BuilderWhereCondition::call($query, $withTrashed, $params);

        if (!isset($params['sort'])) {
            $query->orderBy('sort', 'asc');
        }

        $this->buildSortQuery($query, $params);

        return $query->get($columns);
    }

    // get all inspectors with filter

    public function getListWithFilter($params = null, array $relations = [], $columns = ['*']): Collection
    {
        $query = ($this->model)::query();

        $query = BuilderWhereCondition::call($query, false, $params);
        $this->applySearchNameFilters($query, $params);
        $query = $this->addSoftDeleteCondition($query);

        return $query->get(is_array($columns) ? $columns : func_get_args());
    }

    public function findDetailById(int $id)
    {
        $query = ($this->model)::query();
        $query = $this->addSoftDeleteCondition($query);

        return $query->findOrFail($id);
    }

    /**
     * Check if inspector is being used in t_before_check table
     *
     * @param int $inspectorId
     * @return bool
     */
    public function checkInspectorUsage(int $inspectorId): bool
    {
        $usageCount = DB::table('t_before_check')
            ->where('t_inspector_id', $inspectorId)
            ->where('del_flg', 0)
            ->count();

        return $usageCount > 0;
    }

    private function applySearchNameFilters(Builder $query, array $params): void
    {
        $searchName = $params['srchName'] ?? null;
        $searchKana = $params['srchKana'] ?? null;
        $sort = $params['sortType'] ?? 'asc';

        if (!empty($searchName) || !empty($searchKana)) {
            $query->where(function (Builder $subQuery) use ($searchName, $searchKana): void {
                if (!empty($searchName)) {
                    $subQuery->where('name', 'like', '%' . $searchName . '%');
                }
                if (!empty($searchKana)) {
                    $subQuery->where('name_kana', 'like', '%' . $searchKana . '%');
                }
            });
        }

        // Order by name field based on sort parameter
        if (in_array($sort, ['asc', 'desc'])) {
            $query->orderBy('name', $sort);
        } else {
            $query->orderBy('name', 'asc');
        }
    }

}
