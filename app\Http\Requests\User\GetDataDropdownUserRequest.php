<?php

declare(strict_types=1);

namespace App\Http\Requests\User;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GetDataDropdownUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules()
    {
        $rules = [
            'customer_name' => ['nullable', 'string', Rule::requiredIf(fn () => null === $this->get('m_customer-cus_Name_EN-equal')
                    && null === $this->get('m_customer-id-equal')
                    && null === $this->get('ch_charge_sale'))],
            'm_customer-cus_Name_EN-equal' => ['nullable', 'string', Rule::requiredIf(fn () => null === $this->get('customer_name')
                    && null === $this->get('m_customer-id-equal')
                    && null === $this->get('ch_charge_sale'))],
            'm_customer-id-equal' => ['nullable', 'string', Rule::requiredIf(fn () => null === $this->get('customer_name')
                    && null === $this->get('m_customer-cus_Name_EN-equal')
                    && null === $this->get('ch_charge_sale'))],
            'ch_charge_sale' => ['nullable', 'string', Rule::requiredIf(fn () => null === $this->get('customer_name')
                    && null === $this->get('m_customer-cus_Name_EN-equal')
                    && null === $this->get('m_customer-id-equal'))],
        ];

        return $rules;
    }
}
