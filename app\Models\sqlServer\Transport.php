<?php

declare(strict_types=1);

namespace App\Models\sqlServer;

use App\Enums\CommonCode\CommonCodeCategory;
use App\Models\as400\VehicleTransport;
use App\Models\sqlServer\Trait\Transport\FilterTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Transport extends Model
{
    use FilterTrait;

    public const CREATED_AT = 'reg_date';
    public const UPDATED_AT = 'up_date';

    protected $fillable = [
        'del_flg',
        'reg_date',
        'up_date',
        'up_owner',
        'id',
        'm_customer_id',
        'fr_aa_place_id',
        'fr_name',
        'fr_addr',
        'fr_tel',
        'to_name',
        'to_addr',
        'to_tel',
        'pos_no',
        'aa_no',
        'car_name',
        'car_no',
        'car_color',
        'plate_cut_flg',
        'plate_no',
        'odr_date',
        'note',
        'odr_flg',
        'st_cd',
        'deli_price',
        'to_plan_date',
        'to_date',
        'output_flg',
        'ref_no',
        'relate_id',
        'optn1_flg',
        'package_fee',
        'agent_ref_no',
        'fr_place_id',
        'to_place_id',
        'to_etc_place_id',
        'inp_ah_name',
        'plate_cut_fin',
        'plate_cut_fin_date',
        'm_trans_id',
        'sales_price',
        'to_aa_place_id',
        'tp_id',
        'country',
        'service_name',
        'd2d_kbn',
        'vessel_voy',
        'etd',
        'eta',
        'fin_ship',
        'service_kbn',
        'country_cd',
        'plate_send_date',
        'plate_send_no',
        'plate_send_co',
        'country_name',
        'date_of_payment',
        'undrivable_flg',
        'tall_flg',
        'lowdown_flg',
        'long_flg',
        'from_plan_date',
        'port_cd',
        'port',
        'country_area',
        'country_free',
        'auction_date',
        'auction_chk',
        'auction_txt',
        'auc_name',
        'auc_addr',
        'auc_tel',
        'pos_chk',
        'pos_chk_text',
        'date_of_payment_time',
        'old_flg',
        'luxury_flg',
        'luxury_flg_insrance',
        'other_flg',
        'other_flg_txt',
        'tick_no_flg',
        'plate_address_chk',
        'plate_send_name',
        'plate_send_zipcd',
        'plate_send_address',
        'plate_send_tel',
        'narikiri_trans_flg',
        'luxury_money',
    ];

    protected $table = 't_transport';
    protected $casts = [
        'deli_price' => 'float',
        'sales_price' => 'float',
    ];

    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'm_customer_id');
    }

    public function commonInspection(): BelongsTo
    {
        return $this->belongsTo(CommonCode::class, 'st_cd', 'cd')->where('cd_kbn', CommonCodeCategory::INSPECTION_STATUS->value);
    }

    public function transportNotes(): HasMany
    {
        return $this->hasMany(TransportNote::class, 'tp_id', 'id');
    }

    public function vehicleTransports(): HasMany
    {
        return $this->hasMany(VehicleTransport::class, 'HNCC03', 'ref_no');
    }

    public function aaPlace(): BelongsTo
    {
        return $this->belongsTo(AaPlace::class, 'to_aa_place_id', 'id');
    }

    public function frAaPlace(): BelongsTo
    {
        return $this->belongsTo(AaPlace::class, 'fr_aa_place_id', 'id');
    }
}
