<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\SupportService\SupportServiceRequest;
use App\Http\Resources\SupportService\SupportServiceResource;
use App\Repositories\sqlServer\SupportServiceRepository;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use OpenApi\Attributes as OA;

class SupportServiceController extends Controller
{
    private SupportServiceRepository $supportServiceRepository;

    public function __construct(SupportServiceRepository $supportServiceRepository)
    {
        $this->supportServiceRepository = $supportServiceRepository;
    }

    #[OA\Get(
        path: '/api/admin/support-services/not-deleted',
        description: 'Retrieve a list of all active support services for dropdown/selection purposes. Supports sorting capabilities.',
        summary: 'Get list of active support services',
        security: [['access_token' => []]],
        tags: ['Support Service Management'],
        parameters: [
            new OA\Parameter(
                name: 'sort',
                description: 'Sorting parameter(s). Format: "column|direction" or multiple sorts separated by comma. Examples: "id|asc", "label_name|desc", "id|asc,label_name|desc"',
                in: 'query',
                required: false,
                schema: new OA\Schema(
                    type: 'string',
                    pattern: '^([a-zA-Z0-9_]+\\|(asc|desc))(,\\s*[a-zA-Z0-9_]+\\|(asc|desc))*$',
                    example: 'sort_no|asc',
                    nullable: true
                )
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful operation',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'code', type: 'integer', example: 200),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Success'),
                        new OA\Property(
                            property: 'data',
                            type: 'array',
                            items: new OA\Items(
                                properties: [
                                    new OA\Property(property: 'id', type: 'integer', example: 1, description: 'Support service ID'),
                                    new OA\Property(property: 'label_name', type: 'string', example: 'Customer Support', description: 'Support service label name')
                                ]
                            )
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 400,
                description: 'Bad Request - Invalid sort parameter format',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 400),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'The sort parameter must be in the format "column|direction".')
                    ]
                )
            ),
            new OA\Response(
                response: 401,
                description: 'Unauthorized - Invalid or missing access token',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 401),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Unauthorized')
                    ]
                )
            ),
            new OA\Response(
                response: 403,
                description: 'Forbidden - Insufficient permissions',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 403),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Forbidden')
                    ]
                )
            ),
            new OA\Response(
                response: 500,
                description: 'Internal Server Error - Database error',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 500),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Internal Server Error'),
                        new OA\Property(
                            property: 'error',
                            properties: [
                                new OA\Property(property: 'code', type: 'string', example: 'QueryException'),
                                new OA\Property(property: 'message', type: 'string', example: 'Database query error')
                            ],
                            type: 'object'
                        )
                    ]
                )
            )
        ]
    )]
    public function getSupportServiceNotDeleted(SupportServiceRequest $request): AnonymousResourceCollection
    {
        return SupportServiceResource::collection(
            $this->supportServiceRepository->getSupportServiceNotDeleted($request->validated())
        );
    }
}
