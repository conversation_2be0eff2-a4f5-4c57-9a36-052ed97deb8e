<?php

declare(strict_types=1);

use App\Http\Controllers\HomePage\BlogListController;
use App\Http\Controllers\HomePage\CompanyController;
use App\Http\Controllers\HomePage\ContactController;
use App\Http\Controllers\HomePage\HomeController;
use App\Http\Controllers\HomePage\IntroductionController;
use App\Http\Controllers\HomePage\NewsListController;
use App\Http\Controllers\HomePage\PostViewController;
use App\Http\Controllers\HomePage\QuestionController;
use App\Http\Controllers\HomePage\RecruitController;
use App\Http\Controllers\HomePage\SailingScheduleController;
use App\Http\Controllers\HomePage\ServiceController;
use App\Http\Controllers\HomePage\SwitchLanguageController;
use App\Http\Controllers\HomePage\YardListController;
use App\Http\Middleware\SetLocale;
use Illuminate\Support\Facades\Route;

Route::get('/health', fn () => response()->json(['status' => 'ok']));

$routeDefinition = function (): void {
    Route::get('/question', [QuestionController::class, 'index'])->name('question');
    Route::get('/recruit', [RecruitController::class, 'index'])->name('recruit');
    Route::get('/hubnet-introduction', [IntroductionController::class, 'index'])->name('introduction');
    Route::prefix('/service')->group(function (): void {
        Route::get('/', [ServiceController::class, 'index'])->name('service');
        Route::get('/shipping-export-import', [ServiceController::class, 'shippingExportImport'])->name('service.shipping-export-import');
        Route::get('/d2d-package', [ServiceController::class, 'd2dPackage'])->name('service.d2d-package');
        Route::get('/inland-transport', [ServiceController::class, 'inlandTransport'])->name('service.inland-transport');
        Route::get('/insurance', [ServiceController::class, 'insurance'])->name('service.insurance');
        Route::get('/car-shipping-info', [ServiceController::class, 'carShippingInfo'])->name('service.car-shipping-info');
        Route::get('/overseas-documents', [ServiceController::class, 'overseasDocuments'])->name('service.overseas-documents');
        Route::get('/recall-repair', [ServiceController::class, 'recallRepair'])->name('service.recall-repair');
        Route::get('/auction-vehicle-check', [ServiceController::class, 'auctionVehicleCheck'])->name('service.auction-vehicle-check');
        Route::get('/photo-condition', [ServiceController::class, 'photoCondition'])->name('service.photo-condition');
        Route::get('/repair-easywork-inspection', [ServiceController::class, 'repairEasyworkInspection'])->name('service.repair-easywork-inspection');
        Route::get('/document-file', [ServiceController::class, 'documentFile'])->name('service.document-file');
    });
    Route::get('/', [HomeController::class, 'index'])->name('home');
    Route::get('/sailing-schedule', [SailingScheduleController::class, 'index'])->name('schedule');
    Route::get('/autohub-yard-list', [YardListController::class, 'index'])->name('yard-list');
    Route::get('/company', [CompanyController::class, 'index'])->name('company');
    Route::get('/autohub-blog-list', [BlogListController::class, 'index'])->name('blog-list');
    Route::get('/autohub-post-view', [PostViewController::class, 'index'])->name('post-view');
    Route::get('/autohub-news-list', [NewsListController::class, 'index'])->name('news-list');
    Route::get('/contact', [ContactController::class, 'index'])->name('contact');
    Route::post('/contact', [ContactController::class, 'handleContact'])->name('contact-post');
};

Route::get('/switch-language', SwitchLanguageController::class)->name('language.switch');
Route::group(['middleware' => SetLocale::class], $routeDefinition);
Route::group(['prefix' => '{locale}', 'middleware' => SetLocale::class], $routeDefinition);
Route::group(['prefix' => '{locale}', 'middleware' => SetLocale::class, 'as' => 'localized.'], $routeDefinition);
