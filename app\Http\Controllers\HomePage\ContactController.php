<?php

declare(strict_types=1);

namespace App\Http\Controllers\HomePage;

use App\Http\Controllers\Controller;
use App\Http\Requests\Contact\HandleContactRequest;
use App\Jobs\SendMailContactJob;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;

class ContactController extends Controller
{
    public function index(Request $request): View
    {
        $page = $request->input('page', 'new');
        return view('home-page.contact', [
            'showFooter' => false,
            'page' => $page,
            'select_contact' => $request->input('select_contact', '0'),
            'company_name' => $request->input('company_name', ''),
            'name' => $request->input('name', ''),
            'mail_address' => $request->input('mail_address', ''),
            'tel' => $request->input('tel', ''),
            'message' => $request->input('message', ''),
        ]);
    }

    public function handleContact(HandleContactRequest $request): View
    {
        if ('proc' === $request->input('page')) {
            return $this->processContactSubmission($request);
        }

        return view('home-page.contact', array_merge([
            'showFooter' => false,
            'page' => 'conf',
        ], $request->all()));
    }

    private function processContactSubmission(Request $request): View
    {
        try {
            $currentLang = app()->getLocale();

            SendMailContactJob::dispatchSync(
                $request->input('select_contact') ?? '',
                $request->input('company_name') ?? '',
                $request->input('name') ?? '',
                $request->input('mail_address') ?? '',
                $request->input('tel') ?? '',
                $request->input('message') ?? '',
                $currentLang
            );

            $sendOK = "OK";

        } catch (Exception $e) {
            Log::error('Exception occurred while sending email', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $sendOK = "NG";
        }

        return view('home-page.contact', array_merge([
            'showFooter' => false,
            'page' => 'proc',
            'sendOK' => $sendOK,
        ], $request->all()));
    }
}
