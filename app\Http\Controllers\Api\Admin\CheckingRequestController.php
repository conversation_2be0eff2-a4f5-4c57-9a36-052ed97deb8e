<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\Admin;

use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Http\Requests\CheckingRequest\BulkAssignInspectorRequest;
use App\Http\Requests\CheckingRequest\BulkUpdateStatusRequest;
use App\Http\Requests\CheckingRequest\ConfirmCreateCheckingRQRequest;
use App\Http\Requests\CheckingRequest\CreateCheckingRQRequest;
use App\Http\Requests\CheckingRequest\CsvCheckingRQRequest;
use App\Http\Requests\CheckingRequest\ListCheckingRQRequest;
use App\Http\Requests\CheckingRequest\UpdateCheckingRQRequest;
use App\Http\Resources\CheckingRequest\CheckingRequestResource;
use App\Http\Resources\CommonCode\InspectionStatusResource;
use App\Services\CheckingRequest\BulkAssignInspectorService;
use App\Services\CheckingRequest\BulkUpdateStatusService;
use App\Services\CheckingRequest\ConfirmCreateCheckingRequestService;
use App\Services\CheckingRequest\CreateCheckingRequestService;
use App\Services\CheckingRequest\DetailCheckingRequestService;
use App\Services\CheckingRequest\ExportCsvCheckingRequestService;
use App\Services\CheckingRequest\ExportCsvTokenService;
use App\Services\CheckingRequest\GetListCheckingRequestService;
use App\Services\CheckingRequest\UpdateCheckingRequestService;
use App\Services\CommonCode\CommonCodeService;
use Exception;
use MarcinOrlowski\ResponseBuilder\Exceptions\ArrayWithMixedKeysException;
use MarcinOrlowski\ResponseBuilder\Exceptions\ConfigurationNotFoundException;
use MarcinOrlowski\ResponseBuilder\Exceptions\IncompatibleTypeException;
use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Response;

class CheckingRequestController extends Controller
{
    private CommonCodeService $commonCodeService;
    private GetListCheckingRequestService $getListCheckingRequestService;
    private DetailCheckingRequestService $detailCheckingRequestService;
    private BulkUpdateStatusService $bulkUpdateStatusService;
    private BulkAssignInspectorService  $bulkAssignInspectorService;
    private ExportCsvCheckingRequestService $exportCsvCheckingRequestService;
    private CreateCheckingRequestService $createCheckingRequestService;
    private ExportCsvTokenService $exportCsvTokenService;
    private ConfirmCreateCheckingRequestService $confirmCreateCheckingRequestService;
    private UpdateCheckingRequestService $updateCheckingRequestService;

    public function __construct(
        CommonCodeService $commonCodeService,
        GetListCheckingRequestService $getListCheckingRequestService,
        DetailCheckingRequestService $detailCheckingRequestService,
        BulkUpdateStatusService $bulkUpdateStatusService,
        BulkAssignInspectorService $bulkAssignInspectorService,
        ExportCsvCheckingRequestService $exportCsvCheckingRequestService,
        CreateCheckingRequestService $createCheckingRequestService,
        ExportCsvTokenService  $exportCsvTokenService,
        ConfirmCreateCheckingRequestService $confirmCreateCheckingRequestService,
        UpdateCheckingRequestService $updateCheckingRequestService
    ) {
        $this->commonCodeService = $commonCodeService;
        $this->getListCheckingRequestService = $getListCheckingRequestService;
        $this->detailCheckingRequestService = $detailCheckingRequestService;
        $this->bulkUpdateStatusService = $bulkUpdateStatusService;
        $this->bulkAssignInspectorService = $bulkAssignInspectorService;
        $this->exportCsvCheckingRequestService = $exportCsvCheckingRequestService;
        $this->createCheckingRequestService = $createCheckingRequestService;
        $this->exportCsvTokenService = $exportCsvTokenService;
        $this->confirmCreateCheckingRequestService = $confirmCreateCheckingRequestService;
        $this->updateCheckingRequestService = $updateCheckingRequestService;
    }

    #[OA\Get(
        path: '/api/admin/checking-request',
        description: 'Retrieve a paginated list of checking requests with filtering and search capabilities.',
        summary: 'Get checking request list',
        security: [['access_token' => []]],
        tags: ['Checking Request'],
        parameters: [
            new OA\Parameter(
                name: 'limit',
                description: 'Number of items per page',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'integer', minimum: 1, example: 10)
            ),
            new OA\Parameter(
                name: 'page',
                description: 'Page number for pagination',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'integer', minimum: 1, example: 1)
            ),
            new OA\Parameter(
                name: 't_before_check-id-equal',
                description: 'Filter by checking request ID',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'integer', example: 123)
            ),
            new OA\Parameter(
                name: 't_aa_date-t_aa_place_id-equal',
                description: 'Filter by auction place ID',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'integer', example: 86)
            ),
            new OA\Parameter(
                name: 't_before_check-st_cd-equal',
                description: 'Filter by status code',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '0')
            ),
            new OA\Parameter(
                name: 't_before_check-t_inspector_id-equal',
                description: 'Filter by inspector ID',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'integer', example: 5)
            ),
            new OA\Parameter(
                name: 't_before_check-aa_no-equal',
                description: 'Filter by exact auction number',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'AA2024001')
            ),
            new OA\Parameter(
                name: 'date_mode',
                description: 'Date filtering mode (1: 開催日 Event Date, 2: 申込日時 Order Date, 3: 検査員依頼日時 Inspector Request Date)',
                in: 'query',
                required: false,
                // Note: Enum values should match DateMode::getAllValues()
                schema: new OA\Schema(type: 'string', enum: ['1', '2', '3'], example: '2')
            ),
            new OA\Parameter(
                name: 'sdate',
                description: 'Start date for date range filtering (YYYY-MM-DD)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', format: 'date', example: '2024-01-01')
            ),
            new OA\Parameter(
                name: 'edate',
                description: 'End date for date range filtering (YYYY-MM-DD)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', format: 'date', example: '2024-12-31')
            ),
            new OA\Parameter(
                name: 't_before_check-odr_person-like',
                description: 'Search by order person name (partial match)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'John')
            ),
            new OA\Parameter(
                name: 't_before_check-car_name-like',
                description: 'Search by car name (partial match)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'Toyota')
            ),
            new OA\Parameter(
                name: 'customerName',
                description: 'Search by customer name (searches both Japanese and English names)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '田中')
            ),
            new OA\Parameter(
                name: 'orderOption',
                description: 'Sort field option (1: Customer Name JP, 2: AA Place, 3: AA No, 4: Order Date, 5: Open Date, 6: Car Name)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', enum: ['1', '2', '3', '4', '5', '6'], example: '4')
            ),
            new OA\Parameter(
                name: 'sortType',
                description: 'Sort direction (asc: ascending, desc: descending)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', enum: ['asc', 'desc'], example: 'asc')
            ),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful operation',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(
                            property: 'success',
                            type: 'boolean',
                            example: true
                        ),
                        new OA\Property(
                            property: 'data',
                            properties: [
                                new OA\Property(
                                    property: 'data',
                                    type: 'array',
                                    items: new OA\Items(
                                        properties: [
                                            new OA\Property(property: 'id', description: 'Checking request ID', type: 'integer', example: 123),
                                            new OA\Property(property: 'st_cd', description: 'Status code', type: 'string', example: '0'),
                                            new OA\Property(property: 'aa_no', description: 'Auction number', type: 'string', example: 'AA2024001'),
                                            new OA\Property(property: 'optn1_flg', description: 'Option 1 flag', type: 'boolean', example: true),
                                            new OA\Property(property: 'optn2_flg', description: 'Option 2 flag', type: 'boolean', example: false),
                                            new OA\Property(property: 'note', description: 'Note', type: 'string', example: 'Special inspection notes'),
                                            new OA\Property(property: 'photo_flg', description: 'Photo flag', type: 'boolean', example: true),
                                            new OA\Property(property: 'cc_note', description: 'CC note', type: 'string', example: 'Customer care notes'),
                                            new OA\Property(property: 'car_name', description: 'Car name', type: 'string', example: 'Toyota Camry'),
                                            new OA\Property(property: 'check_req_date', description: 'Check request date', type: 'string', format: 'date', example: '2024-01-15'),
                                            new OA\Property(property: 'odr_date', description: 'Order date', type: 'string', format: 'date', example: '2024-01-10'),
                                            new OA\Property(property: 'open_date', description: 'Open date', type: 'string', format: 'date', example: '2024-01-20'),
                                            new OA\Property(property: 'odr_person', description: 'Order person', type: 'string', example: 'John Doe'),
                                            new OA\Property(
                                                property: 'inspector',
                                                description: 'Inspector information',
                                                properties: [
                                                    new OA\Property(property: 'id', type: 'integer', example: 5),
                                                    new OA\Property(property: 'name', type: 'string', example: 'Inspector Name')
                                                ],
                                                type: 'object'
                                            ),
                                            new OA\Property(
                                                property: 'customer',
                                                description: 'Customer information',
                                                properties: [
                                                    new OA\Property(property: 'id', type: 'integer', example: 100),
                                                    new OA\Property(property: 'cus_Name_JP', type: 'string', example: '田中太郎'),
                                                    new OA\Property(property: 'cus_Name_EN', type: 'string', example: 'Tanaka Taro')
                                                ],
                                                type: 'object'
                                            ),
                                            new OA\Property(
                                                property: 'aaDate',
                                                description: 'Auction date information',
                                                properties: [
                                                    new OA\Property(property: 'id', type: 'integer', example: 200),
                                                    new OA\Property(property: 'open_date', type: 'string', format: 'date', example: '2024-01-20'),
                                                    new OA\Property(property: 'odr_date', type: 'string', format: 'date', example: '2024-01-10'),
                                                    new OA\Property(property: 'check_req_date', type: 'string', format: 'date', example: '2024-01-15'),
                                                    new OA\Property(
                                                        property: 'aaPlace',
                                                        description: 'Auction place information',
                                                        properties: [
                                                            new OA\Property(property: 'id', type: 'integer', example: 86),
                                                            new OA\Property(property: 'name', type: 'string', example: 'Tokyo Auction Place')
                                                        ],
                                                        type: 'object'
                                                    )
                                                ],
                                                type: 'object'
                                            )
                                        ]
                                    )
                                ),
                                new OA\Property(
                                    property: 'current_page',
                                    type: 'integer',
                                    example: 1
                                ),
                                new OA\Property(
                                    property: 'last_page',
                                    type: 'integer',
                                    example: 5
                                ),
                                new OA\Property(
                                    property: 'per_page',
                                    type: 'integer',
                                    example: 10
                                ),
                                new OA\Property(
                                    property: 'total',
                                    type: 'integer',
                                    example: 50
                                )
                            ],
                            type: 'object'
                        ),
                        new OA\Property(
                            property: 'message',
                            type: 'string',
                            example: null,
                            nullable: true
                        )
                    ]
                )
            ),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function getListCheckingRequest(ListCheckingRQRequest $request): Response
    {
        return $this->respond($this->getListCheckingRequestService->call($request->validated()));
    }

    #[OA\Get(
        path: '/api/admin/checking-request/{id}',
        description: 'Retrieve detailed information for a specific checking request by ID. Returns the same data structure as the list endpoint but for a single item.',
        summary: 'Get checking request detail',
        security: [['access_token' => []]],
        tags: ['Checking Request'],
        parameters: [
            new OA\Parameter(
                name: 'id',
                description: 'Checking request ID',
                in: 'path',
                required: true,
                schema: new OA\Schema(type: 'integer', example: 123)
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful operation',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(
                            property: 'data',
                            properties: [
                                new OA\Property(property: 'id', description: 'Checking request ID', type: 'integer', example: 123),
                                new OA\Property(property: 'st_cd', description: 'Status code', type: 'string', example: '0'),
                                new OA\Property(property: 'aa_no', description: 'Auction number', type: 'string', example: 'AA2024001'),
                                new OA\Property(property: 'optn1_flg', description: 'Option 1 flag', type: 'boolean', example: true),
                                new OA\Property(property: 'optn2_flg', description: 'Option 2 flag', type: 'boolean', example: false),
                                new OA\Property(property: 'note', description: 'Note', type: 'string', example: 'Special inspection notes'),
                                new OA\Property(property: 'photo_flg', description: 'Photo flag', type: 'boolean', example: true),
                                new OA\Property(property: 'cc_note', description: 'CC note', type: 'string', example: 'Customer care notes'),
                                new OA\Property(property: 'car_name', description: 'Car name', type: 'string', example: 'Toyota Camry'),
                                new OA\Property(property: 'check_req_date', description: 'Check request date', type: 'string', format: 'date', example: '2024-01-15'),
                                new OA\Property(property: 'odr_date', description: 'Order date', type: 'string', format: 'date', example: '2024-01-10'),
                                new OA\Property(property: 'open_date', description: 'Open date', type: 'string', format: 'date', example: '2024-01-20'),
                                new OA\Property(property: 'odr_person', description: 'Order person', type: 'string', example: 'John Doe'),
                                new OA\Property(
                                    property: 'inspector',
                                    description: 'Inspector information',
                                    properties: [
                                        new OA\Property(property: 'id', type: 'integer', example: 5),
                                        new OA\Property(property: 'name', type: 'string', example: 'Inspector Name')
                                    ],
                                    type: 'object'
                                ),
                                new OA\Property(
                                    property: 'customer',
                                    description: 'Customer information',
                                    properties: [
                                        new OA\Property(property: 'id', type: 'integer', example: 100),
                                        new OA\Property(property: 'cus_Name_JP', type: 'string', example: '田中太郎'),
                                        new OA\Property(property: 'cus_Name_EN', type: 'string', example: 'Tanaka Taro')
                                    ],
                                    type: 'object'
                                ),
                                new OA\Property(
                                    property: 'aaDate',
                                    description: 'Auction date information',
                                    properties: [
                                        new OA\Property(property: 'id', type: 'integer', example: 200),
                                        new OA\Property(property: 'open_date', type: 'string', format: 'date', example: '2024-01-20'),
                                        new OA\Property(property: 'odr_date', type: 'string', format: 'date', example: '2024-01-10'),
                                        new OA\Property(property: 'check_req_date', type: 'string', format: 'date', example: '2024-01-15'),
                                        new OA\Property(
                                            property: 'aaPlace',
                                            description: 'Auction place information',
                                            properties: [
                                                new OA\Property(property: 'id', type: 'integer', example: 86),
                                                new OA\Property(property: 'name', type: 'string', example: 'Tokyo Auction Place')
                                            ],
                                            type: 'object'
                                        )
                                    ],
                                    type: 'object'
                                )
                            ],
                            type: 'object'
                        ),
                        new OA\Property(property: 'message', type: 'string', example: null, nullable: true)
                    ]
                )
            ),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 404, description: 'Checking request not found'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function getDetailCheckingRequest(int $id): Response
    {
        $checkingRequest = $this->detailCheckingRequestService->call($id);
        return $this->respond(CheckingRequestResource::make($checkingRequest));
    }

    #[OA\Get(
        path: '/api/admin/checking-request/statuses',
        description: 'Retrieve all available inspection status codes for checking requests. Used for dropdown selections and status filtering.',
        summary: 'Get inspection status list',
        security: [['access_token' => []]],
        tags: ['Checking Request'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful operation',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(
                            property: 'success',
                            type: 'boolean',
                            example: true
                        ),
                        new OA\Property(
                            property: 'data',
                            type: 'array',
                            items: new OA\Items(
                                properties: [
                                    new OA\Property(property: 'cd', description: 'Status code', type: 'string', example: '0'),
                                    new OA\Property(property: 'value1', description: 'Status name in Japanese', type: 'string', example: '確認中')
                                ]
                            )
                        ),
                        new OA\Property(
                            property: 'message',
                            type: 'string',
                            example: null,
                            nullable: true
                        )
                    ]
                )
            ),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function getInspectionStatuses(): Response
    {
        return $this->respond(InspectionStatusResource::collection($this->commonCodeService->getInspectionStatuses()));
    }

    #[OA\Post(
        path: '/api/admin/checking-request/bulk-update-status',
        description: 'Update the status of multiple checking requests in bulk. This endpoint allows administrators to change the status of multiple checking requests at once.',
        summary: 'Bulk update checking request status',
        security: [['access_token' => []]],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(
                required: ['ids', 'status'],
                properties: [
                    new OA\Property(
                        property: 'ids',
                        description: 'Array of checking request IDs to update',
                        type: 'array',
                        items: new OA\Items(type: 'integer', example: 123),
                        example: [123, 124, 125]
                    ),
                    new OA\Property(
                        property: 'status',
                        description: 'New status code to set for all selected checking requests',
                        type: 'integer',
                        example: 1
                    )
                ]
            )
        ),
        tags: ['Checking Request'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Status updated successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(
                            property: 'success',
                            type: 'boolean',
                            example: true
                        ),
                        new OA\Property(
                            property: 'data',
                            type: 'object',
                            example: null,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'message',
                            type: 'string',
                            example: 'Status updated successfully',
                            nullable: true
                        )
                    ]
                )
            ),
            new OA\Response(response: 400, description: 'Bad request - Invalid input data'),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    /**
     * @throws ArrayWithMixedKeysException
     * @throws ApiException
     * @throws ConfigurationNotFoundException
     * @throws IncompatibleTypeException
     */
    public function bulkUpdateStatus(BulkUpdateStatusRequest $request): Response
    {
        $this->bulkUpdateStatusService->call($request->validated());

        return $this->respond();
    }

    #[OA\Put(
        path: '/api/admin/checking-request/bulk-assign-inspector',
        description: 'Bulk assign inspectors to multiple checking requests and send notification emails.',
        summary: 'Bulk assign inspectors',
        security: [['access_token' => []]],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(
                properties: [
                    new OA\Property(
                        property: 'data',
                        type: 'array',
                        items: new OA\Items(
                            properties: [
                                new OA\Property(property: 'id', description: 'Checking request ID', type: 'integer', example: 123),
                                new OA\Property(property: 'inspector_id', description: 'Inspector ID to assign', type: 'integer', example: 5)
                            ]
                        )
                    )
                ]
            )
        ),
        tags: ['Checking Request'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful operation',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'message', type: 'string', example: 'Inspectors assigned successfully')
                    ]
                )
            ),
            new OA\Response(response: 400, description: 'Bad request - validation failed'),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function bulkAssignInspector(BulkAssignInspectorRequest $request): Response
    {
        $this->bulkAssignInspectorService->call($request->validated());

        return $this->respond();
    }

    #[OA\Get(
        path: '/api/admin/checking-request/export-csv',
        description: 'Export checking requests to CSV file with filtering capabilities.',
        summary: 'Export checking requests to CSV',
        security: [['access_token' => []]],
        tags: ['Checking Request'],
        parameters: [
            new OA\Parameter(
                name: 't_before_check-id-equal',
                description: 'Filter by checking request ID',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'integer', example: 123)
            ),
            new OA\Parameter(
                name: 't_aa_date-t_aa_place_id-equal',
                description: 'Filter by auction place ID',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'integer', example: 86)
            ),
            new OA\Parameter(
                name: 't_before_check-st_cd-equal',
                description: 'Filter by status code',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '0')
            ),
            new OA\Parameter(
                name: 't_before_check-t_inspector_id-equal',
                description: 'Filter by inspector ID',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'integer', example: 5)
            ),
            new OA\Parameter(
                name: 't_before_check-aa_no-equal',
                description: 'Filter by exact auction number',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'AA2024001')
            ),
            new OA\Parameter(
                name: 'date_mode',
                description: 'Date filtering mode (1: 開催日 Event Date, 2: 申込日時 Order Date, 3: 検査員依頼日時 Inspector Request Date)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', enum: ['1', '2', '3'], example: '2')
            ),
            new OA\Parameter(
                name: 'sdate',
                description: 'Start date for date range filtering (YYYY-MM-DD)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', format: 'date', example: '2024-01-01')
            ),
            new OA\Parameter(
                name: 'edate',
                description: 'End date for date range filtering (YYYY-MM-DD)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', format: 'date', example: '2024-12-31')
            ),
            new OA\Parameter(
                name: 't_before_check-odr_person-like',
                description: 'Search by order person name (partial match)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'John')
            ),
            new OA\Parameter(
                name: 't_before_check-car_name-like',
                description: 'Search by car name (partial match)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'Toyota')
            ),
            new OA\Parameter(
                name: 'customerName',
                description: 'Search by customer name (searches both Japanese and English names)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '田中')
            ),
            new OA\Parameter(
                name: 'orderOption',
                description: 'Sort field option (1: Customer Name JP, 2: AA Place, 3: AA No, 4: Order Date, 5: Open Date, 6: Car Name)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', enum: ['1', '2', '3', '4', '5', '6'], example: '4')
            ),
            new OA\Parameter(
                name: 'sortType',
                description: 'Sort direction (asc: ascending, desc: descending)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', enum: ['asc', 'desc'], example: 'asc')
            ),
            new OA\Parameter(
                name: 'token',
                description: 'CSV export token (one-time use token generated from generate-token endpoint)',
                in: 'query',
                required: true,
                schema: new OA\Schema(type: 'string', example: '550e8400-e29b-41d4-a716-************')
            ),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'CSV file downloaded successfully',
                content: new OA\MediaType(
                    mediaType: 'text/csv',
                    schema: new OA\Schema(type: 'string', format: 'binary')
                )
            ),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function exportCsv(CsvCheckingRQRequest $request): Response
    {
        return $this->exportCsvCheckingRequestService->call($request->validated());
    }

    #[OA\Post(
        path: '/api/admin/checking-request/export-csv/generate-token',
        summary: 'Generate CSV Export Token',
        description: 'Generate a one-time use token for CSV export. This token is required to access the CSV export endpoint.',
        security: [['access_token' => []]],
        tags: ['Checking Request'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Token generated successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'message', type: 'string', example: 'Success'),
                        new OA\Property(
                            property: 'data',
                            properties: [
                                new OA\Property(
                                    property: 'token',
                                    type: 'string',
                                    description: 'UUID token for CSV export (one-time use)',
                                    example: '550e8400-e29b-41d4-a716-************'
                                )
                            ],
                            type: 'object'
                        )
                    ]
                )
            ),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function generateCsvToken(): Response
    {
        $token = $this->exportCsvTokenService->call();

        return $this->respond([
            'token' => $token
        ]);
    }

    /**
     * @throws ArrayWithMixedKeysException
     * @throws IncompatibleTypeException
     * @throws ConfigurationNotFoundException
     * @throws Exception
     */
    #[OA\Post(
        path: '/api/admin/checking-request',
        description: 'Create a new checking request with the provided data.',
        summary: 'Create checking request',
        security: [['access_token' => []]],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(
                required: ['data'],
                properties: [
                    new OA\Property(
                        property: 'data',
                        required: ['t_aa_date_id', 'odr_person', 'odr_tel', 'odr_mail', 'aa_no', 'photo_flg', 'optn2_flg'],
                        properties: [
                            new OA\Property(property: 't_aa_date_id', description: 'Auction date ID', type: 'integer', example: 200),
                            new OA\Property(property: 'm_customer_id', description: 'Customer ID (optional)', type: 'integer', example: 100, nullable: true),
                            new OA\Property(property: 'odr_person', description: 'Order person name', type: 'string', example: 'John Doe'),
                            new OA\Property(property: 'odr_tel', description: 'Order telephone', type: 'string', example: '+81-3-1234-5678'),
                            new OA\Property(property: 'odr_mail', description: 'Order email', type: 'string', format: 'email', example: '<EMAIL>'),
                            new OA\Property(property: 'aa_no', description: 'Auction number', type: 'string', example: 'AA2024001'),
                            new OA\Property(property: 'car_name', description: 'Car name (optional)', type: 'string', example: 'Toyota Camry', nullable: true),
                            new OA\Property(property: 'note', description: 'Note (optional)', type: 'string', example: 'Special inspection notes', nullable: true),
                            new OA\Property(property: 'cc_note', description: 'CC note (optional)', type: 'string', example: 'Customer care notes', nullable: true),
                            new OA\Property(property: 'photo_flg', description: 'Photo flag', type: 'boolean', example: true),
                            new OA\Property(property: 'optn1_flg', description: 'Option 1 flag (optional)', type: 'boolean', example: true, nullable: true),
                            new OA\Property(property: 'optn2_flg', description: 'Option 2 flag', type: 'boolean', example: false),
                        ],
                        type: 'object'
                    )
                ]
            )
        ),
        tags: ['Checking Request'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Checking request created successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'data', type: 'object', example: null, nullable: true),
                        new OA\Property(property: 'message', type: 'string', example: 'Checking request created successfully', nullable: true)
                    ]
                )
            ),
            new OA\Response(response: 400, description: 'Bad request - validation failed'),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function createCheckingRequest(CreateCheckingRQRequest $request): Response
    {
        $this->createCheckingRequestService->call($request->validated());

        return $this->respond();
    }

    /**
     * Confirm checking request creation
     *
     * Validates if a checking request can be created with the provided data
     * by checking for existing entries with the same parameters.
     *
     * @param ConfirmCreateCheckingRQRequest $request
     * @return Response
     */
    #[OA\Post(
        path: '/api/admin/checking-request/confirm-create',
        description: 'Confirm if a checking request can be created with the provided data. Returns true if a similar request already exists.',
        summary: 'Confirm checking request creation',
        security: [['access_token' => []]],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(
                required: ['data'],
                properties: [
                    new OA\Property(
                        property: 'data',
                        required: ['t_aa_date_id', 'm_customer_id', 'aa_no'],
                        properties: [
                            new OA\Property(property: 't_aa_date_id', description: 'Auction date ID', type: 'integer', example: 200),
                            new OA\Property(property: 'm_customer_id', description: 'Customer ID', type: 'integer', example: 100),
                            new OA\Property(property: 'aa_no', description: 'Auction number', type: 'string', example: 'AA2024001'),
                        ],
                        type: 'object'
                    )
                ]
            )
        ),
        tags: ['Checking Request'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Confirmation check completed successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(
                            property: 'data',
                            properties: [
                                new OA\Property(property: 'isExit', description: 'Whether a similar checking request already exists', type: 'boolean', example: false)
                            ],
                            type: 'object'
                        ),
                        new OA\Property(property: 'message', type: 'string', example: 'Confirmation check completed', nullable: true)
                    ]
                )
            ),
            new OA\Response(response: 400, description: 'Bad request - validation failed'),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function confirmCreate(ConfirmCreateCheckingRQRequest $request): Response
    {
        $isExit = $this->confirmCreateCheckingRequestService->call($request->validated());

        return $this->respond([
            'isExit' => $isExit
        ]);
    }

    /**
     * @throws ArrayWithMixedKeysException
     * @throws IncompatibleTypeException
     * @throws ConfigurationNotFoundException
     * @throws Exception
     */
    #[OA\Put(
        path: '/api/admin/checking-request/{id}',
        description: 'Update an existing checking request with the provided data.',
        summary: 'Update checking request',
        security: [['access_token' => []]],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(
                required: ['aa_no', 'photo_flg', 'optn1_flg', 'optn2_flg', 'st_cd'],
                properties: [
                    new OA\Property(property: 'aa_no', description: 'Auction number', type: 'string', example: 'AA2024001'),
                    new OA\Property(property: 'car_name', description: 'Car name (optional)', type: 'string', example: 'Toyota Camry', nullable: true),
                    new OA\Property(property: 'note', description: 'Note (optional)', type: 'string', example: 'Special inspection notes', nullable: true),
                    new OA\Property(property: 'cc_note', description: 'CC note (optional)', type: 'string', example: 'Customer care notes', nullable: true),
                    new OA\Property(property: 'photo_flg', description: 'Photo flag', type: 'boolean', example: true),
                    new OA\Property(property: 'optn1_flg', description: 'Option 1 flag', type: 'boolean', example: true),
                    new OA\Property(property: 'optn2_flg', description: 'Option 2 flag', type: 'boolean', example: false),
                    new OA\Property(property: 'st_cd', description: 'Status code', type: 'integer', example: 1),
                    new OA\Property(property: 't_inspector_id', description: 'Inspector ID (optional)', type: 'integer', example: 5, nullable: true),
                ]
            )
        ),
        tags: ['Checking Request'],
        parameters: [
            new OA\Parameter(
                name: 'id',
                description: 'Checking request ID',
                in: 'path',
                required: true,
                schema: new OA\Schema(type: 'integer', example: 123)
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Checking request updated successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'data', type: 'object', example: null, nullable: true),
                        new OA\Property(property: 'message', type: 'string', example: 'Checking request updated successfully', nullable: true)
                    ]
                )
            ),
            new OA\Response(response: 400, description: 'Bad request - validation failed'),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 404, description: 'Checking request not found'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function updateCheckingRequest(int $id, UpdateCheckingRQRequest $request): Response
    {
        $this->updateCheckingRequestService->call($id, $request->validated());

        return $this->respond();
    }
}
