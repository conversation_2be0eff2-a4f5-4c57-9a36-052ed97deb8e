<?php

declare(strict_types=1);

namespace App\Models\sqlServer\Trait\Area;

use Illuminate\Database\Eloquent\Builder;

trait FilterTrait
{
    public function scopeIsAcdNotEmpty(Builder $query, mixed $isAcdNotEmpty): Builder
    {
        if ((bool) $isAcdNotEmpty) {
            return $query->whereNotNull('m_area.acd')->where('m_area.acd', '!=', '');
        }
        return $query;
    }

    public function scopeIsCdNotEmpty(Builder $query, mixed $isCdNotEmpty): Builder
    {
        if ((bool) $isCdNotEmpty) {
            return $query->whereNotNull('m_area.cd')->where('m_area.cd', '!=', '');
        }
        return $query;
    }

    public function scopeIsPcdNotEmpty(Builder $query, mixed $isPcdNotEmpty): Builder
    {
        if ((bool) $isPcdNotEmpty) {
            return $query->whereNotNull('m_area.pcd')->where('m_area.pcd', '!=', '');
        }
        return $query;
    }
}
