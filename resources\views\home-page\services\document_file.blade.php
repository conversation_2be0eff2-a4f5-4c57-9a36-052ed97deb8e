@extends('app')

@section('title', __('home-page/document_file.page_title'))

@section('content')
    <x-services.container>
        <x-services.title-page
            titlePage="{{ __('home-page/document_file.title') }}"
            description1="{{ __('home-page/document_file.page_description1') }}"
            description2="{{ __('home-page/document_file.page_description2') }}"
        />
        <x-services.title subTitle="{{ __('home-page/document_file.service_descriptions') }}" />
        <x-services.good-content :content="__('home-page/document_file.good_content')" />
        <div class="mx-auto mt-[45px] w-full px-4">
            <x-services.how-to-use-content page="document_file" />
            <x-services.content-item title="{{ __('common.how_to_use') }}">
                <div
                    class="2sm:py-[4em] 2sm:text-base text-15 mt-[1em] flex flex-col flex-wrap items-center justify-around px-[1em] py-[2em] md:flex-row"
                >
                    <x-services.login-hubnet />
                    <i
                        class="fas fa-caret-right 2sm:my-[0.2em] text-50 rotate-90 transform font-black text-orange-400 md:my-0 md:rotate-0 md:transform-none"
                    ></i>
                    <div class="2sm:w-1/2 2md:w-1/5 relative w-4/5 md:w-1/4">
                        <i
                            class="fas fa-hand-pointer text-50 absolute right-[10%] bottom-0 z-10 font-black text-white text-shadow-black"
                        ></i>
                        <div class="flex flex-wrap justify-between">
                            <div class="shadow-gray-150 mx-[1em] mb-[1em] w-full bg-white text-center">
                                <a
                                    href="{{ env('APP_URL_OLD', '') }}/hn/document/doc_file_list.asp?lan={{ app()->getLocale() }}"
                                    class="relative flex w-full flex-col items-center justify-center"
                                    target="_blank"
                                >
                                    <div
                                        class="absolute top-0 left-0 h-[30px] w-5"
                                        style="
                                            background: url('{{ asset('images/services/label_style08.png') }}')
                                                no-repeat;
                                            background-size: 20px 30px;
                                        "
                                    ></div>
                                    <img
                                        src="{{ asset('images/services/icon_menu_shorui.png') }}"
                                        alt="陸送発注"
                                        class="mt-[10px] w-15"
                                    />
                                    <p class="2sm:text-base text-red-450 pb-[1em] text-xs leading-normal font-bold">
                                        {!! __('home-page/document_file.click_on_car') !!}
                                    </p>
                                </a>
                            </div>
                        </div>
                    </div>
                    <i
                        class="fas fa-caret-right 2sm:my-[0.2em] rotate-90 transform text-6xl font-black text-orange-400 md:my-0 md:rotate-0 md:transform-none"
                    ></i>
                    <div class="2sm:w-1/2 2md:w-1/5 relative w-4/5 md:w-1/4">
                        <i
                            class="fas fa-hand-pointer text-50 absolute right-[10%] bottom-[-20px] z-10 font-black text-white text-shadow-black"
                        ></i>
                        <div class="flex flex-wrap justify-between">
                            <div class="bg-sky-350 mx-[1em] mb-[1em] w-full rounded-sm text-center">
                                <p class="2sm:text-base text-xs leading-[3em] font-bold text-white">
                                    {{ __('common.search') }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </x-services.content-item>
            <x-services.content-item title="{{ __('common.functions') }}">
                <x-services.service-function-item
                    :index="1"
                    :content="__('home-page/document_file.function_title_1')"
                    icon="fas fa-lightbulb"
                    image="doc_file_01.jpg"
                />
                <x-services.service-function-item
                    :index="2"
                    :content="__('home-page/document_file.function_title_2')"
                    icon="fas fa-file-download"
                    :image="App::getLocale() === 'ja' ? 'doc_file_02.jpg' : 'doc_file_02en.jpg'"
                />
                <x-services.service-function-item
                    :index="3"
                    :content="__('home-page/document_file.function_title_3')"
                    icon="fas fa-seedling"
                    image="doc_file_03.jpg"
                />
            </x-services.content-item>
        </div>
        <x-services.service-options
            :description="__('home-page/document_file.hubnet_order')"
            iconRight="icon_menu_shorui.png"
            :titleRight="__('home-page/document_file.shipping_infomation')"
            contentRight=""
            linkRight="{{env('APP_URL_OLD', '')}}/hn/document/doc_file_list.asp?lan={{app()->getLocale()}}"
        />
    </x-services.container>
@endsection
