<?php

declare(strict_types=1);

namespace App\Services\LoadingOrder;

use App\Repositories\sqlServer\LoadingOrderRepository;
use App\Repositories\sqlServer\LoadingOrderSpecialNoteRepository;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class UpdateLoadingOrderService
{
    private LoadingOrderRepository $loadingOrderRepository;
    private LoadingOrderSpecialNoteRepository $loadingOrderSpecialNoteRepository;

    public function __construct(
        LoadingOrderRepository $loadingOrderRepository,
        LoadingOrderSpecialNoteRepository $loadingOrderSpecialNoteRepository
    ) {
        $this->loadingOrderRepository = $loadingOrderRepository;
        $this->loadingOrderSpecialNoteRepository = $loadingOrderSpecialNoteRepository;
    }

    /**
     * @throws Exception
     */
    public function call(int $id, array $body): void
    {
        $data = $this->prepareLoadingOrderData($body);

        DB::transaction(function () use ($id, $body, $data): void {
            $this->loadingOrderRepository->update($id, $data);

            $this->saveSpecialNote($body);
        });
    }

    private function prepareLoadingOrderData(array $body): array
    {
        $now = Carbon::now();

        return [
            'up_date' => $now->format('Y-m-d H:i:s'),
            'up_owner' => (string) auth()->user()->id,

            'odr_kbn' => (int) $body['odr_kbn'],
            'st_cd' => (int) $body['st_cd'],
            'car_no' => isset($body['car_no']) ? (string) $body['car_no'] : '' ,
            'car_name' => isset($body['car_name']) ? (string) $body['car_name'] : '',
            'customs_flg' => isset($body['customs_flg']) ? (int) $body['customs_flg'] : 0,
            'to_name' => isset($body['to_name']) ? (string) $body['to_name'] : '',
            'consignee_name' => isset($body['consignee_name']) ? (string) $body['consignee_name'] : null,
            'consignee_flg' => isset($body['consignee_flg']) ? (int) $body['consignee_flg'] : 0,
            'to_plan_date' => isset($body['to_plan_date']) ? (string) $body['to_plan_date'] : null,
            'to_plan_flg' => isset($body['to_plan_flg']) ? (int) $body['to_plan_flg'] : 0,
            'destination' => isset($body['destination']) ? (string) $body['destination'] : null,
            'port' => isset($body['port']) ? (string) $body['port'] : null,
            'mileage' => isset($body['mileage']) ? (string) $body['mileage'] : null,
            'fob_price' => isset($body['fob_price']) ? (string) $body['fob_price'] : null,
            'part_note' => isset($body['part_note']) ? (string) $body['part_note'] : null,
            'note' => isset($body['note']) ? (string) $body['note'] : null,
            'ref_no' => isset($body['ref_no']) ? (int) $body['ref_no'] : null,
            'car_year' => isset($body['car_year']) ? (string) $body['car_year'] : null,

            'optn2_sub_kbn' => isset($body['optn2_sub_kbn']) ? (int) $body['optn2_sub_kbn'] : null,
            'optn2_sub2_kbn' => isset($body['optn2_sub2_kbn']) ? (int) $body['optn2_sub2_kbn'] : null,
            'optn3_flg' => isset($body['optn3_flg']) ? (int) $body['optn3_flg'] : 0,
            'optn3_sub_txt' => isset($body['optn3_sub_txt']) ? (string) $body['optn3_sub_txt'] : null,
            'optn4_flg' => isset($body['optn4_flg']) ? (int) $body['optn4_flg'] : 0,
            'optn4_sub_kbn' => isset($body['optn4_sub_kbn']) ? (int) $body['optn4_sub_kbn'] : 0,
            'optn5_flg' => isset($body['optn5_flg']) ? (int) $body['optn5_flg'] : 0,
            'optn6_flg' => isset($body['optn6_flg']) ? (int) $body['optn6_flg'] : 0,
            'optn6_sub_kbn' => isset($body['optn6_sub_kbn']) ? (int) $body['optn6_sub_kbn'] : null,
            'optn7_flg' => isset($body['optn7_flg']) ? (int) $body['optn7_flg'] : 0,
            'optn8_flg' => isset($body['optn8_flg']) ? (int) $body['optn8_flg'] : 0,
            'optn10_flg' => isset($body['optn10_flg']) ? (int) $body['optn10_flg'] : 0,
            'optn11_flg' => isset($body['optn11_flg']) ? (int) $body['optn11_flg'] : 0,
            'optn14_flg' => isset($body['optn14_flg']) ? (int) $body['optn14_flg'] : null,
            'optn17_flg' => isset($body['optn17_flg']) ? (int) $body['optn17_flg'] : 0,
            'optn18_flg' => isset($body['optn18_flg']) ? (int) $body['optn18_flg'] : 0,
            'optn21_flg' => isset($body['optn21_flg']) ? (int) $body['optn21_flg'] : 0,
        ];
    }

    /**
     * Format boolean value to 0/1 for database
     */
    private function formatBoolean($value): int
    {
        return $value ? 1 : 0;
    }

    /**
     * @throws Exception
     */
    private function saveSpecialNote(array $body): void
    {
        $mCustomerId = $body['m_customer_id'] ?? null;
        $specialNote = $body['special_note'] ?? null;

        if (null === $specialNote || null === $mCustomerId) {
            return;
        }

        $existingRecord = $this->loadingOrderSpecialNoteRepository->findSpecialNoteByCustomerId($mCustomerId);
        $now = Carbon::now()->format('Y-m-d H:i:s');
        if ($existingRecord && isset($existingRecord->id)) {
            $this->loadingOrderSpecialNoteRepository->update($existingRecord->id, [
                'special_note' => $specialNote,
                'updated_at'   => $now,
            ]);
            return;
        }

        $this->loadingOrderSpecialNoteRepository->createOneWithRelations([
            'tt_id'        => $mCustomerId,
            'special_note' => $specialNote,
            'updated_at'   => $now,
        ]);
    }
}
