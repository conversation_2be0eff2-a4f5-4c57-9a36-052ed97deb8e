.navbar-toggler-icon {
    display: inline-block;
    width: 30px;
    height: 30px;
    vertical-align: middle;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(255, 255, 255, 1)' stroke-width='3' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E") !important;
}

.navbar-dropdown {
    z-index: 5;
    width: 100%;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.8);
    border: solid 1px #bd0a0a;
    margin-top: 8px;
    box-shadow: 0 10px 25px 0 rgba(0, 0, 0, 0.5);
    border-radius: 5px;
}

.navbar-dropdown_item {
    border-bottom: solid 1px #ddd;
    background: rgba(255, 255, 255, 0.8);
    z-index: 5;
    color: #343a40 !important;
    margin-right: 10px;
    margin-left: 10px;
    font-weight: bold;
    text-align: center;
    display: block;
    padding: 0.5rem 1rem;
}

.btn.contact_detail_btn{
    height: 55px;
    padding: 11px;
    font-size: 20px;
    width: 100%;
    background:linear-gradient(-10deg, #dc3545,#f93b4d,#dc3545);
    color:#fff;
    border: solid 2px #dc3545;
    border-radius: .25rem;
    font-weight:bold;
    transition: 0.3s ease-in-out;
}
.btn.contact_detail_btn:hover{
    font-size:22px;
    padding:9px;
    color:#fff;
    border: solid 2px #f93b4d;
    background:linear-gradient(10deg, #dc3545,#f93b4d,#dc3545);
}

@media screen and (max-width:576px) {
    .btn.contact_detail_btn{
        padding:14px;
        font-size:15px;
    }
    .btn.contact_detail_btn:hover{
        padding:14px;
        font-size:16px;
    }
}

@media screen and (min-width:576px) and (max-width:767px) {
    .btn.contact_detail_btn{
        padding:13px;
        font-size:18px;
    }
    .btn.contact_detail_btn:hover{
        padding:10px;
        font-size:20px;
    }
}

@media screen and (min-width: 768px) and (max-width: 991px){
    .btn.contact_detail_btn{
        width:115%;
        padding:13px;
        font-size:18px;
    }
    .btn.contact_detail_btn:hover{
        width:115%;
        padding:10px;
        font-size:20px;
    }
}

@media screen and (min-width:992px) and (max-width:1199px) {
    .btn.contact_detail_btn{
        width:145%;
        padding:13px;
        font-size:18px;
    }
    .btn.contact_detail_btn:hover{
        padding:10px;
        font-size:20px;
    }
}

@media screen and (min-width:1200px) and (max-width:1499px) {
    .btn.contact_detail_btn{
        width:135%;
    }
}

.nav-link-contact::before {
    content: '';
    display: block;
    background: url('data:image/svg+xml;charset=UTF-8,<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 512 512" xml:space="preserve"><path class="st0" d="M510.746,110.361c-2.128-10.754-6.926-20.918-13.926-29.463c-1.422-1.794-2.909-3.39-4.535-5.009c-12.454-12.52-29.778-19.701-47.531-19.701H67.244c-17.951,0-34.834,7-47.539,19.708c-1.608,1.604-3.099,3.216-4.575,5.067c-6.97,8.509-11.747,18.659-13.824,29.428C0.438,114.62,0,119.002,0,123.435v265.137c0,9.224,1.874,18.206,5.589,26.745c3.215,7.583,8.093,14.772,14.112,20.788c1.516,1.509,3.022,2.901,4.63,4.258c12.034,9.966,27.272,15.45,42.913,15.45h377.51c15.742,0,30.965-5.505,42.967-15.56c1.604-1.298,3.091-2.661,4.578-4.148c5.818-5.812,10.442-12.49,13.766-19.854l0.438-1.05c3.646-8.377,5.497-17.33,5.497-26.628V123.435C512,119.06,511.578,114.649,510.746,110.361z M34.823,99.104c0.951-1.392,2.165-2.821,3.714-4.382c7.689-7.685,17.886-11.914,28.706-11.914h377.51c10.915,0,21.115,4.236,28.719,11.929c1.313,1.327,2.567,2.8,3.661,4.272l2.887,3.88l-201.5,175.616c-6.212,5.446-14.21,8.443-22.523,8.443c-8.231,0-16.222-2.99-22.508-8.436L32.19,102.939L34.823,99.104z M26.755,390.913c-0.109-0.722-0.134-1.524-0.134-2.341V128.925l156.37,136.411L28.199,400.297L26.755,390.913z M464.899,423.84c-6.052,3.492-13.022,5.344-20.145,5.344H67.244c-7.127,0-14.094-1.852-20.142-5.344l-6.328-3.668l159.936-139.379l17.528,15.246c10.514,9.128,23.922,14.16,37.761,14.16c13.89,0,27.32-5.032,37.827-14.16l17.521-15.253L471.228,420.18L464.899,423.84z M485.372,388.572c0,0.803-0.015,1.597-0.116,2.304l-1.386,9.472L329.012,265.409l156.36-136.418V388.572z" fill="%23dc3545"></path></svg>') center center no-repeat;
    width: 1.25rem;
    height: 1.25rem;
}

.nav-link-contact-min::after {
    content: '';
    border-top: 4px solid white;
    border-right: 4px solid white;
    border-radius: 2px;
    transform: rotate(45deg) translateY(-50%);
    transition: 0.3s ease-in-out;
    position: absolute;
    top: 50%;
    right: 1.75rem;
    height: 0.625rem;
    width: 0.625rem;
    z-index: 1111;
}

.nav-link-contact-min.nav-link-contact::after {
    border-color: #dc3545 !important;
}

@media screen and (max-width: 480px) {
    .btn.contact_detail_btn {
        padding: 11px;
        line-height: 15px;
    }
}

@media screen and (max-width: 576px) {
    .btn.contact_detail_btn {
        padding: 14px;
        font-size: 15px;
    }
}
