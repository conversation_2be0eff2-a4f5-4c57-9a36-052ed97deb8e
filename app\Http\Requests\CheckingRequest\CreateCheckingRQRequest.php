<?php

declare(strict_types=1);

namespace App\Http\Requests\CheckingRequest;

use Illuminate\Foundation\Http\FormRequest;

class CreateCheckingRQRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            't_aa_date_id' => 'required|integer',
            'm_customer_id' => 'required|integer',
            'odr_person' => 'required|string|max:255',
            'odr_tel' => 'nullable|string|max:20',
            'odr_mail' => 'nullable|email|max:255',
            'aa_no' => 'required|string|max:255',
            'car_name' => 'nullable|string|max:255',
            'note' => 'nullable|string|max:255',
            'cc_note' => 'nullable|string',
            'photo_flg' => 'required|boolean',
            'optn1_flg' => 'required|boolean',
            'optn2_flg' => 'required|boolean',
        ];
    }
}
