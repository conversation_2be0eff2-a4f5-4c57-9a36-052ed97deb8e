$(function () {
    $('a[href^="#"]').click(function () {
        var href = $(this).attr("href");
        $('body,html').animate({scrollTop: $(href == "#" || href == "" ? 'html' : href).offset().top}, 400, 'swing');
        return false;
    });

    var red_left, red_top, yellow_left, yellow_top, blue_left, blue_top, start_flg = 1;
    let timer_red, timer_yellow, timer_blue;
    car_start();

    var win_w = (window.innerWidth), max_left, start_top;
    (win_w >= 1800) ? max_left = 1750 : (win_w >= 1600) ? max_left = 1550 : (win_w >= 1400) ? max_left = 1350 : (win_w >= 1200) ? max_left = 1150 : (win_w >= 992) ? max_left = 950 :
        (win_w >= 800) ? max_left = 750 : (win_w >= 768) ? max_left = 700 : (win_w >= 600) ? max_left = 550 : (win_w >= 576) ? max_left = 500 : (win_w >= 400) ? max_left = 340 : max_left = 260;
    (win_w >= 992) ? start_top = 795 : (win_w >= 768) ? start_top = 745 : (win_w >= 576) ? start_top = 720 : start_top = 475;
    $(window).scroll(function (e) {
        console.log($(window).scrollTop());
        if ($(window).scrollTop() > 100) {
            if (start_flg == 1) (clearInterval(timer_red), clearInterval(timer_yellow), clearInterval(timer_blue), start_flg = 0);
            $("#ah_service_car_red_baloon").fadeIn(500)
            $("#ah_service_car_yellow_baloon").fadeIn(500)
            $("#ah_service_car_blue_baloon").fadeIn(500)
            red_left = $('#ah_service_car_red').css('left').slice(0, -2);
            red_top = win_w >= 576 ? start_top + ($(window).scrollTop() - 100) * ((5500 + start_top) / 7100) : start_top + ($(window).scrollTop() - 100) * ((5500 - start_top) / 5300);
            $('#ah_service_car_red').css('left', red_left);
            $('#ah_service_car_red').css('top', red_top);
            win_w >= 576 ? ($('#ah_service_car_red_baloon').css('left', parseInt(red_left) + 6), $('#ah_service_car_red_baloon').css('top', red_top - 104)) : ($('#ah_service_car_red_baloon').css('left', parseInt(red_left)), $('#ah_service_car_red_baloon').css('top', red_top - 73));
            yellow_left = $('#ah_service_car_yellow').css('left').slice(0, -2);
            yellow_top = win_w >= 576 ? start_top + ($(window).scrollTop() - 100) * ((5500 + start_top) / 6400) : start_top + ($(window).scrollTop() - 100) * ((5500 - start_top) / 5000);
            $('#ah_service_car_yellow').css('left', yellow_left);
            $('#ah_service_car_yellow').css('top', yellow_top);
            $('#ah_service_car_yellow_baloon').css('left', yellow_left - 6);
            win_w >= 576 ? $('#ah_service_car_yellow_baloon').css('top', yellow_top - 103) : $('#ah_service_car_yellow_baloon').css('top', yellow_top - 73);
            blue_left = $('#ah_service_car_blue').css('left').slice(0, -2);
            blue_top = win_w >= 576 ? start_top + ($(window).scrollTop() - 100) * ((5500 + start_top) / 6800) : start_top + ($(window).scrollTop() - 100) * ((5500 - start_top) / 5120);
            $('#ah_service_car_blue').css('left', blue_left);
            $('#ah_service_car_blue').css('top', blue_top);
            $('#ah_service_car_blue_baloon').css('left', blue_left - 6);
            win_w >= 576 ? $('#ah_service_car_blue_baloon').css('top', blue_top - 103) : $('#ah_service_car_blue_baloon').css('top', blue_top - 78);
        } else {
            $("#ah_service_car_red_baloon").fadeOut(500);
            $("#ah_service_car_yellow_baloon").fadeOut(500);
            $("#ah_service_car_blue_baloon").fadeOut(500);
            if (start_flg == 0) (car_start(), start_flg = 1);
        }
    });

    function car_start() {
        timer_red = setInterval(function () {
            const carRed = $('#ah_service_car_red');
            red_left = carRed.css('left').slice(0, -2);
            carRed.css('top', start_top);
            red_left > 1 ? carRed.css('left', red_left - 10) : carRed.css('left', max_left);
        }, 100);
        timer_yellow = setInterval(function () {
            yellow_left = $('#ah_service_car_yellow').css('left').slice(0, -2);
            $('#ah_service_car_yellow').css('top', start_top);
            yellow_left > 1 ? $('#ah_service_car_yellow').css('left', yellow_left - 8) : $('#ah_service_car_yellow').css('left', max_left);
        }, 100);
        timer_blue = setInterval(function () {
            blue_left = $('#ah_service_car_blue').css('left').slice(0, -2);
            $('#ah_service_car_blue').css('top', start_top);
            blue_left > 1 ? $('#ah_service_car_blue').css('left', blue_left - 6) : $('#ah_service_car_blue').css('left', max_left);
        }, 100);
    }

    $('#add_line_close, #add_line_close_768').click(function () {
        $('#add_line_close').css('display', 'none');
        $('#add_line').css('display', 'none');
        $('#add_line_hover').css('display', 'none');
        $('#add_line_1500').css('display', 'none');
        $('#add_line_hover_1500').css('display', 'none');
        $('#add_line_768').css('display', 'none');
        $('#add_line_hover_768').css('display', 'none');
        $('#add_line_close_768').css('display', 'none');
        $('#add_line_500').css('display', 'none');
    });
});
