<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class LogViewerAuthMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  Request  $request
     * @param  Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): mixed
    {
        // Unblock access to log-viewer in local with path log-viewer
        if (str_starts_with($request->path(), 'log-viewer')   && app()->environment(['local', 'dev', 'development'])) {
            // Handle HTTP Basic authentication
            return $this->handleHttpBasicAuth($request, $next);
        }

        // Allow access in other environments
        return $next($request);
    }

    /**
     * Handle HTTP Basic authentication
     *
     * @param  Request  $request
     * @param  Closure  $next
     * @return mixed
     */
    protected function handleHttpBasicAuth(Request $request, Closure $next): mixed
    {
        $authenticationHasPassed = false;

        // Check authentication information from header
        if ($request->header('PHP_AUTH_USER', null) && $request->header('PHP_AUTH_PW', null)) {
            $username = $request->header('PHP_AUTH_USER');
            $password = $request->header('PHP_AUTH_PW');

            // Verify authentication information from environment variables
            if ($username === env('PHP_AUTH_USER') && $password === env('PHP_AUTH_PW')) {
                $authenticationHasPassed = true;
            }
        }

        // If authentication fails, return 401 error and request authentication
        if (false === $authenticationHasPassed) {
            return response()->make('Unauthorized. Authentication required.', 401, [
                'WWW-Authenticate' => 'Basic realm="Log Viewer Authentication"'
            ]);
        }

        // If authentication succeeds, allow access
        return $next($request);
    }
}
