<?php

declare(strict_types=1);

namespace App\Http\Resources\Transport;

use App\Traits\CommonTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TransportEditDetailResource extends JsonResource
{
    use CommonTrait;
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $transportData = collect($this->resource->toArray())->filter(fn ($value) => !is_array($value) && !is_object($value))->map(function ($value) {
            if (is_string($value)) {
                return trim($value);
            }
            return $value;
        });

        $transportData['customer_name'] = $this->cus_name_JP;
        $transportData['trans_file_names'] = $this->file_info;
        unset($transportData['cus_name_JP']);

        return [
            'transport' => $transportData,
            'as_data' => collect([
                'm_customer_id' => $this?->m_customer_id,
                'as_ref_no' => $this?->vehicleTransport?->HNCC03,
                'as_reg_date' => $this->parseDateYmdToDMY($this?->vehicleTransport?->HNTD07 ?? ''),
                'as_veh_name' => $this?->vehicleTransport?->HNNN54,
                'as_chassis_no' => $this?->vehicleTransport?->HNCC54,
                'as_carry_destination' => $this?->vehicleTransport?->HNNO68,
                'as_cnty_destination' => $this?->vehicleTransport?->HNSKNM,
                'as_vessel_name' => $this?->vehicleTransport?->HNNN47,
                'as_voy_no' => $this?->vehicleTransport?->HNNR13,
                'as_agent_ref_no' => $this?->vehicleTransport?->HNASRN,
                'as_agent' => $this?->vehicleTransport?->HNNO79,
                'as_buyer' => $this?->vehicleTransport?->HNFCSN,
                'as_etd' => $this->parseDateYmdToDMY($this?->vehicleTransport?->HNTD14 ?? ''),
                'as_eta' => $this->parseDateYmdToDMY($this?->vehicleTransport?->HNTD79 ?? ''),
                'as_l_port' => $this?->vehicleTransport?->HNNPNM,
                'as_d_port' => $this?->vehicleTransport?->HNNO25,
                'as_doc_receiving_date' => $this->parseDateYmdToDMY($this?->vehicleTransport?->HNTD44 ?? ''),
                'as_carrying_date' => $this->parseDateYmdToDMY($this?->vehicleTransport?->HNTD24 ?? ''),
                'as_keep_scheduled_date' => $this->parseDateYmdToDMY($this?->vehicleTransport?->HNFL71 ?? ''),
                'as_veh_location' => $this?->vehicleTransport?->HNFL95,
                'as_plan_name' => $this?->vehicleTransport?->HNNC25,
                'as_maf_rslt' => $this?->vehicleTransport?->HNMFKK,
            ])->map(fn ($value) => is_string($value) ? trim($value) : $value),
            'transport_notes' => $this->transportNotes,
        ];
    }
}
