<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\File;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;

class GenerateSitemap extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sitemap:generate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate sitemap';

    private $postIds = [
        1027, 1080, 1087, 1089, 1094, 1096, 1119, 1221, 1302,
        1465, 1467, 1496, 1510, 1531, 1536, 1564, 1566, 1591,
        1593, 1667, 944, 969
    ];

    private $tags = [
        '',
        'アフリカ',
        'オセアニア',
        'オーストラリア',
        'ニュージーランド',
        '中古車',
        '中古車輸出',
        '中東',
        '船積み',
    ];

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $sitemap = Sitemap::create();

        $locales = ['en', 'ja'];

        foreach ($locales as $locale) {
            $sitemap->add(Url::create("/{$locale}")->setLastModificationDate(Carbon::now()));
            $sitemap->add(Url::create("/{$locale}/question"));
            $sitemap->add(Url::create("/{$locale}/recruit"));
            $sitemap->add(Url::create("/{$locale}/hubnet-introduction"));
            $sitemap->add(Url::create("/{$locale}/service"));
            $sitemap->add(Url::create("/{$locale}/service/shipping-export-import"));
            $sitemap->add(Url::create("/{$locale}/service/d2d-package"));
            $sitemap->add(Url::create("/{$locale}/service/inland-transport"));
            $sitemap->add(Url::create("/{$locale}/service/insurance"));
            $sitemap->add(Url::create("/{$locale}/service/car-shipping-info"));
            $sitemap->add(Url::create("/{$locale}/service/overseas-documents"));
            $sitemap->add(Url::create("/{$locale}/service/recall-repair"));
            $sitemap->add(Url::create("/{$locale}/service/auction-vehicle-check"));
            $sitemap->add(Url::create("/{$locale}/service/photo-condition"));
            $sitemap->add(Url::create("/{$locale}/service/repair-easywork-inspection"));
            $sitemap->add(Url::create("/{$locale}/service/document-file"));
            $sitemap->add(Url::create("/{$locale}/sailing-schedule"));
            $sitemap->add(Url::create("/{$locale}/autohub-yard-list"));
            $sitemap->add(Url::create("/{$locale}/company"));
            $sitemap->add(Url::create("/{$locale}/autohub-blog-list"));
            $sitemap->add(Url::create("/{$locale}/autohub-news-list"));
            $sitemap->add(Url::create("/{$locale}/contact"));
            foreach ($this->postIds as $postId) {
                $sitemap->add(Url::create("/{$locale}/autohub-post-view?postid={$postId}"));
            }
            foreach ($this->tags as $tag) {
                $sitemap->add(Url::create("/{$locale}/autohub-blog-list?tags={$tag}"));
            }
        }

        $sitemap->writeToFile(public_path('sitemap.xml'));

        if (File::exists(public_path('sitemap.xml'))) {
            chmod(public_path('sitemap.xml'), 0644);
        }
        $this->overwriteRobotsFiel();
    }

    private function overwriteRobotsFiel(): void
    {
        $domain = config('app.url');
        $robotsPath = public_path('robots.txt');

        $content = <<<TXT
User-agent: *
Disallow:

Sitemap: {$domain}/sitemap.xml
TXT;

        File::put($robotsPath, $content);
    }
}
