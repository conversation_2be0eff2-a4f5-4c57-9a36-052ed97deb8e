<?php

declare(strict_types=1);

namespace App\Models\sqlServer;

use Illuminate\Database\Eloquent\Model;

/**
 * Inspector Model
 *
 * Handles list of inspectors/inspection staff
 * Maps to database table: t_inspector
 */
class TransFeeSpecEikou extends Model
{
    public const CREATED_AT = 'reg_date';
    public const UPDATED_AT = 'up_date';

    protected $fillable = [
        'del_flg',
        'reg_date',
        'up_date',
        'up_owner',
        't_aa_place_id',
        'col1',
        'col2',
        'col3',
        'col4',
        'col5',
        'col6',
        'col7',
        'col8',
        'col9',
        'col10',
        'col11',
        'col12',
    ];
    protected $table = 't_trans_fee_spec_eikou';
}
