<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\ToPlace\GetDataDropdownToPlaceRequest;
use App\Http\Resources\ToPlace\ToPlaceDropdownResource;
use App\Services\ToPlace\ToPlaceService;
use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Response;

class ToPlaceController extends Controller
{
    private ToPlaceService $toPlaceService;

    public function __construct(ToPlaceService $toPlaceService)
    {
        $this->toPlaceService = $toPlaceService;
    }

    #[OA\Get(
        path: '/api/admin/to-places/list-active',
        summary: 'Get list of active to places',
        description: 'Retrieve a list of all active destination places for dropdown/selection purposes',
        security: [['access_token' => []]],
        tags: ['To Place Management'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful operation',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'code', type: 'integer', example: 200),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Success'),
                        new OA\Property(
                            property: 'data',
                            type: 'array',
                            items: new OA\Items(
                                properties: [
                                    new OA\Property(property: 'id', type: 'integer', example: 1, description: 'To place ID'),
                                    new OA\Property(property: 'name', type: 'string', example: 'Tokyo Port', description: 'To place name')
                                ]
                            )
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 401,
                description: 'Unauthorized - Invalid or missing access token',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 401),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Unauthorized')
                    ]
                )
            ),
            new OA\Response(
                response: 403,
                description: 'Forbidden - Insufficient permissions',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 403),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Forbidden')
                    ]
                )
            )
        ]
    )]
    public function getToPlaceActive(): Response
    {
        return $this->respond(ToPlaceDropdownResource::collection($this->toPlaceService->getToPlaceActive()));
    }

    #[OA\Get(
        path: '/api/admin/to-places/dropdown',
        summary: 'Get ToPlace data for dropdown',
        description: 'Retrieve a list of destination places for dropdown selection with filtering and sorting capabilities. Supports single or multiple sort conditions.',
        tags: ['To Place Management'],
        parameters: [
            new OA\Parameter(
                name: 't_to_place-sp_flg-equal',
                in: 'query',
                description: 'Filter by special flag. Use 1 for active, 0 for inactive.',
                required: false,
                schema: new OA\Schema(type: 'integer', enum: [0, 1], example: 1)
            ),
            new OA\Parameter(
                name: 'sort',
                in: 'query',
                description: 'Sorting parameter(s). Format: "column|direction" or multiple sorts separated by comma. Examples: "sort_no|asc", "name|desc", "sort_no|asc,name|desc"',
                required: false,
                schema: new OA\Schema(
                    type: 'string',
                    nullable: true,
                    example: 'sort_no|asc',
                    pattern: '^([a-zA-Z0-9_]+\\|(asc|desc))(,\\s*[a-zA-Z0-9_]+\\|(asc|desc))*$'
                )
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful operation',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'code', type: 'integer', example: 200),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Success'),
                        new OA\Property(
                            property: 'data',
                            type: 'array',
                            items: new OA\Items(
                                properties: [
                                    new OA\Property(property: 'id', type: 'integer', example: 1, description: 'To place ID'),
                                    new OA\Property(property: 'name', type: 'string', example: 'Tokyo Port', description: 'To place name'),
                                    new OA\Property(property: 'addr', type: 'string', example: '123 Main St', description: 'Address'),
                                    new OA\Property(property: 'tel', type: 'string', example: '+81-123-4567', description: 'Telephone'),
                                    new OA\Property(property: 'sort_no', type: 'integer', example: 1, description: 'Sort order number')
                                ]
                            )
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 400,
                description: 'Bad Request - Invalid sort parameter format',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 400),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'The sort field must be in the format "column|asc" or "column|desc".')
                    ]
                )
            ),
            new OA\Response(
                response: 401,
                description: 'Unauthorized - Invalid or missing access token',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 401),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Unauthorized')
                    ]
                )
            ),
            new OA\Response(
                response: 403,
                description: 'Forbidden - Insufficient permissions',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 403),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Forbidden')
                    ]
                )
            ),
            new OA\Response(
                response: 500,
                description: 'Internal Server Error - Database error',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 500),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Internal Server Error'),
                        new OA\Property(
                            property: 'error',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'code', type: 'string', example: 'QueryException'),
                                new OA\Property(property: 'message', type: 'string', example: 'Database query error')
                            ]
                        )
                    ]
                )
            )
        ]
    )]
    public function getDataDropdown(GetDataDropdownToPlaceRequest $request): Response
    {
        return $this->respond($this->toPlaceService->getDataDropdown($request->validated()));
    }
}
