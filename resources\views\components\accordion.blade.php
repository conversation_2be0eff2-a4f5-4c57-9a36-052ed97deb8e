<div class="mt-4 lg:mt-8">
    @foreach ($items as $index => $qa)
        <div
            class="group {{ $index % 2 === 0 ? 'border-orange-500 bg-orange-500 hover:bg-orange-500/80' : 'border-orange-200 bg-orange-200 hover:bg-orange-200/80' }}"
        >
            <!-- Accordion Header -->
            <div
                class="flex cursor-pointer items-center justify-between px-4 py-4 md:px-6 lg:px-12"
                onclick="toggleAccordion({{ $qa['id'] }})"
            >
                <div class="flex items-center">
                    @if($qa['showIcon'] ?? true)
                        <i class="fab fa-quora mr-3 text-3xl font-bold text-white"></i>
                    @endif
                    <span class="text-sm font-bold text-white md:text-15">
                        {{ $qa['question'] }}
                    </span>
                </div>
                <div class="transform">
                    <svg
                        class="accordion-icon-{{ $qa['id'] }} h-7 w-7 -rotate-45 {{ $defaultOpen && $index === 0 ? '-rotate-[360deg]' : '' }} border border-white p-0.5 transition-all duration-200 ease-in-out"
                        fill="none"
                        stroke="white"
                        viewBox="0 0 24 24"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="4"
                            class="minus-icon-{{ $qa['id'] }} {{ $defaultOpen && $index === 0 ? '' : 'hidden' }}"
                            d="M20 12H4"
                        />
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="4"
                            class="plus-icon-{{ $qa['id'] }} {{ $defaultOpen && $index === 0 ? 'hidden' : '' }}"
                            transform="rotate(45, 12, 12)"
                            d="M12 4v16m8-8H4"
                        />
                    </svg>
                </div>
            </div>

            <!-- Accordion Content -->
            <div
                class="accordion-content accordion-panel-{{ $qa['id'] }} {{ $index % 2 === 0 ? 'border-orange-500 group-hover:border-orange-500/80' : 'border-orange-200 group-hover:border-orange-200/80' }} box-content overflow-hidden border-2 border-t-0 border-solid bg-white transition-[height,border-width] duration-500 will-change-[height,border-width]"
                style="height: {{ $defaultOpen && $index === 0 ? 'auto' : '0px' }}"
            >
                <div
                    class="accordion-content-{{ $qa['id'] }} flex h-[100%] items-center p-2 transition-all duration-500 will-change-transform md:p-7 lg:p-7"
                    style="transform: {{ $defaultOpen && $index === 0 ? 'translateY(0)' : 'translateY(-100%)' }}"
                >
                    <div class="flex h-[100%] w-full flex-col justify-center">
                        <p class="text-md text-sm text-gray-700 md:text-base">
                            {!! $qa['answer'] !!}
                        </p>
                        @if (isset($qa['link']))
                            <x-button
                                type="submit"
                                variant="primary"
                                size="lg"
                                href="{{ $qa['link']['url'] }}"
                                target="{{ isset($qa['link']['external']) && $qa['link']['external'] ? '_blank' : '_self' }}"
                                class="mt-4 text-15 !font-bold !p-[12px] h-[50px]"
                            >
                                {{ $qa['link']['text'] }}
                            </x-button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    @endforeach
</div>
@once
    @push('scripts')
        <script>
            function toggleAccordion(id) {
                const panel = document.querySelector(`.accordion-panel-${id}`)
                const content = document.querySelector(`.accordion-content-${id}`)
                const minusIcon = document.querySelector(`.minus-icon-${id}`)
                const plusIcon = document.querySelector(`.plus-icon-${id}`)
                const icon = document.querySelector(`.accordion-icon-${id}`)

                const allPanels = document.querySelectorAll('[class*="accordion-panel-"]')
                const allContents = document.querySelectorAll('[class*="accordion-content-"]')
                const allMinusIcons = document.querySelectorAll('[class*="minus-icon-"]')
                const allPlusIcons = document.querySelectorAll('[class*="plus-icon-"]')
                const allIcons = document.querySelectorAll('[class*="accordion-icon-"]')

                allPanels.forEach((otherPanel, index) => {
                    if (!otherPanel.classList.contains(`accordion-panel-${id}`)) {
                        otherPanel.style.height = '0px'
                        allContents[index].style.transform = 'translateY(-100%)'
                        allMinusIcons[index].classList.add('hidden')
                        allPlusIcons[index].classList.remove('hidden')
                        allIcons[index].classList.remove('-rotate-[360deg]')
                    }
                })
                // Toggle current accordion
                if (panel.style.height === '0px') {
                    panel.style.height = 'auto'
                    const actualHeight = panel.offsetHeight
                    panel.style.height = '0px'
                    panel.offsetHeight
                    panel.style.height = actualHeight + 'px'
                    content.style.transform = 'translateY(0)'
                    minusIcon.classList.remove('hidden')
                    plusIcon.classList.add('hidden')
                    icon.classList.add('-rotate-[360deg]')
                } else {
                    panel.style.height = '0px'
                    content.style.transform = 'translateY(-100%)'
                    minusIcon.classList.add('hidden')
                    plusIcon.classList.remove('hidden')
                    icon.classList.remove('-rotate-[360deg]')
                }
            }

            window.addEventListener('resize', () => {
                const activePanel = document.querySelector(
                    '[class*="accordion-panel-"][style*="height"]:not([style*="height: 0px"])',
                )
                if (activePanel) {
                    activePanel.style.height = 'auto'
                    activePanel.style.height = activePanel.offsetHeight + 'px'
                }
            })
        </script>
    @endpush
@endonce
