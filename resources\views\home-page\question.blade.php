@extends('app')

@section('title')
    {{ __('home-page/question.page_title') }}
@endsection

@push('styles')
    @vite('resources/css/question.css')
@endpush

@section('content')
    <div class="relative block pt-[57px] lg:pt-20">
        <img
            src="{{ asset('images/question_main_image.jpg') }}"
            alt="Question Banner"
            data-aos="fade-in"
            data-aos-delay="300"
            data-aos-duration="3000"
            class="ml-auto block w-[365px] sm:w-[317px] lg:absolute lg:top-20 lg:right-0 lg:w-auto xl:w-[620px]"
        />
        <div class="container mx-auto mt-0 lg:mt-22">
            <div class="mx-auto w-full px-4 lg:w-5xl" data-aos="zoom-in">
                <h1 class="text-2xs xl:text-22 text-center font-bold md:text-left md:text-xl">
                    {{ __('home-page/question.page_description') }}
                </h1>

                <section class="mt-6 lg:mt-9">
                    <h3 class="xl:text-35 text-center text-2xl font-bold md:text-3xl">
                        {{ __('home-page/question.common_questions') }}
                    </h3>
                    <x-accordion
                        :items="[
                            ['id' => '1', 'question' => __('home-page/question.question.question_1'), 'answer' => __('home-page/question.answer.answer_1')],
                            ['id' => '2', 'question' => __('home-page/question.question.question_2'), 'answer' => __('home-page/question.answer.answer_2')],
                            ['id' => '3', 'question' => __('home-page/question.question.question_3'), 'answer' => __('home-page/question.answer.answer_3'), 'link' => ['url' => app()->getLocale() === 'ja' ? '/service' : '/en/service', 'text' => __('button.services'), 'external' => false]],
                            ['id' => '4', 'question' => __('home-page/question.question.question_4'), 'answer' => __('home-page/question.answer.answer_4')],
                            ['id' => '5', 'question' => __('home-page/question.question.question_5'), 'answer' => __('home-page/question.answer.answer_5'), 'link' => ['url' => env('APP_URL_OLD', '').'/hn/login/?lan='.app()->getLocale(), 'text' => __('button.membership'), 'external' => true]],
                            ['id' => '6', 'question' => __('home-page/question.question.question_6'), 'answer' => __('home-page/question.answer.answer_6'), 'link' => ['url' => env('APP_URL_OLD', '').'/hn/login/?lan='.app()->getLocale(), 'text' => __('button.membership'), 'external' => true]],
                            ['id' => '7', 'question' => __('home-page/question.question.question_7'), 'answer' => __('home-page/question.answer.answer_7')],
                            ['id' => '8', 'question' => __('home-page/question.question.question_8'), 'answer' => __('home-page/question.answer.answer_8')],
                            ['id' => '9', 'question' => __('home-page/question.question.question_9'), 'answer' => __('home-page/question.answer.answer_9')],
                            ['id' => '10', 'question' => __('home-page/question.question.question_10'), 'answer' => __('home-page/question.answer.answer_10')],
                            ['id' => '11', 'question' => __('home-page/question.question.question_11'), 'answer' => __('home-page/question.answer.answer_11')],
                        ]"
                    />
                </section>

                <section class="mt-8 lg:mt-12">
                    <h3 class="text-center text-2xl font-bold md:text-3xl xl:text-[35px]">
                        {{ __('home-page/question.unfamiliar_with_export_import') }}
                    </h3>

                    <x-accordion
                        :items="[
                            ['id' => '12', 'question' => __('home-page/question.question.question_12'), 'answer' => __('home-page/question.answer.answer_12')],
                            ['id' => '13', 'question' => __('home-page/question.question.question_13'), 'answer' => __('home-page/question.answer.answer_13')],
                        ]"
                    />
                </section>

                <div class="mx-auto mt-10 mb-20 w-full md:w-[46%]">
                    <a
                        href="{{ app()->getLocale() === 'ja' ? route('post-view', ['postid' => 944]) : route('localized.post-view', ['locale' => 'en', 'postid' => 944]) }}"
                        class="complete-guide__button 2sm:p-[13px] 2sm:hover:p-[13px] 2md:hover:p-[10px] text-15 relative p-[14px] hover:text-[17.3px] sm:text-base lg:p-[11px] lg:hover:p-[9px]"
                        target="_blank"
                    >
                        {{ __('home-page/question.export_car') }}
                    </a>
                </div>
            </div>
        </div>
    </div>
@endsection
