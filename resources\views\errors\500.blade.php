<div class="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
    <div class="w-full max-w-md space-y-8 text-center">
        <div>
            <h1 class="text-9xl font-bold text-gray-300">500</h1>
            <h2 class="mt-6 text-3xl font-extrabold text-gray-900">Server Error</h2>
            <p class="mt-2 text-sm text-gray-600">
                {{ $message ?? 'Something went wrong on our end. Please try again later.' }}
            </p>
        </div>

        @if (config('app.debug') && isset($exception))
            <div class="mt-6 rounded-md border border-red-200 bg-red-50 p-4 text-left">
                <h3 class="mb-2 text-sm font-medium text-red-800">Debug Information:</h3>
                <p class="mb-1 text-xs text-red-700">
                    <strong>File:</strong>
                    {{ $exception->getFile() }}
                </p>
                <p class="mb-1 text-xs text-red-700">
                    <strong>Line:</strong>
                    {{ $exception->getLine() }}
                </p>
                <p class="text-xs text-red-700">
                    <strong>Message:</strong>
                    {{ $exception->getMessage() }}
                </p>
            </div>
        @endif

        <div class="mt-8 space-y-4">
            <a
                href="{{ url('/') }}"
                class="group relative flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none"
            >
                Go Back Home
            </a>

            <button
                onclick="location.reload()"
                class="group relative flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none"
            >
                Try Again
            </button>
        </div>
    </div>
</div>
