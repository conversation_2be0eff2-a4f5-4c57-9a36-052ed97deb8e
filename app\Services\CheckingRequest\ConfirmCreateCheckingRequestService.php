<?php

declare(strict_types=1);

namespace App\Services\CheckingRequest;

use App\Repositories\sqlServer\CheckingRequestRepository;

class ConfirmCreateCheckingRequestService
{
    private CheckingRequestRepository $checkingRequestRepository;
    public function __construct(CheckingRequestRepository $checkingRequestRepository)
    {
        $this->checkingRequestRepository = $checkingRequestRepository;
    }

    public function call(array $body): bool
    {
        return $this->checkingRequestRepository->confirmCreateCheckingRequest($body);
    }
}
