<?php

declare(strict_types=1);

namespace App\Http\Requests\CheckingRequest;

use Illuminate\Foundation\Http\FormRequest;

class ConfirmCreateCheckingRQRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'm_customer_id' => 'required|integer',
            'aa_no' => 'required|string|max:255',
            't_aa_date_id' => 'required|integer',
        ];
    }
}
