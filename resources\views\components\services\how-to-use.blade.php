@php
    /**
    * @var string $content1
    * @var string $content2
    * @var string $icon1
    * @var string $icon2
    */
@endphp

<div class="text-15 bg-white pb-2">
    <h4 class="2sm:py-[0.2em] 2sm:px-[0.5em] 2sm:text-xl !m-0 text-lg font-bold text-orange-400 md:text-2xl">
        {{ __('common.how_to_use') }}
    </h4>
    <ul class="2sm:text-xl 2sm:pl-10 text-15 leading-normal font-bold text-gray-200 opacity-80 md:text-3xl">
        <li class="2sm:my-0 2sm:px-[1em] my-[0.5em] border-b-2 border-orange-400">
            {{ $content1 }}
            @if ($icon1)
                <i class="{{ $icon1 }} ml-[0.5em] text-orange-400"></i>
            @endif
        </li>
        <li class="2sm:my-0 2sm:px-[1em] 2sm:w-[90%] 2sm:ml-auto my-[0.5em] w-full border-b-2 border-orange-400">
            {{ $content2 }}
            @if ($icon2)
                <i class="{{ $icon2 }} ml-[0.5em] text-orange-400"></i>
            @endif
        </li>
    </ul>
</div>
