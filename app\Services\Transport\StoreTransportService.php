<?php

declare(strict_types=1);

namespace App\Services\Transport;

use App\Enums\Transport\TransIdDefault;
use App\Models\sqlServer\AaPlace;
use App\Models\sqlServer\TransFeeSpec;
use App\Models\sqlServer\TransFeeSpecCarry;
use App\Models\sqlServer\TransFeeSpecEikou;
use App\Models\sqlServer\TransFeeSpecJcarry;
use App\Models\sqlServer\TransFeeSpecTozai;
use App\Models\sqlServer\TransFeeTtl;
use App\Models\sqlServer\Transport;
use App\Models\sqlServer\TransportId;
use App\Models\sqlServer\TransportNote;
use App\Models\sqlServer\User;
use App\Repositories\sqlServer\TransAgentConfRepository;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;

class StoreTransportService
{
    public function __construct(
        private TransAgentConfRepository $transAgentConfRepository,
        private TransportAgentConfigService $transportAgentConfigService,
        private User $userModel,
        private AaPlace $aaPlaceModel,
        private Transport $transportModel,
        private TransFeeSpecCarry $transFeeSpecCarryModel,
        private TransFeeSpecJcarry $transFeeSpecJcarryModel,
        private TransFeeSpecEikou $transFeeSpecEikouModel,
        private TransFeeSpecTozai $transFeeSpecTozaiModel,
        private TransFeeSpec $transFeeSpecModel,
        private TransFeeTtl $transFeeTtlModel,
        private TransportId $transportIdModel,
        private TransportNote $transportNoteModel,
        private SendMailStoreTransportService $sendMailStoreTransportService
    ) {
    }

    /**
     * Store transport request
     */
    public function store(array $data)
    {
        $dataTransform = $this->transformDataRequest($data);

        // get customer send mail
        $dataTransform = array_merge($dataTransform, $this->getDataUser((string) ($dataTransform['odr_customer_id'])));


        // overwrite data fr aa place
        $dataTransform = array_merge($dataTransform, $this->getDataFromAaPlace((string) ($dataTransform['fr_aa_place_id'])));

        // overwrite data to aa place
        $dataTransform = array_merge($dataTransform, $this->getDataToAaPlace((string) ($dataTransform['to_aa_place_id'])));

        // init transport
        $transportTozaiId = $this->getTransOnlyId('tozai');
        $transportCarryId = $this->getTransOnlyId('carry');
        $transportJcarryId = $this->getTransOnlyId('jcarry');
        $transportEikouId = $this->getTransOnlyId('eikou');

        // calculate transport cost
        $stockPrice = 0;
        $ahStockPrice = 0;
        $pMatchFlg = 0;

        // get column name price
        $colName = $this->getAaToTransCostColName($dataTransform['to_place_id']);

        if (TransIdDefault::TARGET->value == $dataTransform['m_trans_id']) {
            $transOnlyId = $transportCarryId + 1;
            if ($colName) {
                $stockPrice = $this->getTransCost('carry', $dataTransform['fr_aa_place_id'], $colName);
            }
        } elseif (TransIdDefault::J_BRING->value == $dataTransform['m_trans_id']) {
            $transOnlyId = $transportJcarryId + 1;
            if ($colName) {
                $stockPrice = $this->getTransCost('jcarry', $dataTransform['fr_aa_place_id'], $colName);
            }
        } elseif (TransIdDefault::EIKO_SHOUNEN->value == $dataTransform['m_trans_id']) {
            $transOnlyId = $transportEikouId + 1;
            if ($colName) {
                $stockPrice = $this->getTransCost('eikou', $dataTransform['fr_aa_place_id'], $colName);
            }
        } else {
            $transOnlyId = $transportTozaiId + 1;
            if ($colName) {
                $stockPrice = $this->getTransCost('tozai', $dataTransform['fr_aa_place_id'], $colName);
            }
        }

        if (null !== $stockPrice) {
            $pMatchFlg = 1;
        } else {
            $stockPrice = 0;
        }

        if ($pMatchFlg) {
            $value = $this->getColNameInTransFeeSpec($dataTransform['fr_aa_place_id'], $colName);
            if (null !== $value) {
                $ahStockPrice = $value;
            }
        }

        // add 10000 when narikiri_trans_flg is 1
        if ((int) ($dataTransform['narikiri_trans_flg'])) {
            $ahStockPrice += 10000;
        }

        $payload = array_merge($dataTransform, [
            'tp_id' => $transOnlyId,
            'deli_price' => $stockPrice,
            'sales_price' => $ahStockPrice,
            'del_flg' => 0,
            'reg_date' => now(),
            'up_owner' => Auth::id(),
            'm_customer_id' => Auth::id(),
            'odr_flg' => 1,
            'st_cd' => 0,
            'output_flg' => 0,
            'note' => $dataTransform['autohub_note'],
            'odr_date' => now(),
        ]);
        // store transport
        $dataStore = $this->transformDataStore($payload);

        $transportNew = $this->transportModel::create($dataStore);

        // init data update transport note
        $dataUpdateTransportNote = [
            'tp_id' => $transportNew->id,
            'refno' => 0,
            'reg_date' => now(),
            'note' => trim($dataTransform['note']) ? $dataTransform['note'] : '備考の記載はありません。',
            'name' => '初回登録時コメント(System自動登録)',
            'list_show_flg' => 1
        ];

        $transportNoteNew = $this->transportNoteModel::create($dataUpdateTransportNote);

        // update transport note
        if (!in_array($dataTransform['m_trans_id'], ['14192', '17078'])) {
            $payloadUpdateTransportId = match ($dataTransform['m_trans_id']) {
                TransIdDefault::SHIPPING_EAST_AND_WEST->value => [
                    't_id' => $transOnlyId,
                ],
                TransIdDefault::TARGET->value => [
                    'cg_id' => $transOnlyId,
                ],
                TransIdDefault::J_BRING->value => [
                    'jc_id' => $transOnlyId,
                ],
                TransIdDefault::EIKO_SHOUNEN->value => [
                    'ei_id' => $transOnlyId,
                ],
                default => [],
            };

            $result = $this->transportIdModel::whereNotNull('t_id')->update($payloadUpdateTransportId);
        }
        $this->sendMailStoreTransportService->sendMail(array_merge($payload, [
            'transport_id' => $transportNew->id,
        ]));

        return $transportNew;
    }

    public function transformDataStore(array $data): array
    {
        $onlyKeys = ["del_flg","reg_date","up_owner","m_customer_id","fr_aa_place_id","fr_name","fr_addr","fr_tel","to_name","to_addr","to_tel","pos_no",
            "aa_no","car_name","car_no","plate_cut_flg","plate_no","odr_date","odr_flg","st_cd","deli_price","sales_price","output_flg","optn1_flg","agent_ref_no",
            "fr_place_id","to_place_id","to_etc_place_id","inp_ah_name","m_trans_id","to_aa_place_id","tp_id","country",
            "date_of_payment", "undrivable_flg", "tall_flg", "lowdown_flg", "long_flg",
            "date_of_payment_time","old_flg","luxury_flg","luxury_flg_insrance","other_flg","other_flg_txt","tick_no_flg",
            "plate_send_name","plate_send_zipcd","plate_send_address","plate_send_tel","country_area","country_cd","port_cd","port","country_free",
            "auction_date","auction_txt","auction_chk","auc_name","auc_addr","auc_tel","pos_chk","pos_chk_text","narikiri_trans_flg", "note"];
        return Arr::only($data, $onlyKeys);
    }

    public function getAaToTransCostColName(string $toAaPlaceId)
    {
        $tftColumns = 12;
        if (!$toAaPlaceId) {
            return '';
        }

        $record = $this->transFeeTtlModel::where('del_flg', 0)->orderBy('id', 'asc')->first();
        for ($i = 1; $i <= $tftColumns; $i++) {
            $colName = 'col' . $i;
            $colValue = $record->{$colName};
            if (null === $colValue) {
                $colChk[$i - 1] = ['NONE'];
            } else {
                $parts = array_values(array_filter(
                    array_map('trim', explode('|', $colValue)),
                    fn ($s) => '' !== $s
                ));
                $colChk[$i - 1] = $parts;
            }
        }

        $col = '';
        for ($i = 0; $i < $tftColumns - 1; $i++) {
            for ($j = 0; $j <= count($colChk[$i]) - 1; $j++) {
                if ($colChk[$i][$j] == $toAaPlaceId) {
                    $col = 'col' . ($i + 1);
                }
            }
        }

        return $col;
    }

    private function transformDataRequest(array $data): array
    {
        $posCheckOther = '9';
        return [
            'old_flg' => Arr::get($data, 'old_flg', ''),
            'date_of_payment_time' => Arr::get($data, 'date_of_payment_time', ''),
            'undrivable_flg' => (int) (Arr::get($data, 'undrivable_flg')),
            'tall_flg' => (int) (Arr::get($data, 'tall_flg')),
            'lowdown_flg' => (int) (Arr::get($data, 'lowdown_flg')),
            'long_flg' => (int) (Arr::get($data, 'long_flg')),
            'pos_chk_text' => $posCheckOther == trim(Arr::get($data, 'pos_chk') ?? '') ? trim(Arr::get($data, 'pos_chk_text') ?? '') : '0',
            'luxury_flg' => (int) (Arr::get($data, 'luxury_flg')),
            'luxury_flg_insrance' => (int) (trim(Arr::get($data, 'luxury_flg') ?? '')) ? trim(Arr::get($data, 'luxury_flg_insrance', '')) : '0',
            'other_flg' => (int) (Arr::get($data, 'other_flg')),
            'other_flg_txt' =>  (int) (trim(Arr::get($data, 'other_flg') ?? '')) ? trim(Arr::get($data, 'other_flg_txt', '')) : '0',
            'm_trans_id' => (string) Arr::get($data, 'm_trans_id' ?? ''),
            'fr_aa_place_id' => (string) Arr::get($data, 'fr_aa_place_id'),
            'fr_place_id' => Arr::get($data, 'fr_place_id', ''),
            'odr_customer_id' => (string) Arr::get($data, 'odr_customer_id'),
            'fr_name' => Arr::get($data, 'fr_name'),
            'fr_addr' => Arr::get($data, 'fr_addr'),
            'fr_tel' => Arr::get($data, 'fr_tel1', '') . '-' . Arr::get($data, 'fr_tel2', '') . '-' . Arr::get($data, 'fr_tel3', ''),
            'to_place_id' => (string) Arr::get($data, 'to_place_id'),
            'to_etc_place_id' => Arr::get($data, 'to_etc_place_id'),
            'to_aa_place_id' => (string) Arr::get($data, 'to_aa_place_id'),
            'to_name' => Arr::get($data, 'to_name'),
            'to_addr' => Arr::get($data, 'to_addr'),
            'to_tel' => Arr::get($data, 'to_tel1', '') . '-' . Arr::get($data, 'to_tel2', '') . '-' . Arr::get($data, 'to_tel3', ''),
            'pos_no' => Arr::get($data, 'pos_no'),
            'aa_no' => Arr::get($data, 'aa_no'),
            'car_name' => Arr::get($data, 'car_name'),
            'car_no' => Arr::get($data, 'car_no'),
            'country' => Arr::get($data, 'country'),
            'plate_cut_flg' => (int) (Arr::get($data, 'plate_cut_flg')) ,
            'plate_no' => Arr::get($data, 'plate_no'),
            'note' => Arr::get($data, 'note'),
            'optn1' => (int) (Arr::get($data, 'optn1_flg')),
            'date_of_payment' => Arr::get($data, 'date_of_payment'),
            'port_cd' => Arr::get($data, 'port_cd'),
            'port' => Arr::get($data, 'port'),
            'country_cd' => Arr::get($data, 'country_cd'),
            'country_area' => Arr::get($data, 'country_area'),
            'country_free' => Arr::get($data, 'country_free'),
            'auction_date' => Arr::get($data, 'auction_date'),
            'auction_chk' => Arr::get($data, 'auction_chk'),
            'auction_txt' => Arr::get($data, 'auction_txt'),
            'auc_name' => Arr::get($data, 'auc_name'),
            'auc_addr' => Arr::get($data, 'auc_addr'),
            'auc_tel' => Arr::get($data, 'auc_tel'),
            'pos_chk' => Arr::get($data, 'pos_chk'),
            'tick_no_flg' => (int) (Arr::get($data, 'tick_no_flg')),
            'plate_send_name' => Arr::get($data, 'plate_send_name'),
            'plate_send_zipcd' => Arr::get($data, 'plate_send_zipcd'),
            'plate_send_address' => Arr::get($data, 'plate_send_address'),
            'plate_send_tel' => Arr::get($data, 'plate_send_tel'),
            'narikiri_trans_flg' => (int) (Arr::get($data, 'narikiri_trans_flg')),
            'autohub_note' => Arr::get($data, 'autohub_note'),
            'agent_ref_no' => Arr::get($data, 'agent_ref_no'),
            'inp_ah_name' => Arr::get($data, 'inp_ah_name'),
            'note' => (string) (Arr::get($data, 'note')),
        ];
    }

    private function getDataUser(string $odrCustomerId): array
    {
        $user = $this->userModel::where('id', $odrCustomerId)->where('del_flg', 0)->first();
        return [
            'cus_name_jp' => $user->cus_Name_JP,
            'admin_email' => $user->admin_email,
            'shipping_prsn_email' => $user->shipping_prsn_email,
        ];
    }

    /**
     * Process pickup location data
     */
    private function getDataFromAaPlace(string $frAaPlaceId): array
    {
        $result = [];
        // Auction pickup
        if (!empty($frAaPlaceId) && '0' != $frAaPlaceId) {
            // Get AA place info from database
            $aaPlace = $this->aaPlaceModel::where('id', $frAaPlaceId)->where('del_flg', 0)->first();
            if ($aaPlace) {
                $result = [
                    'fr_name' => $aaPlace->name,
                    'fr_addr' => $aaPlace->addr,
                    'fr_tel' => $aaPlace->tel,
                ];
            }
        }

        return $result;
    }

    private function getDataToAaPlace(string $toAaPlaceId): array
    {
        $result = [];
        // Auction pickup
        if (!empty($toAaPlaceId) && '0' != $toAaPlaceId) {
            // Get AA place info from database
            $aaPlace = $this->aaPlaceModel::where('id', $toAaPlaceId)->where('del_flg', 0)->first();

            if ($aaPlace) {
                $result = [
                    'fr_name' => $aaPlace->name,
                    'fr_addr' => $aaPlace->addr,
                    'fr_tel' => $aaPlace->tel,
                ];
            }
        }

        return $result;
    }

    private function getTransOnlyId(string $agentName)
    {
        $record = $this->transportIdModel::first();
        return match ($agentName) {
            'tozai' => $record->t_id,
            'carry' => $record->cg_id,
            'jcarry' => $record->jc_id,
            'eikou' => $record->ei_id,
            default => null,
        };
    }

    private function getTransCost(string $agentName, string $toAaPlaceId, string $colName)
    {
        $record = match ($agentName) {
            'tozai' => $this->transFeeSpecTozaiModel::where($colName, $toAaPlaceId)->first(),
            'carry' => $this->transFeeSpecCarryModel::where($colName, $toAaPlaceId)->first(),
            'jcarry' => $this->transFeeSpecJcarryModel::where($colName, $toAaPlaceId)->first(),
            'eikou' => $this->transFeeSpecEikouModel::where($colName, $toAaPlaceId)->first(),
            default => null,
        };

        if ($record) {
            return $record->{$colName};
        }

        return null;
    }

    private function getColNameInTransFeeSpec(string $fnAaPlaceId, string $colName)
    {
        $record = $this->transFeeSpecModel::where('t_aa_place_id', $fnAaPlaceId)->first();
        if ($record) {
            return $record->{$colName};
        }

        return null;
    }
}
