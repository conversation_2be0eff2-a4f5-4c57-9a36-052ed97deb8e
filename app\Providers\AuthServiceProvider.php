<?php

declare(strict_types=1);

namespace App\Providers;

use App\Guards\JwtGuard;
use App\Services\Jwt\JwtService;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [

    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        // Register JWT guard
        Auth::extend('jwt', function ($app) {
            $provider = Auth::createUserProvider('users');
            $request = $app->make(Request::class);
            $jwtService = $app->make(JwtService::class);

            return new JwtGuard($provider, $request, $jwtService);
        });
    }
}
