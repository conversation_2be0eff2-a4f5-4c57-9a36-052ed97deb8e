<?php

declare(strict_types=1);

namespace App\Http\Resources\RouteSchedule;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RouteScheduleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'route_code' => trim($this->route_code),
            'route_name' => trim($this->route_name),
        ];
    }
}
