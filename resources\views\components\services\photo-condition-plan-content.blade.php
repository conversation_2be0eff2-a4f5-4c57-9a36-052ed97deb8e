@php
    /**
     * @var int $index
     * @var string $title
     * @var string $content1
     * @var string $content2
     * @var string $icon2
     */
    $bgTitle = '';
    $colorIcon = '';
    switch ($index) {
        case 0:
            $bgTitle = 'bg-[linear-gradient(to_right,_#4facfe_0%,_#00f2fe_100%)]';
            $colorIcon = 'text-[#2ACEFF]';
            break;
        case 1:
            $bgTitle = 'bg-[linear-gradient(to_right,_#43e97b_0%,_#38f9d7_100%)]';
            $colorIcon = 'text-[#3DF1A8]';
            break;
        case 2:
            $bgTitle = 'bg-[linear-gradient(to_right,_#fa709a_0%,_#fee140_100%)]';
            $colorIcon = 'text-[#FCAA6D]';
            break;
        case 3:
            $bgTitle = 'bg-[linear-gradient(to_right,_#0c3483_0%,_#a2b6df_100%)]';
            $colorIcon = 'text-[#5472AF]';
            break;
    }
    $classContent = '';
    if ($content2) {
        $classContent = 'text-left';
    }
@endphp

<div class="relative my-[0.1em] md:my-[0.3em]">
    <dl class="2sm:flex 2sm:items-center block text-center shadow-gray-100">
        <dt
            class="{{ $bgTitle }} 2md:text-xl 2sm:text-sm 2sm:w-[13.5em] 2sm:p-[1em] text-15 md:text-17 w-full rounded-[30px_0_40px_0] p-[0.5em] leading-normal font-bold text-white shadow-gray-100 md:w-[15em]"
        >
            {!! $title !!}
        </dt>
        <dd
            class="{{ $classContent }} 2sm:text-13 2sm:p-0 2sm:text-center 2md:text-xl md:text-17 mx-[1em] py-[1.5em] text-center text-sm text-black md:mx-[2em]"
        >
            <i class="fas fa-camera {{ $colorIcon }} 2sm:text-23 2md:text-3xl md:text-27 mx-[0.2em] text-xl"></i>
            {{ $content1 }}
            @if ($content2)
                <i class="{{ $icon2 }} {{ $colorIcon }} 2sm:text-23 2md:text-3xl md:text-27 mx-[0.2em] text-xl"></i>
                {{ $content2 }}
            @endif
        </dd>
    </dl>
</div>
