<?php

declare(strict_types=1);

namespace App\Models\sqlServer\Trait\Transport;

use App\Enums\Transport\DateMode;
use App\Enums\UserRole;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

trait FilterTrait
{
    public function scopeTransportIds(Builder $query, string $strTransportIds): Builder
    {
        $columnName = Auth::user()->hn_cd == UserRole::admin->value ? 't_transport.id' : 't_transport.tp_id';
        $transportIds = explode(' ', trim(preg_replace('/\s+/', ' ', trim($strTransportIds))));
        if (count($transportIds) > 1) {
            return $query->whereIn($columnName, $transportIds);
        }
        return $query->where($columnName, $strTransportIds);
    }

    public function scopeTpIds(Builder $query, string $strTransportIds): Builder
    {
        $columnName = Auth::user()->hn_cd == UserRole::admin->value ? 't_transport.id' : 't_transport.tp_id';
        $transportIds = explode(' ', trim(preg_replace('/\s+/', ' ', trim($strTransportIds))));
        $transportIdsNumber = array_filter($transportIds, fn ($item) => is_numeric($item));
        if (count($transportIdsNumber) > 0) {
            if (count($transportIdsNumber) > 1) {
                return $query->whereIn($columnName, $transportIdsNumber);
            }
            return $query->where($columnName, $transportIdsNumber[0]);

        }
        return $query;

    }

    public function scopeAaPlaceId(Builder $query, string $strAAPlaceId): Builder
    {
        return $query->where('t_transport.fr_aa_place_id', $strAAPlaceId);
    }

    public function scopeToPlaceId(Builder $query, string $strToPlaceId): Builder
    {
        return $query->where('t_transport.to_place_id', $strToPlaceId);
    }

    public function scopeRefNos(Builder $query, string $strRefNo): Builder
    {
        $refNoArray = explode(' ', trim(preg_replace('/\s+/', ' ', trim($strRefNo))));
        if (count($refNoArray) > 1) {
            return $query->whereIn('t_transport.ref_no', $refNoArray);
        }
        return $query->where('t_transport.ref_no', $strRefNo);
    }

    public function scopeFrName(Builder $query, string $strFrName): Builder
    {
        return $query->where('t_transport.fr_name', 'like', '%' . $strFrName . '%');
    }

    public function scopeToName(Builder $query, string $strToName): Builder
    {
        return $query->where('t_transport.to_name', 'like', '%' . $strToName . '%');
    }

    public function scopeCustomerName(Builder $query, string $strCustomerName): Builder
    {
        return $query->where(
            function (Builder $subQuery) use ($strCustomerName): void {
                $subQuery->where('m_customer.cus_name_JP', 'like', '%' . $strCustomerName . '%')
                    ->orWhere('m_customer.cus_name_EN', 'like', '%' . $strCustomerName . '%');
            }
        );
    }

    public function scopeCarNo(Builder $query, string $strCarNo): Builder
    {
        $arrayCarNo = explode(' ', trim(preg_replace('/\s+/', ' ', trim($strCarNo))));
        if (count($arrayCarNo) > 1) {
            return $query->where(function ($q) use ($arrayCarNo): void {
                foreach ($arrayCarNo as $no) {
                    $q->orWhere(DB::raw("REPLACE(t_transport.car_no, '-', '')"), $no);
                }
            });
        } else {
            return $query->where(DB::raw("REPLACE(t_transport.car_no, '-', '')"), 'like', '%' . str_replace('-', '', $strCarNo[0]) . '%');
        }
    }

    public function scopeCarName(Builder $query, string $strCarName): Builder
    {
        return $query->where('t_transport.car_name', 'like', '%' . $strCarName . '%');
    }

    public function scopeAaNos(Builder $query, string $strAANo): Builder
    {
        $arrayAANo = explode(' ', trim(preg_replace('/\s+/', ' ', trim($strAANo))));
        if (count($arrayAANo) > 1) {
            return $query->whereIn('t_transport.aa_no', $arrayAANo);
        }
        return $query->where('t_transport.aa_no', 'like', '%' . $strAANo . '%');
    }

    public function scopeStCd(Builder $query, string $strStCd): Builder
    {
        if ('' != $strStCd && '999' != $strStCd) {
            return $query->where('t_transport.st_cd', $strStCd);
        }
        return $query;

    }

    public function scopePlateCut(Builder $query, string $strPlateCut): Builder
    {
        if ('1' == $strPlateCut) {
            return $query->where(function ($q): void {
                $q->where('t_transport.plate_cut_flg', 1)
                    ->orWhere('t_transport.plate_cut_flg', 3);
            });
        } elseif ('2' == $strPlateCut) {
            return $query->where('t_transport.plate_cut_flg', 0);
        } else {
            return $query;
        }
    }

    public function scopeMTransId(Builder $query, string $strMTransId): Builder
    {
        if (Auth::user()->hn_cd == UserRole::admin->value && $strMTransId && '999' != $strMTransId) {
            return $query->where('t_transport.m_trans_id', $strMTransId);
        }
        return $query;
    }

    public function scopeChargeSaleCustomer(Builder $query, string $strChargeSale): Builder
    {
        if (Auth::user()->hn_cd == UserRole::admin->value && $strChargeSale && '0' != $strChargeSale) {
            return $query->where('m_customer.ah_sales_id', $strChargeSale);
        }
        return $query;
    }

    public function scopeCustomerId(Builder $query, mixed $customerId): Builder
    {
        if ($customerId) {
            return $query->where('t_transport.m_trans_id', $customerId);
        }
        return $query;
    }

    public function scopeStartDate(Builder $query, string $strStartDate): Builder
    {
        $data = $this->getTimeByDateMode('start_time', request()->get('date_mode'));
        return $query->where($data['date_column'], '>=', $strStartDate . $data['time_value']);
    }

    public function scopeEndDate(Builder $query, string $strEndDate): Builder
    {
        $data = $this->getTimeByDateMode('end_time', request()->get('date_mode'));
        return $query->where($data['date_column'], '<=', $strEndDate . $data['time_value']);
    }

    /**
     * @param string $key
     * @param string|null $dateMode
     * @return array
     * [
     *  'date_column' => 't_transport.odr_date',
     *  'time_value' => ' 00:00:00' | ' 23:59:59' | ''
     * ]
     */
    private function getTimeByDateMode(string $key, ?string $dateMode = null): array
    {
        $data = [
            'start_time' => ' 00:00:00',
            'end_time' => ' 23:59:59',
            'date_column' => 't_transport.odr_date',
        ];

        switch ($dateMode) {
            case DateMode::ORD_DATE->value:
                $data['date_column'] = 't_transport.odr_date';
                $data['start_time'] = ' 00:00:00';
                $data['end_time'] = ' 23:59:59';
                break;
            case DateMode::TO_PLAN_DATE->value:
                $data['date_column'] = 't_transport.to_plan_date';
                $data['start_time'] = '';
                $data['end_time'] = '';
                break;
            case DateMode::TO_DATE->value:
                $data['date_column'] = 't_transport.to_date';
                $data['start_time'] = '';
                $data['end_time'] = '';
                break;
            case DateMode::FROM_PLAN_DATE->value:
                $data['date_column'] = 't_transport.from_plan_date';
                $data['start_time'] = '';
                $data['end_time'] = '';
                break;
            default:
                // Default to ORD_DATE if dateMode is not recognized
                $data['date_column'] = 't_transport.odr_date';
                $data['start_time'] = ' 00:00:00';
                $data['end_time'] = ' 23:59:59';
                break;
        }

        return [
            'date_column' => $data['date_column'],
            'time_value' => $data[$key],
        ];
    }
}
