@php
    /**
     * @var string $page
     */

    use Illuminate\Support\Facades\App;

    $contents = '';
    $image = '';
    $classContents = '2md:w-1/2';
    $classImage = '2md:w-1/2';
    $classList = 'list-decimal';
    $header = '';
    $description = '';
    $classContainer = 'flex items-center';
    $classItem = '';

    switch ($page) {
        case 'shipping_export_import':
            $contents = __('home-page/shipping_export_import.how_to_use_list');
            $image = 'ah_service_image02.png';
            $classItem = 'lg:my-[1em]';
            break;
        case 'd2d_package':
            $contents = __('home-page/d2d_package.how_to_use_list');
            $image = 'ah_service_image01.png';
            break;
        case 'car_shipping_info':
            $contents = __('home-page/car_shipping_info.service_list');
            $image = 'ah_service_image05.png';
            $classContents = '2md:w-2/3';
            $classImage = '2md:w-1/3';
            $classList = 'list-disc';
            $classItem = 'lg:my-[1em]';
            break;
        case 'auction_vehicle_check':
            $contents = __('home-page/auction_vehicle_check.pre_purchase_check_list');
            $image = 'ah_service_image09.png';
            $header = __('home-page/auction_vehicle_check.pre_purchase_check');
            $description = __('home-page/auction_vehicle_check.pre_purchase_check_des');
            $classContainer = '';
            $classItem = 'lg:my-[0.7em]';
            break;
        case 'document_file':
            $contents = __('home-page/document_file.document_list');
            $image = app()->getLocale() === 'ja' ? 'ah_service_image12.png' : 'ah_service_image12_en.png';
            $header = __('home-page/document_file.documents');
            $classContainer = '';
    }
@endphp

<div class="mx-[-15px] flex flex-wrap">
    <div class="{{ $classContents }} relative w-full px-[15px]">
        <div
            class="{{ $classContainer }} text-15 shadow-gray-220 mb-[15px] h-full bg-stone-50 px-[15px] pt-[15px] pb-[5px] md:mb-[10px]"
        >
            @if ($header)
                <h4 class="2sm:text-xl m-[0.5em] block text-lg font-medium text-gray-700 md:text-2xl">
                    {!! $header !!}
                </h4>
            @endif

            <ul class="{{ $classList }} text-15 translate-x-[-0.5em] transform pl-10">
                @foreach ($contents as $item)
                    <li class="{{ $classItem }} text-15 my-[0.5em] md:text-base">{{ $item }}</li>
                @endforeach
            </ul>

            @if ($description)
                <p class="text-13 mb-[1.5em] ml-[0.8em] text-red-500 md:text-sm">
                    {!! $description !!}
                </p>
            @endif
        </div>
    </div>
    <div class="{{ $classImage }} relative w-full px-[15px]">
        <img src="{{ asset('images/services/' . $image) }}" class="2md:mt-0 mx-auto mt-6 w-full !max-w-max" alt="" />
        @if ($page === 'd2d_package')
            <img
                src="{{ asset('images/services/d2d_AUTOHUB_sm.png') }}"
                alt="「D2D」はAUTHUBの登録商標です。"
                class="2sm:hidden absolute top-3 right-0 w-30"
            />
            <img
                src="{{ asset('images/services/d2d_autohub.png') }}"
                alt="「D2D」はAUTHUBの登録商標です。"
                class="2sm:block 2md:top-[-13px] absolute top-3 right-0 hidden"
            />
        @endif
    </div>
</div>
