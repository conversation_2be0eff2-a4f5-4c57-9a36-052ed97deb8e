<?php

declare(strict_types=1);

namespace App\Repositories\sqlServer;

use App\Models\sqlServer\LoadingOrderSpecialNote;
use App\Repositories\BaseRepository;

class LoadingOrderSpecialNoteRepository extends BaseRepository
{
    public function model(): mixed
    {
        return LoadingOrderSpecialNote::class;
    }

    public function findSpecialNoteByCustomerId(int $customerId)
    {
        $query = ($this->model)::query();

        return $query
            ->where('tt_id', $customerId)
            ->first();
    }
}
