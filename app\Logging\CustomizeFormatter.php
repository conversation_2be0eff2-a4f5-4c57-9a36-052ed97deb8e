<?php

declare(strict_types=1);

namespace App\Logging;

use Illuminate\Log\Logger;

class CustomizeFormatter
{
    /**
     * Customize the given logger instance.
     *
     * @param Logger $logger
     * @return void
     */
    public function __invoke($logger): void
    {
        foreach ($logger->getHandlers() as $handler) {
            $sapi = php_sapi_name();
            $handler->setFilenameFormat("{filename}-{$sapi}-{date}", 'Y-m-d');
        }
    }
}
