@extends('app')

@section('title', __('home-page/auction_vehicle_check.page_title'))

@section('content')
    <x-services.container>
        <x-services.title-page
            titlePage="{{ __('home-page/auction_vehicle_check.title') }}"
            description1="{{ __('home-page/auction_vehicle_check.page_description1') }}"
            description2="{{ __('home-page/auction_vehicle_check.page_description2') }}"
        />
        <x-services.title subTitle="{{ __('home-page/auction_vehicle_check.service_descriptions') }}" />
        <x-services.good-content :content="__('home-page/auction_vehicle_check.good_content')" />
        <div class="mx-auto mt-[45px] w-full px-4">
            <x-services.how-to-use-content page="auction_vehicle_check" />
            <x-services.content-item title="{{ __('common.how_to_use') }}">
                <div
                    class="2sm:py-[4em] 2sm:text-base text-15 mt-[1em] flex flex-col flex-wrap items-center justify-around px-[1em] py-[2em] md:flex-row"
                >
                    <div class="2sm:w-1/2 2md:w-1/5 relative w-4/5 md:w-1/4">
                        <i
                            class="fas fa-hand-pointer text-50 absolute right-[10%] bottom-0 z-10 font-black text-white text-shadow-black"
                        ></i>
                        <div class="m-0 flex flex-wrap justify-between">
                            <div class="shadow-gray-150 mx-[1em] mb-[1em] flex w-full bg-red-500 text-center">
                                <a
                                    href="{{ env('APP_URL_OLD', '') }}/hn?lan={{ app()->getLocale() }}"
                                    target="_blank"
                                    class="flex w-full flex-col items-center justify-center p-[1em] text-center"
                                >
                                    <i class="fas fa-sign-in-alt text-40 mb-[0.1em] font-black text-white"></i>
                                    <p class="2sm:text-base text-center text-xs leading-normal font-bold text-white">
                                        {!! __('common.login_hubnet') !!}
                                    </p>
                                </a>
                            </div>
                        </div>
                    </div>
                    <i
                        class="fas fa-caret-right 2sm:my-[0.2em] text-50 rotate-90 transform font-black text-orange-400 md:my-0 md:rotate-0 md:transform-none"
                    ></i>
                    <div class="2sm:w-1/2 2md:w-1/5 relative w-4/5 md:w-1/4">
                        <i
                            class="fas fa-hand-pointer text-50 absolute right-[10%] bottom-0 z-10 font-black text-white text-shadow-black"
                        ></i>
                        <div class="flex flex-wrap justify-between">
                            <div class="shadow-gray-150 mx-[1em] mb-[1em] w-full bg-white text-center">
                                <a
                                    href="{{ env('APP_URL_OLD', '') }}/hn/check{{ app()->getLocale() == 'ja' ? '' : '/?lan=' . app()->getLocale() }}"
                                    class="relative flex w-full flex-col items-center justify-center"
                                    target="_blank"
                                >
                                    <div
                                        class="absolute top-0 left-0 h-[30px] w-5"
                                        style="
                                            background: url('{{ asset('images/services/label_style08.png') }}')
                                                no-repeat;
                                            background-size: 20px 30px;
                                        "
                                    ></div>
                                    <img
                                        src="{{ asset('images/services/icon_menu_06.png') }}"
                                        alt="陸送発注"
                                        class="mt-[10px] w-15"
                                    />
                                    <p class="2sm:text-base text-red-450 pb-[1em] text-xs leading-normal font-bold">
                                        {!! __('home-page/auction_vehicle_check.preliminary_check_order') !!}
                                    </p>
                                </a>
                            </div>
                        </div>
                    </div>
                    <i class="fas fa-arrows-alt-h text-60 m-[0.3em] rotate-90 text-[#17A2B8] md:m-0 md:rotate-0"></i>
                    <div class="2sm:w-1/2 2md:w-1/5 relative w-4/5 md:w-1/4">
                        <i
                            class="fas fa-phone text-60 absolute right-0 bottom-[-10%] left-[70%] z-10 text-white text-shadow-black"
                        ></i>
                        <div class="flex flex-wrap justify-between">
                            <div class="shadow-gray-100-big mx-[1em] mb-[1em] w-full rounded-sm bg-white">
                                <p
                                    class="2sm:text-base text-sky-350 p-[1em] text-center text-xs leading-normal font-bold"
                                >
                                    {!! __('home-page/auction_vehicle_check.pre_purchase_reported') !!}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </x-services.content-item>
            <x-services.content-item title="{{ __('common.functions') }}">
                <x-services.service-function-item
                    :index="1"
                    :content="__('home-page/auction_vehicle_check.function_1')"
                    icon="fas fa-globe"
                    image="auction_check_01.jpg"
                />
                <x-services.service-function-item
                    :index="2"
                    :content="__('home-page/auction_vehicle_check.function_2')"
                    icon="far fa-calendar-check"
                    :image="App::getLocale() === 'ja' ? 'auction_check_02.jpg' : 'auction_check_02_en.jpg'"
                />
            </x-services.content-item>
        </div>
        <x-services.service-options
            :description="__('home-page/auction_vehicle_check.hubnet_order')"
            iconRight="icon_menu_06.png"
            :titleRight="__('home-page/auction_vehicle_check.support_service')"
            :contentRight="__('home-page/auction_vehicle_check.support_service_content')"
            linkRight="{{ env('APP_URL_OLD', '') }}/hn/check/?lan={{ app()->getLocale() }}"
        />
    </x-services.container>
@endsection
