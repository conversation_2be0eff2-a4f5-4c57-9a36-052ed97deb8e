<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Exceptions\ApiException;
use App\Exceptions\AuthException;
use App\Models\sqlServer\LoginSession;
use App\Models\sqlServer\User;
use App\Services\Jwt\JwtService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response as HttpResponse;

readonly class JwtAuthenticationMiddleware
{
    public function __construct(private JwtService $jwtService)
    {
    }

    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     * @throws ApiException
     */
    public function handle(Request $request, Closure $next): mixed
    {
        $token = $this->getTokenFromRequest($request);

        if (!$token) {
            throw new AuthException(code: HttpResponse::HTTP_UNAUTHORIZED);
        }

        // Verify token
        $payload = $this->jwtService->verifyToken($token);

        if (!$payload) {
            throw new AuthException(code: HttpResponse::HTTP_UNAUTHORIZED);
        }

        // Check login session
        $this->checkLoginSession($payload);

        $request->attributes->set('login_session_id', $payload['login_session_id']);

        Auth::setUser($this->getUserFromPayload($payload));

        return $next($request);
    }

    /**
     * Get JWT token from request
     *
     * @param Request $request
     * @return string|null
     */
    private function getTokenFromRequest(Request $request): ?string
    {
        $bearer = $request->header('Authorization');

        if (!$bearer) {
            return null;
        }

        if (str_starts_with($bearer, 'Bearer ')) {
            return mb_substr($bearer, 7);
        }

        return $bearer;
    }

    /**
     * Get user from payload
     *
     * @param array $payload
     * @return User
     */
    private function getUserFromPayload(array $payload): User
    {
        $userId = $payload['sub'];
        return User::where('idx', $userId)->where('del_flg', 0)
            ->where('login_flg', 0)
            ->where('exchange_flg', 0)
            ->first();
    }

    private function checkLoginSession(array $payload): void
    {
        $loginSessionId = $payload['login_session_id'];
        $loginSession = LoginSession::where('id', $loginSessionId)->where('expired_at', '>', now())->exists();
        if (!$loginSession) {
            throw new AuthException(code: HttpResponse::HTTP_UNAUTHORIZED);
        }
    }
}
