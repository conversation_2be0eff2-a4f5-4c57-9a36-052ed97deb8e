let rep = null;

function scroll(position) {
    if (position === 'start') {
        rep = setTimeout(() => scroll('start'), 5);
        window.scrollBy(0, 1);
        $('#scroll-stop-button').css('display', 'block');
    } else {
        clearTimeout(rep);
        $('#scroll-stop-button').css('display', 'none');
        const y = $(window).scrollTop();
        setTimeout(() => {
            $(window).scrollTop(y);
        }, 0);
    }
}

$("#scroll_button").click(function () {
    if (!/\(iP/.test(navigator.userAgent)) {
        scroll("start");
    }
});
$("#scroll-stop-button").click(function () {
    scroll("stop");
});


function addPx(value) {
    if (typeof value === 'string' && (value.includes('px') || value.includes('%') || value.includes('em') || value.includes('rem'))) {
        return value;
    }
    return value + 'px';
}

$("#top_hubnet_img,#top_hubnet_img_300").click(function () {
    const lang = $("#lang").val();
    const urlOld = $("#urlOld").val();
    const url = urlOld + '/hn/login/index.asp?lan=' + lang;
    window.open(url, '_blank');
});
$("#top_recruit_img,#top_recruit_img_300").click(function () {
    const lang = $("#lang").val();
    window.location.href = lang + '/recruit';
});
$("#backtotop").click(function () {
    $(window).scrollTop(0);
});

var win_w, car_x, car_y;
$(window).scroll(function (e) {
    let top_y, left_x;
    $(this).scrollTop() > 0 ? $('.header').css('background', 'rgba(255,255,255,0.8)') : $('.header').css('background', 'rgba(255,255,255,1)');
    // console.log("scroll:"+$(window).scrollTop());

    win_w = (window.innerWidth);

    let offset = 0;
    if (win_w >= 1800) {
        offset = -680;
    } else if (win_w >= 1500) {
        offset = -480;
    } else if (win_w >= 1200) {
        offset = -1000;
    } else if (win_w >= 992) {
        offset = -1000;
    } else if (win_w >= 768) {
        offset = -1000;
    } else if (win_w >= 576) {
        offset = -1380;
    } else {
        offset = -1380;
    }

    const currentScroll = $(window).scrollTop() + offset;

    if (win_w >= 1800) {
        currentScroll > 290 ? ($("#scroll_button").hide(), $('#car_icon').show()) : ($("#scroll_button").show(), $('#car_icon').hide());
        currentScroll > 570 ? $("#cloud01").fadeIn(500) : $("#cloud01").fadeOut(500);
        currentScroll > 830 ? $("#cloud02").fadeIn(500) : $("#cloud02").fadeOut(500);
        currentScroll > 1195 ? $("#cloud03").fadeIn(500) : $("#cloud03").fadeOut(500);
        currentScroll > 1500 ? $("#cloud04").fadeIn(500) : $("#cloud04").fadeOut(500);
        currentScroll > 2180 ? $("#rikuso_moji01").fadeIn(500) : $("#rikuso_moji01").fadeOut(500);
        currentScroll > 2280 ? $("#rikuso_moji02").fadeIn(500) : $("#rikuso_moji02").fadeOut(500);
        currentScroll > 2700 ? $("#sagyo_pop01").fadeIn(500) : $("#sagyo_pop01").fadeOut(500);
        currentScroll > 2750 ? $("#sagyo_pop02").fadeIn(500) : $("#sagyo_pop02").fadeOut(500);
        currentScroll > 2800 ? $("#sagyo_pop03").fadeIn(500) : $("#sagyo_pop03").fadeOut(500);
        currentScroll > 2850 ? $("#sagyo_pop04").fadeIn(500) : $("#sagyo_pop04").fadeOut(500);
        currentScroll > 2900 ? $("#sagyo_pop05").fadeIn(500) : $("#sagyo_pop05").fadeOut(500);
        currentScroll > 2580 ? $("#sagyo_pop_img").fadeIn(1000) : $("#sagyo_pop_img").fadeOut(1000);
        currentScroll > 2600 ? $("#sagyo_pop_photo01").fadeIn(1000) : $("#sagyo_pop_photo01").fadeOut(1000);
        currentScroll > 2650 ? $("#sagyo_pop_photo02").fadeIn(1000) : $("#sagyo_pop_photo02").fadeOut(1000);
        currentScroll > 2700 ? $("#sagyo_pop_photo03").fadeIn(1000) : $("#sagyo_pop_photo03").fadeOut(1000);
        currentScroll > 4780 ? $("#top_hubnet_moji").fadeIn(1000) : $("#top_hubnet_moji").fadeOut(1000);
        currentScroll > 5080 ? $("#top_hubnet_img").fadeIn(1000) : $("#top_hubnet_img").fadeOut(1000);
        currentScroll > 5680 ? $("#top_recruit_moji").fadeIn(1000) : $("#top_recruit_moji").fadeOut(1000);
        currentScroll > 5950 ? $("#top_recruit_img").fadeIn(1000) : $("#top_recruit_img").fadeOut(1000);

        //start
        if (currentScroll < 400) {
            $('#car_icon').css({'top': addPx(-15), 'left': addPx(122)});
            $('#track_icon').css({'top': addPx(1735), 'left': addPx(112)});
        } else if (currentScroll >= 400 && currentScroll < 800) {
            top_y = -15 + (currentScroll - 400) * ((442 + 15) / 400);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(122)});
            $('#track_icon').css({'top': addPx(1735), 'left': addPx(112)});
        } else if (currentScroll >= 800 && currentScroll < 875) {
            top_y = 442 + (currentScroll - 800) * ((545 - 442) / 75);
            left_x = 122 + (currentScroll - 800) * ((150 - 122) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1735), 'left': addPx(112)});
        } else if (currentScroll >= 875 && currentScroll < 1200) {
            top_y = 545 + (currentScroll - 875) * ((854 - 545) / 325);
            left_x = 150 + (currentScroll - 875) * ((438 - 150) / 325);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1735), 'left': addPx(112)});
        } else if (currentScroll >= 1200 && currentScroll < 1275) {
            top_y = 854 + (currentScroll - 1200) * ((914 - 854) / 75);
            left_x = 438 + (currentScroll - 1200) * ((455 - 438) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1735), 'left': addPx(112)});
        } else if (currentScroll >= 1275 && currentScroll < 1350) {
            top_y = 914 + (currentScroll - 1275) * ((977 - 914) / 75);
            left_x = 455 - (currentScroll - 1275) * ((455 - 427) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1735), 'left': addPx(112)});
        } else if (currentScroll >= 1350 && currentScroll < 1650) {
            top_y = 977 + (currentScroll - 1350) * ((1268 - 977) / 300);
            left_x = 427 - (currentScroll - 1350) * ((427 - 153) / 300);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1735), 'left': addPx(112)});
        } else if (currentScroll >= 1650 && currentScroll < 1750) {
            top_y = 1268 + (currentScroll - 1650) * ((1320 - 1268) / 100);
            left_x = 153 - (currentScroll - 1650) * ((153 - 127) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1735), 'left': addPx(112)});
        } else if (currentScroll >= 1750 && currentScroll < 2050) {
            top_y = 1320 + (currentScroll - 1750) * ((1725 - 1320) / 300);
            left_x = 127 - (currentScroll - 1750) * ((127 - 122) / 300);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1735), 'left': addPx(112)});
        } else if (currentScroll >= 2050 && currentScroll < 2450) {
            left_x = 122 + (currentScroll - 2050) * ((1547 - 122) / 400);
            $('#car_icon').css({'top': addPx(1725), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1725 + 10), 'left': addPx(left_x - 10)});
            $('#ship_icon').css({'top': addPx(2590), 'left': addPx(1204)});
        } else if (currentScroll >= 2450 && currentScroll < 2600) {
            top_y = 1725 + (currentScroll - 2450) * ((2046 - 1725) / 150);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(1547)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(1547 - 10)});
            $('#ship_icon').css({'top': addPx(2590), 'left': addPx(1204)});
        } else if (currentScroll >= 2600 && currentScroll < 2750) {
            left_x = 1547 - (currentScroll - 2600) * ((1547 - 1000) / 150);
            $('#car_icon').css({'top': addPx(2046), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2590), 'left': addPx(1204)});
        } else if (currentScroll >= 2750 && currentScroll < 2800) {
            top_y = 2046 + (currentScroll - 2750) * ((2081 - 2046) / 50);
            left_x = 1000 - (currentScroll - 2750) * ((1000 - 883) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2590), 'left': addPx(1204)});
        } else if (currentScroll >= 2800 && currentScroll < 2850) {
            top_y = 2081 + (currentScroll - 2800) * ((2142 - 2081) / 50);
            left_x = 883 - (currentScroll - 2800) * ((883 - 826) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2590), 'left': addPx(1204)});
        } else if (currentScroll >= 2850 && currentScroll < 2900) {
            top_y = 2142 + (currentScroll - 2850) * ((2252 - 2142) / 50);
            left_x = 826 - (currentScroll - 2850) * ((826 - 797) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2590), 'left': addPx(1204)});
        } else if (currentScroll >= 2900 && currentScroll < 2950) {
            top_y = 2252 + (currentScroll - 2900) * ((2390 - 2252) / 50);
            left_x = 797 + (currentScroll - 2900) * ((851 - 797) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2590), 'left': addPx(1204)});
        } else if (currentScroll >= 2950 && currentScroll < 3025) {
            top_y = 2390 + (currentScroll - 2950) * ((2469 - 2390) / 75);
            left_x = 851 + (currentScroll - 2950) * ((987 - 851) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2590), 'left': addPx(1204)});
        } else if (currentScroll >= 3025 && currentScroll < 3125) {
            top_y = 2469 + (currentScroll - 3025) * ((2478 - 2469) / 100);
            left_x = 987 + (currentScroll - 3025) * ((1254 - 987) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2590), 'left': addPx(1204)});
        } else if (currentScroll >= 3125 && currentScroll < 3200) {
            top_y = 2478 + (currentScroll - 3125) * ((2610 - 2478) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(1254)});
            $('#ship_icon').css({'top': addPx(2590), 'left': addPx(1204)});
        } else if (currentScroll >= 3200 && currentScroll < 3300) {
            top_y = 2610 + (currentScroll - 3200) * ((2760 - 2610) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(1254)});
            $('#ship_icon').css({'top': addPx(top_y - 20), 'left': addPx(1254 - 50)}); //ship top:-20 left:-50
        } else if (currentScroll >= 3300 && currentScroll < 3700) {
            left_x = 1254 - (currentScroll - 3300) * ((1254 + 250) / 400);
            $('#car_icon').css({'top': addPx(2760), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2760 - 20), 'left': addPx(left_x - 50)});
        } else if (currentScroll >= 3700 && currentScroll < 4300) {
            $('#track_icon').css({'top': addPx(4033), 'left': addPx(450)}); //track top:+10 left:-10
        } else if (currentScroll >= 4300 && currentScroll < 4450) {
            left_x = (-250) + (currentScroll - 4300) * ((250 + 210) / 150);
            $('#car_icon').css({'top': addPx(4028), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(4028 - 20), 'left': addPx(left_x - 50)});
        } else if (currentScroll >= 4450 && currentScroll < 4500) {
            left_x = 210 + (currentScroll - 4450) * ((460 - 210) / 50);
            $('#car_icon').css({'top': addPx(4028), 'left': addPx(left_x)});
        } else if (currentScroll >= 4500 && currentScroll < 4600) {
            top_y = 4028 + (currentScroll - 4500) * ((4076 - 4028) / 100);
            left_x = 460 + (currentScroll - 4500) * ((731 - 460) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 4600 && currentScroll < 4750) {
            top_y = 4076 + (currentScroll - 4600) * ((4150 - 4076) / 150);
            left_x = 731 + (currentScroll - 4600) * ((942 - 731) / 150);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 4750 && currentScroll < 4900) {
            top_y = 4150 + (currentScroll - 4750) * ((4313 - 4150) / 150);
            left_x = 942 + (currentScroll - 4750) * ((1137 - 942) / 150);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 4900 && currentScroll < 5000) {
            top_y = 4313 + (currentScroll - 4900) * ((4404 - 4313) / 100);
            left_x = 1137 + (currentScroll - 4900) * ((1209 - 1137) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5000 && currentScroll < 5100) {
            top_y = 4404 + (currentScroll - 5000) * ((4543 - 4404) / 100);
            left_x = 1209 + (currentScroll - 5000) * ((1266 - 1209) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5100 && currentScroll < 5200) {
            top_y = 4543 + (currentScroll - 5100) * ((4666 - 4543) / 100);
            left_x = 1266 + (currentScroll - 5100) * ((1280 - 1266) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5200 && currentScroll < 5350) {
            top_y = 4666 + (currentScroll - 5200) * ((4865 - 4666) / 150);
            left_x = 1280 - (currentScroll - 5200) * ((1280 - 1247) / 150);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5350 && currentScroll < 5450) {
            top_y = 4865 + (currentScroll - 5350) * ((5006 - 4865) / 100);
            left_x = 1247 - (currentScroll - 5350) * ((1247 - 1169) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5450 && currentScroll < 5550) {
            top_y = 5006 + (currentScroll - 5450) * ((5129 - 5006) / 100);
            left_x = 1169 - (currentScroll - 5450) * ((1169 - 1047) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5550 && currentScroll < 5625) {
            top_y = 5129 + (currentScroll - 5550) * ((5179 - 5129) / 75);
            left_x = 1047 - (currentScroll - 5550) * ((1047 - 945) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5625 && currentScroll < 5825) {
            top_y = 5179 + (currentScroll - 5625) * ((5243 - 5179) / 200);
            left_x = 945 - (currentScroll - 5625) * ((945 - 424) / 200);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5825 && currentScroll < 5900) {
            top_y = 5243 + (currentScroll - 5825) * ((5297 - 5243) / 75);
            left_x = 424 - (currentScroll - 5825) * ((424 - 305) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5900 && currentScroll < 5950) {
            top_y = 5297 + (currentScroll - 5900) * ((5378 - 5297) / 50);
            left_x = 305 - (currentScroll - 5900) * ((305 - 237) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5950 && currentScroll < 6000) {
            top_y = 5378 + (currentScroll - 5950) * ((5455 - 5378) / 50);
            left_x = 237 - (currentScroll - 5950) * ((237 - 203) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 6000 && currentScroll < 6050) {
            top_y = 5455 + (currentScroll - 6000) * ((5597 - 5455) / 50);
            left_x = 203 - (currentScroll - 6000) * ((203 - 178) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 6050 && currentScroll < 6150) {
            top_y = 5597 + (currentScroll - 6050) * ((5743 - 5597) / 100);
            left_x = 178 + (currentScroll - 6050) * ((196 - 178) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 6150 && currentScroll < 6250) {
            top_y = 5743 + (currentScroll - 6150) * ((5896 - 5743) / 100);
            left_x = 196 + (currentScroll - 6150) * ((253 - 196) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 6250 && currentScroll < 6350) {
            top_y = 5896 + (currentScroll - 6250) * ((6026 - 5896) / 100);
            left_x = 253 + (currentScroll - 6250) * ((366 - 253) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 6350 && currentScroll < 6450) {
            top_y = 6026 + (currentScroll - 6350) * ((6100 - 6026) / 100);
            left_x = 366 + (currentScroll - 6350) * ((527 - 366) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 6450 && currentScroll < 6700) {
            left_x = 527 + (currentScroll - 6450) * ((1250 - 527) / 250);
            $('#car_icon').css({'top': addPx(6100), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(6100 + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 6700 && currentScroll < 6800) {
        } else if (currentScroll >= 6800 && currentScroll < 6900) {
            left_x = 1250 + (currentScroll - 6800) * ((2300 - 1250) / 100);
            $('#car_icon').css({'top': addPx(6100), 'left': addPx(1250)});
            $('#track_icon').css({'top': addPx(6100 + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 6900) {
            $('#track_icon').css({'top': addPx(1735), 'left': addPx(112)});
        }

    } else if (win_w >= 1500) {
        // currentScroll > 290 ? ($("#scroll_button").hide(),$('#car_icon').show()) : ($("#scroll_button").show(),$('#car_icon').hide());
        $(window).scrollTop() > 290 ? $("#scroll_button").hide() : $("#scroll_button").show();
        currentScroll > 290 ? $('#car_icon').show() : $('#car_icon').hide();
        currentScroll > 580 ? $("#cloud01").fadeIn(500) : $("#cloud01").fadeOut(500);
        currentScroll > 870 ? $("#cloud02").fadeIn(500) : $("#cloud02").fadeOut(500);
        currentScroll > 1350 ? $("#cloud03").fadeIn(500) : $("#cloud03").fadeOut(500);
        currentScroll > 1560 ? $("#cloud04").fadeIn(500) : $("#cloud04").fadeOut(500);
        currentScroll > 2080 ? $("#rikuso_moji01").fadeIn(500) : $("#rikuso_moji01").fadeOut(500);
        currentScroll > 2150 ? $("#rikuso_moji02").fadeIn(500) : $("#rikuso_moji02").fadeOut(500);
        currentScroll > 2500 ? $("#sagyo_pop01").fadeIn(500) : $("#sagyo_pop01").fadeOut(500);
        currentScroll > 2540 ? $("#sagyo_pop02").fadeIn(500) : $("#sagyo_pop02").fadeOut(500);
        currentScroll > 2580 ? $("#sagyo_pop03").fadeIn(500) : $("#sagyo_pop03").fadeOut(500);
        currentScroll > 2620 ? $("#sagyo_pop04").fadeIn(500) : $("#sagyo_pop04").fadeOut(500);
        currentScroll > 2660 ? $("#sagyo_pop05").fadeIn(500) : $("#sagyo_pop05").fadeOut(500);
        currentScroll > 2440 ? $("#sagyo_pop_img").fadeIn(1000) : $("#sagyo_pop_img").fadeOut(1000);
        currentScroll > 2470 ? $("#sagyo_pop_photo01").fadeIn(1000) : $("#sagyo_pop_photo01").fadeOut(1000);
        currentScroll > 2500 ? $("#sagyo_pop_photo02").fadeIn(1000) : $("#sagyo_pop_photo02").fadeOut(1000);
        currentScroll > 2520 ? $("#sagyo_pop_photo03").fadeIn(1000) : $("#sagyo_pop_photo03").fadeOut(1000);
        currentScroll > 4430 ? $("#top_hubnet_moji").fadeIn(1000) : $("#top_hubnet_moji").fadeOut(1000);
        currentScroll > 4600 ? $("#top_hubnet_img").fadeIn(1000) : $("#top_hubnet_img").fadeOut(1000);
        currentScroll > 5250 ? $("#top_recruit_moji").fadeIn(1000) : $("#top_recruit_moji").fadeOut(1000);
        currentScroll > 5400 ? $("#top_recruit_img").fadeIn(1000) : $("#top_recruit_img").fadeOut(1000);

        if (currentScroll < 400) {
            $('#car_icon').css({'top': addPx(-15), 'left': addPx(100)});
            $('#track_icon').css({'top': addPx(1530), 'left': addPx(90)});
        } else if (currentScroll >= 400 && currentScroll < 800) {
            top_y = -15 + (currentScroll - 400) * ((390 + 15) / 400);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(100)});
            $('#track_icon').css({'top': addPx(1530), 'left': addPx(90)});
        } else if (currentScroll >= 800 && currentScroll < 875) {
            top_y = 390 + (currentScroll - 800) * ((470 - 390) / 75);
            left_x = 100 + (currentScroll - 800) * ((125 - 100) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1530), 'left': addPx(90)});
        } else if (currentScroll >= 875 && currentScroll < 1200) {
            top_y = 470 + (currentScroll - 875) * ((734 - 470) / 325);
            left_x = 125 + (currentScroll - 875) * ((374 - 125) / 325);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1530), 'left': addPx(90)});
        } else if (currentScroll >= 1200 && currentScroll < 1275) {
            top_y = 734 + (currentScroll - 1200) * ((789 - 734) / 75);
            left_x = 374 + (currentScroll - 1200) * ((398 - 374) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1530), 'left': addPx(90)});
        } else if (currentScroll >= 1275 && currentScroll < 1350) {
            top_y = 789 + (currentScroll - 1275) * ((854 - 789) / 75);
            left_x = 398 - (currentScroll - 1275) * ((398 - 374) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1530), 'left': addPx(90)});
        } else if (currentScroll >= 1350 && currentScroll < 1650) {
            top_y = 854 + (currentScroll - 1350) * ((1112 - 854) / 300);
            left_x = 374 - (currentScroll - 1350) * ((374 - 130) / 300);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1530), 'left': addPx(90)});
        } else if (currentScroll >= 1650 && currentScroll < 1700) {
            top_y = 1112 + (currentScroll - 1650) * ((1150 - 1112) / 50);
            left_x = 130 - (currentScroll - 1650) * ((130 - 104) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1530), 'left': addPx(90)});
        } else if (currentScroll >= 1700 && currentScroll < 2000) {
            top_y = 1150 + (currentScroll - 1700) * ((1520 - 1150) / 300);
            left_x = 104 - (currentScroll - 1700) * ((104 - 100) / 300);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1530), 'left': addPx(90)});
        } else if (currentScroll >= 2000 && currentScroll < 2300) {
            left_x = 100 + (currentScroll - 2000) * ((1360 - 100) / 300);
            $('#car_icon').css({'top': addPx(1520), 'left': addPx(left_x)});	//track top:+10 left:-10
            $('#track_icon').css({'top': addPx(1520 + 10), 'left': addPx(left_x - 10)});
            $('#ship_icon').css({'top': addPx(2284), 'left': addPx(1085)});
        } else if (currentScroll >= 2300 && currentScroll < 2450) {
            top_y = 1520 + (currentScroll - 2300) * ((1796 - 1520) / 150);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(1360)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(1360 - 10)});
            $('#ship_icon').css({'top': addPx(2284), 'left': addPx(1085)});
        } else if (currentScroll >= 2450 && currentScroll < 2550) {
            left_x = 1360 - (currentScroll - 2450) * ((1360 - 818) / 100);
            $('#car_icon').css({'top': addPx(1796), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2284), 'left': addPx(1085)});
        } else if (currentScroll >= 2550 && currentScroll < 2600) {
            top_y = 1796 + (currentScroll - 2550) * ((1837 - 1796) / 50);
            left_x = 818 - (currentScroll - 2550) * ((818 - 758) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2284), 'left': addPx(1085)});
        } else if (currentScroll >= 2600 && currentScroll < 2650) {
            top_y = 1837 + (currentScroll - 2600) * ((1971 - 1837) / 50);
            left_x = 758 - (currentScroll - 2600) * ((758 - 694) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2284), 'left': addPx(1085)});
        } else if (currentScroll >= 2650 && currentScroll < 2700) {
            top_y = 1971 + (currentScroll - 2650) * ((2134 - 1971) / 50);
            left_x = 694 + (currentScroll - 2650) * ((782 - 694) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2284), 'left': addPx(1085)});
        } else if (currentScroll >= 2700 && currentScroll < 2750) {
            top_y = 2134 + (currentScroll - 2700) * ((2168 - 2134) / 50);
            left_x = 782 + (currentScroll - 2700) * ((900 - 782) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2284), 'left': addPx(1085)});
        } else if (currentScroll >= 2750 && currentScroll < 2850) {
            top_y = 2168 + (currentScroll - 2750) * ((2192 - 2168) / 100);
            left_x = 900 + (currentScroll - 2750) * ((1135 - 900) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2284), 'left': addPx(1085)});
        } else if (currentScroll >= 2850 && currentScroll < 2950) {
            top_y = 2192 + (currentScroll - 2850) * ((2304 - 2192) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(1135)});
            $('#ship_icon').css({'top': addPx(2284), 'left': addPx(1085)});
        } else if (currentScroll >= 2950 && currentScroll < 3050) {
            top_y = 2304 + (currentScroll - 2950) * ((2428 - 2304) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(1135)});	//ship top:-20 left:-50
            $('#ship_icon').css({'top': addPx(top_y - 20), 'left': addPx(1135 - 50)});
        } else if (currentScroll >= 3050 && currentScroll < 3450) {
            left_x = 1135 - (currentScroll - 3050) * ((1135 + 250) / 400);
            $('#car_icon').css({'top': addPx(2428), 'left': addPx(left_x)});	//ship top:-20 left:-50
            $('#ship_icon').css({'top': addPx(2428 - 20), 'left': addPx(left_x - 50)});
        } else if (currentScroll >= 3450 && currentScroll < 4000) {
            $('#track_icon').css({'top': addPx(3688), 'left': addPx(400)});	//track top:+10 left:-10
        } else if (currentScroll >= 4000 && currentScroll < 4150) {
            left_x = (-250) + (currentScroll - 4000) * ((250 + 160) / 150);
            $('#car_icon').css({'top': addPx(3678), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(3678 - 20), 'left': addPx(left_x - 50)});
        } else if (currentScroll >= 4150 && currentScroll < 4200) {
            left_x = 210 + (currentScroll - 4150) * ((410 - 210) / 50);
            $('#car_icon').css({'top': addPx(3678), 'left': addPx(left_x)});
        } else if (currentScroll >= 4200 && currentScroll < 4300) {
            top_y = 3678 + (currentScroll - 4200) * ((3740 - 3678) / 100);
            left_x = 410 + (currentScroll - 4200) * ((660 - 410) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 4300 && currentScroll < 4400) {
            top_y = 3740 + (currentScroll - 4300) * ((3830 - 3740) / 100);
            left_x = 660 + (currentScroll - 4300) * ((855 - 660) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 4400 && currentScroll < 4500) {
            top_y = 3830 + (currentScroll - 4400) * ((3990 - 3830) / 100);
            left_x = 855 + (currentScroll - 4400) * ((1030 - 855) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 4500 && currentScroll < 4575) {
            top_y = 3990 + (currentScroll - 4500) * ((4098 - 3990) / 75);
            left_x = 1030 + (currentScroll - 4500) * ((1093 - 1030) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 4575 && currentScroll < 4650) {
            top_y = 4098 + (currentScroll - 4575) * ((4203 - 4098) / 75);
            left_x = 1093 + (currentScroll - 4575) * ((1117 - 1093) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 4650 && currentScroll < 4725) {
            top_y = 4203 + (currentScroll - 4650) * ((4282 - 4203) / 75);
            left_x = 1117 + (currentScroll - 4650) * ((1122 - 1117) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 4725 && currentScroll < 4850) {
            top_y = 4282 + (currentScroll - 4725) * ((4470 - 4282) / 125);
            left_x = 1122 - (currentScroll - 4725) * ((1122 - 1083) / 125);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 4850 && currentScroll < 4950) {
            top_y = 4470 + (currentScroll - 4850) * ((4578 - 4470) / 100);
            left_x = 1083 - (currentScroll - 4850) * ((1083 - 1009) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 4950 && currentScroll < 5050) {
            top_y = 4578 + (currentScroll - 4950) * ((4678 - 4578) / 100);
            left_x = 1009 - (currentScroll - 4950) * ((1009 - 900) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5050 && currentScroll < 5100) {
            top_y = 4678 + (currentScroll - 5050) * ((4719 - 4678) / 50);
            left_x = 900 - (currentScroll - 5050) * ((900 - 818) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5100 && currentScroll < 5300) {
            top_y = 4719 + (currentScroll - 5100) * ((4761 - 4719) / 200);
            left_x = 818 - (currentScroll - 5100) * ((818 - 414) / 200);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5300 && currentScroll < 5350) {
            top_y = 4761 + (currentScroll - 5300) * ((4794 - 4761) / 50);
            left_x = 414 - (currentScroll - 5300) * ((414 - 306) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5350 && currentScroll < 5400) {
            top_y = 4794 + (currentScroll - 5350) * ((4853 - 4794) / 50);
            left_x = 306 - (currentScroll - 5350) * ((306 - 228) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5400 && currentScroll < 5450) {
            top_y = 4853 + (currentScroll - 5400) * ((4915 - 4853) / 50);
            left_x = 228 - (currentScroll - 5400) * ((228 - 185) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5450 && currentScroll < 5500) {
            top_y = 4915 + (currentScroll - 5450) * ((4977 - 4915) / 50);
            left_x = 185 - (currentScroll - 5450) * ((185 - 164) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5500 && currentScroll < 5575) {
            top_y = 4977 + (currentScroll - 5500) * ((5084 - 4977) / 75);
            left_x = 164 - (currentScroll - 5500) * ((164 - 149) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5575 && currentScroll < 5650) {
            top_y = 5084 + (currentScroll - 5575) * ((5202 - 5084) / 75);
            left_x = 149 + (currentScroll - 5575) * ((162 - 149) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5650 && currentScroll < 5750) {
            top_y = 5202 + (currentScroll - 5650) * ((5331 - 5202) / 100);
            left_x = 162 + (currentScroll - 5650) * ((208 - 162) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5750 && currentScroll < 5850) {
            top_y = 5331 + (currentScroll - 5750) * ((5463 - 5331) / 100);
            left_x = 208 + (currentScroll - 5750) * ((319 - 208) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5850 && currentScroll < 5950) {
            top_y = 5463 + (currentScroll - 5850) * ((5530 - 5463) / 100);
            left_x = 319 + (currentScroll - 5850) * ((463 - 319) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5950 && currentScroll < 6200) {
            left_x = 463 + (currentScroll - 5950) * ((1100 - 463) / 250);
            $('#car_icon').css({'top': addPx(5530), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(5530 + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 6200 && currentScroll < 6300) {
        } else if (currentScroll >= 6300 && currentScroll < 6400) {
            left_x = 1100 + (currentScroll - 6300) * ((2000 - 1100) / 100);
            $('#car_icon').css({'top': addPx(5530), 'left': addPx(1100)});
            $('#track_icon').css({'top': addPx(5530 + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 6400) {
            $('#track_icon').css({'top': addPx(1530), 'left': addPx(90)});
        }

    } else if (win_w >= 1200) {
        // currentScroll > 148 ? ($("#scroll_button").hide(),$('#car_icon').show()) : ($("#scroll_button").show(),$('#car_icon').hide());
        $(window).scrollTop() > 148 ? $("#scroll_button").hide() : $("#scroll_button").show();
        currentScroll > 148 ? $('#car_icon').show() : $('#car_icon').hide();
        currentScroll > 628 ? $("#cloud01").fadeIn(500) : $("#cloud01").fadeOut(500);
        currentScroll > 858 ? $("#cloud02").fadeIn(500) : $("#cloud02").fadeOut(500);
        currentScroll > 1242 ? $("#cloud03").fadeIn(500) : $("#cloud03").fadeOut(500);
        currentScroll > 1472 ? $("#cloud04").fadeIn(500) : $("#cloud04").fadeOut(500);
        currentScroll > 2065 ? $("#rikuso_moji01").fadeIn(500) : $("#rikuso_moji01").fadeOut(500);
        currentScroll > 2150 ? $("#rikuso_moji02").fadeIn(500) : $("#rikuso_moji02").fadeOut(500);
        currentScroll > 2480 ? $("#sagyo_pop01").fadeIn(500) : $("#sagyo_pop01").fadeOut(500);
        currentScroll > 2510 ? $("#sagyo_pop02").fadeIn(500) : $("#sagyo_pop02").fadeOut(500);
        currentScroll > 2540 ? $("#sagyo_pop03").fadeIn(500) : $("#sagyo_pop03").fadeOut(500);
        currentScroll > 2570 ? $("#sagyo_pop04").fadeIn(500) : $("#sagyo_pop04").fadeOut(500);
        currentScroll > 2600 ? $("#sagyo_pop05").fadeIn(500) : $("#sagyo_pop05").fadeOut(500);
        currentScroll > 2400 ? $("#sagyo_pop_img").fadeIn(1000) : $("#sagyo_pop_img").fadeOut(1000);
        currentScroll > 2422 ? $("#sagyo_pop_photo01").fadeIn(1000) : $("#sagyo_pop_photo01").fadeOut(1000);
        currentScroll > 2450 ? $("#sagyo_pop_photo02").fadeIn(1000) : $("#sagyo_pop_photo02").fadeOut(1000);
        currentScroll > 2498 ? $("#sagyo_pop_photo03").fadeIn(1000) : $("#sagyo_pop_photo03").fadeOut(1000);
        currentScroll > 4060 ? $("#top_hubnet_moji").fadeIn(1000) : $("#top_hubnet_moji").fadeOut(1000);
        currentScroll > 4270 ? $("#top_hubnet_img").fadeIn(1000) : $("#top_hubnet_img").fadeOut(1000);
        currentScroll > 4675 ? $("#top_recruit_moji").fadeIn(1000) : $("#top_recruit_moji").fadeOut(1000);
        currentScroll > 4830 ? $("#top_recruit_img").fadeIn(1000) : $("#top_recruit_img").fadeOut(1000);

        //start
        if (currentScroll < 400) {
            $('#car_icon').css({'top': addPx(-115), 'left': addPx(69)});
            $('#track_icon').css({'top': addPx(1550), 'left': addPx(58)});
        } else if (currentScroll >= 400 && currentScroll < 800) {
            top_y = -115 + (currentScroll - 400) * ((390 + 115) / 400);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(69)});
            $('#track_icon').css({'top': addPx(1550), 'left': addPx(58)});
        } else if (currentScroll >= 800 && currentScroll < 875) {
            top_y = 390 + (currentScroll - 800) * ((475 - 390) / 75);
            left_x = 69 + (currentScroll - 800) * ((104 - 69) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1550), 'left': addPx(58)});
        } else if (currentScroll >= 875 && currentScroll < 1200) {
            top_y = 475 + (currentScroll - 875) * ((737 - 475) / 325);
            left_x = 104 + (currentScroll - 875) * ((291 - 104) / 325);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1550), 'left': addPx(58)});
        } else if (currentScroll >= 1200 && currentScroll < 1250) {
            top_y = 737 + (currentScroll - 1200) * ((790 - 737) / 50);
            left_x = 291 + (currentScroll - 1200) * ((310 - 291) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1550), 'left': addPx(58)});
        } else if (currentScroll >= 1250 && currentScroll < 1300) {
            top_y = 790 + (currentScroll - 1250) * ((861 - 790) / 50);
            left_x = 310 - (currentScroll - 1250) * ((310 - 278) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1550), 'left': addPx(58)});
        } else if (currentScroll >= 1300 && currentScroll < 1600) {
            top_y = 861 + (currentScroll - 1300) * ((1094 - 861) / 300);
            left_x = 278 - (currentScroll - 1300) * ((278 - 105) / 300);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1550), 'left': addPx(58)});
        } else if (currentScroll >= 1600 && currentScroll < 1650) {
            top_y = 1094 + (currentScroll - 1600) * ((1170 - 1094) / 50);
            left_x = 105 - (currentScroll - 1600) * ((105 - 70) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1550), 'left': addPx(58)});
        } else if (currentScroll >= 1650 && currentScroll < 1950) {
            top_y = 1170 + (currentScroll - 1650) * ((1540 - 1170) / 300);
            left_x = 70 - (currentScroll - 1650) * ((70 - 68) / 300);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1550), 'left': addPx(58)});
        } else if (currentScroll >= 1950 && currentScroll < 2250) {
            left_x = 68 + (currentScroll - 1950) * ((1074 - 68) / 300);
            $('#car_icon').css({'top': addPx(1540), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1540 + 10), 'left': addPx(left_x - 10)});	//track top:+10 left:-10
            $('#ship_icon').css({'top': addPx(2170), 'left': addPx(806)});
        } else if (currentScroll >= 2250 && currentScroll < 2400) {
            top_y = 1540 + (currentScroll - 2250) * ((1765 - 1540) / 150);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(1074)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(1074 - 10)});
            $('#ship_icon').css({'top': addPx(2170), 'left': addPx(806)});
        } else if (currentScroll >= 2400 && currentScroll < 2500) {
            left_x = 1074 - (currentScroll - 2400) * ((1074 - 710) / 100);
            $('#car_icon').css({'top': addPx(1765), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2170), 'left': addPx(806)});
        } else if (currentScroll >= 2500 && currentScroll < 2550) {
            top_y = 1765 + (currentScroll - 2500) * ((1800 - 1765) / 50);
            left_x = 710 - (currentScroll - 2500) * ((710 - 587) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2170), 'left': addPx(806)});
        } else if (currentScroll >= 2550 && currentScroll < 2600) {
            top_y = 1800 + (currentScroll - 2550) * ((1883 - 1800) / 50);
            left_x = 587 - (currentScroll - 2550) * ((587 - 545) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2170), 'left': addPx(806)});
        } else if (currentScroll >= 2600 && currentScroll < 2650) {
            top_y = 1883 + (currentScroll - 2600) * ((1986 - 1883) / 50);
            left_x = 545 + (currentScroll - 2600) * ((575 - 545) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2170), 'left': addPx(806)});
        } else if (currentScroll >= 2650 && currentScroll < 2700) {
            top_y = 1986 + (currentScroll - 2650) * ((2058 - 1986) / 50);
            left_x = 575 + (currentScroll - 2650) * ((659 - 575) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2170), 'left': addPx(806)});
        } else if (currentScroll >= 2700 && currentScroll < 2775) {
            top_y = 2058 + (currentScroll - 2700) * ((2068 - 2058) / 75);
            left_x = 659 + (currentScroll - 2700) * ((862 - 659) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2170), 'left': addPx(806)});
        } else if (currentScroll >= 2775 && currentScroll < 2850) {
            top_y = 2068 + (currentScroll - 2775) * ((2190 - 2068) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(862)});
            $('#ship_icon').css({'top': addPx(2170), 'left': addPx(806)});
        } else if (currentScroll >= 2850 && currentScroll < 2900) {
            top_y = 2190 + (currentScroll - 2850) * ((2270 - 2190) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(862)});
            $('#ship_icon').css({'top': addPx(top_y - 20), 'left': addPx(862 - 56)});	//ship top:-20 left:-56
        } else if (currentScroll >= 2900 && currentScroll < 3300) {
            left_x = 862 - (currentScroll - 2900) * ((862 + 250) / 400);
            $('#car_icon').css({'top': addPx(2270), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2270 - 20), 'left': addPx(left_x - 56)});
        } else if (currentScroll >= 3300 && currentScroll < 3650) {
            $('#track_icon').css({'top': addPx(3445), 'left': addPx(306)});	//track top:+10 left:-10
        } else if (currentScroll >= 3650 && currentScroll < 3800) {
            left_x = (-250) + (currentScroll - 3650) * ((250 + 91) / 150);
            $('#car_icon').css({'top': addPx(3435), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(3435 - 20), 'left': addPx(left_x - 56)});
        } else if (currentScroll >= 3800 && currentScroll < 3900) {
            left_x = 91 + (currentScroll - 3800) * ((316 - 91) / 100);
            $('#car_icon').css({'top': addPx(3435), 'left': addPx(left_x)});
        } else if (currentScroll >= 3900 && currentScroll < 4000) {
            top_y = 3435 + (currentScroll - 3900) * ((3478 - 3435) / 100);
            left_x = 316 + (currentScroll - 3900) * ((574 - 316) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 4000 && currentScroll < 4100) {
            top_y = 3478 + (currentScroll - 4000) * ((3603 - 3478) / 100);
            left_x = 574 + (currentScroll - 4000) * ((760 - 574) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 4100 && currentScroll < 4200) {
            top_y = 3603 + (currentScroll - 4100) * ((3738 - 3603) / 100);
            left_x = 760 + (currentScroll - 4100) * ((857 - 760) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 4200 && currentScroll < 4275) {
            top_y = 3738 + (currentScroll - 4200) * ((3828 - 3738) / 75);
            left_x = 857 + (currentScroll - 4200) * ((878 - 857) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 4275 && currentScroll < 4350) {
            top_y = 3828 + (currentScroll - 4275) * ((3915 - 3828) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(878)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(878 - 10)});
        } else if (currentScroll >= 4350 && currentScroll < 4450) {
            top_y = 3915 + (currentScroll - 4350) * ((4072 - 3915) / 100);
            left_x = 878 - (currentScroll - 4350) * ((878 - 832) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 4450 && currentScroll < 4525) {
            top_y = 4072 + (currentScroll - 4450) * ((4163 - 4072) / 75);
            left_x = 832 - (currentScroll - 4450) * ((832 - 761) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 4525 && currentScroll < 4600) {
            top_y = 4163 + (currentScroll - 4525) * ((4240 - 4163) / 75);
            left_x = 761 - (currentScroll - 4525) * ((761 - 650) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 4600 && currentScroll < 4800) {
            top_y = 4240 + (currentScroll - 4600) * ((4283 - 4240) / 200);
            left_x = 650 - (currentScroll - 4600) * ((650 - 297) / 200);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 4800 && currentScroll < 4875) {
            top_y = 4283 + (currentScroll - 4800) * ((4349 - 4283) / 75);
            left_x = 297 - (currentScroll - 4800) * ((297 - 167) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 4875 && currentScroll < 4925) {
            top_y = 4349 + (currentScroll - 4875) * ((4411 - 4349) / 50);
            left_x = 167 - (currentScroll - 4875) * ((167 - 128) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 4925 && currentScroll < 5050) {
            top_y = 4411 + (currentScroll - 4925) * ((4558 - 4411) / 125);
            left_x = 128 - (currentScroll - 4925) * ((128 - 104) / 125);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5050 && currentScroll < 5150) {
            top_y = 4558 + (currentScroll - 5050) * ((4720 - 4558) / 100);
            left_x = 104 + (currentScroll - 5050) * ((147 - 104) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5150 && currentScroll < 5200) {
            top_y = 4720 + (currentScroll - 5150) * ((4812 - 4720) / 50);
            left_x = 147 + (currentScroll - 5150) * ((218 - 147) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5200 && currentScroll < 5275) {
            top_y = 4812 + (currentScroll - 5200) * ((4887 - 4812) / 75);
            left_x = 218 + (currentScroll - 5200) * ((344 - 218) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5275 && currentScroll < 5500) {
            left_x = 344 + (currentScroll - 5275) * ((840 - 344) / 225);
            $('#car_icon').css({'top': addPx(4887), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(4887 + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5500 && currentScroll < 5600) {
        } else if (currentScroll >= 5600 && currentScroll < 5700) {
            left_x = 840 + (currentScroll - 5600) * ((1600 - 840) / 100);
            $('#car_icon').css({'top': addPx(4887), 'left': addPx(840)});
            $('#track_icon').css({'top': addPx(4887 + 10), 'left': addPx(left_x - 10)});
        } else if (currentScroll >= 5700) {
            $('#track_icon').css({'top': addPx(1550), 'left': addPx(58)});
        }
    } else if (win_w >= 992) {
        // currentScroll > 174 ? ($("#scroll_button").hide(),$('#car_icon').show()) : ($("#scroll_button").show(),$('#car_icon').hide());
        $(window).scrollTop() > 174 ? $("#scroll_button").hide() : $("#scroll_button").show();
        currentScroll > 174 ? $('#car_icon').show() : $('#car_icon').hide();
        currentScroll > 600 ? $("#cloud01").fadeIn(500) : $("#cloud01").fadeOut(500);
        currentScroll > 865 ? $("#cloud02").fadeIn(500) : $("#cloud02").fadeOut(500);
        currentScroll > 1075 ? $("#cloud03").fadeIn(500) : $("#cloud03").fadeOut(500);
        currentScroll > 1495 ? $("#cloud04").fadeIn(500) : $("#cloud04").fadeOut(500);
        currentScroll > 1690 ? $("#rikuso_moji01").fadeIn(500) : $("#rikuso_moji01").fadeOut(500);
        currentScroll > 1758 ? $("#rikuso_moji02").fadeIn(500) : $("#rikuso_moji02").fadeOut(500);
        currentScroll > 2100 ? $("#sagyo_pop01").fadeIn(500) : $("#sagyo_pop01").fadeOut(500);
        currentScroll > 2130 ? $("#sagyo_pop02").fadeIn(500) : $("#sagyo_pop02").fadeOut(500);
        currentScroll > 2160 ? $("#sagyo_pop03").fadeIn(500) : $("#sagyo_pop03").fadeOut(500);
        currentScroll > 2190 ? $("#sagyo_pop04").fadeIn(500) : $("#sagyo_pop04").fadeOut(500);
        currentScroll > 2220 ? $("#sagyo_pop05").fadeIn(500) : $("#sagyo_pop05").fadeOut(500);
        currentScroll > 1996 ? $("#sagyo_pop_img").fadeIn(1000) : $("#sagyo_pop_img").fadeOut(1000);
        currentScroll > 2020 ? $("#sagyo_pop_photo01").fadeIn(1000) : $("#sagyo_pop_photo01").fadeOut(1000);
        currentScroll > 2040 ? $("#sagyo_pop_photo02").fadeIn(1000) : $("#sagyo_pop_photo02").fadeOut(1000);
        currentScroll > 2060 ? $("#sagyo_pop_photo03").fadeIn(1000) : $("#sagyo_pop_photo03").fadeOut(1000);
        currentScroll > 3615 ? $("#top_hubnet_moji").fadeIn(1000) : $("#top_hubnet_moji").fadeOut(1000);
        currentScroll > 3770 ? $("#top_hubnet_img").fadeIn(1000) : $("#top_hubnet_img").fadeOut(1000);
        currentScroll > 4110 ? $("#top_recruit_moji").fadeIn(1000) : $("#top_recruit_moji").fadeOut(1000);
        currentScroll > 4250 ? $("#top_recruit_img").fadeIn(1000) : $("#top_recruit_img").fadeOut(1000);

        //start
        if (currentScroll < 400) {
            $('#car_icon').css({'top': addPx(-84), 'left': addPx(52)});
            $('#track_icon').css({'top': addPx(1293), 'left': addPx(49)});
        } else if (currentScroll >= 400 && currentScroll < 700) {
            top_y = -84 + (currentScroll - 400) * ((308 + 84) / 300);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(52)});
            $('#track_icon').css({'top': addPx(1293), 'left': addPx(49)});
        } else if (currentScroll >= 700 && currentScroll < 775) {
            top_y = 308 + (currentScroll - 700) * ((372 - 308) / 75);
            left_x = 52 + (currentScroll - 700) * ((71 - 52) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1293), 'left': addPx(49)});
        } else if (currentScroll >= 775 && currentScroll < 1000) {
            top_y = 372 + (currentScroll - 775) * ((607 - 372) / 225);
            left_x = 71 + (currentScroll - 775) * ((239 - 71) / 225);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1293), 'left': addPx(49)});
        } else if (currentScroll >= 1000 && currentScroll < 1050) {
            top_y = 607 + (currentScroll - 1000) * ((654 - 607) / 50);
            left_x = 239 + (currentScroll - 1000) * ((251 - 239) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1293), 'left': addPx(49)});
        } else if (currentScroll >= 1050 && currentScroll < 1100) {
            top_y = 654 + (currentScroll - 1050) * ((706 - 654) / 50);
            left_x = 251 - (currentScroll - 1050) * ((251 - 229) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1293), 'left': addPx(49)});
        } else if (currentScroll >= 1100 && currentScroll < 1325) {
            top_y = 706 + (currentScroll - 1100) * ((919 - 706) / 225);
            left_x = 229 - (currentScroll - 1100) * ((229 - 72) / 225);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1293), 'left': addPx(49)});
        } else if (currentScroll >= 1325 && currentScroll < 1375) {
            top_y = 919 + (currentScroll - 1325) * ((948 - 919) / 50);
            left_x = 72 - (currentScroll - 1325) * ((72 - 55) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1293), 'left': addPx(49)});
        } else if (currentScroll >= 1375 && currentScroll < 1600) {
            top_y = 948 + (currentScroll - 1375) * ((1277 - 948) / 225);
            left_x = 55 - (currentScroll - 1375) * ((55 - 52) / 225);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1293), 'left': addPx(49)});	//track top+15 left-3
        } else if (currentScroll >= 1600 && currentScroll < 1850) {
            left_x = 52 + (currentScroll - 1600) * ((891 - 52) / 250);
            $('#car_icon').css({'top': addPx(1277), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1277 + 15), 'left': addPx(left_x - 3)});
            $('#ship_icon').css({'top': addPx(1798), 'left': addPx(672)});
        } else if (currentScroll >= 1850 && currentScroll < 2000) {
            top_y = 1277 + (currentScroll - 1850) * ((1470 - 1277) / 150);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(891)});
            $('#track_icon').css({'top': addPx(top_y + 15), 'left': addPx(891 - 3)});
            $('#ship_icon').css({'top': addPx(1798), 'left': addPx(672)});
        } else if (currentScroll >= 2000 && currentScroll < 2150) {
            left_x = 891 - (currentScroll - 2000) * ((891 - 572) / 150);
            $('#car_icon').css({'top': addPx(1470), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(1798), 'left': addPx(672)});
        } else if (currentScroll >= 2150 && currentScroll < 2200) {
            top_y = 1470 + (currentScroll - 2150) * ((1508 - 1470) / 50);
            left_x = 572 - (currentScroll - 2150) * ((572 - 472) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(1798), 'left': addPx(672)});
        } else if (currentScroll >= 2200 && currentScroll < 2250) {
            top_y = 1508 + (currentScroll - 2200) * ((1579 - 1508) / 50);
            left_x = 472 - (currentScroll - 2200) * ((472 - 449) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(1798), 'left': addPx(672)});
        } else if (currentScroll >= 2250 && currentScroll < 2300) {
            top_y = 1579 + (currentScroll - 2250) * ((1675 - 1579) / 50);
            left_x = 449 + (currentScroll - 2250) * ((490 - 449) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(1798), 'left': addPx(672)});
        } else if (currentScroll >= 2300 && currentScroll < 2350) {
            top_y = 1675 + (currentScroll - 2300) * ((1720 - 1675) / 50);
            left_x = 490 + (currentScroll - 2300) * ((595 - 490) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(1798), 'left': addPx(672)});
        } else if (currentScroll >= 2350 && currentScroll < 2400) {
            left_x = 595 + (currentScroll - 2350) * ((710 - 595) / 50);
            $('#car_icon').css({'top': addPx(1720), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(1798), 'left': addPx(672)});
        } else if (currentScroll >= 2400 && currentScroll < 2475) {
            top_y = 1720 + (currentScroll - 2400) * ((1808 - 1720) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(710)});
            $('#ship_icon').css({'top': addPx(1798), 'left': addPx(672)});
        } else if (currentScroll >= 2475 && currentScroll < 2525) {
            top_y = 1808 + (currentScroll - 2475) * ((1881 - 1808) / 50);
            left_x = 710 - (currentScroll - 2475) * ((710 - 658) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(top_y - 10), 'left': addPx(left_x - 38)});	//ship top:-10 left:-38
        } else if (currentScroll >= 2525 && currentScroll < 2900) {
            left_x = 658 - (currentScroll - 2525) * ((658 + 250) / 375);
            $('#car_icon').css({'top': addPx(1881), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(1881 - 10), 'left': addPx(left_x - 38)});
        } else if (currentScroll >= 2900 && currentScroll < 3300) {
            //stop
            $('#track_icon').css({'top': addPx(3041), 'left': addPx(267)});
        } else if (currentScroll >= 3300 && currentScroll < 3450) {
            left_x = (-250) + (currentScroll - 3300) * ((250 + 70) / 150);
            $('#car_icon').css({'top': addPx(3026), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(3026 - 10), 'left': addPx(left_x - 38)});
        } else if (currentScroll >= 3450 && currentScroll < 3550) {
            left_x = 70 + (currentScroll - 3450) * ((270 - 70) / 100);
            $('#car_icon').css({'top': addPx(3026), 'left': addPx(left_x)});
        } else if (currentScroll >= 3550 && currentScroll < 3650) {
            top_y = 3026 + (currentScroll - 3550) * ((3069 - 3026) / 100);
            left_x = 270 + (currentScroll - 3550) * ((461 - 270) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 15), 'left': addPx(left_x - 3)});
        } else if (currentScroll >= 3650 && currentScroll < 3750) {
            top_y = 3069 + (currentScroll - 3650) * ((3171 - 3069) / 100);
            left_x = 461 + (currentScroll - 3650) * ((628 - 461) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 15), 'left': addPx(left_x - 3)});
        } else if (currentScroll >= 3750 && currentScroll < 3850) {
            top_y = 3171 + (currentScroll - 3750) * ((3280 - 3171) / 100);
            left_x = 628 + (currentScroll - 3750) * ((707 - 628) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 15), 'left': addPx(left_x - 3)});
        } else if (currentScroll >= 3850 && currentScroll < 3925) {
            top_y = 3280 + (currentScroll - 3850) * ((3362 - 3280) / 75);
            left_x = 707 + (currentScroll - 3850) * ((728 - 707) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 15), 'left': addPx(left_x - 3)});
        } else if (currentScroll >= 3925 && currentScroll < 3975) {
            top_y = 3362 + (currentScroll - 3925) * ((3433 - 3362) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(728)});
            $('#track_icon').css({'top': addPx(top_y + 15), 'left': addPx(728 - 3)});
        } else if (currentScroll >= 3975 && currentScroll < 4050) {
            top_y = 3433 + (currentScroll - 3975) * ((3542 - 3433) / 75);
            left_x = 728 - (currentScroll - 3975) * ((728 - 698) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 15), 'left': addPx(left_x - 3)});
        } else if (currentScroll >= 4050 && currentScroll < 4100) {
            top_y = 3542 + (currentScroll - 4050) * ((3619 - 3542) / 50);
            left_x = 698 - (currentScroll - 4050) * ((698 - 648) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 15), 'left': addPx(left_x - 3)});
        } else if (currentScroll >= 4100 && currentScroll < 4150) {
            top_y = 3619 + (currentScroll - 4100) * ((3686 - 3619) / 50);
            left_x = 648 - (currentScroll - 4100) * ((648 - 564) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 15), 'left': addPx(left_x - 3)});
        } else if (currentScroll >= 4150 && currentScroll < 4350) {
            top_y = 3686 + (currentScroll - 4150) * ((3739 - 3686) / 200);
            left_x = 564 - (currentScroll - 4150) * ((564 - 235) / 200);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 15), 'left': addPx(left_x - 3)});
        } else if (currentScroll >= 4350 && currentScroll < 4400) {
            top_y = 3739 + (currentScroll - 4350) * ((3789 - 3739) / 50);
            left_x = 235 - (currentScroll - 4350) * ((235 - 136) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 15), 'left': addPx(left_x - 3)});
        } else if (currentScroll >= 4400 && currentScroll < 4450) {
            top_y = 3789 + (currentScroll - 4400) * ((3851 - 3789) / 50);
            left_x = 136 - (currentScroll - 4400) * ((136 - 99) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 15), 'left': addPx(left_x - 3)});
        } else if (currentScroll >= 4450 && currentScroll < 4525) {
            top_y = 3851 + (currentScroll - 4450) * ((3960 - 3851) / 75);
            left_x = 99 - (currentScroll - 4450) * ((99 - 81) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 15), 'left': addPx(left_x - 3)});
        } else if (currentScroll >= 4525 && currentScroll < 4600) {
            top_y = 3960 + (currentScroll - 4525) * ((4080 - 3960) / 75);
            left_x = 81 + (currentScroll - 4525) * ((108 - 81) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 15), 'left': addPx(left_x - 3)});
        } else if (currentScroll >= 4600 && currentScroll < 4675) {
            top_y = 4080 + (currentScroll - 4600) * ((4174 - 4080) / 75);
            left_x = 108 + (currentScroll - 4600) * ((167 - 108) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 15), 'left': addPx(left_x - 3)});
        } else if (currentScroll >= 4675 && currentScroll < 4750) {
            top_y = 4174 + (currentScroll - 4675) * ((4243 - 4174) / 75);
            left_x = 167 + (currentScroll - 4675) * ((285 - 167) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 15), 'left': addPx(left_x - 3)});
        } else if (currentScroll >= 4750 && currentScroll < 4950) {
            left_x = 285 + (currentScroll - 4750) * ((698 - 285) / 200);
            $('#car_icon').css({'top': addPx(4243), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(4243 + 15), 'left': addPx(left_x - 3)});
        } else if (currentScroll >= 4950 && currentScroll < 5000) {
        } else if (currentScroll >= 5000 && currentScroll < 5100) {
            left_x = 698 + (currentScroll - 5000) * ((1300 - 698) / 100);
            $('#car_icon').css({'top': addPx(4243), 'left': addPx(698)});
            $('#track_icon').css({'top': addPx(4243 + 15), 'left': addPx(left_x - 3)});
        } else if (currentScroll >= 5100) {
            $('#track_icon').css({'top': addPx(1293), 'left': addPx(49)});
        }
    } else if (win_w >= 768) {
        // currentScroll > 175 ? ($("#scroll_button").hide(),$('#car_icon').show()) : ($("#scroll_button").show(),$('#car_icon').hide())
        $(window).scrollTop() > 175 ? $("#scroll_button").hide() : $("#scroll_button").show();
        currentScroll > 175 ? $('#car_icon').show() : $('#car_icon').hide();
        currentScroll > 680 ? $("#cloud01").fadeIn(500) : $("#cloud01").fadeOut(500);
        currentScroll > 986 ? $("#cloud02").fadeIn(500) : $("#cloud02").fadeOut(500);
        currentScroll > 1210 ? $("#cloud03").fadeIn(500) : $("#cloud03").fadeOut(500);
        currentScroll > 1640 ? $("#cloud04").fadeIn(500) : $("#cloud04").fadeOut(500);
        currentScroll > 1842 ? $("#rikuso_moji01").fadeIn(500) : $("#rikuso_moji01").fadeOut(500);
        currentScroll > 1915 ? $("#rikuso_moji02").fadeIn(500) : $("#rikuso_moji02").fadeOut(500);
        currentScroll > 2300 ? $("#sagyo_pop01").fadeIn(500) : $("#sagyo_pop01").fadeOut(500);
        currentScroll > 2330 ? $("#sagyo_pop02").fadeIn(500) : $("#sagyo_pop02").fadeOut(500);
        currentScroll > 2360 ? $("#sagyo_pop03").fadeIn(500) : $("#sagyo_pop03").fadeOut(500);
        currentScroll > 2390 ? $("#sagyo_pop04").fadeIn(500) : $("#sagyo_pop04").fadeOut(500);
        currentScroll > 2420 ? $("#sagyo_pop05").fadeIn(500) : $("#sagyo_pop05").fadeOut(500);
        currentScroll > 2144 ? $("#sagyo_pop_img").fadeIn(1000) : $("#sagyo_pop_img").fadeOut(1000);
        currentScroll > 2180 ? $("#sagyo_pop_photo01").fadeIn(1000) : $("#sagyo_pop_photo01").fadeOut(1000);
        currentScroll > 2215 ? $("#sagyo_pop_photo02").fadeIn(1000) : $("#sagyo_pop_photo02").fadeOut(1000);
        currentScroll > 2250 ? $("#sagyo_pop_photo03").fadeIn(1000) : $("#sagyo_pop_photo03").fadeOut(1000);
        currentScroll > 3760 ? $("#top_hubnet_moji_768").fadeIn(1000) : $("#top_hubnet_moji_768").fadeOut(1000);
        currentScroll > 3875 ? $("#top_hubnet_img").fadeIn(1000) : $("#top_hubnet_img").fadeOut(1000);
        currentScroll > 4268 ? $("#top_recruit_moji_768").fadeIn(1000) : $("#top_recruit_moji_768").fadeOut(1000);
        currentScroll > 4400 ? $("#top_recruit_img").fadeIn(1000) : $("#top_recruit_img").fadeOut(1000);

        //start
        if (currentScroll < 400) {
            $('#car_icon').css({'top': addPx(-78), 'left': addPx(24)});
            $('#track_icon').css({'top': addPx(1482), 'left': addPx(19)});
        } else if (currentScroll >= 400 && currentScroll < 850) {
            top_y = -78 + (currentScroll - 400) * ((506 + 78) / 450);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(24)});
            $('#track_icon').css({'top': addPx(1482), 'left': addPx(19)});
        } else if (currentScroll >= 850 && currentScroll < 925) {
            top_y = 506 + (currentScroll - 850) * ((582 - 506) / 75);
            left_x = 24 + (currentScroll - 850) * ((52 - 24) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1482), 'left': addPx(19)});
        } else if (currentScroll >= 925 && currentScroll < 1150) {
            top_y = 582 + (currentScroll - 925) * ((781 - 582) / 225);
            left_x = 52 + (currentScroll - 925) * ((179 - 52) / 225);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1482), 'left': addPx(19)});
        } else if (currentScroll >= 1150 && currentScroll < 1200) {
            top_y = 781 + (currentScroll - 1150) * ((833 - 781) / 50);
            left_x = 179 + (currentScroll - 1150) * ((193 - 179) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1482), 'left': addPx(19)});
        } else if (currentScroll >= 1200 && currentScroll < 1250) {
            top_y = 833 + (currentScroll - 1200) * ((885 - 833) / 50);
            left_x = 193 - (currentScroll - 1200) * ((193 - 176) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1482), 'left': addPx(19)});
        } else if (currentScroll >= 1250 && currentScroll < 1475) {
            top_y = 885 + (currentScroll - 1250) * ((1092 - 885) / 225);
            left_x = 176 - (currentScroll - 1250) * ((176 - 43) / 225);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1482), 'left': addPx(19)});
        } else if (currentScroll >= 1475 && currentScroll < 1525) {
            top_y = 1092 + (currentScroll - 1475) * ((1147 - 1092) / 50);
            left_x = 43 - (currentScroll - 1475) * ((43 - 26) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1482), 'left': addPx(19)});
        } else if (currentScroll >= 1525 && currentScroll < 1750) {
            top_y = 1147 + (currentScroll - 1525) * ((1472 - 1147) / 225);
            left_x = 26 - (currentScroll - 1525) * ((26 - 25) / 225);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1482), 'left': addPx(19)});	//track top+10 left-6
        } else if (currentScroll >= 1750 && currentScroll < 2000) {
            left_x = 25 + (currentScroll - 1750) * ((663 - 25) / 250);
            $('#car_icon').css({'top': addPx(1472), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1472 + 10), 'left': addPx(left_x - 6)});
        } else if (currentScroll >= 2000 && currentScroll < 2150) {
            top_y = 1472 + (currentScroll - 2000) * ((1682 - 1472) / 150);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(663)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(663 - 6)});
            $('#ship_icon').css({'top': addPx(2060), 'left': addPx(487)});
        } else if (currentScroll >= 2150 && currentScroll < 2350) {
            left_x = 663 - (currentScroll - 2150) * ((663 - 388) / 200);
            $('#car_icon').css({'top': addPx(1682), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2060), 'left': addPx(487)});
        } else if (currentScroll >= 2350 && currentScroll < 2400) {
            top_y = 1682 + (currentScroll - 2350) * ((1722 - 1682) / 50);
            left_x = 388 - (currentScroll - 2350) * ((388 - 310) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2060), 'left': addPx(487)});
        } else if (currentScroll >= 2400 && currentScroll < 2475) {
            top_y = 1722 + (currentScroll - 2400) * ((1810 - 1722) / 75);
            left_x = 310 - (currentScroll - 2400) * ((310 - 286) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2060), 'left': addPx(487)});
        } else if (currentScroll >= 2475 && currentScroll < 2550) {
            top_y = 1810 + (currentScroll - 2475) * ((1924 - 1810) / 75);
            left_x = 286 + (currentScroll - 2475) * ((322 - 286) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2060), 'left': addPx(487)});
        } else if (currentScroll >= 2550 && currentScroll < 2600) {
            top_y = 1924 + (currentScroll - 2550) * ((1973 - 1924) / 50);
            left_x = 322 + (currentScroll - 2550) * ((384 - 322) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2060), 'left': addPx(487)});
        } else if (currentScroll >= 2600 && currentScroll < 2650) {
            top_y = 1973 + (currentScroll - 2600) * ((1978 - 1973) / 50);
            left_x = 384 + (currentScroll - 2600) * ((516 - 384) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2060), 'left': addPx(487)});
        } else if (currentScroll >= 2650 && currentScroll < 2700) {
            top_y = 1978 + (currentScroll - 2650) * ((2060 - 1978) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(516)});
            $('#ship_icon').css({'top': addPx(2060), 'left': addPx(487)});
        } else if (currentScroll >= 2700 && currentScroll < 2750) {
            top_y = 2060 + (currentScroll - 2700) * ((2122 - 2060) / 50);
            left_x = 516 - (currentScroll - 2700) * ((516 - 455) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(top_y), 'left': addPx(left_x - 29)});	//ship top:0 left:-29
        } else if (currentScroll >= 2750 && currentScroll < 3100) {
            left_x = 455 - (currentScroll - 2750) * ((455 + 250) / 350);
            $('#car_icon').css({'top': addPx(2122), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2122), 'left': addPx(left_x - 29)});
        } else if (currentScroll >= 3100 && currentScroll < 3500) {
            //stop
            $('#track_icon').css({'top': addPx(3237), 'left': addPx(219)});	//track top+10 left-6
        } else if (currentScroll >= 3500 && currentScroll < 3600) {
            left_x = (-250) + (currentScroll - 3500) * ((250 + 70) / 100);
            $('#car_icon').css({'top': addPx(3227), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(3227), 'left': addPx(left_x - 29)});
        } else if (currentScroll >= 3600 && currentScroll < 3700) {
            left_x = 70 + (currentScroll - 3600) * ((225 - 70) / 100);
            $('#car_icon').css({'top': addPx(3227), 'left': addPx(left_x)});
        } else if (currentScroll >= 3700 && currentScroll < 3800) {
            top_y = 3227 + (currentScroll - 3700) * ((3264 - 3227) / 100);
            left_x = 225 + (currentScroll - 3700) * ((418 - 225) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 6)});
        } else if (currentScroll >= 3800 && currentScroll < 3875) {
            top_y = 3264 + (currentScroll - 3800) * ((3360 - 3264) / 75);
            left_x = 423 + (currentScroll - 3800) * ((533 - 423) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 6)});
        } else if (currentScroll >= 3875 && currentScroll < 3975) {
            top_y = 3360 + (currentScroll - 3875) * ((3462 - 3360) / 100);
            left_x = 533 + (currentScroll - 3875) * ((597 - 533) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 6)});
        } else if (currentScroll >= 3975 && currentScroll < 4050) {
            top_y = 3462 + (currentScroll - 3975) * ((3571 - 3462) / 75);
            left_x = 597 + (currentScroll - 3975) * ((617 - 597) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 6)});
        } else if (currentScroll >= 4050 && currentScroll < 4100) {
            top_y = 3571 + (currentScroll - 4050) * ((3643 - 3571) / 50);
            left_x = 617 - (currentScroll - 4050) * ((617 - 609) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 6)});
        } else if (currentScroll >= 4100 && currentScroll < 4200) {
            top_y = 3643 + (currentScroll - 4100) * ((3764 - 3643) / 100);
            left_x = 609 - (currentScroll - 4100) * ((609 - 567) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 6)});
        } else if (currentScroll >= 4200 && currentScroll < 4300) {
            top_y = 3764 + (currentScroll - 4200) * ((3867 - 3764) / 100);
            left_x = 567 - (currentScroll - 4200) * ((567 - 465) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 6)});
        } else if (currentScroll >= 4300 && currentScroll < 4500) {
            top_y = 3867 + (currentScroll - 4300) * ((3908 - 3867) / 200);
            left_x = 465 - (currentScroll - 4300) * ((465 - 190) / 200);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 6)});
        } else if (currentScroll >= 4500 && currentScroll < 4575) {
            top_y = 3908 + (currentScroll - 4500) * ((3959 - 3908) / 75);
            left_x = 190 - (currentScroll - 4500) * ((190 - 109) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 6)});
        } else if (currentScroll >= 4575 && currentScroll < 4650) {
            top_y = 3959 + (currentScroll - 4575) * ((4050 - 3959) / 75);
            left_x = 109 - (currentScroll - 4575) * ((109 - 71) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 6)});
        } else if (currentScroll >= 4650 && currentScroll < 4700) {
            top_y = 4050 + (currentScroll - 4650) * ((4120 - 4050) / 50);
            left_x = 71 - (currentScroll - 4650) * ((71 - 63) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 6)});
        } else if (currentScroll >= 4700 && currentScroll < 4775) {
            top_y = 4120 + (currentScroll - 4700) * ((4257 - 4120) / 75);
            left_x = 63 + (currentScroll - 4700) * ((92 - 63) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 6)});
        } else if (currentScroll >= 4775 && currentScroll < 4850) {
            top_y = 4257 + (currentScroll - 4775) * ((4364 - 4257) / 75);
            left_x = 92 + (currentScroll - 4775) * ((175 - 92) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 6)});
        } else if (currentScroll >= 4850 && currentScroll < 4925) {
            top_y = 4364 + (currentScroll - 4850) * ((4407 - 4364) / 75);
            left_x = 175 + (currentScroll - 4850) * ((294 - 175) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 6)});
        } else if (currentScroll >= 4925 && currentScroll < 5050) {
            left_x = 294 + (currentScroll - 4925) * ((487 - 294) / 125);
            $('#car_icon').css({'top': addPx(4407), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(4407 + 10), 'left': addPx(left_x - 6)});
        } else if (currentScroll >= 5050 && currentScroll < 5150) {
        } else if (currentScroll >= 5150 && currentScroll < 5250) {
            left_x = 487 + (currentScroll - 5150) * ((1000 - 487) / 100);
            $('#car_icon').css({'top': addPx(4407), 'left': addPx(487)});
            $('#track_icon').css({'top': addPx(4407 + 10), 'left': addPx(left_x - 6)});
        } else if (currentScroll >= 5250) {
            $('#track_icon').css({'top': addPx(1482), 'left': addPx(19)});
        }
    } else if (win_w >= 576) {
        // currentScroll > 165 ? ($("#scroll_button").hide(),$('#car_icon').show()) : ($("#scroll_button").show(),$('#car_icon').hide());
        $(window).scrollTop() > 165 ? $("#scroll_button").hide() : $("#scroll_button").show();
        currentScroll > 165 ? $('#car_icon').show() : $('#car_icon').hide();
        currentScroll > 695 ? $("#cloud01").fadeIn(500) : $("#cloud01").fadeOut(500);
        currentScroll > 1015 ? $("#cloud02").fadeIn(500) : $("#cloud02").fadeOut(500);
        currentScroll > 1540 ? $("#cloud03").fadeIn(500) : $("#cloud03").fadeOut(500);
        currentScroll > 2000 ? $("#cloud04").fadeIn(500) : $("#cloud04").fadeOut(500);
        currentScroll > 2252 ? $("#rikuso_moji01").fadeIn(500) : $("#rikuso_moji01").fadeOut(500);
        currentScroll > 2342 ? $("#rikuso_moji02").fadeIn(500) : $("#rikuso_moji02").fadeOut(500);
        currentScroll > 2780 ? $("#sagyo_pop01").fadeIn(500) : $("#sagyo_pop01").fadeOut(500);
        currentScroll > 2820 ? $("#sagyo_pop02").fadeIn(500) : $("#sagyo_pop02").fadeOut(500);
        currentScroll > 2860 ? $("#sagyo_pop03").fadeIn(500) : $("#sagyo_pop03").fadeOut(500);
        currentScroll > 2900 ? $("#sagyo_pop04").fadeIn(500) : $("#sagyo_pop04").fadeOut(500);
        currentScroll > 2940 ? $("#sagyo_pop05").fadeIn(500) : $("#sagyo_pop05").fadeOut(500);
        currentScroll > 2600 ? $("#sagyo_pop_img").fadeIn(1000) : $("#sagyo_pop_img").fadeOut(1000);
        currentScroll > 2450 ? $("#sagyo_pop_photo01").fadeIn(1000) : $("#sagyo_pop_photo01").fadeOut(1000);
        currentScroll > 2500 ? $("#sagyo_pop_photo02").fadeIn(1000) : $("#sagyo_pop_photo02").fadeOut(1000);
        currentScroll > 2550 ? $("#sagyo_pop_photo03").fadeIn(1000) : $("#sagyo_pop_photo03").fadeOut(1000);
        currentScroll > 4650 ? $("#top_hubnet_moji_300").fadeIn(1000) : $("#top_hubnet_moji_300").fadeOut(1000);
        currentScroll > 4880 ? $("#top_hubnet_img_300").fadeIn(1000) : $("#top_hubnet_img_300").fadeOut(1000);
        currentScroll > 5700 ? $("#top_recruit_moji_300").fadeIn(1000) : $("#top_recruit_moji_300").fadeOut(1000);
        currentScroll > 5900 ? $("#top_recruit_img_300").fadeIn(1000) : $("#top_recruit_img_300").fadeOut(1000);

        //start
        if (currentScroll < 300) {
            $('#car_icon').css({'top': addPx(-82), 'left': addPx(34)});
            $('#track_icon').css({'top': addPx(1891), 'left': addPx(30)});
        } else if (currentScroll >= 300 && currentScroll < 900) {
            top_y = -82 + (currentScroll - 300) * ((623 + 82) / 600);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(34)});
            $('#track_icon').css({'top': addPx(1891), 'left': addPx(30)});
        } else if (currentScroll >= 900 && currentScroll < 1000) {
            top_y = 623 + (currentScroll - 900) * ((705 - 623) / 100);
            left_x = 34 + (currentScroll - 900) * ((60 - 34) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1891), 'left': addPx(30)});
        } else if (currentScroll >= 1000 && currentScroll < 1100) {
            top_y = 705 + (currentScroll - 1000) * ((803 - 705) / 100);
            left_x = 60 + (currentScroll - 1000) * ((116 - 60) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1891), 'left': addPx(30)});
        } else if (currentScroll >= 1100 && currentScroll < 1175) {
            top_y = 803 + (currentScroll - 1100) * ((868 - 803) / 75);
            left_x = 116 - (currentScroll - 1100) * ((116 - 108) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1891), 'left': addPx(30)});
        } else if (currentScroll >= 1175 && currentScroll < 1325) {
            top_y = 868 + (currentScroll - 1175) * ((985 - 868) / 150);
            left_x = 108 - (currentScroll - 1175) * ((108 - 27) / 150);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1891), 'left': addPx(30)});
        } else if (currentScroll >= 1325 && currentScroll < 1375) {
            top_y = 985 + (currentScroll - 1325) * ((1038 - 985) / 50);
            left_x = 27 - (currentScroll - 1325) * ((27 - 8) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1891), 'left': addPx(30)});
        } else if (currentScroll >= 1375 && currentScroll < 1475) {
            top_y = 1038 + (currentScroll - 1375) * ((1116 - 1038) / 100);
            left_x = 8 + (currentScroll - 1375) * ((14 - 8) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1891), 'left': addPx(30)});
        } else if (currentScroll >= 1475 && currentScroll < 1600) {
            top_y = 1116 + (currentScroll - 1475) * ((1242 - 1116) / 125);
            left_x = 14 + (currentScroll - 1475) * ((100 - 14) / 125);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1891), 'left': addPx(30)});
        } else if (currentScroll >= 1600 && currentScroll < 1675) {
            top_y = 1242 + (currentScroll - 1600) * ((1305 - 1242) / 75);
            left_x = 100 + (currentScroll - 1600) * ((115 - 100) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1891), 'left': addPx(30)});
        } else if (currentScroll >= 1675 && currentScroll < 1700) {
            top_y = 1305 + (currentScroll - 1675) * ((1331 - 1305) / 25);
            left_x = 115 - (currentScroll - 1675) * ((115 - 113) / 25);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1891), 'left': addPx(30)});
        } else if (currentScroll >= 1700 && currentScroll < 1750) {
            top_y = 1331 + (currentScroll - 1700) * ((1385 - 1331) / 50);
            left_x = 113 - (currentScroll - 1700) * ((113 - 85) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1891), 'left': addPx(30)});
        } else if (currentScroll >= 1750 && currentScroll < 1800) {
            top_y = 1385 + (currentScroll - 1750) * ((1432 - 1385) / 50);
            left_x = 85 - (currentScroll - 1750) * ((85 - 55) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1891), 'left': addPx(30)});
        } else if (currentScroll >= 1800 && currentScroll < 1850) {
            top_y = 1432 + (currentScroll - 1800) * ((1480 - 1432) / 50);
            left_x = 55 - (currentScroll - 1800) * ((55 - 36) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1891), 'left': addPx(30)});
        } else if (currentScroll >= 1850 && currentScroll < 2175) {
            top_y = 1480 + (currentScroll - 1850) * ((1881 - 1480) / 325);
            left_x = 36 - (currentScroll - 1850) * ((36 - 34) / 325);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1891), 'left': addPx(30)});	//track top:+10 left:-4
        } else if (currentScroll >= 2175 && currentScroll < 2450) {
            left_x = 34 + (currentScroll - 2175) * ((479 - 34) / 275);
            $('#car_icon').css({'top': addPx(1881), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1881 + 10), 'left': addPx(left_x - 4)});
        } else if (currentScroll >= 2450 && currentScroll < 2650) {
            top_y = 1881 + (currentScroll - 2450) * ((2141 - 1881) / 200);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(479)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(479 - 4)});
            $('#ship_icon').css({'top': addPx(2635), 'left': addPx(255)});
        } else if (currentScroll >= 2650 && currentScroll < 2800) {
            left_x = 479 - (currentScroll - 2650) * ((479 - 247) / 150);
            $('#car_icon').css({'top': addPx(2141), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2635), 'left': addPx(255)});
        } else if (currentScroll >= 2800 && currentScroll < 2850) {
            top_y = 2141 + (currentScroll - 2800) * ((2186 - 2141) / 50);
            left_x = 247 - (currentScroll - 2800) * ((247 - 167) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2635), 'left': addPx(255)});
        } else if (currentScroll >= 2850 && currentScroll < 2950) {
            top_y = 2186 + (currentScroll - 2850) * ((2290 - 2186) / 100);
            left_x = 167 - (currentScroll - 2850) * ((167 - 127) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2635), 'left': addPx(255)});
        } else if (currentScroll >= 2950 && currentScroll < 3000) {
            top_y = 2290 + (currentScroll - 2950) * ((2370 - 2290) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(127)});
            $('#ship_icon').css({'top': addPx(2635), 'left': addPx(255)});
        } else if (currentScroll >= 3000 && currentScroll < 3100) {
            top_y = 2370 + (currentScroll - 3000) * ((2493 - 2370) / 100);
            left_x = 127 + (currentScroll - 3000) * ((174 - 127) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2635), 'left': addPx(255)});
        } else if (currentScroll >= 3100 && currentScroll < 3150) {
            top_y = 2493 + (currentScroll - 3100) * ((2542 - 2493) / 50);
            left_x = 174 + (currentScroll - 3100) * ((232 - 174) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2635), 'left': addPx(255)});
        } else if (currentScroll >= 3150 && currentScroll < 3200) {
            top_y = 2542 + (currentScroll - 3150) * ((2551 - 2542) / 50);
            left_x = 232 + (currentScroll - 3150) * ((288 - 232) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2635), 'left': addPx(255)});
        } else if (currentScroll >= 3200 && currentScroll < 3300) {
            top_y = 2551 + (currentScroll - 3200) * ((2643 - 2551) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(288)});
            $('#ship_icon').css({'top': addPx(2635), 'left': addPx(255)});
        } else if (currentScroll >= 3300 && currentScroll < 3400) {
            top_y = 2643 + (currentScroll - 3300) * ((2717 - 2643) / 100);
            left_x = 288 - (currentScroll - 3300) * ((288 - 204) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(top_y - 8), 'left': addPx(left_x - 33)});	//ship top-8 left-33
        } else if (currentScroll >= 3400 && currentScroll < 3600) {
            left_x = 204 - (currentScroll - 3400) * ((204 + 180) / 200);
            $('#car_icon').css({'top': addPx(2717), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2717 - 8), 'left': addPx(left_x - 33)});
        } else if (currentScroll >= 3600 && currentScroll < 4200) {
            //stop
            $('#track_icon').css({'top': addPx(3965), 'left': addPx(294)});	//track top:+10 left:-4
        } else if (currentScroll >= 4200 && currentScroll < 4325) {
            left_x = (-180) + (currentScroll - 4200) * ((136 + 180) / 125);
            $('#car_icon').css({'top': addPx(3955), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(3955 - 8), 'left': addPx(left_x - 33)});
        } else if (currentScroll >= 4325 && currentScroll < 4475) {
            left_x = 136 + (currentScroll - 4325) * ((298 - 136) / 150);
            $('#car_icon').css({'top': addPx(3955), 'left': addPx(left_x)});
        } else if (currentScroll >= 4475 && currentScroll < 4675) {
            top_y = 3955 + (currentScroll - 4475) * ((4175 - 3955) / 200);
            left_x = 298 + (currentScroll - 4475) * ((386 - 298) / 200);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 4)});
        } else if (currentScroll >= 4675 && currentScroll < 4775) {
            top_y = 4175 + (currentScroll - 4675) * ((4285 - 4175) / 100);
            left_x = 386 + (currentScroll - 4675) * ((420 - 386) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 4)});
        } else if (currentScroll >= 4775 && currentScroll < 4875) {
            top_y = 4285 + (currentScroll - 4775) * ((4389 - 4285) / 100);
            left_x = 420 + (currentScroll - 4775) * ((437 - 420) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 4)});
        } else if (currentScroll >= 4875 && currentScroll < 5050) {
            top_y = 4389 + (currentScroll - 4875) * ((4590 - 4389) / 175);
            left_x = 437 + (currentScroll - 4875) * ((441 - 437) / 175);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 4)});
        } else if (currentScroll >= 5050 && currentScroll < 5250) {
            top_y = 4590 + (currentScroll - 5050) * ((4848 - 4590) / 200);
            left_x = 441 - (currentScroll - 5050) * ((441 - 431) / 200);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 4)});
        } else if (currentScroll >= 5250 && currentScroll < 5350) {
            top_y = 4848 + (currentScroll - 5250) * ((4972 - 4848) / 100);
            left_x = 431 - (currentScroll - 5250) * ((431 - 402) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 4)});
        } else if (currentScroll >= 5350 && currentScroll < 5450) {
            top_y = 4972 + (currentScroll - 5350) * ((5072 - 4972) / 100);
            left_x = 402 - (currentScroll - 5350) * ((402 - 309) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 4)});
        } else if (currentScroll >= 5450 && currentScroll < 5525) {
            top_y = 5072 + (currentScroll - 5450) * ((5109 - 5072) / 75);
            left_x = 309 - (currentScroll - 5450) * ((309 - 219) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 4)});
        } else if (currentScroll >= 5525 && currentScroll < 5600) {
            top_y = 5109 + (currentScroll - 5525) * ((5150 - 5109) / 75);
            left_x = 219 - (currentScroll - 5525) * ((219 - 130) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 4)});
        } else if (currentScroll >= 5600 && currentScroll < 5675) {
            top_y = 5150 + (currentScroll - 5600) * ((5231 - 5150) / 75);
            left_x = 130 - (currentScroll - 5600) * ((130 - 55) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 4)});
        } else if (currentScroll >= 5675 && currentScroll < 5775) {
            top_y = 5231 + (currentScroll - 5675) * ((5332 - 5231) / 100);
            left_x = 55 - (currentScroll - 5675) * ((55 - 40) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 4)});
        } else if (currentScroll >= 5775 && currentScroll < 6025) {
            top_y = 5332 + (currentScroll - 5775) * ((5636 - 5332) / 250);
            left_x = 40 - (currentScroll - 5775) * ((40 - 31) / 250);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 4)});
        } else if (currentScroll >= 6025 && currentScroll < 6125) {
            top_y = 5636 + (currentScroll - 6025) * ((5724 - 5636) / 100);
            left_x = 31 + (currentScroll - 6025) * ((38 - 31) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 4)});
        } else if (currentScroll >= 6125 && currentScroll < 6225) {
            top_y = 5724 + (currentScroll - 6125) * ((5847 - 5724) / 100);
            left_x = 38 + (currentScroll - 6125) * ((63 - 38) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 4)});
        } else if (currentScroll >= 6225 && currentScroll < 6325) {
            top_y = 5847 + (currentScroll - 6225) * ((5970 - 5847) / 100);
            left_x = 63 + (currentScroll - 6225) * ((110 - 63) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 4)});
        } else if (currentScroll >= 6325 && currentScroll < 6400) {
            top_y = 5970 + (currentScroll - 6325) * ((6029 - 5970) / 75);
            left_x = 110 + (currentScroll - 6325) * ((188 - 110) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 4)});
        } else if (currentScroll >= 6400 && currentScroll < 6600) {
            top_y = 6029 + (currentScroll - 6400) * ((6048 - 6029) / 200);
            left_x = 188 + (currentScroll - 6400) * ((264 - 188) / 200);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 10), 'left': addPx(left_x - 4)});
        } else if (currentScroll >= 6600 && currentScroll < 6700) {
        } else if (currentScroll >= 6700 && currentScroll < 6900) {
            left_x = 264 + (currentScroll - 6700) * ((900 - 264) / 200);
            $('#track_icon').css({'top': addPx(6048 + 10), 'left': addPx(left_x)});
        } else if (currentScroll >= 6900) {
            $('#track_icon').css({'top': addPx(1891), 'left': addPx(30)});
        }
    } else {
        // currentScroll > 177 ? ($("#scroll_button").hide(),$('#car_icon').show()) : ($("#scroll_button").show(),$('#car_icon').hide());
        $(window).scrollTop() > 177 ? $("#scroll_button").hide() : $("#scroll_button").show();
        currentScroll > 177 ? $('#car_icon').show() : $('#car_icon').hide();
        currentScroll > 841 ? $("#cloud01").fadeIn(500) : $("#cloud01").fadeOut(500);
        currentScroll > 1390 ? $("#cloud02").fadeIn(500) : $("#cloud02").fadeOut(500);
        currentScroll > 1660 ? $("#cloud03").fadeIn(500) : $("#cloud03").fadeOut(500);
        currentScroll > 2035 ? $("#cloud04").fadeIn(500) : $("#cloud04").fadeOut(500);
        currentScroll > 2240 ? $("#rikuso_moji01").fadeIn(500) : $("#rikuso_moji01").fadeOut(500);
        currentScroll > 2320 ? $("#rikuso_moji02").fadeIn(500) : $("#rikuso_moji02").fadeOut(500);
        currentScroll > 2695 ? $("#sagyo_pop01").fadeIn(500) : $("#sagyo_pop01").fadeOut(500);
        currentScroll > 2756 ? $("#sagyo_pop02").fadeIn(500) : $("#sagyo_pop02").fadeOut(500);
        currentScroll > 2809 ? $("#sagyo_pop03").fadeIn(500) : $("#sagyo_pop03").fadeOut(500);
        currentScroll > 2865 ? $("#sagyo_pop04").fadeIn(500) : $("#sagyo_pop04").fadeOut(500);
        currentScroll > 2905 ? $("#sagyo_pop05").fadeIn(500) : $("#sagyo_pop05").fadeOut(500);
        currentScroll > 2600 ? $("#sagyo_pop_img").fadeIn(1000) : $("#sagyo_pop_img").fadeOut(1000);
        currentScroll > 2400 ? $("#sagyo_pop_photo01").fadeIn(1000) : $("#sagyo_pop_photo01").fadeOut(1000);
        currentScroll > 2450 ? $("#sagyo_pop_photo02").fadeIn(1000) : $("#sagyo_pop_photo02").fadeOut(1000);
        currentScroll > 2500 ? $("#sagyo_pop_photo03").fadeIn(1000) : $("#sagyo_pop_photo03").fadeOut(1000);
        currentScroll > 4000 ? $("#top_hubnet_moji_300").fadeIn(1000) : $("#top_hubnet_moji_300").fadeOut(1000);
        currentScroll > 4200 ? $("#top_hubnet_img_300").fadeIn(1000) : $("#top_hubnet_img_300").fadeOut(1000);
        currentScroll > 4670 ? $("#top_recruit_moji_300").fadeIn(1000) : $("#top_recruit_moji_300").fadeOut(1000);
        currentScroll > 4850 ? $("#top_recruit_img_300").fadeIn(1000) : $("#top_recruit_img_300").fadeOut(1000);

        //start
        if (currentScroll < 300) {
            $('#car_icon').css({'top': addPx(-98), 'left': addPx(4)});
            $('#track_icon').css({'top': addPx(1902), 'left': addPx(5)});
        } else if (currentScroll >= 300 && currentScroll < 1100) {
            top_y = -98 + (currentScroll - 300) * ((775 + 98) / 800);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(4)});
            $('#track_icon').css({'top': addPx(1902), 'left': addPx(5)});
        } else if (currentScroll >= 1100 && currentScroll < 1200) {
            top_y = 775 + (currentScroll - 1100) * ((847 - 775) / 100);
            left_x = 4 + (currentScroll - 1100) * ((24 - 4) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1902), 'left': addPx(5)});
        } else if (currentScroll >= 1200 && currentScroll < 1325) {
            top_y = 847 + (currentScroll - 1200) * ((945 - 847) / 125);
            left_x = 24 + (currentScroll - 1200) * ((76 - 24) / 125);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1902), 'left': addPx(5)});
        } else if (currentScroll >= 1325 && currentScroll < 1375) {
            top_y = 945 + (currentScroll - 1325) * ((994 - 945) / 50);
            left_x = 76 - (currentScroll - 1325) * ((76 - 70) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1902), 'left': addPx(5)});
        } else if (currentScroll >= 1375 && currentScroll < 1500) {
            top_y = 994 + (currentScroll - 1375) * ((1127 - 994) / 125);
            left_x = 70 - (currentScroll - 1375) * ((70 + 15) / 125);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1902), 'left': addPx(5)});
        } else if (currentScroll >= 1500 && currentScroll < 1550) {
            top_y = 1127 + (currentScroll - 1500) * ((1176 - 1127) / 50);
            left_x = (-15) - (currentScroll - 1500) * ((20 - 15) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1902), 'left': addPx(5)});
        } else if (currentScroll >= 1550 && currentScroll < 1600) {
            top_y = 1176 + (currentScroll - 1550) * ((1240 - 1176) / 50);
            left_x = (-20) + (currentScroll - 1550) * ((20 + 1) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1902), 'left': addPx(5)});
        } else if (currentScroll >= 1600 && currentScroll < 1700) {
            top_y = 1240 + (currentScroll - 1600) * ((1333 - 1240) / 100);
            left_x = 1 + (currentScroll - 1600) * ((66 - 1) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1902), 'left': addPx(5)});
        } else if (currentScroll >= 1700 && currentScroll < 1750) {
            top_y = 1333 + (currentScroll - 1700) * ((1390 - 1333) / 50);
            left_x = 66 + (currentScroll - 1700) * ((74 - 66) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1902), 'left': addPx(5)});
        } else if (currentScroll >= 1750 && currentScroll < 1775) {
            top_y = 1390 + (currentScroll - 1750) * ((1418 - 1390) / 25);
            left_x = 74 - (currentScroll - 1750) * ((74 - 65) / 25);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1902), 'left': addPx(5)});
        } else if (currentScroll >= 1775 && currentScroll < 1850) {
            top_y = 1418 + (currentScroll - 1775) * ((1512 - 1418) / 75);
            left_x = 65 - (currentScroll - 1775) * ((65 - 12) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1902), 'left': addPx(5)});
        } else if (currentScroll >= 1850 && currentScroll < 1875) {
            top_y = 1512 + (currentScroll - 1850) * ((1550 - 1512) / 25);
            left_x = 12 - (currentScroll - 1850) * ((12 - 5) / 25);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1902), 'left': addPx(5)});
        } else if (currentScroll >= 1875 && currentScroll < 2175) {
            top_y = 1550 + (currentScroll - 1875) * ((1884 - 1550) / 300);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(5)});
            $('#track_icon').css({'top': addPx(1902), 'left': addPx(5)});
        } else if (currentScroll >= 2175 && currentScroll < 2400) {
            left_x = 5 + (currentScroll - 2175) * ((286 - 5) / 225);
            $('#car_icon').css({'top': addPx(1884), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(1902), 'left': addPx(left_x)});	//track top+18 left0
        } else if (currentScroll >= 2400 && currentScroll < 2600) {
            top_y = 1884 + (currentScroll - 2400) * ((2206 - 1884) / 200);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(286)});
            $('#track_icon').css({'top': addPx(top_y + 18), 'left': addPx(286)});
        } else if (currentScroll >= 2600 && currentScroll < 2700) {
            left_x = 286 - (currentScroll - 2600) * ((286 - 127) / 100);
            $('#car_icon').css({'top': addPx(2206), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2550), 'left': addPx(127)});
        } else if (currentScroll >= 2700 && currentScroll < 2750) {
            top_y = 2206 + (currentScroll - 2700) * ((2242 - 2206) / 50);
            left_x = 127 - (currentScroll - 2700) * ((127 - 76) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2550), 'left': addPx(127)});
        } else if (currentScroll >= 2750 && currentScroll < 2800) {
            top_y = 2242 + (currentScroll - 2750) * ((2300 - 2242) / 50);
            left_x = 76 - (currentScroll - 2750) * ((76 - 53) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2550), 'left': addPx(127)});
        } else if (currentScroll >= 2800 && currentScroll < 2850) {
            top_y = 2300 + (currentScroll - 2800) * ((2353 - 2300) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(53)});
            $('#ship_icon').css({'top': addPx(2550), 'left': addPx(127)});
        } else if (currentScroll >= 2850 && currentScroll < 2900) {
            top_y = 2353 + (currentScroll - 2850) * ((2411 - 2353) / 50);
            left_x = 53 + (currentScroll - 2850) * ((75 - 53) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2550), 'left': addPx(127)});
        } else if (currentScroll >= 2900 && currentScroll < 2950) {
            top_y = 2411 + (currentScroll - 2900) * ((2476 - 2411) / 50);
            left_x = 75 + (currentScroll - 2900) * ((147 - 75) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2550), 'left': addPx(127)});
        } else if (currentScroll >= 2950 && currentScroll < 3050) {
            top_y = 2476 + (currentScroll - 2950) * ((2547 - 2476) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(147)});
            $('#ship_icon').css({'top': addPx(2550), 'left': addPx(127)});
        } else if (currentScroll >= 3050 && currentScroll < 3150) {
            top_y = 2547 + (currentScroll - 3050) * ((2612 - 2547) / 100);
            left_x = 147 - (currentScroll - 3050) * ((147 - 117) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(top_y + 3), 'left': addPx(left_x - 20)});	//ship top+3 left-20
        } else if (currentScroll >= 3150 && currentScroll < 3350) {
            left_x = 117 - (currentScroll - 3150) * ((117 + 180) / 200);
            $('#car_icon').css({'top': addPx(2612), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(2612 + 3), 'left': addPx(left_x - 20)});
        } else if (currentScroll >= 3350 && currentScroll < 3600) {
            //stop
            $('#track_icon').css({'top': addPx(3473), 'left': addPx(196)});	//track top+18 left0
        } else if (currentScroll >= 3600 && currentScroll < 3700) {
            left_x = (-180) + (currentScroll - 3600) * ((47 + 180) / 100);
            $('#car_icon').css({'top': addPx(3455), 'left': addPx(left_x)});
            $('#ship_icon').css({'top': addPx(3455 + 3), 'left': addPx(left_x - 20)});
        } else if (currentScroll >= 3700 && currentScroll < 3850) {
            left_x = 47 + (currentScroll - 3700) * ((196 - 47) / 150);
            $('#car_icon').css({'top': addPx(3455), 'left': addPx(left_x)});
        } else if (currentScroll >= 3850 && currentScroll < 4000) {
            top_y = 3455 + (currentScroll - 3850) * ((3624 - 3455) / 150);
            left_x = 196 + (currentScroll - 3850) * ((243 - 196) / 150);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 18), 'left': addPx(left_x)});
        } else if (currentScroll >= 4000 && currentScroll < 4075) {
            top_y = 3624 + (currentScroll - 4000) * ((3712 - 3624) / 75);
            left_x = 243 + (currentScroll - 4000) * ((263 - 243) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 18), 'left': addPx(left_x)});
        } else if (currentScroll >= 4075 && currentScroll < 4200) {
            top_y = 3712 + (currentScroll - 4075) * ((3861 - 3712) / 125);
            left_x = 263 + (currentScroll - 4075) * ((270 - 263) / 125);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 18), 'left': addPx(left_x)});
        } else if (currentScroll >= 4200 && currentScroll < 4250) {
            top_y = 3861 + (currentScroll - 4200) * ((3952 - 3861) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(270)});
            $('#track_icon').css({'top': addPx(top_y + 18), 'left': addPx(270)});
        } else if (currentScroll >= 4250 && currentScroll < 4350) {
            top_y = 3952 + (currentScroll - 4250) * ((4084 - 3952) / 100);
            left_x = 270 - (currentScroll - 4250) * ((270 - 255) / 100);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 18), 'left': addPx(left_x)});
        } else if (currentScroll >= 4350 && currentScroll < 4400) {
            top_y = 4084 + (currentScroll - 4350) * ((4141 - 4084) / 50);
            left_x = 255 - (currentScroll - 4350) * ((255 - 226) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 18), 'left': addPx(left_x)});
        } else if (currentScroll >= 4400 && currentScroll < 4450) {
            top_y = 4141 + (currentScroll - 4400) * ((4184 - 4141) / 50);
            left_x = 226 - (currentScroll - 4400) * ((226 - 191) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 18), 'left': addPx(left_x)});
        } else if (currentScroll >= 4450 && currentScroll < 4500) {
            top_y = 4184 + (currentScroll - 4450) * ((4218 - 4184) / 50);
            left_x = 191 - (currentScroll - 4450) * ((191 - 114) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 18), 'left': addPx(left_x)});
        } else if (currentScroll >= 4500 && currentScroll < 4550) {
            top_y = 4218 + (currentScroll - 4500) * ((4254 - 4218) / 50);
            left_x = 114 - (currentScroll - 4500) * ((114 - 43) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 18), 'left': addPx(left_x)});
        } else if (currentScroll >= 4550 && currentScroll < 4600) {
            top_y = 4254 + (currentScroll - 4550) * ((4303 - 4254) / 50);
            left_x = 43 - (currentScroll - 4550) * ((43 - 14) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 18), 'left': addPx(left_x)});
        } else if (currentScroll >= 4600 && currentScroll < 4800) {
            top_y = 4303 + (currentScroll - 4600) * ((4533 - 4303) / 200);
            left_x = 14 - (currentScroll - 4600) * ((14 - 1) / 200);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 18), 'left': addPx(left_x)});
        } else if (currentScroll >= 4800 && currentScroll < 4875) {
            top_y = 4533 + (currentScroll - 4800) * ((4611 - 4533) / 75);
            left_x = 1 + (currentScroll - 4800) * ((6 - 1) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 18), 'left': addPx(left_x)});
        } else if (currentScroll >= 4875 && currentScroll < 4950) {
            top_y = 4611 + (currentScroll - 4875) * ((4690 - 4611) / 75);
            left_x = 6 + (currentScroll - 4875) * ((20 - 6) / 75);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 18), 'left': addPx(left_x)});
        } else if (currentScroll >= 4950 && currentScroll < 5100) {
            top_y = 4690 + (currentScroll - 4950) * ((4781 - 4690) / 150);
            left_x = 20 + (currentScroll - 4950) * ((62 - 20) / 150);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 18), 'left': addPx(left_x)});
        } else if (currentScroll >= 5100 && currentScroll < 5150) {
            top_y = 4781 + (currentScroll - 5100) * ((4817 - 4781) / 50);
            left_x = 62 + (currentScroll - 5100) * ((113 - 62) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 18), 'left': addPx(left_x)});
        } else if (currentScroll >= 5150 && currentScroll < 5200) {
            top_y = 4817 + (currentScroll - 5150) * ((4833 - 4817) / 50);
            left_x = 113 + (currentScroll - 5150) * ((159 - 113) / 50);
            $('#car_icon').css({'top': addPx(top_y), 'left': addPx(left_x)});
            $('#track_icon').css({'top': addPx(top_y + 18), 'left': addPx(left_x)});
        } else if (currentScroll >= 5200 && currentScroll < 5300) {
        } else if (currentScroll >= 5300 && currentScroll < 5500) {
            left_x = 159 + (currentScroll - 5300) * ((600 - 159) / 200);
            $('#track_icon').css({'top': addPx(4833 + 18), 'left': addPx(left_x)});
        } else if (currentScroll >= 5500) {
            $('#track_icon').css({'top': addPx(1902), 'left': addPx(5)});
        }
    }
});
