<?php

declare(strict_types=1);

namespace App\Http\Requests\Transport;

use App\Enums\UserRole;
use App\Enums\Transport\Statuses;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class TransportBulkUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
    protected function prepareForValidation()
    {
        $idList = $this->input('id_list');
        $updateAll = $this->input('update_all', false);
        
        if ($updateAll) {
            $this->merge([
                'id_list' => [],
            ]);
            return;
        }
        
        if ($idList === null || trim($idList) === '' || trim($idList) === ',') {
            $this->merge([
                'id_list' => [],
            ]);
            return;
        }
        
        $items = explode(',', $idList);

        $items = array_filter(array_map(function ($item) {
            $item = trim($item);
            return is_numeric($item) ? (int) $item : null;
        }, $items));

        $this->merge([
            'id_list' => array_values($items),
        ]);
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $comment = $this->input('comment');
            $commtBikoReporter = $this->input('commt_biko_reporter');
            
            if (!empty($comment) || !empty($commtBikoReporter)) {
                if (empty($comment)) {
                    $validator->errors()->add('comment', 'コメント欄に何も入力されていません');
                }
                if (empty($commtBikoReporter)) {
                    $validator->errors()->add('commt_biko_reporter', '入力者欄に何も入力されていません');
                }
            }
        });
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'id_list' => ['sometimes', 'array'],
        ];

        if (!$this->input('update_all', false) && $this->input('id_list')) {
            $rules['id_list.*'] = ['numeric'];
        }

        $transportFields = [
            'update_all' => 'sometimes|boolean',
            'st_cd' => ['nullable', 'string', 'max:10', Rule::in(Statuses::cases())],
            'from_plan_date' => 'nullable|date_format:Y-m-d',
            'to_plan_date' => 'nullable|date_format:Y-m-d',
            'to_date' => 'nullable|date_format:Y-m-d',
            'plate_send_date' => 'nullable|date_format:Y-m-d',
            'plate_send_co' => 'nullable|string|max:255',
            'plate_send_no' => 'nullable|string|max:255',
            'deli_price' => 'nullable|numeric',
            'sales_price' => 'nullable|numeric',
        ];

        $transportNotesFields = [
            'comment' => 'nullable|string|max:255',
            'commt_biko_reporter' => 'nullable|string|max:50',
        ];

        $rules = array_merge($rules, $transportFields, $transportNotesFields);

        if (Auth::user()->hn_cd != UserRole::admin->value) {
            $customerAllowedFields = [
                'st_cd', 'from_plan_date', 'to_plan_date', 'to_date',
                'plate_send_date', 'plate_send_co', 'plate_send_no',
                'deli_price', 'comment', 'commt_biko_reporter'
            ];
            
            foreach ($rules as $field => $rule) {
                if (!in_array($field, $customerAllowedFields) && !in_array($field, ['id_list', 'send_email'])) {
                    unset($rules[$field]);
                }
            }
        }

        return $rules;
    }

    public function validated($key = null, $default = null): array
    {
        $validated = parent::validated($key, $default);

        // Separate transport and transport_notes data
        $transportData = [];
        $transportNotesData = [];

        // t_transport fields
        $transportFields = [
            'st_cd', 'from_plan_date', 'to_plan_date', 'to_date',
            'plate_send_date', 'plate_send_co', 'plate_send_no',
            'deli_price', 'sales_price', 'update_all'
        ];

        $transportNotesFields = [
            'comment', 'commt_biko_reporter',
        ];

        foreach ($validated as $key => $value) {
            if (in_array($key, $transportFields)) {
                $transportData[$key] = $value;
            } elseif (in_array($key, $transportNotesFields)) {
                $transportNotesData[$key] = $value;
            }
        }
        return [
            'id_list' => $validated['id_list'] ?? [],
            'transport_data' => $transportData,
            'transport_notes_data' => $transportNotesData,
        ];
    }
} 