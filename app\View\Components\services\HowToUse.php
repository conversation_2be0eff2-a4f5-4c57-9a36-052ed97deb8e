<?php

declare(strict_types=1);

namespace App\View\Components\services;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class HowToUse extends Component
{
    /**
     * Create a new component instance.
     */
    public function __construct(public string $content1, public string $content2, public string $icon1, public string $icon2)
    {

    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.services.how-to-use');
    }
}
