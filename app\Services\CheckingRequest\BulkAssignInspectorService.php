<?php

declare(strict_types=1);

namespace App\Services\CheckingRequest;

use App\Enums\CheckingRequest\Statuses;
use App\Repositories\sqlServer\CheckingRequestRepository;
use Illuminate\Support\Facades\DB;

class BulkAssignInspectorService
{
    private CheckingRequestRepository $checkingRequestRepository;
    private MailAssignInspectorService $mailAssignInspectorService;

    public function __construct(CheckingRequestRepository $checkingRequestRepository, MailAssignInspectorService $mailAssignInspectorService)
    {
        $this->checkingRequestRepository = $checkingRequestRepository;
        $this->mailAssignInspectorService = $mailAssignInspectorService;
    }

    public function call(array $body): void
    {
        $dataUpdate = $this->prepareData($body['data']);

        DB::transaction(function () use ($dataUpdate): void {
            foreach ($dataUpdate as $item) {
                $id = $item['id'];
                unset($item['id']);

                $this->checkingRequestRepository->update((int)$id, $item);
            }
        });

        $this->sendMailMulti($dataUpdate);
    }

    private function prepareData(array $data): array
    {
        $userId = auth()->user()->id;

        return array_map(fn ($item) => [
            'id' => $item['id'],
            'up_date' => now(),
            'up_owner' =>  $userId,
            'st_cd' => Statuses::RECEIVED,
            't_inspector_id' => $item['inspector_id'],
            'check_req_date' => now(),
        ], $data);
    }

    /**
     * Send mail notifications for multiple checking requests
     *
     * @param array $data Array of checking request data with inspector assignments
     */
    private function sendMailMulti(array $data): void
    {
        foreach ($data as $item) {
            $this->mailAssignInspectorService->call($item['id']);
        }
    }
}
