<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Http\Resources\SaleStaff\SaleStaffDropdownResource;
use App\Services\SaleStaff\SaleStaffService;
use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Response;

class SaleStaffController extends Controller
{
    private SaleStaffService $saleStaffService;

    public function __construct(SaleStaffService $saleStaffService)
    {
        $this->saleStaffService = $saleStaffService;
    }


    #[OA\Get(
        path: '/api/admin/sales-staff/list-active',
        summary: 'Get list of active sales staff',
        description: 'Retrieve a list of all active sales staff members for dropdown/selection purposes',
        security: [['access_token' => []]],
        tags: ['Sales Staff Management'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful operation',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'code', type: 'integer', example: 200),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Success'),
                        new OA\Property(
                            property: 'data',
                            type: 'array',
                            items: new OA\Items(
                                properties: [
                                    new OA\Property(property: 'id', type: 'integer', example: 1, description: 'Sales staff ID'),
                                    new OA\Property(property: 'name', type: 'string', example: 'John Doe', description: 'Sales staff name')
                                ]
                            )
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 401,
                description: 'Unauthorized - Invalid or missing access token',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 401),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Unauthorized')
                    ]
                )
            ),
            new OA\Response(
                response: 403,
                description: 'Forbidden - Insufficient permissions',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 403),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Forbidden')
                    ]
                )
            )
        ]
    )]
    public function getSaleStaffActive(): Response
    {
        return $this->respond(SaleStaffDropdownResource::collection($this->saleStaffService->getSaleStaffActive()));
    }

    #[OA\Get(
        path: '/api/admin/sales-staff/list-ordered-by-english-name',
        description: 'Retrieve a list of active sales staff ordered by English name (ascending) for dropdown/selection purposes',
        summary: 'Get sales staff ordered by English name',
        security: [['access_token' => []]],
        tags: ['Sales Staff Management'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful operation',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'code', type: 'integer', example: 200),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Success'),
                        new OA\Property(
                            property: 'data',
                            type: 'array',
                            items: new OA\Items(
                                properties: [
                                    new OA\Property(property: 'id', description: 'Sales staff ID', type: 'integer', example: 1),
                                    new OA\Property(property: 'name', description: 'Sales staff name', type: 'string', example: 'John Doe')
                                ]
                            )
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 401,
                description: 'Unauthorized - Invalid or missing access token',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 401),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Unauthorized')
                    ]
                )
            ),
            new OA\Response(
                response: 403,
                description: 'Forbidden - Insufficient permissions',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 403),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Forbidden')
                    ]
                )
            )
        ]
    )]
    public function getSaleStaffOrderedByEnglishName(): Response
    {
        return $this->respond(SaleStaffDropdownResource::collection($this->saleStaffService->getSaleStaffOrderedByEnglishName()));
    }
}
