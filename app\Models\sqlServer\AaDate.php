<?php

declare(strict_types=1);

namespace App\Models\sqlServer;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * AaDate Model
 *
 * Handles auction schedule/calendar information
 * Maps to database table: t_aa_date
 */
class AaDate extends Model
{
    public const CREATED_AT = 'reg_date';
    public const UPDATED_AT = 'up_date';

    protected $guarded = [];
    protected $table = 't_aa_date';

    protected $casts = [
        'del_flg' => 'boolean',
    ];

    public function aaPlace(): BelongsTo
    {
        return $this->belongsTo(AaPlace::class, 't_aa_place_id', 'id');
    }
}
