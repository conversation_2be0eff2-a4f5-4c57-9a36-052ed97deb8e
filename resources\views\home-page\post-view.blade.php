@extends('app')

@section('title', $title)

@push('styles')
    @vite('resources/css/blog.css')
@endpush

@section('content')
    <div class="relative w-full px-[15px]">
        <x-common.hubnet-button />
        <div
            class="2sm:max-w-[720px] mx-auto mt-[100px] mb-[60px] max-w-[540px] md:max-w-[960px] lg:mt-[150px] lg:mb-[90px] lg:max-w-[1140px]"
        >
            <div class="blog-view-area" data-aos="fade-up" class="aos-init" data-aos-duration="1000">
                {!! $content !!}
            </div>
        </div>
    </div>
@endsection

@section('footer')

@endsection

@push('scripts')
    <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', () => {
            //ヘッダーの言語切替のパラムを変更
            const params = new URLSearchParams(window.location.search)
            const postid = params.get('postid')

            if (postid) {
                const langLink = document.querySelector('.menu-language > a')
                if (langLink) {
                    langLink.setAttribute('href', `?lan=en&postid=${postid}`)
                }
            }
        })
    </script>
@endpush
