<configuration>
  <dsncollection>
    <dsn alias="${DB_AS400_DSN}" host="${DB_AS400_HOST}" name="${DB_AS400_DATABASE}" port="${DB_AS400_PORT}">
    </dsn>
  </dsncollection>
  <databases>
    <database host="${DB_AS400_HOST}" name="${DB_AS400_DB_ALIAS}" port="${DB_AS400_PORT}">
      <parameter name="CommProtocol" value="TCPIP"/>
      <parameter name="UID" value="${DB_AS400_USERNAME_UPPER}"/>
      <parameter name="IPCInstance" value="${DB_AS400_USERNAME_UPPER}"/>
      <parameter name="CurrentSchema" value="${DB_AS400_USERNAME_UPPER}"/>
    </database>
  </databases>
</configuration>
