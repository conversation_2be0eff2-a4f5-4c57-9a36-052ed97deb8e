<?php

declare(strict_types=1);

namespace App\Services\LoadingOrder;

use App\Constants\PageMetaData;
use App\Http\Resources\LoadingOrder\ListLoadingOrderResource;
use App\Repositories\sqlServer\LoadingOrderRepository;
use Exception;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class GetListLoadingOrderService
{
    private LoadingOrderRepository $loadingOrderRepository;
    private CheckingFileOldHubnetService $checkingFileOldHubnetService;

    public function __construct(LoadingOrderRepository $loadingOrderRepository, CheckingFileOldHubnetService $checkingFileOldHubnetService)
    {
        $this->loadingOrderRepository = $loadingOrderRepository;
        $this->checkingFileOldHubnetService = $checkingFileOldHubnetService;
    }

    public function call(array $params): AnonymousResourceCollection|array
    {
        $relations = ['customer'];

        if (Arr::has($params, ['limit','page'])) {
            return $this->getListPaginate($params);
        }

        $checkingRequests = $this->loadingOrderRepository->getList($params, $relations);

        return ListLoadingOrderResource::collection($checkingRequests);
    }

    public function getListPaginate(array $params): array
    {
        $relations = ['customer'];

        $pagination = $this->loadingOrderRepository->paginate($params, $relations);

        $loadingOrderCollection = ListLoadingOrderResource::collection($pagination);

        $fileInformationData = $this->fetchFileInfo($pagination);
        $resultLoadingOrderData = $this->addFileInfoToCollection($loadingOrderCollection, $fileInformationData);

        return [
            'items' => $resultLoadingOrderData,
            'meta' => (new PageMetaData($pagination))->toArray()
        ];
    }


    /**
     * Build parameters for loading file API call
     *
     * @param mixed $pagination
     * @return array
     */
    private function buildFileApiParams($pagination): array
    {
        return $pagination->map(fn ($itemCollection) => [
            'm_customer_id' => $itemCollection->m_customer_id,
            'loading_id' => $itemCollection->id
        ])->filter(fn ($params) => isset($params['m_customer_id']) && isset($params['loading_id']))->values()->toArray();
    }

    /**
     * Fetch file information for all loading orders
     *
     * @param mixed $pagination
     * @return array
     */
    private function fetchFileInfo($pagination): array
    {
        $fileCheckParams = $this->buildFileApiParams($pagination);

        if (empty($fileCheckParams)) {
            return [];
        }

        try {
            return $this->checkingFileOldHubnetService->call($fileCheckParams);
        } catch (Exception $e) {
            Log::error('Failed to get file information for loading orders', [
                'error' => $e->getMessage(),
                'params' => $fileCheckParams
            ]);
            return [];
        }
    }

    /**
     * Map loading_id to file information
     *
     * @param array $fileInformationData
     * @return array
     */
    private function mapFileInfor(array $fileInformationData): array
    {
        return collect($fileInformationData)
            ->keyBy('loading_id')
            ->toArray();
    }

    /**
     * Add file information to collection data
     *
     * @param AnonymousResourceCollection $listLoadingOrderCollection
     * @param array $fileInformationData
     * @return \Illuminate\Support\Collection
     */
    private function addFileInfoToCollection(
        AnonymousResourceCollection $listLoadingOrderCollection,
        array $fileInformationData
    ): \Illuminate\Support\Collection {
        $fileInfoMapping = $this->mapFileInfor($fileInformationData);

        return $listLoadingOrderCollection->map(function ($resource) use ($fileInfoMapping) {
            $loadingArray = $resource->toArray(request());

            $loadingId = $loadingArray['id'];
            $loadingArray['file_information'] = $fileInfoMapping[$loadingId] ?? null;

            return $loadingArray;
        });
    }
}
