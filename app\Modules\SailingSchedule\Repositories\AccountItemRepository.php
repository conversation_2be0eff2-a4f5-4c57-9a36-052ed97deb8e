<?php

declare(strict_types=1);

namespace App\Modules\SailingSchedule\Repositories;

use App\Models\as400\AccountItems;
use App\Modules\SailingSchedule\Constants\PortAreaOrder;
use App\Repositories\BaseRepository;
use Illuminate\Support\Facades\DB;

class AccountItemRepository extends BaseRepository
{
    public function model(): mixed
    {
        return AccountItems::class;
    }

    public function getCountry(): mixed
    {
        $data = DB::connection('as400')->select('
            SELECT DISTINCT
                MCONTP.CRNN37 AS "name",
                MPORTP.PTKNCD AS "code"
            FROM ABDLIB.MCONTP MCONTP
            INNER JOIN HCDLIB.MPORTP MPORTP
                ON MCONTP.CRCC52 = MPORTP.PTKNCD
            WHERE
                MCONTP.CRCC52 <> ' . PortAreaOrder::JAPAN_CODE . '
                AND MCONTP.CRCC51 = \'GJ03\'
            ORDER BY MCONTP.CRNN37 ASC
        ');

        return $data;
    }

    public function getPort(): mixed
    {
        $data = DB::connection('as400')->select("
        SELECT
            PTCC20 AS code,
            PTNO74 AS name,
            PTKNCD AS country_code,
            CRNN37 AS country_name,
            PTKRCD AS route_code
        FROM
            HCDLIB.MPORTP
        LEFT JOIN
            ABDLIB.MCONTP ON CRCC51 = 'GJ03' AND PTKNCD = CRCC52
        WHERE
            PTNO74 NOT LIKE '*%';
        ");

        return $data;
    }
}
