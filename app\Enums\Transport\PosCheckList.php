<?php

declare(strict_types=1);

namespace App\Enums\Transport;

enum PosCheckList: string
{
    case AI_OCK = '1';
    case TC_WEB = '2';
    case WAN_PLUS_POS = '3';
    case WAN_PLUS_AUTO_SERVER = '4';
    case SIGMA_NET = '5';
    case OK_NET = '6';
    case QUICK_SHARE_INVENTORY = '7';
    case JU_CORPORATION = '8';
    case OTHER_BID_NAME = '9';
    case SELF_BID_NAME = '10';

    public static function getNameTransId(string $mTransId): string
    {
        return match ($mTransId) {
            self::AI_OCK->value => 'アイオーク',
            self::TC_WEB->value => 'TC-web',
            self::WAN_PLUS_POS->value => 'ワンプラ（自社POS）',
            self::WAN_PLUS_AUTO_SERVER->value => 'ワンプラ（オートサーバー）',
            self::SIGMA_NET->value => 'シグマネット',
            self::OK_NET->value => 'オークネット',
            self::QUICK_SHARE_INVENTORY->value => 'クイック共有在庫',
            self::JU_CORPORATION->value => 'JUコーポレーション',
            self::OTHER_BID_NAME->value => '自社名義以外で落札した場合の名義',
            self::SELF_BID_NAME->value => '自社名義で落札',
        };
    }
}
