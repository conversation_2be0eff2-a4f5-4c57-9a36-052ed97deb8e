<?php

declare(strict_types=1);

namespace App\Enums\CheckingRequest;

enum CsvHeaders: string
{
    case INSPECTION_ID = '下見ID';
    case EVENT_DATE = '開催日';
    case VENUE_NAME = '会場名';
    case COMPANY_NAME = '申込会社名';
    case ORDER_PERSON = '申込担当者名';
    case ORDER_TEL = '申込担当者TEL';
    case ORDER_EMAIL = '申込担当者メールアドレス';
    case ORDER_DATE = '申込日';
    case AUCTION_NUMBER = '出品番号';
    case CAR_NAME = '車名';
    case NOTE = '備考';
    case CC_NOTE = 'コールセンター備考';
    case PHOTO_FLAG = '写真撮影';
    case BASIC_INSPECTION = '基本下見';
    case WHEEL_RUST = '足回り、サビ';
    case STATUS = '状況';
    case INSPECTOR_NAME = '検査員名';
    case INSPECTION_REQUEST_DATE = '検査員依頼日時';

    /**
     * Get all headers as array
     */
    public static function getAllHeaders(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get header by case name
     */
    public static function getHeader(string $caseName): string
    {
        return constant("self::{$caseName}")->value;
    }
}
