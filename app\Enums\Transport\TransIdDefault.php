<?php

declare(strict_types=1);

namespace App\Enums\Transport;

enum TransIdDefault: string
{
    case SHIPPING_EAST_AND_WEST = '1005'; // 東西海運
    case TARGET = '7875'; // キャリーゴール
    case J_BRING = '14192'; // ジェイキャリー
    case EIKO_SHOUNEN = '17078'; // 栄港商運

    // Id company special
    case LOGICO = '8107'; // ロジコ
    case DREAM_INTERNATIONAL = '8973'; // ドリームインターナショナル

    public static function getNameTransId(string $mTransId): string
    {
        return match ($mTransId) {
            self::SHIPPING_EAST_AND_WEST->value => '東西海運',
            self::TARGET->value => 'キャリーゴール',
            self::J_BRING->value => 'ジェイキャリー',
            self::EIKO_SHOUNEN->value => '栄港商運',
            self::LOGICO->value => 'ロジコ',
            self::DREAM_INTERNATIONAL->value => 'ドリームインターナショナル',
            default => ''
        };
    }
}
