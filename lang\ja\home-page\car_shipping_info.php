<?php

declare(strict_types=1);

return [
    'page_title' => '車両・船積情報｜株式会社AUTOHUB',
    'title' => '車両・船積情報　Track&Trace',
    'page_description1' => 'Car & Shipping',
    'page_description2' => 'Information',
    'service_descriptions' => 'ご依頼いただきました車両情報・写真・船積状況等をいつでもご覧いただけます。',
    'service_list' => [
        '車両の情報や船積情報を簡単に検索・閲覧が可能です。',
        '詳細ぺージでは、写真をまとめてダウンロードすることも可能です。',
        'お客様と当社スタッフが同じ情報を共有しています。',
        'D2Dサービス車両のリリース依頼も可能です。'
    ],
    'click_car' => "車両・船積情報を<br>クリック",
    'list_function' => [
        '1. 仕向国・車名・車台番号・船名等の<span class="2sm:text-xl mx-[0.3em] font-bold text-orange-400">検索フィルター</span>',
        '2. リストの<span class="2sm:text-xl mx-[0.3em] font-bold text-orange-400">並び順</span>は自由に変更が可能',
        '3. 検索結果をCSVで<span class="2sm:text-xl mx-[0.3em] font-bold text-orange-400">ダウンロード</span>',
        '4. 車両の写真または車名をクリックで、車両詳細ページへ移動<br>詳細ぺージでは輸出抹消コピー等の<span class="2sm:text-xl mx-[0.3em] font-bold text-orange-400">アップロード</span>機能も搭載',
    ],
    'function_descriptions' => 'ご利用のサービス内容によって、写真やレポート等の<br>ご確認・ダウンロードが可能（※詳細は担当の営業スタッフまで）',
    'hubnet_order' => 'HUBNET会員の方はログイン後、こちらからご確認いただけます。',
    'hubnet_login' => "HUBNET<br>ログイン",
    'car_information' => '車両・船積情報',
    'track_trace' => '(トラックアンドトレース)',
    'line_title' => '車両情報は<span class="2sm:text-40 mx-[0.2em] text-xl text-lime-500">LINE</span>からでも<br>簡単に確認することが可能です。',
    'line_search' => '車台番号　AH No.　車名で検索',
    'line_bot_1' => '自動応答システム',
    'line_bot_2' => '「LineBot」',
    'line_bot_3' => 'が登場!!',
    'friend_description' => 'AUTOHUB公式アカウントを<br>友だち追加後、<br>HUBNETとLINEアカウントの<br>連携を行う事でご利用いただけます。',
    'friend_btn' => '友だち追加はこちら'
];
