<div>
    @php
        $i = 0;
    @endphp

    <table class="w-full border-collapse">
        @foreach ($sales as $key => $value)
            <tr class="border-gray {{ $i == 0 ? '!border-none' : '' }} border-t border-dashed">
                <td class="w-1/4 p-1 text-center text-xs font-bold md:p-3 md:text-sm">{{ __($value['title']) }}</td>
                <td class="p-1 text-xs md:p-3 md:text-sm">
                    {!! __($value['content']) !!}
                </td>
            </tr>
            @php
                $i++;
            @endphp
        @endforeach

        <tr class="border-gray hidden border-t border-dashed md:table-row">
            <td class="w-1/4 p-1 text-center text-xs font-bold md:p-3 md:text-sm"></td>
            <td class="p-1 text-xs md:p-3 md:text-sm">
                <iframe
                    class="h-[315px] w-full max-w-[560px]"
                    src="https://www.youtube.com/embed/mkF7T8yMb1s"
                    title="YouTube video player"
                    frameborder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowfullscreen
                ></iframe>
            </td>
        </tr>
        <tr class="border-gray table-row border-t border-dashed md:hidden">
            <td class="p-1 pt-2 text-xs md:p-3 md:text-sm" colspan="2">
                <iframe
                    class="h-[315px] w-full max-w-[560px]"
                    src="https://www.youtube.com/embed/mkF7T8yMb1s"
                    title="YouTube video player"
                    frameborder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowfullscreen
                ></iframe>
            </td>
        </tr>
    </table>

    <div class="mt-2 flex justify-center">
        <a
            href="https://en-gage.net/autohub_career/work_4102256/?via_recruit_page=1"
            class="rounded bg-orange-500 px-6 py-3 font-bold text-white transition-colors duration-300 hover:bg-orange-200"
        >
            {{ __('button.apply') }}
        </a>
    </div>
</div>
