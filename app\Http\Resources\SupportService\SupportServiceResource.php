<?php

declare(strict_types=1);

namespace App\Http\Resources\SupportService;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SupportServiceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'label_name' => $this->label_name
        ];
    }
}
