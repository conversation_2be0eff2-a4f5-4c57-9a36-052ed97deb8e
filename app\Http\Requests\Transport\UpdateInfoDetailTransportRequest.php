<?php

declare(strict_types=1);

namespace App\Http\Requests\Transport;

use App\Enums\UserRole;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateInfoDetailTransportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules()
    {
        $rules = [
            'to_plan_date' => 'nullable|date_format:Y-m-d',
            'to_date' => 'nullable|date_format:Y-m-d',
            'from_plan_date' => 'nullable|date_format:Y-m-d',
            'plate_send_date' => 'nullable|date_format:Y-m-d',
            'plate_send_no' => 'nullable|string|max:255',
            'plate_send_co' => 'nullable|string|max:255',
        ];

        if (Auth::user()->hn_cd == UserRole::admin->value) {
            $rules['deli_price'] = 'nullable|numeric|min:0';
            $rules['sales_price'] = 'nullable|numeric|min:0';
        } else {
            $rules['deli_price'] = 'nullable|numeric|min:0';
        }

        return $rules;
    }
}
