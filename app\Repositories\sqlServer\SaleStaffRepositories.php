<?php

declare(strict_types=1);

namespace App\Repositories\sqlServer;

use App\Models\sqlServer\SalesStaff;
use App\Repositories\BaseRepository;

class SaleStaffRepositories extends BaseRepository
{
    public function model(): string
    {
        return SalesStaff::class;
    }

    public function getSaleStaffActive()
    {
        $query = ($this->model)::query()->select('idx', 's_name', 's_id')->where('del_flg', 0)->orderBy('s_name', 'ASC');
        return $query->get();
    }

    public function getSaleStaffOrderedByEnglishName()
    {
        $query = ($this->model)::query()->select('idx', 's_name', 's_id')->where('del_flg', 0)->orderBy('s_name_en', 'asc');
        return $query->get();
    }
}
