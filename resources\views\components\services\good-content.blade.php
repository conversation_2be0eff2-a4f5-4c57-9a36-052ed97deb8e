@php
    /**
    * @var array $content
    */
@endphp

<div
    class="2sm:text-15 relative mx-auto mt-[1em] w-full border-t-2 border-b-2 border-sky-300 pt-[0.5em] pr-[1.5em] pb-[1.7em] pl-[0.5em] text-sm text-gray-700 md:pb-[1em] md:pl-[1.5em] md:text-xl"
>
    <div
        class="absolute top-[-10px] left-[10px] h-[calc(100%+20px)] w-[10px] rounded-[10px] bg-sky-300 opacity-20 md:w-5"
    ></div>
    <div
        class="absolute top-[-10px] right-[10px] h-[calc(100%+20px)] w-[10px] rounded-[10px] bg-sky-300 opacity-20 md:w-5"
    ></div>
    <div class="w-full px-[15px]">
        <h4 class="2sm:text-xl !my-[0.5em] ml-[1em] text-lg font-medium md:!mt-0 md:!mb-2 md:ml-[10px] md:text-3xl">
            <span class="md:text-40 text-25 text-cyan-300 text-shadow-cyan-300">G</span>
            <span class="2sm:text-xl text-lg text-lime-400 text-shadow-lime-400 md:text-3xl">o</span>
            <span class="text-21 md:text-33 text-lime-400 text-shadow-lime-400">o</span>
            <span class="text-23 md:text-38 text-lime-400 text-shadow-lime-400">d</span>
            <span class="md:text-40 text-25 text-lime-400 text-shadow-lime-400">!</span>
        </h4>
    </div>
    <div class="w-full px-[15px]">
        <ul class="2sm:text-15 !mb-0 list-disc pl-10 text-sm md:text-xl">
            @foreach ($content as $item)
                <li>{{ $item }}</li>
            @endforeach
        </ul>
    </div>
</div>
