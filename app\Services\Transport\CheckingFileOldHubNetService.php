<?php

declare(strict_types=1);

namespace App\Services\Transport;

use App\Enums\OldHubNetApi;
use Exception;
use Illuminate\Support\Facades\Http;

class CheckingFileOldHubNetService
{
    private string $apiBaseUrl;
    private string $apiEndpoint;
    private int $requestTimeout;

    /**
     * Initialize service with API configuration
     *
     * Loads base URL from environment and endpoint/timeout from enum constants
     */
    public function __construct()
    {
        $this->apiBaseUrl = env('APP_URL_OLD', '');
        $this->apiEndpoint = OldHubNetApi::TRANS_FILE_CHECK_ENDPOINT->value;
        $this->requestTimeout = (int) OldHubNetApi::TIMEOUT->value;
    }

    /**
     * Retrieve file information for transport records from old HubNet API
     *
     * Makes authenticated HTTP POST request to legacy API with transport data.
     * Includes login session ID in headers for authentication.
     * Validates response structure and logs both success and failure cases.
     *
     * @param array $params Array of transport records, each containing:
     *                      - m_customer_id: Customer ID (numeric)
     *                      - transport_id: Transport ID (numeric)
     * @return array Array of file info:
     * - customer_id (int)
     * - transport_id (int)
     * - deletePath (string)
     * - downloadPath (string)
     * - fileList (array<array{filename: string}>)
     *
     * @throws Exception When parameters are invalid, request fails, or response is malformed
     */
    public function call(array $params): array
    {
        $this->validateParams($params);
        $loginSessionId = $this->getLoginSessionId();

        $firstRecord = $params[0];
        $queryParams = [
            'm_customer_id' => $firstRecord['m_customer_id'],
            'transport_id' => $firstRecord['transport_id'],
            'login_session_id' => $loginSessionId
        ];

        $response = Http::timeout($this->requestTimeout)
            ->withHeaders([
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'X-Login-Session-Id' => $loginSessionId,
            ])
            ->post($this->apiBaseUrl . $this->apiEndpoint . '?' . http_build_query($queryParams), $params);

        if (!$response->successful()) {
            throw new Exception("HTTP request failed with status: {$response->status()}");
        }

        $responseData = $response->json();

        if (!$this->isValidResponse($responseData)) {
            throw new Exception("Invalid response structure from old HubNet API");
        }

        return $responseData['data'] ?? [];
    }

    /**
     * Validate transport parameters before API call
     *
     * Ensures all required fields are present and properly formatted.
     * Checks for empty arrays and validates data types.
     *
     * @param array $params Transport parameters to validate
     * @throws Exception When parameters are missing, empty, or invalid
     */
    private function validateParams(array $params): void
    {
        if (empty($params)) {
            throw new Exception("Parameters array cannot be empty");
        }

        foreach ($params as $index => $param) {
            if (!isset($param['m_customer_id']) || !isset($param['transport_id'])) {
                throw new Exception("Missing required parameters at index {$index}. Required: m_customer_id, transport_id");
            }

            if (!is_numeric($param['m_customer_id']) || !is_numeric($param['transport_id'])) {
                throw new Exception("Parameters must be numeric at index {$index}");
            }
        }
    }

    /**
     * Validate old HubNet API response structure
     *
     * Ensures response contains all required fields and proper data structure.
     * Validates both top-level response and individual file data items.
     *
     * @param array|null $response Raw API response to validate
     * @return bool True if response structure is valid, false otherwise
     */
    private function isValidResponse(?array $response): bool
    {
        if (!$response) {
            return false;
        }

        $requiredFields = ['success', 'code', 'message', 'data'];
        foreach ($requiredFields as $field) {
            if (!array_key_exists($field, $response)) {
                return false;
            }
        }

        if (!is_array($response['data'])) {
            return false;
        }

        foreach ($response['data'] as $item) {
            if (!is_array($item)) {
                return false;
            }

            $requiredItemFields = ['customer_id', 'transport_id', 'deletePath', 'downloadPath', 'fileList'];
            foreach ($requiredItemFields as $field) {
                if (!array_key_exists($field, $item)) {
                    return false;
                }
            }

            if (!is_array($item['fileList'])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Extract login session ID from current request
     *
     * Retrieves the session ID that was set by JWT authentication middleware.
     * This ID is used for API authentication with the old HubNet system.
     *
     * @return string|null Login session ID or null if not available
     */
    private function getLoginSessionId(): ?string
    {
        return request()->attributes->get('login_session_id');
    }
}
