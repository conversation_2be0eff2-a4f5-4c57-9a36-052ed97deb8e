<?php

declare(strict_types=1);

namespace App\Services\CheckingRequest;

use App\Enums\FlagTransform;
use App\Jobs\SendMailInspectionInforJob;
use App\Repositories\sqlServer\CheckingRequestRepository;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class MailAssignInspectorService
{
    private CheckingRequestRepository $checkingRequestRepository;

    public function __construct(CheckingRequestRepository $checkingRequestRepository)
    {
        $this->checkingRequestRepository = $checkingRequestRepository;
    }

    /**
     * Send mail notification for a single checking request
     *
     * @param int|string $checkingRequestId
     */
    public function call(int | string $checkingRequestId): void
    {
        try {
            $mailContent = $this->getMailContent($checkingRequestId);

            SendMailInspectionInforJob::dispatchSync(
                $mailContent['mail_to'],
                $mailContent
            );

        } catch (Exception $e) {
            Log::error('Failed to send mail for checking request: ' . $checkingRequestId, [
                'error' => $e->getMessage(),
                'checking_request_id' => $checkingRequestId
            ]);
        }
    }

    /**
     * Get mail content for a specific checking request
     *
     * @param int|string $checkingRequestId
     * @return array Mail content data
     */
    private function getMailContent(int | string $checkingRequestId): array
    {
        $checkingRequest = $this->checkingRequestRepository->findDetailById((int)$checkingRequestId);

        $mailInfoConfig = Arr::get(config('mail.mail_info'), config('app.env'));

        return [
            'mail_from' => $mailInfoConfig['center_mail'],
            'mail_to' => $checkingRequest->inspector->mail,

            'open_date' => $checkingRequest->aaDate->open_date,
            't_aa_place_name' => $checkingRequest->aaDate->aaPlace->name,
            'before_check_id' => $checkingRequest->id,
            'm_customer_name' => $checkingRequest->customer->cus_Name_JP,
            'm_customer_tel' => $checkingRequest->customer->tel,
            'odr_person' => $checkingRequest->odr_person,
            'odr_tel' => $checkingRequest->odr_tel,
            'odr_mail' => $checkingRequest->odr_mail,
            'odr_date' => $checkingRequest->odr_date,
            'aa_no' => $checkingRequest->aa_no,
            'car_name' => $checkingRequest->car_name,
            'note' => $checkingRequest->note,
            'cc_note' => $checkingRequest->cc_note,

            'photo_flg' => $this->mappingJPText((string)$checkingRequest->photo_flg),
            'optn2_flg' => $this->mappingJPText($checkingRequest->optn2_flg)
        ];
    }

    private function mappingJPText(?string $value): string
    {
        if (null === $value || '' === trim($value)) {
            return 'なし';
        }

        $flag = FlagTransform::fromValue($value);

        return $flag?->toJapaneseLabel() ?? 'なし';
    }
}
