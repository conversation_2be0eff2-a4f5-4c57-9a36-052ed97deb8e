<?php

declare(strict_types=1);

namespace App\Services\LoadingOrder;

use App\Repositories\sqlServer\LoadingOrderRepository;
use Illuminate\Database\Eloquent\Collection;

class GetLoadingOrderPrintService
{
    private LoadingOrderRepository $loadingOrderRepository;

    public function __construct(LoadingOrderRepository $loadingOrderRepository)
    {
        $this->loadingOrderRepository = $loadingOrderRepository;
    }

    public function call(array $params): Collection
    {
        $ids = empty($params['ids']) ? [] : json_decode($params["ids"]);

        return $this->loadingOrderRepository->getLoadingOrderPrint($ids);
    }
}
