<?php

declare(strict_types=1);

namespace App\Http\Requests\LoadingOrder;

use Illuminate\Foundation\Http\FormRequest;

class ListLoadingOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'limit' => 'nullable|integer|min:1|max:100',
            'page' => 'nullable|integer|min:1',

            't_loading-id-equal' => 'nullable|integer',

            't_loading-odr_date-date_later' => 'nullable|date|date_format:Y-m-d',
            't_loading-odr_date-date_earlier' => 'nullable|date|date_format:Y-m-d',

            't_loading-consignee_name-like' => 'nullable|string',
            't_loading-to_name-like' => 'nullable|string',
            't_loading-destination-like' => 'nullable|string',
            't_loading-car_no-like' => 'nullable|string',
            't_loading-car_name-like' => 'nullable|string',

            'customerName' => 'nullable|string',

            't_loading-st_cd-equal' => 'nullable|string',
            't_loading-odr_kbn-equal' => 'nullable|string',

            'm_customer-ah_sales_id-in' => 'nullable|json',

            'sort' => [
                'nullable',
                'string',
                'regex:/^([a-zA-Z0-9_]+\\|(asc|desc))(,\\s*[a-zA-Z0-9_]+\\|(asc|desc))*$/i',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            't_loading-odr_date-date_later.date_format' => 'The order date later must be in YYYY-MM-DD format.',
            't_loading-odr_date-date_earlier.date_format' => 'The order date earlier must be in YYYY-MM-DD format.',
            'm_customer-ah_sales_id-in.json' => 'The sales person ID must be a valid JSON array.',

            'sort.string' => 'The sort parameter must be a string.',
            'sort.regex' => 'The sort parameter must be in the format "column|direction". Examples: "sort_no|asc", "name|desc", "sort_no|asc,name|desc".',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'limit' => 'records per page',
            'page' => 'page number',
            't_loading-id-equal' => 'loading request ID',
            't_loading-odr_date-date_later' => 'order date later',
            't_loading-odr_date-date_earlier' => 'order date earlier',
            't_loading-consignee_name-like' => 'consignee name',
            't_loading-to_name-like' => 'delivery address name',
            't_loading-destination-like' => 'destination',
            't_loading-car_no-like' => 'car number',
            't_loading-car_name-like' => 'car name',
            'm_customer-cus_name_JP-like' => 'customer name (Japanese)',
            'm_customer-cus_name_EN-like' => 'customer name (English)',
            't_loading-st_cd-equal' => 'status code',
            't_loading-odr_kbn-equal' => 'order classification',
            'm_customer-ah_sales_id-in' => 'sales person ID',
        ];
    }
}
