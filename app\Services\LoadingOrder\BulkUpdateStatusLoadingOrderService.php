<?php

declare(strict_types=1);

namespace App\Services\LoadingOrder;

use App\Repositories\sqlServer\LoadingOrderRepository;

class BulkUpdateStatusLoadingOrderService
{
    private LoadingOrderRepository $loadingOrderRepository;

    public function __construct(LoadingOrderRepository $loadingOrderRepository)
    {
        $this->loadingOrderRepository = $loadingOrderRepository;
    }

    public function call(array $body): void
    {
        $ids = $body['ids'];
        $status = $body['status'];

        $dataUpdate = $this->prepareData($status);

        $this->loadingOrderRepository->bulkUpdateStatus($ids, $dataUpdate);
    }

    private function prepareData($status): array
    {
        return [
            'up_date' => now(),
            'up_owner' => auth()->user()->id,
            'st_cd' => $status
        ];
    }
}
