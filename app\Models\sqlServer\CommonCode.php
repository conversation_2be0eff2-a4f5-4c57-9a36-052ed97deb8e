<?php

declare(strict_types=1);

namespace App\Models\sqlServer;

use Illuminate\Database\Eloquent\Model;

/**
 * CommonCode Model
 *
 * Handles common code/lookup table data
 * Maps to database table: t_com
 */
class CommonCode extends Model
{
    public const CREATED_AT = 'reg_date';
    public const UPDATED_AT = 'up_date';
    protected $table = 't_com';

    protected $fillable = [
        'del_flg',
        'up_owner',
        'cd_kbn',
        'cd',
        'value1',
        'value2',
        'value3',
        'memo',
        'sort_no',
        'upd_flg',
        'site_flg',
        'value1_en',
        'value2_en'
    ];
}
