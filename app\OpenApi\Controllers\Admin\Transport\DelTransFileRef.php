<?php

declare(strict_types=1);

namespace App\OpenApi\Controllers\Admin\Transport;

use OpenApi\Attributes as OA;

#[OA\Post(
    path: '/api/admin/transport/del-trans-file-ref',
    description: 'Delete transport file reference based on customer ID, transport ID, and filename. This endpoint allows users to delete specific transport-related files or documents.',
    summary: 'Delete Transport File Reference',
    security: [['access_token' => []]],
    tags: ['Transport Management'],
    requestBody: new OA\RequestBody(
        required: true,
        description: 'File deletion parameters',
        content: new OA\MediaType(
            mediaType: 'application/json',
            schema: new OA\Schema(
                required: ['customer_id', 'transport_id', 'fname'],
                properties: [
                    new OA\Property(
                        property: 'customer_id',
                        description: 'Customer ID to identify the specific customer',
                        type: 'integer',
                        minimum: 1,
                        example: 12345
                    ),
                    new OA\Property(
                        property: 'transport_id',
                        description: 'Transport ID to identify the specific transport record',
                        type: 'integer',
                        minimum: 1,
                        example: 67890
                    ),
                    new OA\Property(
                        property: 'fname',
                        description: 'Filename of the transport file to delete',
                        type: 'string',
                        maxLength: 255,
                        example: 'transport_document_2024_001.pdf'
                    )
                ]
            )
        )
    ),
    responses: [
        new OA\Response(
            response: 200,
            description: 'File deleted successfully',
            content: new OA\JsonContent(
                properties: [
                    new OA\Property(property: 'success', type: 'boolean', example: true),
                    new OA\Property(property: 'message', type: 'string', example: 'File deleted successfully'),
                    new OA\Property(property: 'code', type: 'integer', example: 200),
                    new OA\Property(
                        property: 'data',
                        properties: [
                            new OA\Property(property: 'customer_id', type: 'integer', example: 12345),
                            new OA\Property(property: 'transport_id', type: 'integer', example: 67890),
                            new OA\Property(property: 'fname', type: 'string', example: 'transport_document_2024_001.pdf'),
                            new OA\Property(property: 'deleted_at', type: 'string', format: 'datetime', example: '2024-01-15T10:30:00Z')
                        ],
                        type: 'object'
                    )
                ]
            )
        ),
        new OA\Response(
            response: 400,
            description: 'Bad Request - Invalid parameters',
            content: new OA\JsonContent(
                properties: [
                    new OA\Property(property: 'success', type: 'boolean', example: false),
                    new OA\Property(property: 'message', type: 'string', example: 'Invalid parameters provided'),
                    new OA\Property(property: 'code', type: 'integer', example: 400)
                ]
            )
        ),
        new OA\Response(
            response: 401,
            description: 'Unauthorized - Invalid or missing access token',
            content: new OA\JsonContent(
                properties: [
                    new OA\Property(property: 'success', type: 'boolean', example: false),
                    new OA\Property(property: 'message', type: 'string', example: 'Unauthorized'),
                    new OA\Property(property: 'code', type: 'integer', example: 401)
                ]
            )
        ),
        new OA\Response(
            response: 403,
            description: 'Forbidden - Insufficient permissions to delete the file',
            content: new OA\JsonContent(
                properties: [
                    new OA\Property(property: 'success', type: 'boolean', example: false),
                    new OA\Property(property: 'message', type: 'string', example: 'Forbidden - Insufficient permissions'),
                    new OA\Property(property: 'code', type: 'integer', example: 403)
                ]
            )
        ),
        new OA\Response(
            response: 404,
            description: 'Not Found - File does not exist',
            content: new OA\JsonContent(
                properties: [
                    new OA\Property(property: 'success', type: 'boolean', example: false),
                    new OA\Property(property: 'message', type: 'string', example: 'File not found'),
                    new OA\Property(property: 'code', type: 'integer', example: 404)
                ]
            )
        ),
        new OA\Response(
            response: 422,
            description: 'Validation Error - Invalid request data',
            content: new OA\JsonContent(
                properties: [
                    new OA\Property(
                        property: 'message',
                        type: 'string',
                        example: 'The given data was invalid.'
                    ),
                    new OA\Property(
                        property: 'errors',
                        properties: [
                            new OA\Property(
                                property: 'customer_id',
                                type: 'array',
                                items: new OA\Items(type: 'string'),
                                example: ['Customer ID is required', 'Customer ID must be a valid integer']
                            ),
                            new OA\Property(
                                property: 'transport_id',
                                type: 'array',
                                items: new OA\Items(type: 'string'),
                                example: ['Transport ID is required', 'Transport ID must be a valid integer']
                            ),
                            new OA\Property(
                                property: 'fname',
                                type: 'array',
                                items: new OA\Items(type: 'string'),
                                example: ['Filename is required', 'Filename must be a valid string']
                            )
                        ],
                        type: 'object'
                    )
                ]
            )
        ),
        new OA\Response(
            response: 500,
            description: 'Internal server error - File deletion failed',
            content: new OA\JsonContent(
                properties: [
                    new OA\Property(property: 'success', type: 'boolean', example: false),
                    new OA\Property(property: 'message', type: 'string', example: 'Internal server error'),
                    new OA\Property(property: 'code', type: 'integer', example: 500)
                ]
            )
        )
    ]
)]
class DelTransFileRef
{
}
