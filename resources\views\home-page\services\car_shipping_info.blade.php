@extends('app')

@section('title', __('home-page/car_shipping_info.page_title'))

@push('styles')
    @vite('resources/css/services.css')
@endpush

@section('content')
    <x-services.container>
        <x-services.title-page
            titlePage="{!! __('home-page/car_shipping_info.title') !!}"
            description1="{!! __('home-page/car_shipping_info.page_description1') !!}"
            description2="{{ __('home-page/car_shipping_info.page_description2') }}"
        />

        <x-services.title subTitle="{{ __('home-page/car_shipping_info.service_descriptions') }}" />
        <div class="mt-6 w-full px-4">
            <x-services.how-to-use-content page="car_shipping_info" />
            <x-services.content-item title="{{ __('common.how_to_use') }}">
                <div
                    class="2sm:py-[4em] 2sm:text-base text-15 mt-[1em] flex flex-col flex-wrap items-center justify-around px-[1em] py-[2em] md:flex-row"
                >
                    <x-services.login-hubnet />
                    <i
                        class="fas fa-caret-right 2sm:my-[0.2em] text-50 rotate-90 transform font-black text-orange-400 md:my-0 md:rotate-0 md:transform-none"
                    ></i>
                    <div class="2sm:w-1/2 2md:w-1/5 relative w-4/5 md:w-1/4">
                        <i
                            class="fas fa-hand-pointer text-50 absolute right-[10%] bottom-0 z-10 font-black text-white text-shadow-black"
                        ></i>
                        <div class="flex flex-wrap justify-between">
                            <div class="shadow-gray-150 mx-[1em] mb-[1em] w-full bg-white text-center">
                                <a
                                    href="{{ env('APP_URL_OLD', '') }}/hn/tat/list_81_.php?lan={{ app()->getLocale() }}"
                                    class="relative flex w-full flex-col items-center justify-center"
                                    target="_blank"
                                >
                                    <div
                                        class="absolute top-0 left-0 h-[30px] w-5"
                                        style="
                                            background: url('{{ asset('images/services/label_style08.png') }}')
                                                no-repeat;
                                            background-size: 20px 30px;
                                        "
                                    ></div>
                                    <img
                                        src="{{ asset('images/services/icon_menu_09.png') }}"
                                        alt="陸送発注"
                                        class="mt-[10px] w-15"
                                    />
                                    <p class="2sm:text-base text-red-450 pb-[1em] text-xs leading-normal font-bold">
                                        {!! __('home-page/car_shipping_info.click_car') !!}
                                    </p>
                                </a>
                            </div>
                        </div>
                    </div>
                    <i
                        class="fas fa-caret-right 2sm:my-[0.2em] rotate-90 transform text-6xl font-black text-orange-400 md:my-0 md:rotate-0 md:transform-none"
                    ></i>
                    <div class="2sm:w-1/2 2md:w-1/5 relative w-4/5 md:w-1/4">
                        <i
                            class="fas fa-hand-pointer text-50 absolute right-[10%] -bottom-5 z-10 font-black text-white text-shadow-black"
                        ></i>
                        <div class="flex flex-wrap justify-between">
                            <div class="bg-sky-350 mx-[1em] mb-[1em] w-full rounded-sm text-center">
                                <p class="2sm:text-base text-xs leading-[3em] font-bold text-white">
                                    {{ __('common.search') }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </x-services.content-item>
            <x-services.content-item title="{{ __('common.functions') }}">
                <div class="2sm:px-4 2sm:my-[3em] 2md:flex-wrap mt-8 mb-20 flex flex-wrap-reverse justify-between">
                    <div class="2md:w-1/2 w-full">
                        <img
                            src="{{ asset('images/services/car_shipping_info_01.jpg') }}"
                            alt=""
                            class="mx-auto w-full !max-w-max"
                        />
                    </div>
                    <div class="2md:w-[47%] w-full">
                        <ul class="list-outside bg-white px-[1em] text-sm">
                            @foreach (__('home-page/car_shipping_info.list_function') as $item)
                                <li class="my-[1em] border-b border-dashed border-gray-100">{!! $item !!}</li>
                            @endforeach
                        </ul>
                        <p class="mb-4 p-[1em] text-xs">
                            {!! __('home-page/car_shipping_info.function_descriptions') !!}
                        </p>
                    </div>
                </div>
            </x-services.content-item>
        </div>
        <x-services.service-options
            :description="__('home-page/car_shipping_info.hubnet_order')"
            iconRight="icon_menu_09.png"
            :titleRight="__('home-page/car_shipping_info.car_information')"
            :contentRight="__('home-page/car_shipping_info.track_trace')"
            linkRight="{{ env('APP_URL_OLD', '') }}/hn/tat/list_81_.php?lan={{ app()->getLocale() }}"
        />
        <div class="mx-auto mt-6 w-full px-4 md:mt-[45px]">
            <aside class="block">
                <p
                    class="2sm:text-28 2md:mb-4 mt-[4em] mb-[0.3em] text-center text-lg font-bold text-gray-200 md:my-0 md:text-[37px]"
                >
                    {!! __('home-page/car_shipping_info.line_title') !!}
                </p>
                <div class="linebot-bg">
                    <div
                        class="2sm:relative 2sm:w-[46%] 2sm:top-[41%] 2sm:left-[-10%] 2sm:bottom-[5%] 2sm:text-right 2sm:order-1 2md:w-2/5 2md:left-[-4%] 2md:bottom-[3%] static order-3 w-full md:absolute md:top-auto md:left-[-2%] md:w-[35%] lg:left-0"
                    >
                        <div class="2md:w-[65%] mx-auto w-4/5">
                            <img
                                src="{{ asset($isJa ? 'images/services/linebot_scr.png' : 'images/services/linebot_scr_en.png') }}"
                                alt=""
                                class="w-full !max-w-max"
                            />
                        </div>
                        <p
                            class="{{ $isJa ? '2sm:top-[-11%] 2sm:right-[14%] 2md:text-right' : '2sm:top-[-22%] 2sm:right-1/5 2md:text-center' }} 2md:text-base 2sm:text-xs 2sm:absolute 2sm:top-[-11%] 2sm:right-[14%] 2sm:text-black 2sm:x-5 text-15 static my-[0.5em] text-center text-white md:static md:top-auto md:right-auto md:text-sm md:text-white"
                        >
                            {!! __('home-page/car_shipping_info.line_search') !!}
                        </p>
                        <img
                            src="{{ asset('images/services/linebot_search.png') }}"
                            alt=""
                            class="{{ $isJa ? '2sm:right-[7%]' : '2sm:right-[-1em] 2md:right-[2em]' }} 2sm:w-[46px] 2sm:bottom-[79%] 2md:w-[46px] absolute right-[8%] bottom-[4%] w-auto md:right-[-1.1em] md:bottom-[4%] md:w-10"
                        />
                    </div>
                    <div
                        class="2sm:absolute 2sm:left-1/2 2sm:text-21 2sm:transform 2sm:-translate-x-1/2 2sm:-translate-y-1/2 2md:text-25 {{ $isJa ? '2sm:top-[76%] md:top-[72%]' : '2sm:top-[73%] md:top-[67%]' }} static text-lg font-bold text-white"
                    >
                        <p class="2sm:px-0 2sm:translate-x-[1.8em] translate-x-[1em] px-[1em]">
                            {!! __('home-page/car_shipping_info.line_bot_1') !!}
                        </p>
                        <h4
                            class="2sm:text-50 2sm:!mb-0 2md:text-[80px] md:text-60 text-center text-[45px] leading-[1.2] font-medium"
                        >
                            {!! __('home-page/car_shipping_info.line_bot_2') !!}
                        </h4>
                        @if ($isJa)
                            <p
                                class="2sm:absolute 2sm:text-25 2sm:right-[-3em] 2sm:bottom-0 2sm:px-0 2md:text-40 static px-[1em] text-right text-xl md:static md:right-auto md:bottom-auto md:text-[35px]"
                            >
                                {!! __('home-page/car_shipping_info.line_bot_3') !!}
                            </p>
                        @endif
                    </div>
                    <div
                        class="2sm:absolute 2sm:shadow-none {{ $isJa ? '2sm:top-[24%] lg:right-[14%]' : '2sm:top-[21%] lg:right-[13%]' }} 2sm:right-[2%] 2sm:my-0 2sm:py-0 2sm:bg-transparent 2md:top-[24%] 2md:right-[7%] 2md:text-base text-11 static my-[1.5em] rounded-sm bg-white py-[1.5em] font-normal shadow-gray-500 md:top-1/4 md:right-[3%] md:text-sm"
                    >
                        <p class="2sm:font-bold text-center text-gray-900 md:mb-4">
                            {!! __('home-page/car_shipping_info.friend_description') !!}
                        </p>
                        <a
                            href="{{ $isJa ? 'https://lin.ee/ua6qQrL' : 'https://lin.ee/gqszijF' }}"
                            target="_blank"
                            class="2sm:mt-[1em] 2sm:text-11 2md:text-base 2sm:w-[11em] 2sm:border-none mx-auto mt-[0.5em] block w-4/5 rounded-sm border border-white bg-lime-500 px-[0.9em] py-[0.7em] text-center text-[18px] font-bold text-white shadow-gray-200 transition duration-500 hover:bg-[#029b02] md:text-sm"
                        >
                            {!! __('home-page/car_shipping_info.friend_btn') !!}
                        </a>
                    </div>
                </div>
            </aside>
        </div>
    </x-services.container>
@endsection
