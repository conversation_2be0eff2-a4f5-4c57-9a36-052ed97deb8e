<?php

declare(strict_types=1);

namespace App\Enums\AaPlace;

/**
 * ChikuRegion Enum
 *
 * Defines Japanese regions for auction places
 * Used for geographic categorization and location filtering
 */
enum ChikuRegion: string
{
    case KINKI = '近畿';
    case HOKKAIDO = '北海道';
    case KANTO = '関東';
    case KOSHINETSU = '甲信越';
    case HOKURIKU = '北陸';
    case TOHOKU = '東北';
    case TOKAI = '東海';
    case CHUGOKU = '中国';
    case SHIKOKU = '四国';
    case KYUSHU = '九州';
    case OKINAWA = '沖縄';

    /**
     * Get all region values as array using array_column for dynamic approach
     *
     * @return array Array of all region names in Japanese
     */
    public static function getAllValues(): array
    {
        return array_column(self::cases(), 'value');
    }
}
