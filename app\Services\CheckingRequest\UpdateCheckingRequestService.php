<?php

declare(strict_types=1);

namespace App\Services\CheckingRequest;

use App\Models\sqlServer\BeforeCheck;
use App\Repositories\sqlServer\CheckingRequestRepository;
use Exception;
use Illuminate\Support\Carbon;

class UpdateCheckingRequestService
{
    private CheckingRequestRepository $checkingRequestRepository;
    private MailAssignInspectorService $mailAssignInspectorService;

    public function __construct(CheckingRequestRepository $checkingRequestRepository, MailAssignInspectorService $mailAssignInspectorService)
    {
        $this->checkingRequestRepository = $checkingRequestRepository;
        $this->mailAssignInspectorService = $mailAssignInspectorService;
    }

    /**
     * @throws Exception
     */
    public function call(int $id, array $body): void
    {
        $existing = BeforeCheck::findOrFail($id);
        $oldInspectorId = $existing->t_inspector_id;

        $updateData = $this->prepareUpdateData($body);

        $this->checkingRequestRepository->update($id, $updateData);

        $newInspectorId = $updateData['t_inspector_id'] ?? null;
        if (null !== $newInspectorId && (string)$newInspectorId !== (string)$oldInspectorId) {
            $this->mailAssignInspectorService->call($id);
        }
    }

    private function prepareUpdateData(array $body): array
    {
        $now = Carbon::now();

        return [
            'up_date' => $now->format('Y-m-d H:i:s'),
            'up_owner' => auth()->user()->id,
            'aa_no' => $body['aa_no'],
            'car_name' => $body['car_name'],
            'note' => $body['note'],
            'cc_note' => $body['cc_note'],
            'photo_flg' => $body['photo_flg'],
            'optn1_flg' => $body['optn1_flg'],
            'optn2_flg' => $body['optn2_flg'],
            'st_cd' => $body['st_cd'],
            't_inspector_id' => $body['t_inspector_id'],
        ];
    }
}
