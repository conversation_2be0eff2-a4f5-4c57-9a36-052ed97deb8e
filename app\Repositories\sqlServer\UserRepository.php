<?php

declare(strict_types=1);

namespace App\Repositories\sqlServer;

use App\Models\sqlServer\User;
use App\Repositories\BaseRepository;
use App\Repositories\common\BuilderWhereCondition;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;

class UserRepository extends BaseRepository
{
    public function model(): string
    {
        return User::class;
    }

    public function getOneBy(array $attrs): object|null
    {
        $query = ($this->model)::query();
        $query = BuilderWhereCondition::call($query, false, $attrs);
        return $query->first();
    }

    public function getListCustomerSales(array $conditions)
    {
        return ($this->model)::query()
            ->select('m_customer.id', 'm_customer.cus_name_JP', 'm_customer.shipping_prsn_name', 'm_customer.ah_sales_id', 'm_sales_staff.e_mail', 'm_sales_staff.team', 'm_sales_staff.s_name', 'm_customer.ah_send_mailaddress', 'm_customer.ah_sales_mail_flg', 'm_customer.bk_trans_price_mail_flg', 'm_customer.bk_trans_plate_mail_flg')
            ->leftJoin('m_sales_staff', 'm_customer.ah_sales_id', '=', 'm_sales_staff.s_id')
            ->where($conditions)
            ->first();
    }

    public function getListCustomerSalesByIdList(array $idList, array $conditions)
    {
        return ($this->model)::query()
            ->selectRaw('
                m_customer.id, m_customer.cus_name_JP, m_customer.shipping_prsn_name, m_customer.ah_sales_id, m_sales_staff.e_mail, m_sales_staff.team, m_sales_staff.s_name,
                m_customer.ah_send_mailaddress, m_customer.ah_sales_mail_flg, m_customer.bk_trans_note_mail_flg, m_customer.bk_trans_price_mail_flg,
                m_customer.bk_trans_plate_mail_flg, m_customer.bk_trans_file_mail_flg, m_customer.bk_trans_cancel_mail_flg, m_customer.bk_trans_onhold_mail_flg
            ')
            ->leftJoin('m_sales_staff', 'm_customer.ah_sales_id', '=', 'm_sales_staff.s_id')
            ->whereIn('id', $idList)
            ->where($conditions)
            ->get();
    }

    public function paginate($params = null, array $relations = [], $columns = ['*']): LengthAwarePaginator
    {
        $query = ($this->model)::query();

        $query->with($relations);

        $params = $params ?: request()->toArray();
        $limit = Arr::pull($params, 'limit', 20);
        $page = Arr::pull($params, 'page');

        $query = BuilderWhereCondition::call($query, false, $params);
        $query = $this->addSoftDeleteCondition($query);

        $this->buildSortQuery($query, $params);

        return $query->paginate($limit, $columns, 'page', $page);
    }

    public function getList($params = null, array $relations = [], $columns = ['*']): Collection
    {
        $query = ($this->model)::query();

        $query->with($relations);

        $query = BuilderWhereCondition::call($query, false, $params);
        $query = $this->addSoftDeleteCondition($query);
        $query = $this->applyFilterScope($query, $params);

        $this->buildSortQuery($query, $params);

        return $query->get(is_array($columns) ? $columns : func_get_args());
    }
}
