<?php

declare(strict_types=1);

namespace App\Http\ResponseBuilder;

use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;

use function is_int;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\ResponseBuilder\Converter;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\ResponseBuilder\Exceptions\ArrayWithMixedKeysException;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\ResponseBuilder\Exceptions\ClassNotFound;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\ResponseBuilder\Exceptions\ConfigurationNotFoundException;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\ResponseBuilder\Exceptions\IncompatibleTypeException;
use Marc<PERSON><PERSON><PERSON><PERSON>\ResponseBuilder\Exceptions\InvalidTypeException;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\ResponseBuilder\Exceptions\MissingConfigurationKeyException;
use Marc<PERSON>O<PERSON><PERSON>\ResponseBuilder\Exceptions\NotIntegerException;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\ResponseBuilder\ResponseBuilder as Builder;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\ResponseBuilder\ResponseBuilder as RB;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\ResponseBuilder\Type;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\ResponseBuilder\Validator;

class ResponseBuilder extends Builder
{
    /**
     * @param bool $success
     * @param int $api_code
     * @param string|int $msg_or_api_code
     * @param array|null $placeholders
     * @param mixed|null $data
     * @param array|null $debug_data
     * @return array
     * @throws ArrayWithMixedKeysException
     * @throws ClassNotFound
     * @throws ConfigurationNotFoundException
     * @throws IncompatibleTypeException
     * @throws InvalidTypeException
     * @throws MissingConfigurationKeyException
     * @throws NotIntegerException
     */
    protected function buildResponse(
        bool       $success,
        int        $api_code,
        string|int $msg_or_api_code,
        ?array     $placeholders = null,
        mixed      $data = null,
        ?array     $debug_data = null
    ): array {
        // ensure $data is either @null, array or object of class with configured mapping.
        $data = (new Converter())->convert($data);
        if (null !== $data) {
            // ensure we get object in final JSON structure in data node
            $data = (object)$data;
        }

        if (null === $data && Config::get(RB::CONF_KEY_DATA_ALWAYS_OBJECT, false)) {
            $data = (object)[];
        }

        // get human readable message for API code or use message string (if given instead of API code)
        if (is_int($msg_or_api_code)) {
            $message = $this->getMessageForApiCode($success, $msg_or_api_code, $placeholders);
        } else {
            Validator::assertIsType('message', $msg_or_api_code, [Type::STRING, Type::INTEGER]);
            $message = $msg_or_api_code;
        }

        $dataFormat = $data;
        if (isset($data->item)) {
            $dataFormat = $data->item;
        }

        if (isset($data->items)) {
            $dataFormat = $data->items;
        }

        /** @noinspection PhpUndefinedClassInspection */
        $response = [
            RB::KEY_SUCCESS => $success,
            RB::KEY_CODE => $api_code,
            RB::KEY_LOCALE => App::getLocale(),
            RB::KEY_MESSAGE => $message,
            RB::KEY_DATA => $dataFormat,
        ];

        if (!empty($data->meta)) {
            $response['meta'] = $data->meta;
        }

        if (null !== $debug_data) {
            $debug_key = Config::get(RB::CONF_KEY_DEBUG_DEBUG_KEY, RB::KEY_DEBUG);
            $response[$debug_key] = $debug_data;
        }

        return $response;
    }
}
