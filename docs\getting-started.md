# Hướng dẫn cài đặt và chạy ứng dụng

## 1. <PERSON><PERSON><PERSON> hình môi trường

### 1.1. Tạo file cấu hình
* Tại thư mục root tạo file `.env.dev` và `.env`
* Copy nội dung của thư mục `.env.example` vào `.env.dev` và `.env`

## 2. Cài đặt và chạy bằng Docker

### 2.1. Cài đặt Docker trên Windows
* Link cài đặt: https://docs.docker.com/desktop/setup/install/windows-install/
* Chọn phiên bản phù hợp với thiết bị

### 2.2. Khởi động Docker Container
```bash
docker compose up -d
```

### 2.3. Khởi tạo dữ liệu

#### 2.3.1. Tạo database
* Cài đặt SSMS (SQL Server Management Studio) [Link tải](https://learn.microsoft.com/en-us/ssms/download-sql-server-management-studio-ssms)
* Connect với Database theo config trong file docker-compose.yml 
* Tạo database tên demo_project
* (<PERSON><PERSON> thể thay thế bằng các tool connect database khác như datagrip(jetbrain) hoặc dbeaver)

#### 2.3.2. Migration database
```bash
docker-compose exec app php artisan migrate
```


#### 2.3.3. Seeder default data
```bash
docker-compose exec app php artisan db:seed
```

#### 2.3.4. Chạy job worker
```bash
docker-compose exec app php artisan queue:work --queue=default --tries=3
```

#### 2.3.5. Chạy websocket server
```bash
docker-compose exec app php artisan reverb:start 
```

## 3. Cài đặt môi trường local

### 3.1. Cài đặt PHP 8.2 trên Windows
* Tải PHP 8.2 từ trang chủ: https://windows.php.net/download#php-8.2 hoặc cài đặt XAMPP ứng với phiên bản PHP 8.2
* Giải nén vào thư mục, ví dụ: `C:\php`
* Thêm đường dẫn PHP vào biến môi trường PATH
* Tạo file php.ini từ file php.ini-development
* Kích hoạt các extension cần thiết trong php.ini:
  ```text
  extension=curl
  extension=fileinfo
  extension=mbstring
  extension=openssl
  extension=pdo_mysql
  extension=pdo_sqlsrv
  extension=sqlsrv
  extension=zip
  extension=sodium
  ```
  
### 3.2. Cài đặt trình quản lí gói
* Tải composer tại link https://getcomposer.org/download/

### 3.3. Cài đặt Driver SQL Server cho SQL Server 2019
* Tải ODBC Driver for SQL Server từ Microsoft: https://learn.microsoft.com/en-us/sql/connect/odbc/download-odbc-driver-for-sql-server
* Tải và cài đặt các Driver PHP cho SQL Server: https://learn.microsoft.com/en-us/sql/connect/php/download-drivers-php-sql-server
* Giải nén và copy các file DLL (php_sqlsrv_82_ts_x64.dll và php_pdo_sqlsrv_82_ts_x64.dll) vào thư mục ext của PHP
* Đảm bảo đã kích hoạt các extension sqlsrv và pdo_sqlsrv trong php.ini

### 3.4. Chạy api trên windown trực tiếp , không qua docker container

#### 3.4.1. Chuẩn bị môi trường (STEP 1)
* Command hai container nginx và app trong file docker-compose.yml
* Nếu container đã chạy thì trong giao diện Docker Desktop tắt hai container laravel-app và laravel-nginx, hoặc chạy lệnh:
  ```bash
  docker stop laravel-nginx laravel-app
  ```

* Copy nội dung của thư mục `.env.example.local` vào `.env`
* Chỉnh sửa lại cấu hình trong file .env cho hợp lý nếu có sự thay đổi

#### 3.4.2. Chạy các service (STEP 2)
* Tại thư mục folder project chạy
```bash
composer install 
```
* Tại thư mục ngoài của folder code, mở ba terminal riêng biệt:
* Terminal 1: Chạy laravel app
    ```bash
    php artisan serve
    ```
* Terminal 1: Chạy queue worker
  ```bash
    php artisan queue:work --queue=default --tries=3
  ```
* Terminal 2: Chạy websocket server
  ```bash
  php artisan reverb:start
  ```
Api chạy ở http://127.0.0.1:8000
Websocket ở http://127.0.0.1:8080

## 4. Lưu ý quan trọng

### 4.1. Tương thích Driver SQL Server trên Windows
* Chú ý phiên bản OS của hệ điều hành Windows để cài đặt driver thích hợp (đang example với phiên bản OS phổ biến Windows 64 bit x86)
* Chú ý sử dụng driver SQL Server phù hợp với phiên bản SQL Server 2019
* Đảm bảo sử dụng đúng phiên bản driver cho PHP 8.2 (SQLSRV 5.10 trở lên)
* Nếu gặp lỗi kết nối, kiểm tra cấu hình network, firewall và cấu hình trong file config/database.php

### 4.2. Truy cập các service
* **Mail Service**: Xem cấu hình trong docker-compose.yml để truy cập web interface của MailHog (thường là http://localhost:8025)
* **S3 Service**: Nếu sử dụng MinIO, xem cấu hình trong docker-compose.yml để truy cập web console (thường là http://localhost:9090)
* **Database**: Ports và credentials được cấu hình trong docker-compose.yml

### 4.3. Cấu hình từ docker-compose.yml
* Tất cả thông tin cấu hình dịch vụ đều được định nghĩa trong file docker-compose.yml
* Kiểm tra file này để lấy thông tin về:
  * Ports được mapping
  * Environment variables
  * Volumes
  * Networks




