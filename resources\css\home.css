body {
    overflow-x: hidden;
}

body main {
    overflow: hidden;
}
.service-menu {
    --border_color: #aaa;
}

.service-menu__contents {
    display: flex;
    gap: 2rem;
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
    padding-top: 10rem;
    padding-bottom: 8rem;
}

@media screen and (max-width: 1500px) {
    .service-menu__contents {
        flex-direction: column;
        max-width: 1000px;
        /* max-width: none; */
        padding-left: 2.875rem;
        padding-right: 2.875rem;
    }
}

@media screen and (max-width: 991px) {
    .service-menu__contents {
        padding-left: 2rem;
        padding-right: 2rem;
    }

    .service-menu__box {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

@media screen and (max-width: 1199px) {
    .service-menu__box {
        padding-left: 2.5rem;
        padding-right: 2.5rem;
    }
}

.service-menu__box {
    border-top: 1px solid var(--border_color);
    border-bottom: 1px solid var(--border_color);
    flex: 1;
    padding: 2rem 1rem;
}

.service-menu__box--order {
    background-color: #C7F4E0;
}

.service-menu__box--conf {
    background-color: #C3EDF2;
}

.service-menu__heading {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.service-menu__heading--order {
    color: #22b573;
}

.service-menu__heading--conf {
    color: #27b2c5;
}

.service-menu__heading-text {
    font-weight: bold;
    font-size: 1.125rem;
    margin-left: 0.625rem;
}

.service-menu__heading-text,
.service-list,
.service-list__icon,
.service-list__text,
.service-desc p {
    margin-bottom: 0;
}

.service-menu__heading-icon {
    display: block;
    width: 40px;
}

.service-list {
    display: flex;
    gap: 1rem;
    align-items: start;
    justify-content: center;
    padding-left: 0;
}

.service-list__item {
    flex: 1;
    list-style: none;
    max-width: calc(100% / 3);
}

.service-list__button {
    background-color: white;
    box-shadow: 2px 2px 2px rgba(0, 0, 0, .5);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    gap: 0.5rem;
    min-height: 145px;
    padding: 0.625rem 0.625rem 0.875rem;
    text-decoration: none;
}

.service-list__icon {
    margin-left: auto;
    margin-right: auto;
    width: 60px;
    height: 60px;
}

.service-list__icon img {
    width: 108%;
}

.service-list__text {
    color: #ef4146;
    display: grid;
    font-size: 0.875rem;
    font-weight: bold;
    line-height: 1.4;
    text-align: center;
}

.service-list__text span {
    color: #666;
    font-size: 0.625rem;
    padding-top: 0.25rem;
}

.service-list__desc {
    margin-top: 1.5rem;
}

.service-desc {
    background-color: #fff;
    border: 1px solid var(--border_color);
    border-radius: 0.625rem;
    box-shadow: 2px 2px 5px gray;
    color: #666;
    font-size: 0.925rem;
    padding: 0.625rem;
    width: 100%;

    position: relative;
}

.service-desc::after {
    content: '';
    position: absolute;
    top: -1.4em;
    left: 50%;

    background-color: #fff;
    border-top: 1px solid var(--border_color);
    border-left: 1px solid var(--border_color);
    transform: translate(-50%, 50%) rotate(45deg);
    transform-origin: center center;
    width: 1.25rem;
    height: 1.25rem;
}

@media screen and (max-width: 767px) {
    .service-menu__contents {
        padding: 6rem 1rem;
    }

    .service-list {
        flex-direction: column;
    }

    .service-list__item {
        display: flex;
        align-items: center;
        max-width: none;
        width: 100%;
    }

    .service-list__button-wrapper {
        width: 40%;
    }

    .service-list__button {
        max-width: 180px;
        width: 100%;
    }

    .service-list__desc {
        font-size: 0.875rem;
        margin-top: 0;
        margin-left: 0.5rem;
        width: 60%;
    }

    .service-desc::after {
        top: 50%;
        left: -1.4em;
        transform: translate(50%, -50%) rotate(-45deg);
    }
}

@media screen and (max-width: 576px) {
    .service-menu__box {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .service-list__button-wrapper {
        width: 50%;
    }

    .service-list__desc {
        width: 50%;
    }
}

.cloud_btn {
    display: inline-block;
    text-align: center;
    vertical-align: middle;
    line-height: 40px;
    font-weight: bold;
    color: #fff;
    background: linear-gradient(45deg, #fff, transparent);
    box-shadow: 5px 5px 10px #fff,
    -1px -1px 10px #fff,
    10px 10px 20px #545b62;
    text-shadow: 5px 5px 10px #495057,
    -1px -1px 10px #495057;
    border: solid 3px #c9e2fb;
    border-radius: 15%;
    transition: color 0.15s ease-in-out,
    background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
}

.cloud_btn:hover {
    background: linear-gradient(45deg, #ddd, transparent);
}

.container-width {
    margin: 45px auto 0;
    display: flex;
    flex-wrap: wrap;
    padding: 0 16px;
}

.col-12 {
    flex: 0 0 100%;
    max-width: 100%;
    padding: 0 16px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -16px
}

.btn {
    display: inline-block;
    font-weight: 400;
    color: #212529;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 0.25rem;
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

.btn-danger.detail-btn2 {
    width: 25%;
    height: 50px;
    line-height: 37px;
    font-size: 19px;
    font-weight: bold;
}

.btn-danger {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-danger:hover {
    color: #fff;
    background-color: #c82333;
    border-color: #bd2130;
}

div.main_title {
    font-size: 40px;
    font-weight: bold;
}

.service-list a:hover {
    color: #0056b3;
    text-decoration: underline;
}

@media screen and (max-width: 576px) {
    .news_p {
        min-height: 42px;
        max-height: 48px;
        padding: 0;
    }
}

@media screen and (max-width: 991px) {
    body {
        font-size: 14px;
    }

    .shutter {
        font-size: 45px;
        padding-top: 150px;
        background-size: 240%;
    }

    div.main_title {
        font-size: 25px;
        font-weight: bold;
    }

    .btn-danger.detail-btn2 {
        width: 35%;
        height: 38px;
        line-height: 28px;
        font-size: 14px;
    }
}

@media (min-width: 576px) {
    .col-sm-2 {
        -ms-flex: 0 0 16.666667%;
        flex: 0 0 16.666667%;
        max-width: 16.666667%;
    }

    .col-sm-10 {
        -ms-flex: 0 0 83.333333%;
        flex: 0 0 83.333333%;
        max-width: 83.333333%;
    }
}

@media screen and (min-width: 577px) and (max-width: 991px) {
    .news_p {
        min-height: 42px;
    }
}

@media (min-width: 768px) {
    .col-md-2 {
        flex: 0 0 16.666667%;
        max-width: 16.666667%;
    }

    .col-md-10 {
        flex: 0 0 83.333333%;
        max-width: 83.333333%;
    }
}

@media (min-width: 1200px) {
    body {
        font-size: 16px;
        font-weight: 400;
        line-height: 1.5;
    }
}

.container-width {
    margin: 45px auto 0;
}

.shutter {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #1e1e1e;
    z-index: 9999;
    font-size: 90px;
    color: #fff;
    text-align: center;
    margin: 0 auto;
    padding-top: 200px;
    font-weight: bold;
}

.service_title, .service_p {
    font-family: 'M PLUS Rounded 1c', sans-serif;
    color: #6c757d;
}

.shutter::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    margin: auto;
    background-color: #fff;
    width: 0;
    height: 1px;
}

.shutter::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    margin: auto;
    background: linear-gradient(to top, #fff, #f4f8fb, #eef7fd, #b7e0fd, #92d0fb, #b7e0fd, #eef7fd, #f4f8fb, #fff, transparent);
    width: 120%;
    height: 0;
}

.shutter {
    -webkit-animation: byeShutter 2.6s forwards;
    animation: byeShutter 2.6s forwards;
}

.shutter::before {
    -webkit-animation: shutterOpen 2.6s forwards;
    animation: shutterOpen 2.6s forwards;
}

.shutter::after {
    -webkit-animation: shutterOpen2 2.6s forwards;
    animation: shutterOpen2 2.6s forwards;
}

.content {
    -webkit-animation: contentScale 2.6s forwards;
    animation: contentScale 2.6s forwards;
}

@keyframes byeShutter {
    70% {
        opacity: 1;
    }
    100% {
        opacity: 0;
        display: none;
        z-index: -1;
    }
}

@keyframes shutterOpen {
    0% {
        width: 0;
        height: 1px;
    }
    50% {
        width: 100%;
        height: 1px;
    }
    90% {
        width: 100%;
        height: 100%;
    }
    100% {
        width: 100%;
        height: 100%;
    }
}

@keyframes shutterOpen2 {
    60% {
        width: 120%;
        height: 0;
        transform: rotate(5deg);
    }
    90% {
        width: 120%;
        height: 100%;
        transform: rotate(-5deg);
    }
    100% {
        width: 120%;
        height: 100%;
        transform: rotate(-5deg);
    }
}

.shutter {
    background: #fff;
    text-align: center;
    width: 100vw;
    height: 100vh;
}

.shutter_inner {
    position: absolute;
    left: 50%;
    top: 50%;
}

#backtotop {
    margin-bottom:  25px;
}

.oshirase a:hover{
    text-decoration: underline !important;
}
.blink {
    animation: blinkAnime 1s infinite alternate;
}

@keyframes blinkAnime {
    0% {
        color: #000000
    }
    100% {
        color: #ffffff
    }
}

@media screen and (max-width: 576px) {
    .container-width {
        max-width: 540px;
    }
}

@media screen and (min-width: 576px) and (max-width: 767px) {
    .container-width {
        max-width: 720px;
    }
}

@media screen and (min-width: 768px) and (max-width: 991px) {
    .container-width {
        max-width: 960px;
    }
}

@media screen and (min-width: 992px) and (max-width: 1199px) {
    .container-width {
        max-width: 960px;
    }
}

@media screen and (min-width: 1200px) and (max-width: 1499px) {
    .container-width {
        max-width: 1140px;
    }
}

@media screen and (min-width: 1500px) and (max-width: 1799px) {
    .container-width {
        max-width: 1140px;
    }
}

@media screen and (min-width: 1800px) {
    .container-width {
        max-width: 1140px;
    }
    #backtotop {
        margin-bottom:  160px;
    }
}
