<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use Illuminate\Support\Facades\DB;

class GetAs400<PERSON>ontroller extends Controller
{
    public function getData()
    {
        $data = DB::connection('as400')->select('
            SELECT DISTINCT MCONTP.CRNN37 AS "name", MPORTP.PTKNCD AS "code"
            FROM ABDLIB.MCONTP MCONTP
            INNER JOIN HCDLIB.MPORTP MPORTP
              ON MCONTP.CRCC52 = MPORTP.PTKNCD
            WHERE MCONTP.CRCC51 = \'GJ03\'
            ORDER BY MCONTP.CRNN37 ASC
            FETCH FIRST 10 ROWS ONLY
        ');

        return response()->json([
            'success' => true,
            'data' => $data,
        ]);
    }
}
