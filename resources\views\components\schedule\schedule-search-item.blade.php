@props([
    'id' => '',
    'title' => '',
    'classContainer' => '',
])

@php
    $classDropdownMenu = 'left-0 ';
    if ($id === 'country') {
        $classDropdownMenu = 'right-0 2sm:left-0 2sm:right-auto ';
    } elseif ($id === 'port') {
        $classDropdownMenu = 'right-0 2sm:left-0 2sm:right-auto ';
    }
@endphp

<div class="{{ $classContainer }} w-1/2 border-b border-[#eeee] px-4 pb-2 lg:w-1/3 lg:pb-[35.5px]">
    <div class="mb-1 pt-[10px] text-sm leading-5 font-semibold text-slate-950 lg:mb-5 lg:p-0">{{ $title }}</div>
    <div class="search-schedule__item-content relative" id="select-{{ $id }}">
        <div class="flex items-center justify-between">
            <input
                type="text"
                placeholder="{{ $title }}"
                class="input-value w-full cursor-pointer text-base leading-6 font-normal placeholder-gray-200 focus:ring-0 focus:outline-none"
                readonly
            />
            <svg width="10" height="7" viewBox="0 0 10 7" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M9.70748 1.23599C9.61461 1.14306 9.50434 1.06935 9.38298 1.01905C9.26161 0.968758 9.13152 0.942871 9.00014 0.942871C8.86877 0.942871 8.73868 0.968758 8.61731 1.01905C8.49595 1.06935 8.38568 1.14306 8.29281 1.23599L5.23548 4.29266C5.17297 4.35515 5.0882 4.39025 4.99981 4.39025C4.91142 4.39025 4.82665 4.35515 4.76415 4.29266L1.70748 1.23599C1.51997 1.04839 1.26562 0.942969 1.00038 0.942906C0.735142 0.942844 0.480742 1.04815 0.293146 1.23566C0.105549 1.42317 0.000123577 1.67752 6.1063e-05 1.94276C-1.45112e-06 2.20799 0.105304 2.46239 0.292812 2.64999L3.35015 5.70732C3.56682 5.92402 3.82405 6.09591 4.10716 6.21319C4.39027 6.33046 4.69371 6.39083 5.00015 6.39083C5.30658 6.39083 5.61002 6.33046 5.89313 6.21319C6.17624 6.09591 6.43347 5.92402 6.65015 5.70732L9.70748 2.64999C9.89495 2.46246 10.0003 2.20816 10.0003 1.94299C10.0003 1.67783 9.89495 1.42352 9.70748 1.23599Z"
                    fill="#AAAAAA"
                ></path>
            </svg>
            <input type="hidden" name="{{ $id }}" id="{{ $id }}" />
        </div>
        <div
            class="dropdown-menu {{ $classDropdownMenu }}absolute top-0 z-100 mt-0.5 hidden w-fit min-w-full translate-y-6 rounded-b-sm border border-[#c5c5c5] bg-[#edf3fb] py-2 text-base text-slate-950"
            style="will-change: transform"
        >
            <input
                type="text"
                id="search-{{ $id }}"
                class="search-schedule__item-search mx-auto mb-[10px] block h-[calc(1.5em+0.75rem+2px)] w-[90%] rounded-sm border border-[#ced4da] bg-white bg-clip-padding px-3 py-1.5 text-base leading-normal text-[#495057]"
            />
            <div class="dropdown_list-item max-h-[175px] overflow-x-hidden overflow-y-auto text-slate-950">
                {{ $slot }}
            </div>
        </div>
    </div>
</div>
