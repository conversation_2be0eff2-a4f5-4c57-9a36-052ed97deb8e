# Cấu trúc thư mục dự án

## 1. Tổ<PERSON> quan

Dự án được tổ chức theo cấu trúc tiêu chuẩn củ<PERSON>, với một số thư mục và tệp tin tùy chỉnh phục vụ nhu cầu cụ thể của dự án.

## 2. <PERSON><PERSON><PERSON> tệp tin gốc

| Tệp tin | <PERSON><PERSON> tả |
|---------|-------|
| `.editorconfig` | Cấu hình để đồng bộ coding style trên các IDE |
| `.env` / `.env.example` | Biến môi trường cho môi trường cục bộ và mẫu |
| `artisan` | Công cụ CLI của <PERSON> |
| `composer.json` / `composer.lock` | Quản lý phụ thuộc PHP |
| `docker-compose.yml` | C<PERSON>u hình cho các container Docker |
| `package.json` | <PERSON><PERSON> thuộc Node.js và scripts |
| `phpunit.xml` | <PERSON><PERSON><PERSON> hình PHPUnit để testing |
| `README.md` | Tài liệu dự án |

## 3. Các thư mục chính

### 3.1. app/

Chứa mã nguồn chính của ứng dụng:

| Thư mục | Mô tả                                                                  |
|---------|------------------------------------------------------------------------|
| `Console/` | Các lệnh Artisan tùy chỉnh                                             |
| `Constants/` | Định nghĩa các hằng số sử dụng trong dự án                             |
| `Dto/` | Định nghĩa các đối tượng chuyển dữ liệu (Data Transfer Objects)        |
| `Events/` | Định nghĩa các sự kiện như notifications, send mail, broadcast         |
| `Exceptions/` | Định nghĩa xử lý ngoại lệ                                              |
| `Jobs/` | Định nghĩa các công việc xử lý nền                                     |
| `Logging/` | Cấu hình và định dạng log                                              |
| `Mail/` | Định nghĩa các lớp xử lý gửi email                                     |
| `Model/` | Định nghĩa các model kết nối với cơ sở dữ liệu thông qua Eloquent ORM  |
| `Providers/` | Cấu hình dependency injection , start up config khi khởi động ứng dụng |
| `Repository/` | Các lớp xử lý truy vấn cơ sở dữ liệu                                   |
| `Http/` | Controllers, middleware và requests                                    |
| `Services/` | Xử lý logic nghiệp vụ                                                  |

### 3.2. Các thư mục khác

| Thư mục | Mô tả |
|---------|-------|
| `bootstrap/` | Các tệp bootstrap của Laravel |
| `config/` | Các tệp cấu hình ứng dụng |
| `database/` | Migrations, seeders và factories |
| `public/` | Thư mục gốc web, chứa assets và entry point (`index.php`) |
| `resources/` | Views, assets và ngôn ngữ |
| `routes/` | Định nghĩa routes |
| `storage/` | Logs, cache và lưu trữ tệp |
| `tests/` | Unit và feature tests |

## 4. Quy ước về tên và cấu trúc

### 4.1. Controllers

- Vị trí: `app/Http/Controllers`
- Đặt tên: `[name]Controller.php` (VD: `UserController.php`)
- API Controllers: `app/Http/Controllers/Api/[Version]/[Tên]Controller.php`

### 4.2. Models

- Vị trí: `app/Models`
- Đặt tên: Số ít, viết hoa (VD: `User.php`, `Post.php`)

### 4.3. Services

- Vị trí: `app/Services/[Nhóm]`
- Đặt tên: `[name]Service.php` (VD: `UserService.php`)

### 4.4. Repositories

- Vị trí: `app/Repository`
- Đặt tên: `[name]Repository.php` (VD: `UserRepository.php`)

This structure provides a comprehensive overview of the project and its components.
