<?php

declare(strict_types=1);

namespace App\Constants;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\ResponseBuilder\ApiCodesHelpers;

class ApiCodes
{
    use ApiCodesHelpers;

    public const UNCAUGHT_EXCEPTION = 200;
    public const HTTP_NOT_FOUND = 113;
    public const HTTP_SERVICE_UNAVAILABLE = 202;
    public const HTTP_EXCEPTION = 203;
    public const UNAUTHORIZED_EXCEPTION = 204;

    // default return code validation exception
    public const VALIDATION_EXCEPTION = 115;
    public const UNAUTHENTICATED_EXCEPTION = 206;
    public const INVALID_TOKEN = 207;
    public const ROUTE_NOT_FOUND = 208;
    public const WHITE_LIST_VALIDATION_EXCEPTION = 209;
    public const EMAIL_USED_EXCEPTION = 210;
    public const PASSWORD_CONFIRM_ERROR = 211;
    public const RESET_TOKEN_NOT_VALID = 212;
    public const RESET_TOKEN_EXPIRED = 213;
    public const FORBIDDEN_RESOURCE = 214;
    public const CONFLICT_DATA = 215;
    public const ROLE_NOT_FOUND = 216;
    public const AUTHENTICATION_ERROR = 217;
    public const TOKEN_NOT_PROVIDED = 218;
    public const WEB_E_MSG_002 = 'WEB_E_MSG_002';
    public const WEB_E_MSG_043 = 'WEB_E_MSG_043';
    public const FILE_DOWN_LOAD_ERROR = 'FILE_DOWN_LOAD_ERROR';

    public const READABLE_CODE_MAP = [
        'default' => 'UnknownException',
        self::UNCAUGHT_EXCEPTION => 'UncaughtException',
        self::HTTP_NOT_FOUND => 'NotFoundException',
        self::HTTP_SERVICE_UNAVAILABLE => 'ServiceUnavailable',
        self::HTTP_EXCEPTION => 'HttpException',
        self::UNAUTHORIZED_EXCEPTION => 'UnauthorizedException',
        self::UNAUTHENTICATED_EXCEPTION => 'UnauthenticatedException',
        self::VALIDATION_EXCEPTION => 'InvalidParametersException',
        self::INVALID_TOKEN => 'InvalidToken',
        self::ROUTE_NOT_FOUND => 'RouteNotFoundException',
        self::WHITE_LIST_VALIDATION_EXCEPTION => 'WhiteListValidationException',
        self::EMAIL_USED_EXCEPTION => 'EmailUsedException',
        self::PASSWORD_CONFIRM_ERROR => 'PasswordConfirmError',
        self::RESET_TOKEN_NOT_VALID => 'ResetTokenNotValid',
        self::RESET_TOKEN_EXPIRED => 'ResetTokenExpired',
        self::FORBIDDEN_RESOURCE => 'ForbiddenResource',
        self::CONFLICT_DATA => 'ConflictData',
        self::ROLE_NOT_FOUND => 'RoleNotFoundException',
        self::AUTHENTICATION_ERROR => 'AuthenticationError',
        self::TOKEN_NOT_PROVIDED => 'Token not provided',
        self::WEB_E_MSG_002 => '○○行目の検査員を選択してください',
        self::WEB_E_MSG_043	 => '下見データに検査員データが存在するため削除できません',
        self::FILE_DOWN_LOAD_ERROR => 'file download error',
    ];

    public static function convertToReadable(int|string $code): string
    {
        return self::READABLE_CODE_MAP[$code] ?? self::READABLE_CODE_MAP['default'];
    }
}
