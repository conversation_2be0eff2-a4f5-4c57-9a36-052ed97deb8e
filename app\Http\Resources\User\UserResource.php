<?php

declare(strict_types=1);

namespace App\Http\Resources\User;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'idx' => $this->idx,
            'id' => $this->id,
            'cus_Name_JP' => $this->cus_Name_kana,
            'cus_Name_kana' => $this->cus_Name_kana,
            'cus_Name_EN' => $this->cus_Name_EN,
            'tel' => $this->tel,
            'fax' => $this->fax,
            'zipcd' => $this->zipcd,
            'address' => $this->address,
            'address2' => $this->address2,
            'address3' => $this->address3,
            'hn_cd' => $this->hn_cd
        ];
    }
}
