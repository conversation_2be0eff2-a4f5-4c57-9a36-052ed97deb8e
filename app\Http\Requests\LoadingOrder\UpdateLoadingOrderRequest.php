<?php

declare(strict_types=1);

namespace App\Http\Requests\LoadingOrder;

use App\Enums\LoadingOrder\LoadingOrderType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\RequiredIf;

class UpdateLoadingOrderRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'odr_kbn'        => 'required|numeric|min:0|max:255',
            'ref_no'         => 'nullable|string|max:100',
            'agent_ref_no'   => 'nullable|string',
            'st_cd'          => 'required|numeric|min:0|max:255',
            'm_customer_id'  => 'nullable|integer',

            'car_year'       => 'nullable|string|max:4',
            'car_no'         => 'required|string|max:25',

            'car_name'       => [
                'nullable',
                'string',
                'max:50'
                , new RequiredIf(fn () => $this->input('odr_kbn') === LoadingOrderType::ADDITIONAL_SUPPORT->value)
            ],

            'mileage'        => 'nullable|string|max:10',
            'fob_price'      => 'nullable|string|max:10',
            'customs_flg'    => 'nullable|boolean',

            'consignee_name' => [
                'nullable',
                'string',
                'max:50',
                Rule::requiredIf(fn () => $this->consigneeNameRule()),
            ],

            'consignee_flg'  => 'nullable|numeric|min:0|max:255',

            'destination'    => 'nullable|string|max:255',
            'port'           => 'nullable|string|max:255',
            'to_name'        => 'nullable|string|max:50',
            'to_plan_date'   => 'nullable|string|max:25',
            'to_plan_flg'    => 'nullable|numeric|min:0|max:255',
            'special_note'   => 'nullable|string',
            'part_note'      => 'nullable|string|max:255',
            'note'           => 'nullable|string|max:255',

            'optn2_sub_kbn'  => 'nullable|numeric|min:0|max:255',
            'optn2_sub2_kbn' => 'nullable|numeric|min:0|max:255',

            'optn3_flg'      => 'nullable|numeric|min:0|max:255',
            'optn3_sub_txt'  => [
                'nullable',
                'string',
                Rule::requiredIf(fn () => $this->plateRemovalFlagRule()),
            ],
            'optn4_flg'      => 'nullable|numeric|min:0|max:255',
            'optn4_sub_kbn'  => [
                'nullable',
                'numeric',
                'min:0',
                'max:255',
                Rule::requiredIf(fn () => $this->marineInsuranceFlagRule()),
            ],

            'optn5_flg'      => 'nullable|numeric|min:0|max:255',

            'optn6_flg'      => 'nullable|numeric|min:0|max:255',
            'optn6_sub_kbn'  => [
                'nullable',
                'numeric',
                'min:0',
                'max:255',
                Rule::requiredIf(fn () => $this->exportInspectionFlagRule())
            ],
            'optn7_flg'      => 'nullable|numeric|min:0|max:255',
            'optn8_flg'      => 'nullable|numeric|min:0|max:255',

            'optn10_flg'     => 'nullable|numeric|min:0|max:255',
            'optn11_flg'     => 'nullable|numeric|min:0|max:255',
            'optn14_flg'     => 'nullable|numeric|min:0|max:255',
            'optn17_flg'     => 'nullable|numeric|min:0|max:255',

            'optn18_flg'     => [
                'nullable',
                'numeric',
                'min:0',
                'max:255',
            ],
            'optn21_flg'     => 'nullable|numeric|min:0|max:255',
        ];
    }

    public function messages(): array
    {
        return [
            'car_name.required_if' => 'The car name field (car_name) is required when order type (odr_kbn) is additional support.',
            'consignee_name.required_if' => 'The consignee name field (consignee_name) is required for this order type (odr_kbn).',
            'consignee_name.required' => $this->getConsigneeNameMessage(),
            'optn3_sub_txt.required' => 'The plate removal text field (optn3_sub_txt) is required when plate removal option (optn3_flg) is selected.',
            'optn4_sub_kbn.required' => 'The marine insurance category field (optn4_sub_kbn) is required when marine insurance option (optn4_flg) is selected.',
            'optn6_sub_kbn.required' => 'The export inspection category field (optn6_sub_kbn) is required when export inspection option (optn6_flg) is selected.',
        ];
    }

    private function getConsigneeNameMessage(): string
    {
        $odr_kbn = (string)$this->input('odr_kbn');
        $consignee_name = (string)$this->input('consignee_name');
        $consignee_flg = (string)$this->input('consignee_flg', '');

        if ('' === $consignee_name && $odr_kbn === LoadingOrderType::AUSTRALIA_SMART->value) {
            return 'Consignee name is required for Australia SMART plan.';
        }

        if (('' === $consignee_name) && ($odr_kbn === LoadingOrderType::NEW_ZEALAND_SMART->value) && blank($consignee_flg)) {
            return 'Please enter consignee name or select undecided for New Zealand SMART plan.';
        }

        if ('' !== $consignee_name && $odr_kbn === LoadingOrderType::NEW_ZEALAND_SMART->value && '1' === $consignee_flg) {
            return 'Please enter consignee name or select undecided, not both for New Zealand SMART plan.';
        }

        return 'The consignee name field (consignee_name) is required for this order type (odr_kbn).';
    }

    private function plateRemovalFlagRule(): bool
    {
        $optn3_flg = $this->input('optn3_flg');
        return !(blank($optn3_flg) || "0" === $optn3_flg);
    }

    private function marineInsuranceFlagRule(): bool
    {
        $optn4_flg = $this->input('optn4_flg');
        return !(blank($optn4_flg) || "0" === $optn4_flg);
    }

    private function exportInspectionFlagRule(): bool
    {
        $odr_kbn = (string)$this->input('odr_kbn');
        $planList = [LoadingOrderType::NEW_ZEALAND_SMART , LoadingOrderType::AUSTRALIA_SMART];

        if (!in_array($odr_kbn, $planList, true)) {
            return false;
        }

        $optn6_flg = $this->input('optn6_flg');

        return !(blank($optn6_flg) || "0" === $optn6_flg);
    }

    private function consigneeNameRule(): bool
    {
        $consignee_name = (string)$this->input('consignee_name');
        $odr_kbn = (string)$this->input('odr_kbn');
        $consignee_flg = (string)$this->input('consignee_flg', '');


        $smartPlans = [
            LoadingOrderType::NEW_ZEALAND_SMART->value,
            LoadingOrderType::AUSTRALIA_SMART->value,
            LoadingOrderType::USA_SMART->value,
            LoadingOrderType::UK_SMART->value,
        ];

        if (!in_array($odr_kbn, $smartPlans, true)) {
            return false;
        }

        if ('' === $consignee_name && $odr_kbn === LoadingOrderType::AUSTRALIA_SMART->value) {
            return true;
        }

        if (('' === $consignee_name) && ($odr_kbn === LoadingOrderType::NEW_ZEALAND_SMART->value) && blank($consignee_flg)) {
            return true;
        }

        return (bool) ('' !== $consignee_name && $odr_kbn === LoadingOrderType::NEW_ZEALAND_SMART->value && '1' === $consignee_flg);
    }
}
