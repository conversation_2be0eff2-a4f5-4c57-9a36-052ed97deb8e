<?php

declare(strict_types=1);

namespace App\Http\Resources\Transport;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\Transport\TransIdDefault;

class BulkEditTransportResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'm_trans_name' => $this->getMTransName($this->m_trans_id),
            'fr_name' => $this->fr_name,
            'to_name' => $this->to_name,
            'st_cd' => $this->st_cd,
            'ref_no' => $this->ref_no,
            'cus_name_JP' => $this->cus_name_JP,
            'car_no' => $this->car_no,
            'car_name' => $this->car_name,
        ];
    }

    private function getMTransName(mixed $mTransId): string
    {
        return match ($mTransId) {
            TransIdDefault::LOGICO->value => 'ロジコ',
            TransIdDefault::DREAM_INTERNATIONAL->value => 'ドリームインターナショナル',
            TransIdDefault::SHIPPING_EAST_AND_WEST->value => '東西海運',
            TransIdDefault::TARGET->value => 'キャリーゴール',
            TransIdDefault::EIKO_SHOUNEN->value => '栄港商運',
            TransIdDefault::J_BRING->value => 'ジェイキャリー',
            default => ''
        };
    }
} 