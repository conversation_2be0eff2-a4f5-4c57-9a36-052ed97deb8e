@extends('app')
@push('styles')
    @vite('resources/css/contact.css')
@endpush

@section('title', __('home-page/contact.page_title'))

@php
    $isJapanese = app()->getLocale() === 'ja';
    $routeContact = $isJapanese ? route('contact-post') : route('localized.contact-post', ['locale' => 'en']);
    $page = $page ?? 'new';
    $select_contact = $select_contact ?? '0';
    $company_name = $company_name ?? '';
    $name = $name ?? '';
    $mail_address = $mail_address ?? '';
    $tel = $tel ?? '';
    $message = $message ?? '';

    $contact_err = $contact_err ?? 0;
    $select_contact_err = $select_contact_err ?? 0;
    $company_name_err = $company_name_err ?? 0;
    $name_err = $name_err ?? 0;
    $mail_address_err = $mail_address_err ?? 0;
    $tel_err = $tel_err ?? 0;

    $select_contact_arr = [
        0 => '---',
        1 => __('home-page/contact.lang_contact_type02'),
        2 => __('home-page/contact.lang_contact_type03'),
        3 => __('home-page/contact.lang_contact_type04'),
        4 => __('home-page/contact.lang_contact_type05'),
    ];
@endphp

@section('content')
    <div class="m-0 w-full p-0" data-aos="zoom-in">
        <div class="contact_header_div"></div>
        <div class="bg-image01 bg-fixed">
            <div class="contact_bg01"></div>
            <div class="contact_bg02"></div>
            <div class="contact_bg03"></div>
        </div>
    </div>

    <div class="mb-11 w-full overflow-hidden px-4">
        <div class="mx-auto mt-12 w-full max-w-6xl px-4 md:mt-32">
            <div class="w-full pt-1 pb-4" data-aos="zoom-in-right" data-aos-duration="1000">
                <h1 class="2sm:text-3xl 2md:pl-0 my-[10px] sm:!mt-6 mb-[10px] pl-[10%] text-25 font-bold md:text-35">
                    {{ __('home-page/contact.lang_contact') ?? 'お問い合わせ' }}
                </h1>
                <h2 class="2sm:text-lg md:text-23 sm:!mb-6 !mb-[15px] text-base font-semibold">
                    {{ __('home-page/contact.lang_contact_title') ?? 'お問い合わせフォーム' }}
                </h2>
                <div class="sm:mb-6 mb-[1rem] flex items-center">
                    <i class="fas fa-angle-double-right mr-3 text-2xl"></i>
                    <p class="2sm:text-base text-[0.8em]">{{ __('home-page/contact.lang_contact_01') }}</p>
                </div>
                <div class="sm:mb-4 mb-[1rem] flex items-center">
                    <i class="fas fa-angle-double-right mr-3 text-2xl"></i>
                    <p class="2sm:text-base text-[0.8em]">{{ __('home-page/contact.lang_contact_02') }}</p>
                </div>
            </div>
        </div>

        @if ($page == 'new')
            <div
                class="2sm:!mt-22 relative z-10 mx-auto !mt-5 w-full max-w-6xl rounded-lg bg-white p-12 shadow"
                id="contact_form"
            >
                <form action="{{ $routeContact }}" method="post" enctype="multipart/form-data">
                    @csrf
                    <div class="2sm:gap-4 2sm:grid-cols-12 grid grid-cols-1 border-b border-dashed border-gray-400 p-5">
                        <div class="2sm:col-span-3 text-center font-bold">
                            {{ __('home-page/contact.lang_contact_type01') ?? 'お問い合わせ種別' }}
                            <span class="ml-1 text-sm font-bold text-red-600">＊</span>
                        </div>
                        <div class="2sm:col-span-6">
                            <select
                                class="{{ $select_contact_err == 1 ? 'border-red-500' : '' }} h-[38px] w-full rounded-md border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                                name="select_contact"
                            >
                                @for ($i = 1; $i < count($select_contact_arr); $i++)
                                    <option
                                        value="{{ $i }}"
                                        {{ (old('select_contact') ?? $select_contact) == $i ? 'selected' : '' }}
                                    >
                                        {{ $select_contact_arr[$i] }}
                                    </option>
                                @endfor
                            </select>
                        </div>
                    </div>

                    <div class="2sm:gap-4 2sm:grid-cols-12 grid grid-cols-1 border-b border-dashed border-gray-400 p-5">
                        <div class="2sm:col-span-3 text-center font-bold">
                            {{ __('home-page/contact.lang_contact_houjin') }}
                            <span class="ml-1 text-sm font-bold text-red-600">＊</span>
                        </div>
                        <div class="2sm:col-span-7">
                            <input
                                type="text"
                                class="{{ $errors->has('company_name') ? 'border-red-500' : '' }} h-[38px] w-full rounded-md border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                                name="company_name"
                                placeholder="{!! __('home-page/contact.lang_contact_houjin01') !!}"
                                value="{{ old('company_name') ?? $company_name }}"
                            />
                        </div>
                    </div>

                    <div class="2sm:gap-4 2sm:grid-cols-12 grid grid-cols-1 border-b border-dashed border-gray-400 p-5">
                        <div class="2sm:col-span-3 text-center font-bold">
                            {{ __('home-page/contact.lang_contact_name') }}
                            <span class="ml-1 text-sm font-bold text-red-600">＊</span>
                        </div>
                        <div class="2sm:col-span-7">
                            <input
                                type="text"
                                class="{{ $errors->has('name') ? 'border-red-500' : '' }} h-[38px] w-full rounded-md border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                                name="name"
                                value="{{ old('name') ?? $name }}"
                            />
                        </div>
                    </div>

                    <div class="2sm:gap-4 2sm:grid-cols-12 grid grid-cols-1 border-b border-dashed border-gray-400 p-5">
                        <div class="2sm:col-span-3 text-center font-bold">
                            {{ __('home-page/contact.lang_contact_mail') }}
                            <span class="ml-1 text-sm font-bold text-red-600">＊</span>
                        </div>
                        <div class="2sm:col-span-8">
                            <input
                                type="email"
                                class="{{ $errors->has('mail_address') ? 'border-red-500' : '' }} h-[38px] w-full rounded-md border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                                name="mail_address"
                                value="{{ old('mail_address') ?? $mail_address }}"
                            />
                        </div>
                    </div>

                    <div class="2sm:gap-4 2sm:grid-cols-12 grid grid-cols-1 border-b border-dashed border-gray-400 p-5">
                        <div class="2sm:col-span-3 text-center font-bold">
                            {{ __('home-page/contact.lang_contact_tel') }}
                            <span class="ml-1 text-sm font-bold text-red-600">＊</span>
                        </div>
                        <div class="2sm:col-span-8">
                            <input
                                type="tel"
                                class="{{ $errors->has('tel') ? 'border-red-500' : '' }} h-[38px] w-full rounded-md border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                                name="tel"
                                value="{{ old('tel') ?? $tel }}"
                            />
                        </div>
                    </div>

                    <div class="2sm:gap-4 2sm:grid-cols-12 grid grid-cols-1 border-b border-dashed border-gray-400 p-5">
                        <div class="2sm:col-span-3 text-center font-bold">
                            {{ __('home-page/contact.lang_contact_message') }}
                        </div>
                        <div class="2sm:col-span-9">
                            <textarea
                                class="h-[182px]rounded-md w-full rounded-md border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                                rows="7"
                                name="message"
                            >{{ old('message') ?? $message }}</textarea>
                        </div>
                    </div>

                    <div class="2sm:gap-4 2sm:grid-cols-12 grid grid-cols-1 p-5">
                        <div class="2sm:col-span-4 2sm:col-start-5">
                            <button
                                type="submit"
                                class="h-14 w-full rounded-full border-2 cursor-pointer btn-contact font-bold text-white transition-all duration-300 hover:from-orange-500 hover:to-orange-600"
                            >
                                {{ __('home-page/contact.lang_next_conf') }}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        @elseif ($page == 'conf')
            <div class="mt-25 min-h-[763px] w-full max-w-6xl p-12 lg:mx-auto" id="contact_form">
                <form
                    id="go_submit"
                    name="go_submit"
                    action="{{ $routeContact }}"
                    method="post"
                    enctype="multipart/form-data"
                >
                    @csrf
                    <input type="hidden" name="page" value="proc" />
                    <input type="hidden" name="select_contact" value="{{ $select_contact }}" />
                    <input type="hidden" name="company_name" value="{{ $company_name }}" />
                    <input type="hidden" name="name" value="{{ $name }}" />
                    <input type="hidden" name="mail_address" value="{{ $mail_address }}" />
                    <input type="hidden" name="tel" value="{{ $tel }}" />
                    <input type="hidden" name="message" value="{{ $message }}" />
                </form>

                <form name="back" action="{{ $routeContact }}" method="post" enctype="multipart/form-data">
                    @csrf
                    <input type="hidden" name="page" value="new" />
                    <input type="hidden" name="select_contact" value="{{ $select_contact }}" />
                    <input type="hidden" name="company_name" value="{{ $company_name }}" />
                    <input type="hidden" name="name" value="{{ $name }}" />
                    <input type="hidden" name="mail_address" value="{{ $mail_address }}" />
                    <input type="hidden" name="tel" value="{{ $tel }}" />
                    <input type="hidden" name="message" value="{{ $message }}" />
                </form>

                <div class="2sm:grid-cols-12 grid grid-cols-1 gap-4 border-b border-dashed border-gray-400 p-5">
                    <div class="2sm:col-span-3 text-center font-bold">
                        {{ __('home-page/contact.lang_contact_type01') }}
                    </div>
                    <div class="2sm:col-span-6">
                        <p>{{ $select_contact_arr[$select_contact] }}</p>
                    </div>
                </div>

                <div class="2sm:grid-cols-12 grid grid-cols-1 gap-4 border-b border-dashed border-gray-400 p-5">
                    <div class="2sm:col-span-3 text-center font-bold">
                        {{ __('home-page/contact.lang_contact_houjin') }}
                    </div>
                    <div class="2sm:col-span-7">
                        <p class="break-all whitespace-break-spaces">{{ $company_name }}</p>
                    </div>
                </div>

                <div class="2sm:grid-cols-12 grid grid-cols-1 gap-4 border-b border-dashed border-gray-400 p-5">
                    <div class="2sm:col-span-3 text-center font-bold">
                        {{ __('home-page/contact.lang_contact_name') }}
                    </div>
                    <div class="2sm:col-span-7">
                        <p class="break-all whitespace-break-spaces">{{ $name }}</p>
                    </div>
                </div>

                <div class="2sm:grid-cols-12 grid grid-cols-1 gap-4 border-b border-dashed border-gray-400 p-5">
                    <div class="2sm:col-span-3 text-center font-bold">
                        {{ __('home-page/contact.lang_contact_mail') }}
                    </div>
                    <div class="2sm:col-span-8">
                        <p class="break-all whitespace-break-spaces">{{ $mail_address }}</p>
                    </div>
                </div>

                <div class="2sm:grid-cols-12 grid grid-cols-1 gap-4 border-b border-dashed border-gray-400 p-5">
                    <div class="2sm:col-span-3 text-center font-bold">
                        {{ __('home-page/contact.lang_contact_tel') }}
                    </div>
                    <div class="2sm:col-span-8">
                        <p class="break-all whitespace-break-spaces">{{ $tel }}</p>
                    </div>
                </div>

                <div
                    class="2sm:grid-cols-12 grid grid-cols-1 gap-4 border-b border-dashed border-gray-400 p-5"
                    style="min-height: 215px"
                >
                    <div class="2sm:col-span-3 text-center font-bold">
                        {{ __('home-page/contact.lang_contact_message') }}
                    </div>
                    <div class="2sm:col-span-9">
                        <p class="break-all whitespace-break-spaces">{!! nl2br(e($message)) !!}</p>
                    </div>
                </div>

                <div class="grid grid-cols-12 gap-4 p-5">
                    <div class="col-span-6 col-start-1 sm:col-span-4 sm:col-start-3 flex justify-end items-center">
                        <button
                            class="h-14 w-[96px] sm:w-full min-w-[96px] max-w-[293px] rounded-full cursor-pointer btn-confirm font-bold text-white transition-all duration-300"
                            id="go_submit_button"
                            onclick="submit_form();"
                        >
                            {{ __('home-page/contact.lang_send') }}
                        </button>
                    </div>
                    <div class="col-span-6 col-start-7 sm:col-span-4 sm:col-start-7 flex justify-start items-center">
                        <button
                            type="button"
                            class="h-14 w-[96px] sm:w-full min-w-[96px] max-w-[293px] rounded-full font-bold text-white transition-all duration-300 btn-back"
                            onclick="document.back.submit();"
                        >
                            {{ __('home-page/contact.lang_return') }}
                        </button>
                    </div>
                </div>
            </div>
        @elseif ($page == 'proc')
            <div class="mx-auto mt-25 w-full max-w-6xl !pt-24" id="contact_form">
                <form name="back" action="{{ $routeContact }}" method="post" enctype="multipart/form-data">
                    @csrf
                    <input type="hidden" name="page" value="new" />
                    <input type="hidden" name="select_contact" value="{{ old('select_contact') }}" />
                    <input type="hidden" name="company_name" value="{{ old('company_name') }}" />
                    <input type="hidden" name="name" value="{{ old('name') }}" />
                    <input type="hidden" name="mail_address" value="{{ old('mail_address') }}" />
                    <input type="hidden" name="tel" value="{{ old('tel') }}" />
                    <input type="hidden" name="message" value="{{ old('message') }}" />
                </form>

                <div class="">
                    <div class="w-full border-t-1 border-b-1 border-dashed border-gray-400 px-5 py-9">
                        @if ($sendOK == 'OK')
                            <p class="mb-4">{{ __('home-page/contact.lang_contact_ok01') }}</p>
                            <p>{{ __('home-page/contact.lang_contact_ok02') }}</p>
                        @else
                            <p>エラーが発生しました。</p>
                            <p>お手数ですが、お問合せTOPからもう一度やり直してください。</p>
                        @endif
                    </div>
                </div>

                <div class="mt-8 grid grid-cols-1 gap-4 p-5 md:grid-cols-12">
                    <div class="md:col-span-6 md:col-start-4">
                        <button
                            type="button"
                            class="h-14 w-full rounded-full cursor-pointer btn-return font-bold text-white transition-all duration-300"
                            onclick="document.back.submit();"
                        >
                            {{ __('home-page/contact.lang_contact_top') }}
                        </button>
                    </div>
                </div>
            </div>
            <div class="mt-[317px] grid grid-cols-1 gap-4"></div>
        @endif
    </div>

    <footer>
        <div
        class="2sm:text-[16px] relative z-10 h-18 px-4 text-[14.4px] leading-18 font-bold text-white [background:linear-gradient(-135deg,#495057,#545b62,#6c757d,#495057,#545b62,#6c757d)]"
    >
        <div class="flex items-center justify-between">
            <div class="h-18 whitespace-nowrap">&copy; 2006-{{ date('Y') }} Autohub Ltd. All rights reserved.</div>
            <div class="hidden items-center lg:flex">
                <a class="mr-3" href="https://www.cssdesignawards.com/sites/autohub/38100/" target="_blank">
                    <img src="{{ asset('images/cssda-special-kudos-purple.png') }}" alt="Imprint" class="h-11 w-11" />
                </a>
                <a class="mr-2" href="https://www.csswinner.com/details/autohub-coltd/14925" target="_blank">
                    <img src="{{ asset('images/star-white.png') }}" alt="Imprint" class="h-11 w-11" />
                </a>
            </div>
        </div>
    </div>
    </footer>
@endsection

<script>
    function submit_form() {
        document.getElementById('go_submit_button').disabled = true
        document.getElementById('go_submit').submit()
    }
</script>
