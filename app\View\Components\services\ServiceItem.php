<?php

declare(strict_types=1);

namespace App\View\Components\services;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class ServiceItem extends Component
{
    /**
     * Create a new component instance.
     */
    public function __construct(public ?int $index, public ?string $title, public ?string $description, public ?string $link, public ?string $bgImage, public ?string $bgBack, public ?string $bgContainer)
    {

    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.services.service-item');
    }
}
