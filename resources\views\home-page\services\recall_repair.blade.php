@extends('app')

@section('title', __('home-page/recall_repair.page_title'))

@php
    $urlOld = env('APP_URL_OLD', '');
    $locale = app()->getLocale();
@endphp

@section('content')
    <x-services.container>
        <x-services.title-page
            titlePage="{{ __('home-page/recall_repair.title') }}"
            description1="{{ __('home-page/recall_repair.page_description1') }}"
            description2="{{ __('home-page/recall_repair.page_description2') }}"
        />
        <x-services.title subTitle="{{ __('home-page/recall_repair.service_descriptions') }}" />
        <x-services.good-content :content="__('home-page/recall_repair.good_content')" />
        <div class="mx-auto mt-[45px] w-full px-4">
            <div
                class="text-15 bg-cover bg-center bg-no-repeat py-[2em] md:py-[5em] md:text-base"
                style="
                    background-image: url('{{ asset('images/services/ah_service_image0802.png') }}');
                    background-color: rgba(255, 255, 255, 0.5);
                    background-blend-mode: lighten;
                "
            >
                <h4 class="2sm:text-xl !m-0 px-[0.5em] py-[0.2em] text-lg font-bold text-white md:text-2xl">
                    {{ __('common.how_to_use') }}
                </h4>
                <ul class="2sm:pl-10 pl-[0.5em] leading-normal font-bold text-gray-200 md:text-3xl">
                    <li
                        class="2sm:text-xl border-b-2 border-b-sky-300 bg-white px-[1em] leading-normal text-gray-500 opacity-80 md:text-3xl"
                    >
                        {{ __('home-page/recall_repair.how_to_use_content_1') }}
                        <i class="fas fa-globe ml-[0.5em] text-sky-300"></i>
                    </li>
                    <li
                        class="2sm:text-xl 2sm:w-[85%] ml-auto w-[94%] border-b-2 border-b-sky-300 bg-white px-[1em] leading-normal text-gray-500 opacity-80 md:text-3xl"
                    >
                        {{ __('home-page/recall_repair.how_to_use_content_2') }}
                        <i class="fas fa-fax ml-[0.5em] text-sky-300"></i>
                    </li>
                </ul>
            </div>
            <x-services.content-item title="{{ __('home-page/recall_repair.details') }}">
                <i
                    class="fas fa-wrench 2sm:text-[250px] 2md:top-[0.5em] 2md:text-[400px] absolute top-[0.6em] text-[200px] text-[#F4F4F7] md:text-[300px]"
                ></i>
                <i
                    class="fas fa-car 2sm:text-[250px] 2md:top-[0.5em] 2md:text-[400px] absolute right-0 bottom-0 text-[200px] text-[#F4F4F7] md:top-[0.6em] md:bottom-auto md:text-[300px]"
                ></i>
                <div class="2sm:mt-15 2md:mt-24 relative z-5 text-center md:mt-18">
                    <img
                        src="{{ asset($isJa ? 'images/services/recall_title.png' : 'images/services/recall_title_en.png') }}"
                        alt=""
                        class="2sm:w-4/5 mx-auto w-[95%] md:w-3/5"
                    />
                </div>
                <div class="flex flex-wrap">
                    @foreach (__('home-page/recall_repair.detail_list') as $item)
                        <dl
                            class="2sm:w-1/2 2sm:p-4 2sm:flex 2sm:flex-col relative z-5 !m-0 flex w-full items-center py-2 md:w-1/4"
                        >
                            <dt
                                class="{{ $isJa ? '2sm:text-17 text-15 h-[9em] w-[9em] rounded-[50%]' : '2sm:text-15 text-13 h-[11em] w-[11em] px-[0.5em]' }} mx-auto flex items-center justify-center bg-gray-100 bg-gradient-to-tr from-[#f6d365] to-[#fda085] text-center font-bold text-white shadow-[2px_2px_2px_#ccc]"
                            >
                                {!! $item['title'] !!}
                            </dt>
                            <dd
                                class="2sm:w-auto {{ $isJa ? '2sm:text-13 2md:text-15 text-sm' : '2sm:text-sm text-13' }} mb-2 w-1/2 p-[1em]"
                            >
                                {!! $item['description'] !!}
                            </dd>
                        </dl>
                    @endforeach
                </div>
            </x-services.content-item>
            <x-services.content-item title="{{ __('home-page/recall_repair.how_to_request') }}">
                <div
                    class="2sm:mt-15 text-15 flex flex-col flex-wrap items-center justify-around px-[1em] md:mt-18 md:flex-row md:flex-nowrap"
                >
                    <div class="2sm:w-1/2 relative w-4/5 md:w-[35%]">
                        <i
                            class="fas fa-hand-pointer text-50 absolute right-[10%] bottom-0 z-10 text-white text-shadow-black"
                        ></i>
                        <div class="flex flex-wrap justify-between">
                            <div class="text-15 mx-[1em] mb-[1em] flex w-full bg-red-500 md:text-base">
                                <a
                                    href="{{ $urlOld }}/hn/?lan={{ $locale }}"
                                    target="_blank"
                                    class="flex w-full flex-col items-center justify-center p-[1em]"
                                >
                                    <i class="fas fa-sign-in-alt text-40 mb-[0.1em] text-white"></i>
                                    <p class="2sm:text-base text-xs font-bold text-white">
                                        {!! __('common.login_hubnet') !!}
                                    </p>
                                </a>
                            </div>
                        </div>
                    </div>
                    <i
                        class="fas fa-long-arrow-alt-right text-40 md:text-60 my-[0.3em] rotate-90 text-orange-400 md:rotate-0"
                    ></i>
                    <div class="2sm:w-1/2 relative w-4/5 md:w-[35%]">
                        <i
                            class="fas fa-hand-pointer text-50 absolute right-[10%] bottom-0 z-10 text-white text-shadow-black"
                        ></i>
                        <div class="flex flex-wrap justify-between">
                            <div class="text-15 shadow-gray-150 mx-[1em] mb-[1em] flex w-full bg-white">
                                <a
                                    href="{{ $urlOld }}/hn/load/sup_lump.asp?scd=4{{ $locale === 'ja' ? '' : '&lan=en' }}"
                                    class="relative block w-full"
                                    target="_blank"
                                >
                                    <div
                                        class="absolute top-0 left-0 h-[30px] w-[20px]"
                                        style="
                                            background: url('{{ asset('images/services/label_style08.png') }}')
                                                no-repeat;
                                            background-size: 20px 30px;
                                        "
                                    ></div>
                                    <img
                                        src="{{ asset('images/services/icon_menu_08.png') }}"
                                        alt="追加サポートサービス"
                                        class="mx-auto mt-[10px] w-15"
                                    />
                                    <p class="text-red-450 mb-4 text-center text-xs font-bold md:text-base">
                                        {!! __('home-page/recall_repair.support_service_title') !!}
                                    </p>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="ml-auto w-full text-center md:w-[48%]">
                    <i class="fas fa-long-arrow-alt-down text-40 md:text-60 my-[0.3em] text-orange-400 md:m-0"></i>
                </div>
                <div
                    class="text-15 flex flex-col-reverse items-center px-[1em] text-gray-700 md:flex-row md:justify-around md:py-[1em] md:text-base"
                >
                    <div class="2sm:w-1/2 w-4/5 md:w-[35%]">
                        <img
                            src="{{ asset($isJa ? 'images/services/recall_repair02.png' : 'images/services/recall_repair02_en.png') }}"
                            alt=""
                            class="w-full"
                        />
                        <p class="2md:text-base text-13 text-center md:text-sm">
                            {!! __('home-page/recall_repair.write_recall_repair') !!}
                        </p>
                    </div>
                    <i
                        class="fas fa-long-arrow-alt-left text-40 md:text-60 my-[0.3em] rotate-270 text-orange-400 md:m-0 md:rotate-0"
                    ></i>
                    <div class="2sm:w-1/2 w-4/5 md:w-[35%]">
                        <div class="">
                            <img
                                src="{{ asset($isJa ? 'images/services/recall_repair01.png' : 'images/services/recall_repair01_en.png') }}"
                                alt=""
                                class="w-full"
                            />
                            <p class="2md:text-base text-13 text-center md:text-sm">
                                {!! __('home-page/recall_repair.check_work') !!}
                                <i class="fas fa-check"></i>
                            </p>
                        </div>
                    </div>
                </div>
            </x-services.content-item>
        </div>
        <x-services.service-options
            :description="__('home-page/recall_repair.hubnet_order')"
            iconRight="icon_menu_08.png"
            :titleRight="__('home-page/recall_repair.support_service')"
            :contentRight="__('home-page/recall_repair.support_service_content')"
            linkRight="{!! $urlOld . '/hn/load/sup_lump.asp?scd=4&lan='.$locale !!}"
        />
    </x-services.container>
@endsection
