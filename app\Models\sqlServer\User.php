<?php

declare(strict_types=1);

namespace App\Models\sqlServer;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Enums\UserRole;
use App\Models\sqlServer\Trait\User\FilterTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use FilterTrait;
    use HasFactory;
    use Notifiable;

    public const CREATED_AT = 'cre_date';

    public const UPDATED_AT = 'up_date';

    protected $table = 'm_customer';

    protected $fillable = [
        'idx',
        'id',
        'pwd',
        'login_flg',
        'id_AH',
        'id_HE',
        'id_AP',
        'id_EA',
        'id_AH_s',
        'id_HE_s',
        'id_AP_s',
        'id_EA_s',
        'cus_sbt',
        'bus_type',
        'cus_Name_JP',
        'cus_Name_kana',
        'cus_Name_EN',
        'tel',
        'fax',
        'zipcd',
        'address',
        'address2',
        'address3',
        'map_tag',
        'city_town',
        'country',
        'destination',
        'rprsnt_prsn',
        'rprsnt_prsn_kana',
        'sales_prsn_id',
        'sales_prsn_name',
        'sales_prsn_kana',
        'sales_prsn_email',
        'shipping_prsn_id',
        'shipping_prsn_name',
        'shipping_prsn_kana',
        'shipping_prsn_email',
        'shipping_prsn_tel',
        'admin_prsn',
        'admin_prsn_kana',
        'admin_email',
        'admin_tel',
        'admin_position',
        'cus_email',
        'imp_exp_cd',
        'hp_url',
        'web_hope_flg',
        'login_date',
        'exchange_flg',
        'cre_date',
        'up_date',
        'dl_date',
        'up_owner',
        'del_flg',
        'send_flg',
        'hn_cd',
        't_worker_id',
        'note',
        'gph_flg',
        'ah_sales_id',
        'ah_sales_name',
        'language',
        'ah_out_sales_id',
        'ah_out_sales_name',
        'type_note',
        'ex_year',
        'sales_plan',
        'buy_plan',
        'sales_vol',
        'ex_vol',
        'note_srv',
        'note_purp',
        'wreq_id',
        'get_sum_gifts',
        'get_sum_gifts_y',
        'get_win_gifts',
        'get_win_gifts_y',
        'get_newyear_card',
        'get_newyear_card_y',
        'send_sum_gifts',
        'send_sum_gifts_y',
        'send_sum_gifts_cnt',
        'send_sum_gifts_y_cnt',
        'send_win_gifts',
        'send_win_gifts_y',
        'send_win_gifts_cnt',
        'send_win_gifts_y_cnt',
        'send_newyear_card',
        'send_newyear_card_y',
        'get_sum_contents',
        'get_win_contents',
        'gifts_last_update',
        'corp_no',
        'kobutsu_sho',
        'notification_email',
        'nzta_mail_flg',
        'nzta_mail_addr',
        'survey_mail_flg',
        'survey_mail_addr',
        'delivery_mail_flg',
        'delivery_mail_addr',
        'document_mail_flg',
        'document_mail_addr',
        'photo_mail_flg',
        'photo_mail_addr',
        'precon_mail_flg',
        'precon_mail_addr',
        'ch_mail_prsn_name3',
        'ch_mail_prsn_kana3',
        'ch_mail_prsn_email3',
        'ch_mail_prsn_tel3',
        'ch_mail_prsn_name4',
        'ch_mail_prsn_kana4',
        'ch_mail_prsn_email4',
        'ch_mail_prsn_tel4',
        'ch_mail_prsn_name5',
        'ch_mail_prsn_kana5',
        'ch_mail_prsn_email5',
        'ch_mail_prsn_tel5',
        'ch_mail_prsn_name6',
        'ch_mail_prsn_kana6',
        'ch_mail_prsn_email6',
        'ch_mail_prsn_tel6',
        'ch_mail_prsn_name7',
        'ch_mail_prsn_kana7',
        'ch_mail_prsn_email7',
        'ch_mail_prsn_tel7',
        'ch_mail_prsn_name8',
        'ch_mail_prsn_kana8',
        'ch_mail_prsn_email8',
        'ch_mail_prsn_tel8',
        'ch_mail_prsn_name9',
        'ch_mail_prsn_kana9',
        'ch_mail_prsn_email9',
        'ch_mail_prsn_tel9',
        'ch_mail_prsn_name10',
        'ch_mail_prsn_kana10',
        'ch_mail_prsn_email10',
        'ch_mail_prsn_tel10',
        'ch_trans_mail_flg1',
        'ch_trans_cancel_mail_flg1',
        'ch_shipping_mail_flg1',
        'ch_photo_mail_flg1',
        'ch_precon_mail_flg1',
        'ch_survey_mail_flg1',
        'ch_nzta_mail_flg1',
        'ch_delivery_mail_flg1',
        'ch_document_mail_flg1',
        'ch_shipping_cover_mail_flg1',
        'ch_trans_mail_flg2',
        'ch_trans_cancel_mail_flg2',
        'ch_shipping_mail_flg2',
        'ch_photo_mail_flg2',
        'ch_precon_mail_flg2',
        'ch_survey_mail_flg2',
        'ch_nzta_mail_flg2',
        'ch_delivery_mail_flg2',
        'ch_document_mail_flg2',
        'ch_shipping_cover_mail_flg2',
        'ch_trans_mail_flg3',
        'ch_trans_cancel_mail_flg3',
        'ch_shipping_mail_flg3',
        'ch_photo_mail_flg3',
        'ch_precon_mail_flg3',
        'ch_survey_mail_flg3',
        'ch_nzta_mail_flg3',
        'ch_delivery_mail_flg3',
        'ch_document_mail_flg3',
        'ch_shipping_cover_mail_flg3',
        'ch_trans_mail_flg4',
        'ch_trans_cancel_mail_flg4',
        'ch_shipping_mail_flg4',
        'ch_photo_mail_flg4',
        'ch_precon_mail_flg4',
        'ch_survey_mail_flg4',
        'ch_nzta_mail_flg4',
        'ch_delivery_mail_flg4',
        'ch_document_mail_flg4',
        'ch_shipping_cover_mail_flg4',
        'ch_trans_mail_flg5',
        'ch_trans_cancel_mail_flg5',
        'ch_shipping_mail_flg5',
        'ch_photo_mail_flg5',
        'ch_precon_mail_flg5',
        'ch_survey_mail_flg5',
        'ch_nzta_mail_flg5',
        'ch_delivery_mail_flg5',
        'ch_document_mail_flg5',
        'ch_shipping_cover_mail_flg5',
        'ch_trans_mail_flg6',
        'ch_trans_cancel_mail_flg6',
        'ch_shipping_mail_flg6',
        'ch_photo_mail_flg6',
        'ch_precon_mail_flg6',
        'ch_survey_mail_flg6',
        'ch_nzta_mail_flg6',
        'ch_delivery_mail_flg6',
        'ch_document_mail_flg6',
        'ch_shipping_cover_mail_flg6',
        'ch_trans_mail_flg7',
        'ch_trans_cancel_mail_flg7',
        'ch_shipping_mail_flg7',
        'ch_photo_mail_flg7',
        'ch_precon_mail_flg7',
        'ch_survey_mail_flg7',
        'ch_nzta_mail_flg7',
        'ch_delivery_mail_flg7',
        'ch_document_mail_flg7',
        'ch_shipping_cover_mail_flg7',
        'ch_trans_mail_flg8',
        'ch_trans_cancel_mail_flg8',
        'ch_shipping_mail_flg8',
        'ch_photo_mail_flg8',
        'ch_precon_mail_flg8',
        'ch_survey_mail_flg8',
        'ch_nzta_mail_flg8',
        'ch_delivery_mail_flg8',
        'ch_document_mail_flg8',
        'ch_shipping_cover_mail_flg8',
        'ch_trans_mail_flg9',
        'ch_trans_cancel_mail_flg9',
        'ch_shipping_mail_flg9',
        'ch_photo_mail_flg9',
        'ch_precon_mail_flg9',
        'ch_survey_mail_flg9',
        'ch_nzta_mail_flg9',
        'ch_delivery_mail_flg9',
        'ch_document_mail_flg9',
        'ch_shipping_cover_mail_flg9',
        'ch_trans_mail_flg10',
        'ch_trans_cancel_mail_flg10',
        'ch_shipping_mail_flg10',
        'ch_photo_mail_flg10',
        'ch_precon_mail_flg10',
        'ch_survey_mail_flg10',
        'ch_nzta_mail_flg10',
        'ch_delivery_mail_flg10',
        'ch_document_mail_flg10',
        'ch_shipping_cover_mail_flg10',
        'ah_trans_estimate_mail_flg',
        'ah_trans_mail_flg',
        'ah_plate_cut_mail_flg',
        'ah_trans_cancel_mail_flg',
        'ah_shipping_mail_flg',
        'ah_document_mail_flg',
        'ah_cs_document_mail_flg',
        'ah_invoice_mail_flg',
        'ah_nzta_mail_flg',
        'ah_delivery_mail_flg',
        'ah_survey_mail_flg',
        'ah_precon_mail_flg',
        'ah_photo_mail_flg',
        'ah_shipping_cover_mail_flg',
        'bk_trans_mail_flg',
        'bk_trans_note_mail_flg',
        'bk_trans_price_mail_flg',
        'bk_trans_plate_mail_flg',
        'bk_trans_file_mail_flg',
        'bk_trans_cancel_mail_flg',
        'ah_send_mailaddress',
        'ah_sales_mail_flg',
        'ch_shipping_cancel_mail_flg1',
        'ch_shipping_cancel_mail_flg2',
        'ch_shipping_cancel_mail_flg3',
        'ch_shipping_cancel_mail_flg4',
        'ch_shipping_cancel_mail_flg5',
        'ch_shipping_cancel_mail_flg6',
        'ch_shipping_cancel_mail_flg7',
        'ch_shipping_cancel_mail_flg8',
        'ch_shipping_cancel_mail_flg9',
        'ch_shipping_cancel_mail_flg10',
        'ah_shipping_cancel_mail_flg',
        'ch_freight_invoice_mail_flg1',
        'ch_freight_invoice_mail_flg2',
        'ch_freight_invoice_mail_flg3',
        'ch_freight_invoice_mail_flg4',
        'ch_freight_invoice_mail_flg5',
        'ch_freight_invoice_mail_flg6',
        'ch_freight_invoice_mail_flg7',
        'ch_freight_invoice_mail_flg8',
        'ch_freight_invoice_mail_flg9',
        'ch_freight_invoice_mail_flg10',
        'ah_freight_invoice_mail_flg',
        'ch_rakupochi_mail_flg1',
        'ch_rakupochi_mail_flg2',
        'ch_rakupochi_mail_flg3',
        'ch_rakupochi_mail_flg4',
        'ch_rakupochi_mail_flg5',
        'ch_rakupochi_mail_flg6',
        'ch_rakupochi_mail_flg7',
        'ch_rakupochi_mail_flg8',
        'ch_rakupochi_mail_flg9',
        'ch_rakupochi_mail_flg10',
        'ah_rakupochi_mail_flg',
        'ch_pay_each_time_flg',
        'ch_deposit_flg',
        'bk_trans_onhold_mail_flg',
        'ch_monthly_invoice_mail_flg1',
        'ch_monthly_invoice_mail_flg2',
        'ch_monthly_invoice_mail_flg3',
        'ch_monthly_invoice_mail_flg4',
        'ch_monthly_invoice_mail_flg5',
        'ch_monthly_invoice_mail_flg6',
        'ch_monthly_invoice_mail_flg7',
        'ch_monthly_invoice_mail_flg8',
        'ch_monthly_invoice_mail_flg9',
        'ch_monthly_invoice_mail_flg10',
        'ah_monthly_invoice_mail_flg',
        'pas_flg',
        'ch_consignee_mail_flg',
        'ch_tsudo_invoice_mail_flg1',
        'ch_tsudo_invoice_mail_flg2',
        'ch_tsudo_invoice_mail_flg3',
        'ch_tsudo_invoice_mail_flg4',
        'ch_tsudo_invoice_mail_flg5',
        'ch_tsudo_invoice_mail_flg6',
        'ch_tsudo_invoice_mail_flg7',
        'ch_tsudo_invoice_mail_flg8',
        'ch_tsudo_invoice_mail_flg9',
        'ch_tsudo_invoice_mail_flg10',
        'ah_tsudo_invoice_mail_flg',
        'ch_consignee_mail_flg1',
        'ch_consignee_mail_flg2',
        'ch_consignee_mail_flg3',
        'ch_consignee_mail_flg4',
        'ch_consignee_mail_flg5',
        'ch_consignee_mail_flg6',
        'ch_consignee_mail_flg7',
        'ch_consignee_mail_flg8',
        'ch_consignee_mail_flg9',
        'ch_consignee_mail_flg10',
        'ah_container_sales_id',
        'ah_container_sales_name',
        'auto_action_flg',
        'plate_shipping_name',
        'plate_shipping_zipcd',
        'plate_shipping_address',
        'plate_shipping_tel',
        'ar_account_status',
        'ch_ein_ssn',
        'ch_dob',
        'reason_found',
    ];

    protected $casts = [
        'role' => UserRole::class,
        'idx' => 'integer',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'pwd',
    ];
}
