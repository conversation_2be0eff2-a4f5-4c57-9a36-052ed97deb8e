<?php

declare(strict_types=1);

namespace App\Services\CheckingRequest;

use App\Constants\ApiCodes;
use App\Enums\ActionLogPageName;
use App\Enums\CheckingRequest\CsvHeaders;
use App\Enums\CheckingRequest\Statuses;
use App\Exceptions\ApiException;
use App\Jobs\StoreActionLogJob;
use App\Repositories\sqlServer\CheckingRequestRepository;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\StreamedResponse;

class ExportCsvCheckingRequestService
{
    private CheckingRequestRepository $checkingRequestRepository;

    public function __construct(CheckingRequestRepository $checkingRequestRepository)
    {
        $this->checkingRequestRepository = $checkingRequestRepository;
    }

    /**
     * @throws ApiException
     */
    public function call(array $params): StreamedResponse
    {
        $token = $params['token'];
        $userId = $this->checkToken($token);

        $timestamp = Carbon::now()->format('YmdHis');
        $filename = "shitami{$timestamp}.txt";

        $headers = [
            'Content-Type' => 'text/plain; charset=UTF-8',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0'
        ];

        $callback = function () use ($params): void {
            $relations = ['customer', 'aaDate.aaPlace', 'inspector'];
            $file = fopen('php://output', 'w');
            fwrite($file, "\xEF\xBB\xBF");

            $this->writeCsvHeader($file);

            $this->checkingRequestRepository->builderQueryExportCSV($params, $relations)
                ->chunk(500, function ($checkingRequests) use ($file): void {
                    $this->processCsvChunk($checkingRequests, $file);
                });

            fclose($file);
        };

        $this->writeLogAction((int)$userId);

        return new StreamedResponse($callback, 200, $headers);
    }

    /**
     * @throws ApiException
     */
    private function checkToken(string $token): string
    {
        $cacheData = Cache::store('database')->pull($token);

        if (!$cacheData) {
            throw new ApiException((string)ApiCodes::INVALID_TOKEN);
        }

        return (string)$cacheData;
    }

    private function writeCsvHeader($file): void
    {
        $headers = CsvHeaders::getAllHeaders();
        fputcsv($file, $headers, "\t");
    }

    private function processCsvChunk(Collection $checkingRequests, $file): void
    {
        foreach ($checkingRequests as $checkingRequest) {
            $row = $this->mapToCsvRow($checkingRequest);
            fputcsv($file, $row, "\t");
        }
    }

    private function mapToCsvRow($checkingRequest): array
    {
        return [
            $checkingRequest->id,
            $checkingRequest->aaDate?->open_date ? Carbon::parse($checkingRequest->aaDate?->open_date)->format('n/j/Y') : '',
            $checkingRequest->aaDate?->aaPlace?->name ?? '',
            $checkingRequest->customer?->cus_Name_JP ?? '',
            $checkingRequest->odr_person ?? '',
            $checkingRequest->odr_tel ?? '',
            $checkingRequest->odr_mail ?? '',
            $checkingRequest->odr_date ? Carbon::parse($checkingRequest->odr_date)->format('n/j/Y g:i:s A') : '',
            $checkingRequest->aa_no ?? '',
            $checkingRequest->car_name ?? '',
            $checkingRequest->note ?? '',
            $checkingRequest->cc_note ?? '',
            $this->flagReplace($checkingRequest->photo_flg),
            $this->flagReplace($checkingRequest->optn1_flg),
            $this->flagReplace($checkingRequest->optn2_flg),
            $this->getStatusText($checkingRequest->st_cd),
            $checkingRequest->inspector?->name ?? '',
            $checkingRequest->check_req_date ? Carbon::parse($checkingRequest->check_req_date)->format('n/j/Y g:i:s A') : ''
        ];
    }

    private function flagReplace($flag): string
    {
        return $flag ? 'あり' : 'なし';
    }

    private function getStatusText($statusCode): string
    {
        try {
            $status = Statuses::from($statusCode);
            return $status->toJapaneseLabel();
        } catch (Exception $e) {
            return '';
        }
    }

    private function writeLogAction(int $userId): void
    {
        try {
            dispatch(new StoreActionLogJob(
                pageName: ActionLogPageName::CHECKING_REQUEST->value,
                dateTime: now(),
                userId: $userId,
                ipAddress: request()->ip()
            ));
        } catch (Exception $e) {
            Log::error('ERROR WRITE LOG CSV CHECKING REQUEST:::' . $e->getMessage());
        }
    }
}
