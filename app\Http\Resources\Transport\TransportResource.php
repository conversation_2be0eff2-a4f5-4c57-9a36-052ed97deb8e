<?php

declare(strict_types=1);

namespace App\Http\Resources\Transport;

use App\Enums\Transport\TransIdDefault;
use App\Traits\CommonTrait;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TransportResource extends JsonResource
{
    use CommonTrait;
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $placeCutFlgDefault = [1, 3];
        $d2dKbnDefault = 2;
        return [
            'id' => $this->id,
            'tp_id' => !in_array($this->m_trans_id, [TransIdDefault::LOGICO->value, TransIdDefault::DREAM_INTERNATIONAL->value])
                ? $this->tp_id : null,
            'transport_id' => $this->id,
            'st_cd' => $this->st_cd,
            'common_value' => $this->commonInspection?->value1, // site admin default value1 because language default jp
            'customer_name_jp' => e($this->cus_name_JP),
            'customer_id' => $this->m_customer_id,
            'pos_no' => $this->pos_no,
            'aa_no' => $this->aa_no,
            'fr_name' => $this->fr_name,
            'car_name' => $this->car_name,
            'car_no' => $this->car_no,
            'deli_price' => $this->deli_price,
            'deli_price_format' => $this->deli_price ? $this->formatPrice($this->deli_price) . '円' : '0円',
            'sales_price' => $this->sales_price,
            'sales_price_format' => $this->sales_price ? $this->formatPrice($this->sales_price) . '円' : '0円',
            'deli_price' => $this->deli_price,
            'deli_price_format' => $this->deli_price ? $this->formatPrice($this->deli_price) . '円' : '0円',
            'm_trans_name' => $this->getMTransName($this->m_trans_id),
            'm_trans_id' => $this->m_trans_id,
            'd2d_kbn' => $this->d2d_kbn,
            'd2d_kbn_txt' => $d2dKbnDefault == $this->d2d_kbn ? 'YES' : '',
            'plate_cut_flg' => $this->plate_cut_flg,
            'plate_cut_flg_txt' => in_array($this->plate_cut_flg, $placeCutFlgDefault) ? '有' : '',
            'plate_cut_fin' => $this->plate_cut_fin,
            'plate_send_date' => $this->plate_send_date ? Carbon::parse($this->plate_send_date)->format('Y-m-d H:i:s') : null,
            'name1' => $this->name1,
            'name2' => $this->name2,
            'name3' => $this->name3,
            'note1' => $this->note1,
            'note2' => $this->note2,
            'note3' => $this->note3,
            'note_date1' => $this->notedate1 ? Carbon::parse($this->notedate1)->format('Y-m-d H:i:s') : null,
            'note_date2' => $this->notedate2 ? Carbon::parse($this->notedate2)->format('Y-m-d H:i:s') : null,
            'note_date3' => $this->notedate3 ? Carbon::parse($this->notedate3)->format('Y-m-d H:i:s') : null,
            'odr_date' => $this->odr_date ? Carbon::parse($this->odr_date)->format('Y-m-d H:i:s') : null,
            'from_plan_date' => $this->from_plan_date ? Carbon::parse($this->from_plan_date)->format('Y-m-d H:i:s') : null,
            'to_plan_date' => $this->to_plan_date ? Carbon::parse($this->to_plan_date)->format('Y-m-d H:i:s') : null,
            'to_date' => $this->to_date ? Carbon::parse($this->to_date)->format('Y-m-d H:i:s') : null,
            'to_tel' => trim($this->to_tel),
            'car_no' => $this->car_no,
            'fr_name' => $this->fr_name,
            'fr_addr' => $this->fr_addr,
            'fr_tel' => trim($this->fr_tel),
            'to_addr' => $this->to_addr,
            'ref_no' => $this->ref_no,
            'country' => $this->country,
            'country_free' => $this->country_free,
            'country_cd' => $this->country_cd,
            'service_name' => $this->service_name,
            'vessel_voy' => $this->vessel_voy,
            'etd' => $this->etd,
            'eta' => $this->eta,
            'fin_ship' => $this->fin_ship,
            'to_name' => $this->to_name,
            'luxury_flg' => $this->luxury_flg,
            'luxury_flg_insrance' => $this->luxury_flg_insrance,
            'other_flg' => $this->other_flg,
            'other_flg_txt' => $this->other_flg_txt,
            'fr_aa_place_id' => $this->fr_aa_place_id,
            'odr_customer_id' => $this->odr_customer_id,
            'to_aa_place_id' => $this->to_aa_place_id,
            'to_etc_place_id' => $this->to_etc_place_id,
            'to_place_id' => $this->to_place_id,
            'pos_no' => $this->pos_no,
            'aa_no' => $this->aa_no,
            'car_name' => $this->car_name,
            'car_no' => $this->car_no,
            'country' => $this->country,
            'plate_cut_flg' => $this->plate_cut_flg,
            'plate_no' => $this->plate_no,
            'note' => $this->note,
            'optn1' => $this->optn1,
            'date_of_payment' => $this->date_of_payment ? Carbon::parse($this->date_of_payment)->format('Y-m-d H:i:s') : null,
            'port_cd' => $this->port_cd,
            'port' => $this->port,
            'country_cd' => $this->country_cd,
            'country_area' => $this->country_area,
            'country_free' => $this->country_free,
            'auction_date' => $this->auction_date ? Carbon::parse($this->auction_date)->format('Y-m-d H:i:s') : null,
            'auction_chk' => $this->auction_chk,
            'auction_txt' => $this->auction_txt,
            'auc_name' => $this->auc_name,
            'auc_addr' => $this->auc_addr,
            'auc_tel' => $this->auc_tel,
            'pos_chk' => $this->pos_chk,
            'tick_no_flg' => $this->tick_no_flg,
            'plate_send_name' => $this->plate_send_name,
            'plate_send_zipcd' => $this->plate_send_zipcd,
            'plate_send_address' => $this->plate_send_address,
            'plate_send_tel' => $this->plate_send_tel,
            'narikiri_trans_flg' => $this->narikiri_trans_flg,
            'autohub_note' => $this->autohub_note,
            'agent_ref_no' => $this->agent_ref_no,
        ];
    }

    private function getMTransName(mixed $mTransId): string | null
    {
        return match ($mTransId) {
            TransIdDefault::LOGICO->value => 'ロジコ',
            TransIdDefault::DREAM_INTERNATIONAL->value => 'ドリームインターナショナル',
            TransIdDefault::SHIPPING_EAST_AND_WEST->value => '東西海運',
            TransIdDefault::TARGET->value => 'キャリーゴール',
            TransIdDefault::EIKO_SHOUNEN->value => '栄港商運',
            TransIdDefault::J_BRING->value => 'ジェイキャリー',
            default => null,
        };
    }
}
