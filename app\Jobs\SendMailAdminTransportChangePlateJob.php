<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Mail\AdminTransportChangePlateMail;
use App\Traits\MailTrait;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Mail;

class SendMailAdminTransportChangePlateJob implements ShouldQueue
{
    use MailTrait;
    use Queueable;

    private array $data;
    private string $mailAddress;

    /**
     * Create a new job instance.
     */
    public function __construct(string $mailAddress, array $data)
    {
        $this->data = $data;
        $this->mailAddress = $mailAddress;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if (!$this->checkMailValid($this->mailAddress)) {
            return;
        }

        Mail::to($this->mailAddress)
            ->send(new AdminTransportChangePlateMail($this->data));
    }
}
