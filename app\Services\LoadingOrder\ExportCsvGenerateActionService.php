<?php

declare(strict_types=1);

namespace App\Services\LoadingOrder;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class ExportCsvGenerateActionService
{
    public function call(): string
    {
        $nonce = Str::uuid()->toString();
        Cache::store('database')->put($nonce, [
            'created_at' => now()->toString(),
            'user_id' => Auth::user()->id,
        ]);

        return $nonce;
    }
}
