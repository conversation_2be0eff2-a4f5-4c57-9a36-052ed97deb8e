<?php

declare(strict_types=1);

namespace App\Repositories\common;

class WhereCondition
{
    private bool $withTrashed;
    private string $keyColumn;
    private mixed $value;
    private string $operator;

    public function __construct(string $keyColumn, mixed $value, bool $withTrashed, string $operator)
    {
        $this->withTrashed = $withTrashed;
        $this->keyColumn = $keyColumn;
        $this->value = $value;
        $this->operator = $operator;
    }

    public function getWithTrashed(): bool
    {
        return $this->withTrashed;
    }

    public function getKeyColumn(): string
    {
        return $this->keyColumn;
    }

    public function getValue(): mixed
    {
        return $this->value;
    }

    public function getOperator(): string
    {
        return $this->operator;
    }
}
