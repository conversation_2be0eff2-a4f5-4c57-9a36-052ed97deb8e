<?php

declare(strict_types=1);

namespace App\Models\sqlServer;

use App\Models\sqlServer\Trait\Area\FilterTrait;
use Illuminate\Database\Eloquent\Model;

/**
 * Inspector Model
 *
 * Handles list of inspectors/inspection staff
 * Maps to database table: t_inspector
 */
class Area extends Model
{
    use FilterTrait;
    public $timestamps = false;
    protected $fillable = [
        'id',
        'pcd',
        'port',
        'cd',
        'country',
        'acd',
        'area',
        'del_flg',
        'area_en',
    ];
    protected $table = 'm_area';
    protected $casts = [
        'del_flg' => 'boolean',
    ];
}
