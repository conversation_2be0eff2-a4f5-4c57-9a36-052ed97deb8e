.common-tab:after,
.common-tab:before {
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    content: '';
    height: 100%;
    position: absolute;
    width: 100%;
}

.common-tab:before {
    background-color: rgba(255, 255, 255, 0.5);
    left: 0;
    top: 0;
    z-index: -1;
}

.common-tab:after {
    background: linear-gradient(-135deg, #dddfe0, #eee, #fff, #fff, #eee, #dddfe0);
    top: 5px;
    left: 5px;
    z-index: -2;
}

.common-tab {
    background-color: #fff;
    position: relative;
    z-index: 1;
    color: #444;
    border-radius: 5px;
}


.rocker {
    display: inline-block;
    position: relative;
    font-size: 1.2em;
    font-weight: bold;
    text-align: center;
    text-transform: uppercase;
    color: #888;
    width: 13em;
    height: 4em;
    border-bottom: 0.5em solid #eee;
}

.rocker::before {
    content: "";
    position: absolute;
    top: 0.5em;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #999;
    border: 0.5em solid #eee;
    border-bottom: 0;
}

.switch-left {
    height: 2.4em;
    width: 5.75em;
    left: 0.85em;
    bottom: 0.8em;
    background-color: #ddd;
    transform: rotate(15deg) skewX(15deg);
}
.switch-left, .switch-right {
    cursor: pointer;
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 2.5em;
    width: 6em;
    transition: 0.2s;
}

.switch-right {
    right: 0.5em;
    bottom: 0;
    background-color: #bd5757;
    color: #fff;
}


.recruit_main_title {
    margin-top: 70px;
}

.switch-right {
    line-height: 0.9em;
}

.switch-right .recruit_switch_right {
    transform: scale(0.6, 1);
}


.rocker {
    display: inline-block;
    position: relative;
    font-size: 1.2em;
    font-weight: bold;
    text-align: center;
    text-transform: uppercase;
    color: #888;
    width: 13em;
    height: 4em;
    border-bottom: 0.5em solid #eee;
}
.rocker::before {
    content: "";
    position: absolute;
    top: 0.5em;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #999;
    border: 0.5em solid #eee;
    border-bottom: 0;
}
.rocker input {
    opacity: 0;
    width: 0;
    height: 0;
}
.switch-left, .switch-right {
    cursor: pointer;
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 2.5em;
    width: 6em;
    transition: 0.2s;
}
.switch-left {
    height: 2.4em;
    width: 5.75em;
    left: 0.85em;
    bottom: 0.8em;
    background-color: #ddd;
    transform: rotate(15deg) skewX(15deg);
}
.switch-right {
    right: 0.5em;
    bottom: 0;
    background-color: #bd5757;
    color: #fff;
}
.switch-left::before,.switch-right::before {
    content: "";
    position: absolute;
    width: 0.4em;
    height: 2.45em;
    bottom: -0.45em;
    background-color: #ccc;
    transform: skewY(-65deg);
}
.switch-left::before {
    left: -0.4em;
}
.switch-right::before {
    right: -0.375em;
    background-color: transparent;
    transform: skewY(65deg);
}
input:checked + .switch-left {
    background-color: #0084d0;
    color: #fff;
    bottom: 0px;
    left: 0.5em;
    height: 2.5em;
    width: 6em;
    transform: rotate(0deg) skewX(0deg);
}
input:checked + .switch-left::before {
    background-color: transparent;
    width: 3.0833em;
}
input:checked + .switch-left + .switch-right {
    background-color: #ddd;
    color: #888;
    bottom: 0.8em;
    right: 0.8em;
    height: 2.4em;
    width: 5.75em;
    transform: rotate(-15deg) skewX(-15deg);
}
input:checked + .switch-left + .switch-right::before {
    background-color: #ccc;
}
input:focus + .switch-left {
    color: #333;
}
input:checked:focus + .switch-left {
    color: #fff;
}
input:focus + .switch-left + .switch-right {
    color: #fff;
}
input:checked:focus + .switch-left + .switch-right {
    color: #333;
}

@media screen and (max-width: 767px) {
    .rocker {
        transform: scale(0.7);
    }
}
