document.addEventListener('DOMContentLoaded', function() {
    const baseOffsets = [102, 455, 425, 590, 240, 1215, 715, 1415, 960, 819, 1215, 1715, 1715, 695, 1495, 1145, 727, 1115, 1415, 1215, 170, 1515, 1245, 1645, 265];
    const scrollFactors = [0.5, 0.6, 0.3, 0.7, 0.7, 0.3, 0.7, 0.7, 0.3, 0.5, 0.3, 0.5, 0.6, 0.7, 0.5, 0.7, 0.5, 0.7, 0.6, 0.3, 0.6, 0.6, 0.7, 0.7, 0.7];

    $(window).scroll(function () {
        const scrollTop = $(window).scrollTop();
        let win_w = window.innerWidth;
        let max_top = (win_w >= 1800) ? 2650 : (win_w >= 1200) ? 2650 : (win_w >= 992) ? 2650 : (win_w >= 768) ? 2200 : (win_w >= 576) ? 2650 : 2650;

        for (let i = 0; i < 25; i++) {
            const top = baseOffsets[i] + scrollTop * scrollFactors[i];
            const j = (i + 1 < 10) ? '0' + (i + 1) : (i + 1);
            if (top < max_top) {
                $('#animation' + j).css('top', top).show();
            } else {
                $('#animation' + j).hide();
            }
        }
    });
});
