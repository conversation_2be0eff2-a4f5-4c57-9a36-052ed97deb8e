$(function () {
    /**
     * Handle date selection
     */
    const initDatepicker = () => {
        var lang = $('#lang').val();
        if (lang === 'ja') {
            $.datepicker.regional["ja"] = {
                closeText: "閉じる",
                prevText: "&#x3C;前",
                nextText: "次&#x3E;",
                currentText: "今日",
                monthNames: ["1月", "2月", "3月", "4月", "5月", "6月",
                    "7月", "8月", "9月", "10月", "11月", "12月"],
                monthNamesShort: ["1月", "2月", "3月", "4月", "5月", "6月",
                    "7月", "8月", "9月", "10月", "11月", "12月"],
                dayNames: ["日曜日", "月曜日", "火曜日", "水曜日", "木曜日", "金曜日", "土曜日"],
                dayNamesShort: ["日", "月", "火", "水", "木", "金", "土"],
                dayNamesMin: ["日", "月", "火", "水", "木", "金", "土"],
                weekHeader: "週",
                firstDay: 0,
                isRTL: false,
                showMonthAfterYear: true,
                yearSuffix: "年"
            };
            $.datepicker.setDefaults($.datepicker.regional["ja"]);
        }
        $("#end-date").datepicker({
            dateFormat: lang === 'en' ? 'd M yy' : 'yy-mm-dd',
            minDate: 0,
            onSelect: function () {
                $('#end-date').siblings('.btn-delete').css('display', 'block')
            }
        }).datepicker("setDate",
            getUrlParameter('endDate')
                ? (new Date(getUrlParameter('endDate')) < new Date() ? new Date() : new Date(getUrlParameter('endDate')))
                : ''
        );


        $("#start-date").datepicker({
            dateFormat: lang === 'en' ? 'd M yy' : 'yy-mm-dd',
            minDate: 0,
            onSelect: function (dateString) {
                $('#end-date').datepicker('option', 'minDate', new Date(dateString))
                $('#start-date').siblings('.btn-delete').css('display', 'block')
            }
        }).datepicker("setDate",
            (!getUrlParameter('startDate') || new Date(getUrlParameter('startDate')) < new Date())
                ? new Date()
                : new Date(getUrlParameter('startDate'))
        );
        var urlParams = new URLSearchParams(window.location.search);
        urlParams.get('startDate') ? urlParams.set('startDate', $("#start-date").val()) : '';
        urlParams.get('endDate') ? urlParams.set('endDate', $("#end-date").val()) : '';
        let newUrl = window.location.pathname
        if (urlParams.toString()) {
            newUrl = newUrl + '?' + urlParams.toString();
        }
        window.history.pushState({}, '', newUrl);
    }

    /**
     * Add event click for form search
     * Add input search for field route, pol, pod, country
     */
    const handleSearchForm = () => {
        const selectId = ['country', 'port', 'route', 'pol']
        selectId.map(id => {
            $(`#search-${id}`).on('keyup', function () {
                var value = $(this).val().toLowerCase();
                $(`#select-${id} .dropdown-item`).filter(function () {
                    var itemName = $(this).data('name').toLowerCase();
                    if ($(this).attr('data-initial-display') === '1') {
                        $(this).toggle(itemName.indexOf(value) > -1);
                    }
                });
            });

            const elements = $(`#select-${id} .dropdown-item`);
            if (elements) {
                elements.click(function () {
                    if (id === 'country') {
                        var countryId = $(this).data('code');
                        handleGetPorts(countryId);
                    }
                    if (id === 'route') {
                        var routeId = $(this).data('code');
                        getCountriesAndPorts(routeId);
                    }
                    $(`#select-${id} .input-value`).val($(this).data('name'));
                    $(`#select-${id} #${id}`).val($(this).data('code'));
                    $(`#select-${id} .dropdown-item`).removeClass('dropdown-item-active');
                    $(this).addClass('dropdown-item-active');
                })
            }
        })
    }

    /**
     * Get Condition search by Params
     */
    const getCondidionSearch = () => {
        let formSeach = {
            'route': getUrlParameter('route'),
            'country': getUrlParameter('country'),
            'port': getUrlParameter('port'),
            'pol': getUrlParameter('pol')
        }
        $.each(formSeach, function (index, item) {
            if (item) {
                $(`#select-${index} #${index}`).val(item);
                var name = $(`#select-${index} .dropdown-item[data-code="${item}"]`).data('name');
                $(`#select-${index} .dropdown-item[data-code="${item}"]`).addClass('dropdown-item-active');
                $(`#select-${index} .input-value`).val(name);
            }
        })
    }

    /**
     * Handle scrolling to the schedule result on mobile
     *
     * @param countryId
     */
    const scrollResultSchedule = () => {
        var isResultSchedule = getUrlParameter('route') || getUrlParameter('country')
            || getUrlParameter('port') || getUrlParameter('pol')
            || getUrlParameter('startDate') || getUrlParameter('endDate');
        var deviceWidth = $(window).width();
        var select = $('.schedule-result_details');
        if (select.length > 0 && isResultSchedule && deviceWidth < 768) {
            var targetOffset = select.offset().top - 80;
            $('html, body').animate({
                scrollTop: targetOffset
            }, 1000);
        }
    }

    /**
     * Handle layout display for schedule results
     */
    const handleDisplayScheduleSailing = () => {
        $('.schedule-result').not(":has(.schedule-result_details #result-schedule-no-data)").css({
            transform: 'scale(1)',
            opacity: '1'
        });
        var elements = $('.sailing-schedule-result');
        var colElements = $('.schedule-result_details');
        var deviceWidth = $(window).width();
        var cols = [];
        var columns = 1;

        if (deviceWidth >= 1650) {
            columns = 5;
            for (let i = 1; i <= columns; i++) {
                cols[i] = ($('<div>', {class: 'custom-col'}));
            }
        } else if (deviceWidth >= 1400) {
            columns = 4;
            for (let i = 1; i <= columns; i++) {
                cols[i] = ($('<div>', {class: 'w-1/4 px-4'}));
            }
        } else if (deviceWidth >= 1024) {
            columns = 3;
            for (let i = 1; i <= columns; i++) {
                cols[i] = ($('<div>', {class: 'w-1/3 px-4'}));
            }
        } else if (deviceWidth > 768) {
            columns = 2;
            for (let i = 1; i <= columns; i++) {
                cols[i] = ($('<div>', {class: 'w-1/2 px-4'}));
            }
        } else {
            columns = 1;
            cols[1] = ($('<div>', {class: 'w-full px-4'}));
        }

        for (let i = 0; i < elements.length; i++) {
            for (let j = 0; j < columns; j++) {
                if (i % columns !== j) continue;
                else {
                    cols[j + 1].append(elements[i]);
                    break;
                }
            }
        }
        cols.forEach(function (col) {
            colElements.append(col);
        });
    }

    /**
     * Get params
     *
     * @param name
     * @returns {string}
     */
    const getUrlParameter = (name) => {
        var urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    }

    /**
     * Handle hover route for Map
     */
    const handleHoverRoute = () => {
        var route = getUrlParameter('route');
        $(`#select-${route}`).addClass('area-active')
    }

    /**
     * handle get port by country
     * @param countryId
     */
    const handleGetPorts = (countryId) => {
        if (countryId) {
            const data = filterOptionsByCondition('country_code', countryId);
            $(`#select-port .input-value`).val('');
            $(`#select-port #port`).val('');
            if (data) {
                handleDataInitialDisplay($(`#select-port .dropdown_list-item .dropdown-item`), '0');
                $.each(data, function (index, value) {
                    handleDataInitialDisplay($(`#select-port .dropdown_list-item .dropdown-item[data-code=${value['code']}]`), '1');
                });
            }
        }
    }
    /**
     * Handle get countries and ports by route
     * @param routeId
     */
    const getCountriesAndPorts = (routeId) => {
        if (routeId) {
            const data = filterOptionsByCondition('route_code', routeId);
            $(`#select-port .input-value`).val('');
            $(`#select-port #port`).val('');
            $(`#select-country .input-value`).val('');
            $(`#select-country #country`).val('');
            if (data) {
                handleDataInitialDisplay($(`#select-port .dropdown_list-item .dropdown-item`), '0');
                $.each(data, function (index, value) {
                    handleDataInitialDisplay($(`#select-port .dropdown_list-item .dropdown-item[data-code=${value['code']}]`), '1');
                });

                const $arrCountry = [...new Map(data.map(item => [item.country_code, item])).values()];
                if ($arrCountry) {
                    handleDataInitialDisplay($(`#select-country .dropdown_list-item .dropdown-item`), '0');
                    $.each($arrCountry, function (index, value) {
                        handleDataInitialDisplay($(`#select-country .dropdown_list-item .dropdown-item[data-code=${value['country_code']}]`), '1');
                    });
                }
            }
        }
    }

    const handleDataInitialDisplay = (selector, condition) => {
        if (condition === '0') {
            selector.hide();
            selector.attr('data-initial-display', '0');
        } else {
            selector.show();
            selector.attr('data-initial-display', '1');
        }
    }

    /**
     * Filter option by condition
     *
     * @param condition
     * @param value
     * @returns {(*&{code: *})[]}
     */
    const filterOptionsByCondition = (condition, value) => {
        var ports = $('#arrPort').val();
        ports = JSON.parse(ports);
        return Object.entries(ports)
            .filter(([key, item]) => item[condition] == value)
            .map(([code, item]) => ({code, ...item}));
    }

    const handleBtnSeeMore = () => {
        var deviceWidth = $(window).width();
        $('.sailing-schedule-result_detail').each(function () {
            const height = $(this)[0].scrollHeight;
            if (height > 205 && deviceWidth >= 768) {
                $(this).siblings('.btn-load').show()
            } else if (height > 85 && deviceWidth < 768) {
                $(this).siblings('.btn-load').show()
            } else {
                $(this).siblings('.btn-load').hide()
            }
        });
    }
    const initApp = () => {
        initDatepicker();
        handleSearchForm();
        getCountriesAndPorts(getUrlParameter('route'));
        handleGetPorts(getUrlParameter('country'));
        getCondidionSearch();
        handleDisplayScheduleSailing();
        handleBtnSeeMore();
        handleHoverRoute();
        scrollResultSchedule()
    }
    $(".sailing-schedule-result").click(function () {
        $(this).toggleClass('open');
        var deviceWidth = $(window).width();
        const maxHeight = $(this).css('max-height')
        const heightContent = $(this).find('.sailing-schedule-result_detail')[0].scrollHeight;
        if (deviceWidth >= 768) {
            if (maxHeight === '320px' && heightContent > 205) {
                const height = $(this).find('.sailing-schedule-result_detail')[0].scrollHeight;
                const heightParent = $(this)[0].scrollHeight + (height - 205);
                const duration = heightParent / 100 * 50;
                $(this).find('.sailing-schedule-result_detail').css('max-height', height + 'px');
                $(this).css('max-height', heightParent + 'px');
                $(this).css('height', 'unset');
                $(this).css('transition-duration', duration + 'ms');
                $(this).find('.sailing-schedule-result_detail').css('transition-duration', duration + 'ms');
                $(this).find('.btn-load .label-more').hide();
                $(this).find('.btn-load .label-less').show();
            } else {
                $(this).find('.sailing-schedule-result_detail').css('max-height', '205px');
                $(this).find('.btn-load .label-more').show();
                $(this).find('.btn-load .label-less').hide();
                $(this).css('max-height', '320px');
            }

        } else {
            if (maxHeight === '200px' && heightContent > 85) {
                const height = $(this).find('.sailing-schedule-result_detail')[0].scrollHeight;
                const heightParent = $(this)[0].scrollHeight + (height - 85);
                const duration = heightParent / 100 * 50;
                $(this).find('.sailing-schedule-result_detail').css('max-height', height + 'px');
                $(this).css('max-height', heightParent + 'px');
                $(this).css('transition-duration', duration + 'ms');
                $(this).find('.sailing-schedule-result_detail').css('transition-duration', duration + 'ms');
                $(this).find('.btn-load .label-more').hide();
                $(this).find('.btn-load .label-less').show();

            } else {
                $(this).find('.sailing-schedule-result_detail').css('max-height', '85px');
                $(this).find('.btn-load .label-more').show();
                $(this).find('.btn-load .label-less').hide();
                $(this).css('max-height', '200px');
            }
        }

    })

    $(window).resize(function () {
        handleDisplayScheduleSailing()
        handleBtnSeeMore();
    })
    $(".schedule_content").click(function () {
        if (!$(this).hasClass('collapse-update')) {
            $(this).addClass('collapse-update');
        } else $(this).removeClass('collapse-update');
    })
    // $("#result-schedule-no-data").modal('show');

    $('.search-schedule__item-content').click(function (e) {
        e.preventDefault();
        if ($(e.target).closest('.search-schedule__item-search').length) {
            return;
        }
        const $dropdown = $(this).find('.dropdown-menu');
        $('.dropdown-menu').not($dropdown).slideUp(200);

        $dropdown.slideToggle(200);
    })

    $(document).click(function (e) {
        if (!$(e.target).closest('.search-schedule__item-content, .dropdown-menu').length) {
            $('.dropdown-menu').slideUp(200);
        }
    });

    $(document).ready(function () {
        if ($('#modal-no-result').length) {
            $('body').css('overflow', 'hidden');
        } else {
            $('body').css({
                'overflow-x': 'hidden',
                'overflow-y': 'auto'
            });
        }
    });

    initApp();
})

/**
 * Event click route on map
 *
 * @param cd
 */
window.distSelect = (cd) => {
    console.log(cd)
    const startDate = $("#start-date").val();
    const endDate = $("#end-date").val();
    $('#area').val(cd);
    $('#startDate').val(startDate);
    $('#endDate').val(endDate);
}

/**
 * Event button Clear
 */
window.clearForm = () => {
    $(`#select-country .dropdown_list-item .dropdown-item`).show();
    $(`#select-port .dropdown_list-item .dropdown-item`).show();
    $(`#select-country .dropdown_list-item .dropdown-item`).attr('data-initial-display', '1');
    $(`#select-port .dropdown_list-item .dropdown-item`).attr('data-initial-display', '1');
    $("#map").children().removeClass('area-active');
    $(".input-value").val('');
    $("#start-date").datepicker("setDate", new Date());
    const selectId = ['country', 'port', 'route', 'pol']
    selectId.map(id => {
        $(`#select-${id} #${id}`).val('');
    })
}
