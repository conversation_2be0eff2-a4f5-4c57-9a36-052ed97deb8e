<?php

declare(strict_types=1);

namespace App\Services\Transport;

use App\Enums\ApiKey;
use Exception;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;
use RuntimeException;

class DownloadTransFileRefService
{
    private const API_ENDPOINT = '/backend/order/api_trans_rel_file_dl.asp';
    private const HTTP_TIMEOUT = 30;
    private const USER_AGENT = 'Laravel-Transport-API/1.0';

    /**
     * Download transport file reference by calling external API
     *
     * @param int|string $customerId
     * @param int|string $transportId
     * @param string $filename
     * @return Response
     */
    public function downloadTransFileRef($customerId, $transportId, string $filename): Response
    {
        try {
            $this->validateParameters($customerId, $transportId, $filename);

            // Convert to integers after validation
            $customerId = (int) $customerId;
            $transportId = (int) $transportId;

            $apiUrl = $this->buildApiUrl();
            $params = $this->buildRequestParams($customerId, $transportId, $filename);

            $response = $this->makeApiRequest($apiUrl, $params);
            $this->logApiCall($apiUrl, $params, $response);

            return $this->handleApiResponse($response, $filename);

        } catch (Exception $e) {
            $this->logError($e, $customerId, $transportId, $filename);
            return $this->createErrorResponse('Internal server error - External API call failed', 500);
        }
    }

    /**
     * Validate input parameters
     *
     * @param int|string $customerId
     * @param int|string $transportId
     * @param string $filename
     * @return void
     * @throws InvalidArgumentException
     */
    private function validateParameters($customerId, $transportId, string $filename): void
    {
        $customerId = (int) $customerId;
        $transportId = (int) $transportId;

        if ($customerId <= 0 || $transportId <= 0 || empty($filename)) {
            throw new InvalidArgumentException('Invalid parameters provided');
        }
    }

    /**
     * Build the complete API URL
     *
     * @return string
     * @throws RuntimeException
     */
    private function buildApiUrl(): string
    {
        $oldApiUrl = config('app.APP_URL_OLD', env('APP_URL_OLD'));

        if (empty($oldApiUrl)) {
            Log::error('APP_URL_OLD is not configured');
            throw new RuntimeException('External API configuration error');
        }

        return rtrim($oldApiUrl, '/') . self::API_ENDPOINT;
    }

    /**
     * Build request parameters
     *
     * @param int|string $customerId
     * @param int|string $transportId
     * @param string $filename
     * @return array
     */
    private function buildRequestParams($customerId, $transportId, string $filename): array
    {
        return [
            'customer_id' => $customerId,
            'transport_id' => $transportId,
            'fname' => $filename,
            'key' => ApiKey::API_OLD_HUBNET->getValue()
        ];
    }

    /**
     * Make HTTP request to external API
     *
     * @param string $apiUrl
     * @param array $params
     * @return \Illuminate\Http\Client\Response
     */
    private function makeApiRequest(string $apiUrl, array $params): \Illuminate\Http\Client\Response
    {
        return Http::timeout(self::HTTP_TIMEOUT)
            ->withHeaders([
                'Accept' => 'application/json',
                'User-Agent' => self::USER_AGENT
            ])
            ->get($apiUrl, $params);
    }

    /**
     * Log API call for debugging
     *
     * @param string $apiUrl
     * @param array $params
     * @param \Illuminate\Http\Client\Response $response
     * @return void
     */
    private function logApiCall(string $apiUrl, array $params, \Illuminate\Http\Client\Response $response): void
    {
        Log::info('DownloadTransFileRef API call', [
            'url' => $apiUrl,
            'params' => $this->sanitizeParamsForLogging($params),
            'status_code' => $response->status(),
            'response_size' => strlen($response->body())
        ]);
    }

    /**
     * Sanitize parameters for logging (hide sensitive data)
     *
     * @param array $params
     * @return array
     */
    private function sanitizeParamsForLogging(array $params): array
    {
        $sanitized = $params;
        if (isset($sanitized['key'])) {
            $sanitized['key'] = '***HIDDEN***';
        }
        return $sanitized;
    }

    /**
     * Handle API response based on status code
     *
     * @param \Illuminate\Http\Client\Response $response
     * @param string $filename
     * @return Response
     */
    private function handleApiResponse(\Illuminate\Http\Client\Response $response, string $filename): Response
    {
        if ($response->successful()) {
            return $this->createSuccessResponse($response);
        }

        return $this->createErrorResponseForStatus($response);
    }

    /**
     * Create success response with file content
     *
     * @param \Illuminate\Http\Client\Response $response
     * @return Response
     */
    private function createSuccessResponse(\Illuminate\Http\Client\Response $response): Response
    {
        return response($response->body(), $response->status(), $response->headers());
    }

    /**
     * Create error response based on HTTP status code
     *
     * @param \Illuminate\Http\Client\Response $response
     * @return Response
     */
    private function createErrorResponseForStatus(\Illuminate\Http\Client\Response $response): Response
    {
        $statusCode = $response->status();

        $errorMessages = [
            400 => 'Bad request to external API',
            404 => 'File not found on external server',
            500 => 'External API server error'
        ];

        $message = $errorMessages[$statusCode] ?? 'External API error';

        return $this->createErrorResponse($message, $statusCode);
    }

    /**
     * Create standardized error response
     *
     * @param string $message
     * @param int $statusCode
     * @return Response
     */
    private function createErrorResponse(string $message, int $statusCode): Response
    {
        return response()->json([
            'success' => false,
            'message' => $message,
            'code' => $statusCode
        ], $statusCode);
    }

    /**
     * Log error with context
     *
     * @param Exception $e
     * @param int|string $customerId
     * @param int|string $transportId
     * @param string $filename
     * @return void
     */
    private function logError(Exception $e, $customerId, $transportId, string $filename): void
    {
        Log::error('DownloadTransFileRef API error: ' . $e->getMessage(), [
            'customer_id' => $customerId,
            'transport_id' => $transportId,
            'filename' => $filename,
            'trace' => $e->getTraceAsString()
        ]);
    }
}
