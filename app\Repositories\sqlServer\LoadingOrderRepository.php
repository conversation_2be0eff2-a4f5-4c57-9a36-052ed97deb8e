<?php

declare(strict_types=1);

namespace App\Repositories\sqlServer;

use App\Models\sqlServer\LoadingOrder;
use App\Repositories\BaseRepository;
use App\Repositories\common\BuilderWhereCondition;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class LoadingOrderRepository extends BaseRepository
{
    public function model(): mixed
    {
        return LoadingOrder::class;
    }

    public function paginate($params = null, array $relations = [], $columns = ['*']): LengthAwarePaginator
    {
        $query = ($this->model)::query();

        $query->with($relations)
            ->leftJoinCustom('customer');

        $params = $params ?: request()->toArray();
        $limit = Arr::pull($params, 'limit', 20);
        $page = Arr::pull($params, 'page');

        $query = BuilderWhereCondition::call($query, false, $params);
        $this->applyCustomerNameFilters($query, $params);
        $query = $this->addSoftDeleteCondition($query);

        $this->buildSortQuery($query, $params);

        return $query->paginate($limit, $columns, 'page', $page);
    }

    public function getList($params = null, array $relations = [], $columns = ['*']): Collection
    {
        $query = ($this->model)::query();

        $query->with($relations)
            ->leftJoinCustom('customer');

        $query = BuilderWhereCondition::call($query, false, $params);
        $this->applyCustomerNameFilters($query, $params);
        $query = $this->addSoftDeleteCondition($query);

        $this->buildSortQuery($query, $params);

        return $query->get(is_array($columns) ? $columns : func_get_args());
    }

    public function bulkUpdateStatus(array $ids, array $attributes): void
    {
        $query = ($this->model)::query();

        $query
            ->whereIn('id', $ids)
            ->update($attributes);
    }

    public function getDetailById($id)
    {
        $query = ($this->model)::query();

        return $query
            ->select(
                [
                    't_loading.*',
                    'm_customer.cus_Name_JP as mc_cus_Name_JP',
                    'm_customer.cus_name_EN as mc_cus_name_EN',
                    'm_customer.ah_sales_name as mc_ah_sales_name',
                    't_loading_special_note.special_note as lpn_special_note'
                ]
            )
            ->leftJoin('m_customer', 'm_customer.id', '=', 't_loading.m_customer_id')
            ->leftJoin('t_loading_special_note', 't_loading.m_customer_id', '=', 't_loading_special_note.tt_id')
            ->where('t_loading.id', $id)
            ->where('t_loading.del_flg', '=', 0)
            ->firstOrFail();
    }

    public function getLoadingOrderPrint(array $ids): \Illuminate\Database\Eloquent\Collection
    {
        $query = ($this->model)::query();

        return $query
            ->select(
                [
                    't_loading.*',
                    'm_customer.cus_Name_JP as mc_cus_Name_JP',
                    'm_customer.cus_name_EN as mc_cus_name_EN',
                    'm_customer.ah_sales_name as mc_ah_sales_name',
                    't_loading_special_note.special_note as lpn_special_note'
                ]
            )
            ->leftJoin('m_customer', 'm_customer.id', '=', 't_loading.m_customer_id')
            ->leftJoin('t_loading_special_note', 't_loading.m_customer_id', '=', 't_loading_special_note.tt_id')
            ->whereIn('t_loading.id', $ids)
            ->where('t_loading.del_flg', '=', 0)
            ->orderBy('t_loading.id', 'desc')
            ->get();
    }

    public function builderQueryExportCSV(array $params, array $relations = []): Builder
    {
        $query = ($this->model)::query();

        $query
            ->select([
                't_loading.del_flg',
                't_loading.reg_date',
                't_loading.up_date',
                't_loading.up_owner',
                't_loading.id',
                't_loading.odr_kbn',
                't_loading.odr_sub_kbn',
                't_loading.odr_date',
                't_loading.st_cd',
                't_loading.m_customer_id',
                't_loading.consignee_name',
                DB::raw("REPLACE(t_loading.consignee_name, CHAR(9), '') as consignee_name_clean"),
                't_loading.to_plan_date',
                't_loading.to_plan_flg',
                't_loading.destination',
                't_loading.port',
                't_loading.car_no',
                't_loading.car_name',
                't_loading.car_color',
                't_loading.mileage',
                't_loading.fob_price',
                't_loading.customs_flg',
                't_loading.to_name',
                't_loading.part_note',
                't_loading.note',
                't_loading.optn1_flg',
                't_loading.optn2_flg',
                't_loading.optn2_sub_kbn',
                't_loading.optn2_sub2_kbn',
                't_loading.optn3_flg',
                't_loading.optn3_sub_txt',
                't_loading.optn4_flg',
                't_loading.optn4_sub_kbn',
                't_loading.optn5_flg',
                't_loading.optn5_sub_txt',
                't_loading.optn6_flg',
                't_loading.optn6_sub_kbn',
                't_loading.optn7_flg',
                't_loading.optn8_flg',
                't_loading.optn9_flg',
                't_loading.optn10_flg',
                't_loading.optn11_flg',
                't_loading.ref_no',
                't_loading.relate_id',
                't_loading.fjc_ins_flg',
                't_loading.optn14_flg',
                't_loading.agent_ref_no',
                't_loading.optn17_flg',
                't_loading.car_year',
                't_loading.optn18_flg',
                't_loading.optn21_flg',
                't_loading.price_currency',
                't_loading.price_quotation',
                'm_customer.cus_name_JP',
                'm_customer.ah_sales_name',
                't_loading_special_note.special_note',
            ])
            ->leftJoin('m_customer', 'm_customer.id', '=', 't_loading.m_customer_id')
            ->leftJoin('t_loading_special_note', 't_loading.m_customer_id', '=', 't_loading_special_note.tt_id');

        $query = BuilderWhereCondition::call($query, false, $params);

        $this->applyCustomerNameFilters($query, $params);

        return $this->addSoftDeleteCondition($query);
    }

    /**
     * Apply custom customer name filters based on customerName parameter
     *
     * @param Builder $query
     * @param array $params
     * @return void
     */
    private function applyCustomerNameFilters(Builder $query, array $params): void
    {
        $customerName = $params['customerName'] ?? null;

        if (!empty($customerName)) {
            $query->where(function (Builder $subQuery) use ($customerName): void {
                $subQuery->where('m_customer.cus_name_JP', 'like', '%' . $customerName . '%')
                    ->orWhere('m_customer.cus_name_EN', 'like', '%' . $customerName . '%');
            });
        }
    }
}
