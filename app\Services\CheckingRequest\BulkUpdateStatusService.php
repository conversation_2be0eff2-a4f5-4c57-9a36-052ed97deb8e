<?php

declare(strict_types=1);

namespace App\Services\CheckingRequest;

use App\Constants\ApiCodes;
use App\Enums\CheckingRequest\Statuses;
use App\Exceptions\ApiException;
use App\Repositories\sqlServer\CheckingRequestRepository;

class BulkUpdateStatusService
{
    private CheckingRequestRepository $checkingRequestRepository;
    public function __construct(CheckingRequestRepository $checkingRequestRepository)
    {
        $this->checkingRequestRepository = $checkingRequestRepository;
    }

    /**
     * @throws ApiException
     */
    public function call(array $params): void
    {
        $ids = $params['ids'];
        $status = $params['status'];
        $dataUpdate = $this->prepareData($status);

        $this->validateStatus($ids, $status);

        $this->checkingRequestRepository->bulkUpdateStatus($ids, $dataUpdate);
    }

    private function validateStatus(array $ids, $status): void
    {
        $statusesRequiringInspector = [
            Statuses::RECEIVED->value,
            Statuses::INSPECTED->value
        ];

        if (!in_array($status, $statusesRequiringInspector)) {
            return;
        }

        $listCheckingRequests = $this->checkingRequestRepository->findByIds($ids);
        $listCheckingRequests->each(
            function ($checkingRequest): void {
                if (!$checkingRequest->t_inspector_id) {
                    throw new ApiException(ApiCodes::WEB_E_MSG_002, 400);
                }
            }
        );
    }

    private function prepareData($status): array
    {
        return [
            'up_date' => now(),
            'up_owner' => auth()->user()->id,
            'st_cd' => $status
        ];
    }
}
