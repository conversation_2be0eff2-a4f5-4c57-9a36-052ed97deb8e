@props([
    'schedules' => [],
])

@php
    $VOY_TBA = 'TBA';
    $VOY_TBN = 'TBN';
    $TBA = 99999999;
    $NUMBER_OF_POL = 15;
    $NUMBER_OF_POD = 25;
    $isJa = app()->getLocale() === 'ja';

    $formatDateTime = fn ($date) => ! $date ? '' : (new DateTime($date))->format($isJa ? 'm/d' : 'j M');
@endphp

<div class="schedule-result 2sm:mx-0 mx-6">
    <div class="schedule-result_details -mx-4 flex flex-wrap">
        @foreach ($schedules as $data)
            <div class="sailing-schedule-result max-h-50 w-full px-4 duration-[261.5]">
                <div class="sailing-schedule__ship flex justify-between">
                    <div class="flex w-full items-center">
                        <img src="{{ asset('images/schedule/ship.svg') }}" alt="ship icon" />
                        <h5 class="sailing-schedule_ship-name" title="{{ $data['ship_name'] }}">
                            {{ $data['ship_name'] }}
                        </h5>
                        <div
                            class="schedule-sailing_voy-no @if(trim($data['voy_no']) === $VOY_TBA || trim($data['voy_no']) === $VOY_TBN)text-red-500 @endif"
                        >
                            @if (trim($data['voy_no']) == $VOY_TBA || trim($data['voy_no']) == $VOY_TBN)
                                {{ $VOY_TBA }}
                            @else
                                V.{{ trim($data['voy_no']) }}
                            @endif
                        </div>
                    </div>
                </div>
                <div class="sailing-schedule_name-company">
                    {{ strpos($data['company_name'], '(') !== false ? strstr($data['company_name'], '(', true) : $data['company_name'] }}
                </div>

                <div class="sailing-schedule-result_detail">
                    @for ($i = 1; $i <= $NUMBER_OF_POL; $i++)
                        @continue(! isset($data["sc_internal_$i"]))
                        <div class="sailing-schedule-result_content flex justify-between">
                            <img
                                src="{{ asset('images/schedule/port.svg') }}"
                                class="absolute top-[5px] left-0"
                                alt=""
                            />
                            <div class="flex">
                                <img
                                    src="{{ asset('images/countries/' . $data["sc_internal_$i"]['country_code'] . '.svg') }}"
                                    alt="country flag"
                                    class="h-6 w-6 pr-[5px]"
                                />
                                <span class="sailing-schedule_name-port">
                                    {{ trim($data["sc_internal_$i"]['port_name']) }}
                                </span>
                            </div>
                            <span
                                class="sailing-schedule_date {{ $data["sc_internal_$i"]['date'] === $TBA ? '!text-red-500' : '' }}"
                            >
                                {{ $data["sc_internal_$i"]['date'] == $TBA ? 'TBA' : $formatDateTime($data["sc_internal_$i"]['date']) }}
                            </span>
                        </div>
                    @endfor

                    @for ($i = 1; $i <= $NUMBER_OF_POD; $i++)
                        @continue(! isset($data["sc_external_$i"]))
                        <div class="sailing-schedule-result_content flex justify-between">
                            <img
                                src="{{ asset('images/schedule/port.svg') }}"
                                class="absolute top-[5px] left-0"
                                alt=""
                            />
                            <div class="flex">
                                <img
                                    src="{{ asset('images/countries/' . $data["sc_external_$i"]['country_code'] . '.svg') }}"
                                    alt="country flag"
                                    class="h-6 w-6 pr-[5px]"
                                />
                                <span class="sailing-schedule_name-port">
                                    {{ trim($data["sc_external_$i"]['port_name']) }}
                                </span>
                            </div>
                            <span
                                class="sailing-schedule_date {{ $data["sc_external_$i"]['date'] === $TBA ? '!text-red-500' : '' }}"
                            >
                                {{ $data["sc_external_$i"]['date'] == $TBA ? 'TBA' : $formatDateTime($data["sc_external_$i"]['date']) }}
                            </span>
                        </div>
                    @endfor
                </div>

                <div class="btn-load hidden text-center">
                    <span class="label-more">{{ __('home-page/sailing_schedule.see_more') }}</span>
                    <span class="label-less hidden">{{ __('home-page/sailing_schedule.see_less') }}</span>
                </div>
            </div>
        @endforeach
    </div>
</div>
