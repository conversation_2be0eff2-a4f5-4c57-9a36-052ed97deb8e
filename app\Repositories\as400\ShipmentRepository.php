<?php

declare(strict_types=1);

namespace App\Repositories\as400;

use App\Models\as400\Shipment;
use App\Repositories\BaseRepository;

class ShipmentRepository extends BaseRepository
{
    public function model(): mixed
    {
        return Shipment::class;
    }

    public function getShipmentByCarNo(string $carNo)
    {
        return ($this->model)::query()
            ->where('CMCY15', '<>', '1')
            ->where('CMCC54', '!=', '')
            ->where('CMCC54', '!=', null)
            ->where('CMCC54', '=', $carNo)
            ->get();
    }
}
