<?php

declare(strict_types=1);

namespace App\Services\AaPlace;

use App\Enums\AaPlace\ChikuRegion;
use App\Repositories\sqlServer\AaPlaceRepositories;
use Illuminate\Support\Collection;

class AaPlaceService
{
    private AaPlaceRepositories $aaPlaceRepositories;

    public function __construct(AaPlaceRepositories $aaPlaceRepositories)
    {
        $this->aaPlaceRepositories = $aaPlaceRepositories;
    }

    public function getAaPlaceChikuRegion(array $params = [])
    {
        $rawData = $this->aaPlaceRepositories->getAaPlaceChikuRegion($params);

        return $this->mappingData($rawData);
    }

    public function getAuctionDates(): Collection
    {
        $rawData = $this->aaPlaceRepositories->getAuctionDates();

        return $this->mappingAuctionDatesData($rawData);
    }

    private function mappingData(mixed $rawData)
    {
        $grouped = collect($rawData)
            ->groupBy(fn ($item) => $item->value2)
            ->map(function ($group) {
                $first = $group->first();

                return [
                    'region_name' => $first->value2,
                    'prefecture_code' => $first->cd,
                    'halls' => $group->map(fn ($item) => [
                        'id' => $item->id,
                        'name' => $item->name,
                        'addr' => $item->addr,
                        'tel' => $item->tel,
                    ])->values(),
                ];
            });

        $orderedResult = collect();

        foreach (ChikuRegion::cases() as $region) {
            $regionName = $region->value;

            if ($grouped->has($regionName)) {
                $orderedResult->push($grouped->get($regionName));
            }
        }

        return $orderedResult;
    }

    private function mappingAuctionDatesData(mixed $rawData)
    {
        return collect($rawData)->map(fn ($item) => [
            'tad_id' => $item->tad_id,
            'open_date' => $item->open_date,
            'place_name' => $item->name,
        ]);
    }
}
