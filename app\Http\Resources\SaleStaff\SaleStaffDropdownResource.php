<?php

declare(strict_types=1);

namespace App\Http\Resources\SaleStaff;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SaleStaffDropdownResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->s_id,
            'name' => $this->s_name,
        ];
    }
}
