<?php

declare(strict_types=1);

namespace App\Enums\CheckingRequest;

enum Statuses: string
{
    case CONFIRMING       = '0'; // 確認中
    case RECEIVED         = '1'; // 受付済
    case INSPECTED        = '2'; // 下見完了
    case ON_HOLD          = '3'; // 保留中
    case NOT_INSPECTED    = '8'; // 下見できず
    case CANCELED         = '9'; // キャンセル

    public function toJapaneseLabel(): string
    {
        return match ($this) {
            self::CONFIRMING => '確認中',
            self::RECEIVED => '受付済',
            self::INSPECTED => '下見完了',
            self::ON_HOLD => '保留中',
            self::NOT_INSPECTED => '下見できず',
            self::CANCELED => 'キャンセル',
        };
    }
}
