@extends('app')

@section('title', __('home-page/d2d_package.page_title'))

@once
    @push('styles')
        <style>
            b {
                color: #000000;
                font-weight: 400;
            }
        </style>
    @endpush
@endonce

@section('content')
    <x-services.container>
        <x-services.title-page
            titlePage="{{ __('home-page/d2d_package.title') }}"
            description1="{{ __('home-page/d2d_package.page_description1') }}"
            description2="{{ __('home-page/d2d_package.page_description2') }}"
        />

        <x-services.title subTitle="{{ __('home-page/d2d_package.service_descriptions') }}" />
        <x-services.good-content :content="__('home-page/d2d_package.good_content')" />
        <div class="mx-auto mt-[45px] w-full px-4">
            <x-services.how-to-use
                :content1="__('home-page/d2d_package.how_to_use_content_1')"
                :content2="__('home-page/d2d_package.how_to_use_content_2')"
                icon1="fas fa-globe"
                icon2="fas fa-fax"
            />
            <x-services.how-to-use-content page="d2d_package" />
            <x-services.title :titlePage="__('home-page/d2d_package.service_list')" />
            <x-services.table-package />
            <x-services.important-notice
                :title="__('home-page/d2d_package.important_notice')"
                :contents="__('home-page/d2d_package.important_notice_list')"
            />
            <div class="flex flex-wrap justify-between gap-[25px]">
                @foreach (__('home-page/d2d_package.offices') as $office)
                    <div
                        class="mb-[1em] flex w-full flex-col justify-between rounded-sm p-[1.5em] text-sm shadow-[inset_2px_2px_10px_#ccc] md:mb-0 md:w-[48%] md:text-base"
                    >
                        <div class="text-sm md:text-base">
                            <h4 class="text-center text-lg font-bold md:text-2xl">{{ $office['title'] }}</h4>
                            <h4 class="!mb-[1em] text-center text-lg font-bold md:text-2xl">
                                {{ $office['subTitle'] }}
                            </h4>
                            @foreach ($office['address'] as $line)
                                {!! $line !!}
                            @endforeach
                        </div>
                        <div>
                            <div class="mb-4 w-full text-sm md:text-base">
                                <x-services.google-map
                                    :id="$office['mapId']"
                                    :positionX="$office['positionX']"
                                    :positionY="$office['positionY']"
                                    :office="$office['mapOffice']"
                                    :officeDetail="$office['mapDetail']"
                                />
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
        <x-services.service-options
            :description="__('home-page/d2d_package.hubnet_order')"
            iconRight="smartplan_icon_01.png"
            :titleRight="__('home-page/d2d_package.support_service')"
            :contentRight="__('home-page/d2d_package.support_service_content')"
            linkRight="{!! env('APP_URL_OLD', '') . '/hn/load/d2d_lump.asp?scd=0&lan=' . app()->getLocale() !!}"
        />
    </x-services.container>
@endsection
