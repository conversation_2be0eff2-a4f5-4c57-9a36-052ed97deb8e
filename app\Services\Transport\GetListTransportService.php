<?php

declare(strict_types=1);

namespace App\Services\Transport;

use App\Constants\PageMetaData;
use App\Enums\UserRole;
use App\Http\Resources\Transport\TransportResource;
use App\Repositories\sqlServer\TransportRepository;
use Exception;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class GetListTransportService
{
    private TransportRepository $transportRepository;
    private CheckingFileOldHubNetService  $checkingFileOldHubNetService;

    public function __construct(
        TransportRepository $transportRepository,
        CheckingFileOldHubNetService $checkingFileOldHubNetService,
    ) {
        $this->transportRepository = $transportRepository;
        $this->checkingFileOldHubNetService = $checkingFileOldHubNetService;
    }

    public function getListPaginate(array $params)
    {
        $params = $this->applyUserFiltering($params);

        $pagination = $this->getTransportPagination($params);
        $transportCollection = TransportResource::collection($pagination);

        $fileInformationData = $this->fetchTransportFileInfo($pagination);
        $resultTransportData = $this->addFileInfoToTransports(
            $transportCollection,
            $fileInformationData
        );

        return [
            'items' => $resultTransportData,
            'meta' => (new PageMetaData($pagination))->toArray()
        ];
    }

    /**
     * Apply user-specific filtering based on user role
     *
     * @param array $params
     * @return array
     */
    private function applyUserFiltering(array $params): array
    {
        if (Auth::user()->hn_cd != UserRole::admin->value) {
            $params['customer_id'] = Auth::user()->id;
        }

        return $params;
    }

    /**
     * Get paginated transport data from repository
     *
     * @param array $params
     * @return mixed
     */
    private function getTransportPagination(array $params): mixed
    {
        return $this->transportRepository->getPaginate($params, [
            'commonInspection'
        ]);
    }

    /**
     * Build parameters for transport file API call
     *
     * @param mixed $pagination
     * @return array
     */
    private function buildFileApiParams($pagination): array
    {
        return $pagination->map(fn ($transport) => [
            'm_customer_id' => $transport->m_customer_id,
            'transport_id' => $transport->id
        ])->filter(fn ($params) => isset($params['m_customer_id']) && isset($params['transport_id']))->values()->toArray();
    }

    /**
     * Fetch file information for all transports
     *
     * @param mixed $pagination
     * @return array
     */
    private function fetchTransportFileInfo($pagination): array
    {
        $fileCheckParams = $this->buildFileApiParams($pagination);

        if (empty($fileCheckParams)) {
            return [];
        }

        try {
            return $this->checkingFileOldHubNetService->call($fileCheckParams);
        } catch (Exception $e) {
            Log::error('Failed to get file information for transports', [
                'error' => $e->getMessage(),
                'params' => $fileCheckParams
            ]);
            return [];
        }
    }

    /**
     * Map transport_id to file information
     *
     * @param array $fileInformationData
     * @return array
     */
    private function mapTransportFileInfo(array $fileInformationData): array
    {
        return collect($fileInformationData)
            ->keyBy('transport_id')
            ->toArray();
    }

    /**
     * Add file information to transport data
     *
     * @param AnonymousResourceCollection $transportCollection
     * @param array $fileInformationData
     * @return \Illuminate\Support\Collection
     */
    private function addFileInfoToTransports(
        AnonymousResourceCollection $transportCollection,
        array $fileInformationData
    ): \Illuminate\Support\Collection {
        $fileInfoMapping = $this->mapTransportFileInfo($fileInformationData);

        return $transportCollection->map(function ($transportResource) use ($fileInfoMapping) {
            $transportArray = $transportResource->toArray(request());
            $transportId = $transportArray['transport_id'];
            $transportArray['fileInformation'] = $fileInfoMapping[$transportId] ?? null;
            return $transportArray;
        });
    }
}
