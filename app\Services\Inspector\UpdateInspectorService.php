<?php

declare(strict_types=1);

namespace App\Services\Inspector;

use App\Repositories\sqlServer\InspectorRepository;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;

class UpdateInspectorService
{
    private InspectorRepository $inspectorRepository;

    public function __construct(InspectorRepository $inspectorRepository)
    {
        $this->inspectorRepository = $inspectorRepository;
    }

    /**
     * @throws Exception
     */
    public function call(int $id, array $body)
    {
        if (!$this->inspectorRepository->findDetailById($id)) {
            return null;
        }

        $updateData = $this->prepareUpdateData($body);
        $inspector = $this->inspectorRepository->update($id, $updateData);
        return $inspector;
    }

    private function prepareUpdateData(array $body): array
    {
        $now = Carbon::now();

        return [
            'up_date' => $now->format('Y-m-d H:i:s'),
            'up_owner' => Auth::user()?->id ?? 1,
            'name' => $body['name'],
            'name_kana' => $body['name_kana'],
            'tel' => $body['tel'],
            'mail' => $body['mail'],
        ];
    }
}
