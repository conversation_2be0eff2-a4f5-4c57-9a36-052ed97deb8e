<?php

declare(strict_types=1);

namespace App\Http\Requests\Area;

use App\Enums\Area\OrderOptions;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GetDataDropdownAreaRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules()
    {
        $rules = [
            'order_option' => ['nullable' , Rule::in(OrderOptions::getAllValues())],
            'sort_type' => ['nullable', Rule::in('asc', 'desc')],
            'is_acd_not_empty' => ['nullable', 'numeric'],
            'is_cd_not_empty' => ['nullable', 'numeric'],
            'is_pcd_not_empty' => ['nullable', 'numeric'],
            'distinct' => [
                'nullable',
                'string',
                Rule::in(['port', 'country', 'area']),
                Rule::requiredIf((bool) ($this->order_option)),
            ],
        ];

        return $rules;
    }
}
