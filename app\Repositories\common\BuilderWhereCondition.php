<?php

declare(strict_types=1);

/**
 * Class BuilderWhereCondition
 * Handles dynamic where conditions for database queries
 * @package App\Repositories\common
 */

namespace App\Repositories\common;

use Illuminate\Database\Eloquent\Builder;

class BuilderWhereCondition
{
    /**
     * Process and apply where conditions from request parameters
     * @param Builder $query Query builder instance
     * @param bool $withTrashed Include soft deleted records
     * @param array|null $params Request parameters
     * @return Builder Modified query builder
     */
    public static function call(Builder $query, bool $withTrashed = false, ?array $params = null): Builder
    {
        $queries = $params ?: request()->query();
        $whereConditions = [];

        foreach ($queries as $filterKey => $filterValue) {
            $parts = explode('-', $filterKey);

            if (count($parts) < 3) {
                continue;
            }

            $operator = array_pop($parts); // e.g.: "equal"
            $field = array_pop($parts); // e.g.: "title"
            $table = implode('_', $parts);

            $keyColumn = $table . '.' . $field;

            $whereCondition = new WhereCondition($keyColumn, $filterValue, $withTrashed, $operator);

            $whereConditions[] = $whereCondition;
        }

        return self::buildCondition($query, $whereConditions);
    }

    /**
     * @param Builder $query
     * @var WhereCondition[] $whereConditions
     * @return Builder
     */
    public static function buildCondition(Builder $query, array $whereConditions): Builder
    {
        foreach ($whereConditions as $condition) {
            self::applyFilter($query, $condition->getKeyColumn(), $condition->getValue(), $condition->getOperator());
        }

        return $query;
    }

    /**
     * Apply specific filter condition to query builder
     * @param Builder $query Query builder instance
     * @param string $key Column key
     * @param mixed $value Filter value
     * @param string $operator Comparison operator
     * @return void
     */
    private static function applyFilter(Builder $query, string $key, mixed $value, string $operator): void
    {
        switch ($operator) {
            case 'equal':
                self::applyEqualFilter($query, $key, $value);
                break;

            case 'like':
                self::applyLikeFilter($query, $key, $value);
                break;

            case 'in':
                self::applyInFilter($query, $key, $value);
                break;

            case 'date_later':
                self::applyDateLater($query, $key, $value);
                break;

            case 'date_earlier':
                self::applyDateEarlier($query, $key, $value);
                break;

            default:
                $query->where($key, $value);
                break;
        }
    }

    /**
     * Apply equality filter condition
     * @param Builder $query Query builder instance
     * @param string $key Column key
     * @param mixed $value Filter value
     * @return void
     */
    private static function applyEqualFilter(Builder $query, string $key, mixed $value): void
    {
        $query->where($key, $value);
    }

    /**
     * Apply LIKE filter condition
     * @param Builder $query Query builder instance
     * @param string $key Column key
     * @param mixed $value Filter value
     * @return void
     */
    private static function applyLikeFilter(Builder $query, string $key, mixed $value): void
    {
        $query->where($key, 'like', '%' . $value . '%');
    }

    /**
     * Apply IN filter condition
     * @param Builder $query Query builder instance
     * @param string $key Column key
     * @param mixed $value Filter value
     * @return void
     */
    private static function applyInFilter(Builder $query, string $key, mixed $value): void
    {
        if (is_array(json_decode($value, true))) {
            $query->whereIn($key, json_decode($value, true));
        }
    }

    private static function applyDateLater(Builder $query, string $key, mixed $value): void
    {
        $query->where($key, '>=', $value);
    }

    private static function applyDateEarlier(Builder $query, string $key, mixed $value): void
    {
        $query->where($key, '<=', $value);
    }
}
