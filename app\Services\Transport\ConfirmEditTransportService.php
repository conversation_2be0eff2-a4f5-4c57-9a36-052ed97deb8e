<?php

declare(strict_types=1);

namespace App\Services\Transport;

use App\Repositories\sqlServer\TransportRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB as DBFacade;
use Illuminate\Support\Arr;

class ConfirmEditTransportService
{
    public function __construct(
        private TransportRepository $transportRepository,
        private StoreFileTransportService $storeFileTransportService
    ) {
    }

    public function updateTransport(int $id, array $data)
    {
        $transport = $this->transportRepository->getInfoDetailTransport($id);

        // logic update ..

        return $transport;
    }

    public function confirmUpdate($id, array $params = [])
    {
        if (count(Arr::get($params, 'trans_file') ?? [])) {
            $response = $this->storeFileTransportService->call([
                'm_customer_id' => Arr::get($params, 'm_customer_id'),
                'transport_id' => $id,
                'files' => Arr::get($params, 'trans_file', [])
            ]);

            dd($response);
        }
        dd(12312);
    }

    private function createEditNote($originalTransport, $updatedTransport, array $params): string
    {
        $changes = [];

        if ($originalTransport->deli_price != $updatedTransport->deli_price) {
            $changes[] = "Delivery price: {$originalTransport->deli_price} → {$updatedTransport->deli_price}";
        }

        if ($originalTransport->sales_price != $updatedTransport->sales_price) {
            $changes[] = "Sales price: {$originalTransport->sales_price} → {$updatedTransport->sales_price}";
        }

        if ($originalTransport->comment != $updatedTransport->comment) {
            $changes[] = "Comment updated";
        }

        $note = "Transport edit confirmed by " . Auth::user()->name . " on " . now()->format('Y-m-d H:i:s');

        if (!empty($changes)) {
            $note .= "\nChanges: " . implode(', ', $changes);
        }

        return $note;
    }

    /**
     * Prepare transport data for update
     *
     * @param array $params
     * @return array
     */
    private function prepareTransportData(array $params): array
    {
        $transportFields = [
            // Basic info
            'fr_aa_place_id', 'fr_name', 'fr_addr', 'fr_tel',
            'to_name', 'to_addr', 'to_tel', 'pos_no', 'aa_no',

            // Car info
            'car_name', 'car_no', 'car_color', 'plate_cut_flg', 'plate_no',
            'luxury_money', 'package_fee',

            // Dates
            'from_plan_date', 'to_plan_date', 'to_date', 'plate_send_date',
            'date_of_payment', 'date_of_payment_time', 'auction_date',

            // Status and flags
            'st_cd', 'optn1_flg', 'undrivable_flg', 'tall_flg', 'lowdown_flg', 'long_flg',
            'old_flg', 'luxury_flg', 'luxury_flg_insrance', 'other_flg', 'tick_no_flg',
            'auction_chk', 'pos_chk',

            // Prices
            'deli_price', 'sales_price',

            // References
            'ref_no', 'agent_ref_no', 'plate_send_no', 'plate_send_co',
            'other_flg_txt', 'pos_chk_text', 'auction_txt',

            // Places
            'fr_place_id', 'to_place_id', 'to_etc_place_id', 'to_aa_place_id',
            'm_trans_id',

            // Country and shipping
            'country', 'country_free', 'country_cd', 'country_area',
            'port_cd', 'port', 'service_name', 'vessel_voy', 'etd', 'eta', 'fin_ship',
            'service_kbn',

            // Auction info
            'auc_name', 'auc_addr', 'auc_tel',

            // Plate sending
            'plate_send_name', 'plate_send_zipcd', 'plate_send_address', 'plate_send_tel',
        ];

        $data = [];
        foreach ($transportFields as $field) {
            if (isset($params[$field])) {
                $data[$field] = $params[$field];
            }
        }

        return $data;
    }

    /**
     * Check for transport changes and set flags
     *
     * @param object $transport
     * @param array $params
     * @return array
     */
    private function checkTransportChanges($transport, array $params): array
    {
        $flags = [
            'price_changed' => false,
            'plate_changed' => false,
            'comment_changed' => false,
            'file_changed' => false,
            'status_changed' => false
        ];

        // Check price change
        if (isset($params['deli_price']) && $transport->deli_price != $params['deli_price']) {
            $flags['price_changed'] = true;
        }

        // Check plate send info change
        if (isset($params['plate_send_date']) && $transport->plate_send_date != $params['plate_send_date']) {
            $flags['plate_changed'] = true;
        }
        if (isset($params['plate_send_co']) && $transport->plate_send_co != $params['plate_send_co']) {
            $flags['plate_changed'] = true;
        }
        if (isset($params['plate_send_no']) && $transport->plate_send_no != $params['plate_send_no']) {
            $flags['plate_changed'] = true;
        }

        // Check comment change
        if (isset($params['comment']) && !empty($params['comment']) && $transport->comment !== $params['comment']) {
            $flags['comment_changed'] = true;
        }

        // Check status change
        if (isset($params['st_cd']) && $transport->st_cd != $params['st_cd']) {
            $flags['status_changed'] = true;
        }

        // Check file change
        if (isset($params['file']) && !empty($params['file'])) {
            $flags['file_changed'] = true;
        }
        return $flags;
    }

    /**
     * Create transport note when comment changes
     *
     * @param object $transport
     * @param array $newNote
     * @return void
     */
    private function createTransportNote($transport, array $newNote): void
    {
        // Create new transport note with updated comment
        $noteData = [
            'tp_id' => $transport->id,
            'refno' => $transport->ref_no ?? '', // Ensure refno is not null
            'note' => $newNote['comment'],
            'name' => $newNote['name'],
            'list_show_flg' => 1,
            'reg_date' => now()
        ];

        $this->transportNoteRepository->create($noteData);
    }

    /**
     * Bulk edit transport records
     *
     * @param array $data
     * @return array
     */
    public function bulkEditTransport(array $data): array
    {
        return DB::transaction(function () use ($data) {
            $idList = $data['id_list'] ?? [];
            $transportData = $this->prepareDataForBulkEdit($data['transport_data'] ?? []);
            $notesData = $data['transport_notes_data'] ?? [];

            $notesCreated = 0;
            $mailSentCount = 0;

            // Get all transports using whereIn for better performance
            $oldTransports = $this->transportRepository->getInfoTransportByIdList($idList);

            // Bulk update using whereIn instead of individual updates
            if (!empty($oldTransports)) {
                $transportIds = $oldTransports->pluck('id')->toArray();
                $this->transportRepository->updateStatusTransports($transportIds, $transportData);

                // Get updated transports for mail sending
                $updatedTransportsList = $this->transportRepository->getInfoTransportByIdList($transportIds);
                foreach ($updatedTransportsList as $updatedTransport) {
                    $originalTransport = $oldTransports->firstWhere('id', $updatedTransport->id);
                    $tpId = $updatedTransport->id;
                    // Check transport changes (price, plate, status)
                    $changeFlags = $this->checkTransportChanges($originalTransport, $transportData);

                    // Check comment changes separately
                    $commentChanged = false;
                    if (!empty($notesData['comment'])) {
                        $oldTransportNote = $this->transportNoteRepository->getNewestNoteByTransportId($tpId);
                        $oldComment = $oldTransportNote ? $oldTransportNote->note : '';
                        //create new note
                        $noteData = $this->prepareNotesData($tpId, $notesData);
                        $createdNote = $this->transportNoteRepository->create($noteData);
                        if ($createdNote) {
                            $notesCreated++;
                        }
                        $newTransportNote = $this->transportNoteRepository->getNewestNoteByTransportId($tpId);
                        $newComment = $newTransportNote ? $newTransportNote->note : '';
                        //check comment changed
                        $commentChanged = ($oldComment !== $newComment);
                    }

                    // Send comment change mail
                    if($commentChanged) {
                        $this->sendMailTransportService->sendMailNotification('comment_change', [
                            'transport' => $updatedTransport,
                            'comment' => $notesData['comment']
                        ]);
                        $mailSentCount++;
                    }

                    // Send price/plate change mail
                    if($changeFlags['price_changed'] || $changeFlags['plate_changed']) {
                        $this->sendMailTransportService->sendMailNotifyByUpdateInfo($updatedTransport, $originalTransport);
                        $mailSentCount++;
                    }

                    // Send status change mail
                    if($changeFlags['status_changed']) {
                        $this->sendMailTransportService->sendMailNotification('status_change', [
                            'idListTransport' => [$updatedTransport->id],
                            'status' => $transportData['st_cd']
                        ]);
                        $mailSentCount++;
                    }
                }
            }

            return [
                'transport_ids' => $idList,
                'mail_sent' => $mailSentCount > 0,
                'mail_sent_count' => $mailSentCount,
                'notes_created' => $notesCreated
            ];
        });
    }

    /**
     * Prepare and validate data before updating transport and notes
     *
     * @param array $data
     * @return array
     */
    private function prepareDataForBulkEdit(array $data): array
    {
        $transportData = [
            'up_owner' => Auth::user()->id,
            'up_date' => now()->format('Y-m-d H:i:s'),
        ];

        if (isset($data['st_cd']) && $data['st_cd'] !== null) {
            $transportData['st_cd'] = $data['st_cd'];
        }
        if (isset($data['from_plan_date']) && $data['from_plan_date'] !== null) {
            $transportData['from_plan_date'] = $data['from_plan_date'];
        }
        if (isset($data['to_plan_date']) && $data['to_plan_date'] !== null) {
            $transportData['to_plan_date'] = $data['to_plan_date'];
        }
        if (isset($data['to_date']) && $data['to_date'] !== null) {
            $transportData['to_date'] = $data['to_date'];
        }
        if (isset($data['plate_send_date']) && $data['plate_send_date'] !== null) {
            $transportData['plate_send_date'] = $data['plate_send_date'];
        }
        if (isset($data['plate_send_co']) && $data['plate_send_co'] !== null) {
            $transportData['plate_send_co'] = $data['plate_send_co'];
        }
        if (isset($data['plate_send_no']) && $data['plate_send_no'] !== null) {
            $transportData['plate_send_no'] = $data['plate_send_no'];
        }
        if (isset($data['deli_price']) && $data['deli_price'] !== null) {
            $transportData['deli_price'] = $data['deli_price'];
        }
        if (isset($data['sales_price']) && $data['sales_price'] !== null) {
            $transportData['sales_price'] = $data['sales_price'];
        }

        return $transportData;
    }

    private function prepareNotesData(int $tpId, array $data): array
    {
        return [
            'tp_id' => $tpId,
            'refno' => 0,
            'note' => $data['comment'],
            'name' => $data['commt_biko_reporter'] ?? Auth::user()->name ?? 'System',
            'list_show_flg' => 1,
            'reg_date' => now()
        ];
    }

    /**
     * Get ref_no from AS400 when ref_no is null
     *
     * @param string $m_customer_id
     * @param string $car_no
     * @return string|null
     */
    private function getRefNoFromAS400(string $m_customer_id, string $car_no): ?string
    {
        try {
            $sql = "SELECT HNCC03, HNTD24, HNCS74, HNSKNM, HNCY25, HNNC25, HND2Dk, HNNN47, HNNR13, HNTD14, HNTD79, HNTD19
                    FROM HCDLIB.THBNTP
                    WHERE HNAGTI = ?
                    AND HNCC54 = ?
                    AND (HNCY15 <> '1' AND HNYCKB <> '1' AND HNFL01 <> '1')
                    ORDER BY HNCC03 DESC";

            $result = DBFacade::connection('as400')->select($sql, [$m_customer_id, $car_no]);

            if (!empty($result)) {
                // Return the first result's HNCC03 (ref_no)
                return $result[0]->HNCC03 ?? null;
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Error querying AS400 for ref_no', [
                'm_customer_id' => $m_customer_id,
                'car_no' => $car_no,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Send mail notification for transport changes
     *
     * @param object $updatedTransport
     * @param int $originalId
     * @param array $flags
     * @return array|null
     */
    public function sendMailNotification($updatedTransport, int $originalId, array $flags = []): ?array
    {
        $originalTransport = $this->transportRepository->getInfoDetailTransport($originalId);
        if ($originalTransport) {
            if (isset($flags['status_changed']) && $flags['status_changed']) {
                $this->sendMailTransportService->sendMailNotifyByUpdateStatus([$originalId], $updatedTransport->st_cd);
                return ['status_mail_sent' => true];
            }

            return $this->sendMailTransportService->sendMailNotifyByUpdateInfo($updatedTransport, $originalTransport);
        }
        return null;
    }

    /**
     * Bulk update all transport records
     *
     * @param array $data
     * @return array
     */
    public function bulkUpdateAll(array $data): array
    {
        $allTransportIds = $this->transportRepository->getAllTransportIds();
        $data['id_list'] = $allTransportIds;
        return $this->bulkEditTransport($data);
    }
}
