<?php

declare(strict_types=1);

namespace App\Services\ProcessLock;

use App\Repositories\sqlServer\ProcessLockRepository;
use Exception;
use Illuminate\Support\Str;

class ProcessLockService
{
    private ProcessLockRepository $processLockRepository;

    public function __construct(ProcessLockRepository $processLockRepository)
    {
        $this->processLockRepository = $processLockRepository;
    }

    public function lock(string $lockName): array
    {
        $exists = $this->processLockRepository->getExistLockActiveByLockName($lockName);

        if ($exists) {
            return ['status' => false, 'lock_id' => null];
        }

        $lockId = Str::random(20);
        $inserted = $this->processLockRepository->store([
            'lock_id' => $lockId,
            'lock_name' => $lockName,
            'del_flg' => 0,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        if (!$inserted) {
            throw new Exception('Failed to acquire lock');
        }

        return ['status' => true, 'lock_id' => $lockId];
    }

    public function unlock(string $lockId): bool
    {
        $affected = $this->processLockRepository->updateOneByLockId($lockId, ['del_flg' => 1]);

        if (!$affected) {
            throw new Exception('Failed to release lock');
        }
        return true;
    }
}
