<?php

declare(strict_types=1);

namespace App\Http\Requests\Contact;

use Illuminate\Foundation\Http\FormRequest;

class HandleContactRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        // Validate for both confirmation page and final submission
        if ('new' !== $this->input('page')) {
            return [
                'select_contact' => 'required|integer|min:1',
                'company_name' => 'required|string',
                'name' => 'required|string',
                'mail_address' => 'required',
                'tel' => [
                    'required',
                    'string',
                    'min:10',
                    'max:11',
                    'regex:/^0[0-9]{9,10}$/'
                ],
            ];
        }

        return [];
    }

    public function messages(): array
    {
        return [
            'select_contact.required' => 'お問い合わせ種別を選択してください。',
            'select_contact.min' => 'お問い合わせ種別を正しく選択してください。',
            'company_name.required' => '法人名を入力してください。',
            'company_name.regex' => '法人名に無効な文字が含まれています。',
            'name.required' => 'お名前を入力してください。',
            'name.regex' => 'お名前に無効な文字が含まれています。',
            'mail_address.required' => 'メールアドレスを入力してください。',
            'mail_address.email' => '正しいメールアドレスを入力してください。',
            'tel.required' => '電話番号を入力してください。',
            'tel.regex' => '電話番号の形式が正しくありません。',
        ];
    }

    public function attributes(): array
    {
        return [
            'select_contact' => 'お問い合わせ種別',
            'company_name' => '法人名',
            'name' => 'お名前',
            'mail_address' => 'メールアドレス',
            'tel' => '電話番号',
            'message' => 'お問い合わせ内容',
        ];
    }
}
