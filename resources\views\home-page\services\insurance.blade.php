@extends('app')

@section('title', __('home-page/insurance.page_title'))

@section('content')
    <x-services.container>
        <x-services.title-page
            titlePage="{{ __('home-page/insurance.title') }}"
            description1="{{ __('home-page/insurance.page_description1') }}"
            description2="{{ __('home-page/insurance.page_description2') }}"
        />
        <x-services.title subTitle="{{ __('home-page/insurance.service_descriptions') }}" />
        <x-services.good-content :content="__('home-page/insurance.good_content')" />
        <div class="mx-auto mt-[45px] w-full px-4">
            <x-services.how-to-use
                :content1="__('home-page/insurance.how_to_use_content_1')"
                :content2="__('home-page/insurance.how_to_use_content_2')"
                icon1="fas fa-hand-holding-heart"
                icon2=""
            />
            <div class="my-4 w-full">
                <img src="{{ asset('images/services/insurance_lg.png') }}" alt="" class="2sm:block hidden w-full" />
                <img src="{{ asset('images/services/insurance_sm.png') }}" alt="" class="2sm:hidden block w-full" />
            </div>
        </div>
        <div class="2md:px-4 mx-auto mt-[45px] w-full">
            <div
                class="2sm:mt-4 2sm:mb-20 2sm:flex 2sm:justify-between 2md:mt-12 2md:mb-40 block w-full md:mt-8 md:mb-24"
            >
                <ul class="2sm:py-[0.4em] 2md:w-3/4 flex w-full flex-col justify-between font-bold">
                    <li
                        class="2sm:mx-0 2sm:p-[1em] 2sm:border-l-40 text-15 mx-[1em] my-[0.5em] border border-l-20 border-gray-50 border-l-cyan-100 px-[1.5em] py-[0.5em] text-left text-gray-900 md:text-xl"
                    >
                        <span>{{ __('home-page/insurance.how_to_use_content_3') }}</span>
                    </li>
                    <li
                        class="2sm:mx-0 2sm:p-[1em] 2sm:border-l-40 text-15 mx-[1em] my-[0.5em] border border-l-20 border-gray-50 border-l-cyan-100 px-[1.5em] py-[0.5em] text-left text-gray-900 md:text-xl"
                    >
                        <span>{{ __('home-page/insurance.how_to_use_content_4') }}</span>
                    </li>
                </ul>
                <div class="2sm:w-2/5 2md:w-1/2 relative w-full">
                    <img
                        src="{{ asset('images/services/insurance_02.jpg') }}"
                        alt=""
                        class="2md:w-3/5 2sm:absolute 2sm:w-3/4 2sm:left-[8%] 2sm:bottom-[10%] 2sm:mb-0 2sm:translate-none static mb-[50px] w-[35%] translate-x-[30%] translate-y-[10%] transform shadow-cyan-100 lg:w-1/2"
                    />
                </div>
            </div>
        </div>
    </x-services.container>
@endsection
