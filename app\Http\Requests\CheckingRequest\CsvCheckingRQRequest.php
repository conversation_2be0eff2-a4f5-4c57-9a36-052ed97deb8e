<?php

declare(strict_types=1);

namespace App\Http\Requests\CheckingRequest;

use App\Enums\CheckingRequest\DateMode;
use App\Enums\CheckingRequest\OrderOptions;
use App\Enums\SortType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CsvCheckingRQRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            't_before_check-id-equal' => ['nullable', 'integer'],
            't_aa_date-t_aa_place_id-equal' => ['nullable', 'integer'],
            't_before_check-st_cd-equal' => ['nullable', 'numeric'],
            't_before_check-t_inspector_id-equal' => ['nullable', 'integer'],
            't_before_check-aa_no-equal' => ['nullable', 'string'],

            'date_mode' => ['nullable', Rule::in(DateMode::getAllValues())],
            'sdate' => ['nullable', 'date'],
            'edate' => ['nullable', 'date'],

            't_before_check-odr_person-like' => ['nullable', 'string'],
            't_before_check-car_name-like' => ['nullable', 'string'],

            'customerName' => ['nullable', 'string'],

            'orderOption' => ['nullable' , Rule::in(OrderOptions::getAllValues())],
            'sortType' => ['nullable', Rule::in(SortType::getAllValues())],

            'token' => ['required', 'string'],
        ];
    }
}
