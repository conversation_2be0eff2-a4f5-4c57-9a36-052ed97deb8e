@php
    use Illuminate\Support\Arr;
@endphp

@once
    @push('styles')
        <style>
            .step-item .step-counter::before {
                content: '';
                border: solid transparent;
                border-width: 8px;
                border-top-color: #fbba5f;
                position: absolute;
                top: calc(100% - 2px);
                left: 50%;
                -webkit-transform: translateX(-50%);
                transform: translateX(-50%);
            }

            .step-arrow:after {
                content: '';
                position: absolute;
                width: 14px;
                height: 14px;
                right: -22px;
                top: calc(50% - 10px);
                display: inline-block;
                border-top: 4px solid #fda06f;
                border-right: 4px solid #fda06f;
                -webkit-transform: rotate(45deg);
                transform: rotate(45deg);
            }

            @media (max-width: 499px) {
                .step-arrow::before {
                    content: '';
                    position: absolute;
                    width: 14px;
                    height: 14px;
                    right: calc(50% - 7px);
                    bottom: -22px;
                    display: inline-block;
                    border-top: 4px solid #fda06f;
                    border-right: 4px solid #fda06f;
                    -webkit-transform: rotate(135deg);
                    transform: rotate(135deg);
                }
                .step-arrow:after {
                    display: none;
                }
            }
        </style>
    @endpush
@endonce

<div class="mx-auto grid max-w-240 grid-cols-1 gap-8 sm:grid-cols-2 md:grid-cols-4">
    @foreach ($items as $key => $item)
        <div
            class="step-item h-50 md:h-55 {{ $key == count($items) - 1 ? '' : 'step-arrow' }} relative mt-20 rounded-xl border-3 border-orange-300 bg-white px-6 py-6 text-center"
        >
            <div
                class="step-counter absolute -top-18 left-1/2 flex h-12 w-12 -translate-x-1/2 items-center justify-center rounded-full bg-orange-300 text-18 font-bold text-white before:content-['']"
            >
                0{{ $key + 1 }}
            </div>
            <h3 class="mt-4 mb-2 text-xl font-bold text-orange-400">
                {{ Arr::get($item, 'title') }}
            </h3>
            <p class="mb-4 text-left text-base text-gray-700">
                {{ Arr::get($item, 'content') }}
            </p>
            @if (Arr::get($item, 'link'))
                <a
                    href="{{ Arr::get($item, 'link') }}"
                    class="mx-auto block w-[120px] cursor-pointer rounded-md bg-orange-500 px-4 py-2 text-15 text-white hover:bg-orange-200 leading-6"
                >
                    {{ __('button.apply') }}
                </a>
            @endif
        </div>
    @endforeach
</div>
