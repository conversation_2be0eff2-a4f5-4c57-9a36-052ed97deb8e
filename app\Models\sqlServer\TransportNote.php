<?php

declare(strict_types=1);

namespace App\Models\sqlServer;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TransportNote extends Model
{
    public $timestamps = false;

    protected $fillable = [
        'tp_id',
        'refno',
        'note',
        'name',
        'list_show_flg',
        'reg_date',
    ];
    protected $table = 't_transport_notes';

    public function transport(): BelongsTo
    {
        return $this->belongsTo(Transport::class, 'tp_id');
    }
}
