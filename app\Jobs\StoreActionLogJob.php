<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Models\sqlServer\ActionLog;
use Illuminate\Support\Carbon;

class StoreActionLogJob
{
    /**
     * Create a new job instance.
     */
    public function __construct(
        private string $pageName,
        private Carbon $dateTime,
        private int $userId,
        private string $ipAddress,
    ) {
    }

    /**
     * Execute the job.
     */
    public function handle(ActionLog $actLog): void
    {
        $actLog->create([
            'up_owner' => $this->userId,
            'acc_page' => $this->pageName,
            'reg_date' => $this->dateTime->format('Y-m-d'), // format Y-m-d 00:00:00
            'login_id' => $this->userId,
            'del_flg' => false,
            'acc_datetime' => $this->dateTime,
            'acc_ip' => $this->ipAddress,
        ]);
    }
}
