<?php

declare(strict_types=1);

namespace App\Exceptions\Helpers;

use App\Constants\ApiCodes;
use App\Exceptions\ApiException;
use App\Exceptions\AuthException;
use App\Http\ResponseBuilder\ResponseBuilder as RB;
use Illuminate\Support\Facades\Config;
use Il<PERSON><PERSON>\Validation\ValidationException;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\ResponseBuilder\ExceptionHandlerHelper as Helper;
use Symfony\Component\HttpFoundation\Response as HttpResponse;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Throwable;

class ExceptionHandlerHelper extends Helper
{
    /**
     * {
     *   "success": false,
     *   "error": {
     *       "code": "InvalidParametersException",
     *       "message": "",
     *       "details": [
     *           {
     *               "property": "",
     *               "details": [ "" ]
     *           }
     *       ]
     *   },
     *   "debug": { ... } // app.debug = true
     * }
     *
     * @param Throwable $ex
     * @param int|string $api_code
     * @param int|null $http_code
     * @param string|null $error_message
     * @return HttpResponse
     */
    public static function error(Throwable $ex, int|string $api_code, ?int $http_code = null, ?string $error_message = null): HttpResponse
    {
        $code = ApiCodes::convertToReadable($api_code);
        $message = $error_message ?? $ex->getMessage();
        $statusCode = $http_code ?? ($ex instanceof HttpException ? $ex->getStatusCode() : $ex->getCode());
        $statusCode = $statusCode >= RB::ERROR_HTTP_CODE_MIN ? $statusCode : RB::DEFAULT_HTTP_CODE_ERROR;

        $details = [];
        $debug = null;

        switch (true) {
            case $ex instanceof ApiException:
                $code = $ex->getErrorCode();
                $message = $ex->getMessageCustom() ?? ApiCodes::convertToReadable($code);
                $details = $ex->getDetails();
                $statusCode = $ex->getCode();
                break;
            case $ex instanceof AuthException:
                $code = $ex->getCode();
                $message = $ex->getMessage();
                $details = [];
                $statusCode = $ex->getCode();
                break;
            case $ex instanceof ValidationException:
                $errors = $ex->errors();
                $firstErrorMessage = collect($errors)->flatten()->first();
                $message = $firstErrorMessage ?? $message;

                foreach ($errors as $field => $messages) {
                    $details[] = [
                        'property' => $field,
                        'details' => $messages,
                    ];
                }
                break;

            default:
                $details[] = [
                    'property' => 'general',
                    'details' => [],
                ];
                break;
        }

        if (Config::get('app.debug', false)) {
            $debug = [
                'class' => get_class($ex),
                'file' => $ex->getFile(),
                'line' => $ex->getLine(),
            ];
        }

        $response = [
            'success' => false,
            'error' => [
                'code' => $code,
                'message' => $message,
                'details' => $details,
            ],
        ];

        if (null !== $debug) {
            $response['debug'] = $debug;
        }

        return response()->json($response, $statusCode);
    }
}
