<?php

declare(strict_types=1);

namespace App\Traits;

use Carbon\Carbon;
use Exception;

trait CommonTrait
{
    // format string date to all type to Y-m-d
    public static function formatDate(string|null $date): string
    {
        if (null === $date) {
            return '';
        }

        $formats = ['d/m/y',  'd/m/Y', 'd-m-y', 'd-m-Y', 'Ymd'];
        foreach ($formats as $format) {
            try {
                return Carbon::createFromFormat($format, $date)->format('Y-m-d');
            } catch (Exception $e) {
                continue;
            }
        }
        return $date;
    }

    public static function parseDateYmdToDMY(string|null $data): string
    {
        if (null === $data || empty($data) || strlen($data) < 8) {
            return "";
        }

        try {
            return Carbon::createFromFormat('Ymd', $data)->format('d-M-Y');
        } catch (Exception $e) {
            return "";
        }
    }
    public function formatPrice(mixed $number): string
    {
        return number_format((float) $number, 0, '.', ',');
    }

    protected function getFlagReplace2Ja(string $flag): string
    {
        return match ($flag) {
            '2' => 'いいえ',
            '0' => 'いいえ',
            '1' => 'はい',
            default => '',
        };
    }

    /**
     * Serialize input data to handle special characters for SQL Server
     *
     * @param string $value
     * @return string
     */
    protected function serialize(string $value): string
    {
        // URL decode first
        $value = urldecode($value);

        // Remove null bytes and other control characters
        $value = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $value);

        // Trim whitespace
        $value = trim($value);
        
        // Check if already escaped to avoid double escaping
        if (strpos($value, '[[') !== false || strpos($value, '[]') !== false) {
            return $value;
        }

        // SQL Server LIKE special characters that need to be escaped: %, _, [, ], ^
        // Use square bracket escaping for SQL Server LIKE

        // Use a single pass approach to avoid double escaping
        $escaped = '';
        $length = strlen($value);
        
        for ($i = 0; $i < $length; $i++) {
            $char = $value[$i];

            switch ($char) {
                case '[':
                    $escaped .= '[[]';
                    break;
                case '%':
                    $escaped .= '[%]';
                    break;
                case '_':
                    $escaped .= '[_]';
                    break;
                case '^':
                    $escaped .= '[^]';
                    break;
                default:
                    $escaped .= $char;
            }
        }

        return $escaped;
    }

    /**
     * Serialize array of values with specified LIKE search fields
     *
     * @param array $array
     * @param array $likeFields Fields that need LIKE search (will be serialized)
     * @return array
     */
    protected function serializeArray(array $array, array $likeFields = []): array
    {
        $serialized = [];

        foreach ($array as $key => $value) {
            if (is_string($value)) {
                // Only serialize if the field is in likeFields
                if (in_array($key, $likeFields)) {
                    $serializedValue = $this->serialize($value);
                    if (!empty($serializedValue)) {
                        $serialized[$key] = $serializedValue;
                    }
                } else {
                    // For non-LIKE fields, just trim and clean
                    $value = urldecode($value);
                    $value = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $value);
                    $value = trim($value);
                    if (!empty($value)) {
                        $serialized[$key] = $value;
                    }
                }
            } elseif (is_array($value)) {
                $serialized[$key] = $this->serializeArray($value, $likeFields);
            } else {
                $serialized[$key] = $value;
            }
        }

        return $serialized;
    }
}
