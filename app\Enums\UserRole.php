<?php

declare(strict_types=1);

namespace App\Enums;

enum UserRole: int
{
    case other = 0;
    case admin = 1;
    case callCenter = 2;
    case transportUnit = 3;
    case nz = 4;
    case imageUploader = 5;
    case constructionUnit = 6;
    case constructionUnitAdditional = 7;
    case member = 9;
    case constructionUnitAutoLink = 60; // AutoLink
    case constructionUnitNagase = 70; // Nagase
    case sameIndustry = 100;

    public static function getPermissionAdmin(): array
    {
        return [
            self::admin->value,
            self::callCenter->value,
            self::transportUnit->value,
            self::nz->value,
            self::imageUploader->value,
            self::constructionUnit->value,
            self::constructionUnitAdditional->value,
            self::constructionUnitAutoLink->value,
            self::constructionUnitNagase->value,
        ];
    }

    public static function getPermissionAdminTransport(): array
    {
        return [
            self::admin->value,
            self::callCenter->value,
            self::transportUnit->value,
        ];
    }

    public static function getPermissionAdminTransportStore(): array
    {
        return [
            self::admin->value,
            self::transportUnit->value,
        ];
    }

    public static function getPermissionLoadingOrder(): array
    {
        return [
            self::admin->value,
        ];
    }

    public static function getPermissionAdminTransportFile(): array
    {
        return [
            self::admin->value,
            self::transportUnit->value,
        ];
    }
}
