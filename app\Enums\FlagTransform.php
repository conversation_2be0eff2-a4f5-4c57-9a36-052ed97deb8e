<?php

declare(strict_types=1);

namespace App\Enums;

enum FlagTransform: string
{
    case ON = '1';
    case OFF = '0';
    case ON_ALT = 'on';
    case OFF_ALT = 'off';
    case ON_3 = '3';

    public static function fromValue(string $value): ?self
    {
        return match ($value) {
            '1' => self::ON,
            '3' => self::ON_3,
            'on' => self::ON_ALT,
            '0' => self::OFF,
            'off' => self::OFF_ALT,
            default => null,
        };
    }

    public function toJapaneseLabel(): string
    {
        return match ($this) {
            self::ON, self::ON_ALT, self::ON_3 => 'あり',
            self::OFF, self::OFF_ALT => 'なし',
        };
    }
}
