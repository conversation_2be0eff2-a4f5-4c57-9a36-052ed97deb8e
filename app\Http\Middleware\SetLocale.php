<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Symfony\Component\HttpFoundation\Response;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  Closure(Request): (Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $locale = $request->segment(1);
        $supportedLocales = ['en'];

        if (in_array($locale, $supportedLocales)) {
            App::setLocale($locale);
        } elseif (null !== $locale && preg_match('/^[a-z]{2}$/', $locale)) {
            App::setLocale('ja');
            $segments = $request->segments();
            unset($segments[0]);

            $newPath = implode('/', $segments);

            $query = $request->getQueryString();
            $redirectUrl = '/' . $newPath . ($query ? '?' . $query : '');

            return redirect($redirectUrl);
        } else {
            App::setLocale('ja');
        }

        return $next($request);
    }
}
