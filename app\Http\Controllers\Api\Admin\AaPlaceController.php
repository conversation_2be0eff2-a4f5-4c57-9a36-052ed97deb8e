<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Services\AaPlace\AaPlaceService;
use Illuminate\Http\Request;
use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Response;

class AaPlaceController extends Controller
{
    private AaPlaceService $AaPlaceService;

    public function __construct(AaPlaceService $AaPlaceService)
    {
        $this->AaPlaceService = $AaPlaceService;
    }

    #[OA\Get(
        path: '/api/admin/aaplace/chiku-region',
        description: 'Retrieve auction places grouped by Japanese regions (Chiku). Returns places organized by region with their respective halls.',
        summary: 'Get auction places by region',
        security: [['access_token' => []]],
        tags: ['Auction Place'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful operation',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(
                            property: 'success',
                            type: 'boolean',
                            example: true
                        ),
                        new OA\Property(
                            property: 'data',
                            type: 'array',
                            items: new OA\Items(
                                properties: [
                                    new OA\Property(
                                        property: 'region_name',
                                        description: 'Japanese region name',
                                        type: 'string',
                                        example: '近畿'
                                    ),
                                    new OA\Property(
                                        property: 'prefecture_code',
                                        description: 'Prefecture code',
                                        type: 'string',
                                        example: '28'
                                    ),
                                    new OA\Property(
                                        property: 'halls',
                                        description: 'List of auction halls in this region',
                                        type: 'array',
                                        items: new OA\Items(
                                            properties: [
                                                new OA\Property(
                                                    property: 'id',
                                                    description: 'Hall ID',
                                                    type: 'integer',
                                                    example: 86
                                                ),
                                                new OA\Property(
                                                    property: 'name',
                                                    description: 'Hall name',
                                                    type: 'string',
                                                    example: 'ZIP大阪 / ZIP OSAKA'
                                                )
                                            ]
                                        ),
                                        example: [
                                            [
                                                'id' => 86,
                                                'name' => 'ZIP大阪 / ZIP OSAKA'
                                            ],
                                            [
                                                'id' => 87,
                                                'name' => 'TAA兵庫 / TAA HYOGO'
                                            ],
                                            [
                                                'id' => 88,
                                                'name' => 'いすゞima神戸 / ISUZU ima KOBE'
                                            ],
                                            [
                                                'id' => 89,
                                                'name' => 'HAA神戸 / HAA KOBE'
                                            ],
                                            [
                                                'id' => 90,
                                                'name' => 'ホンダAA関西 / HONDA AA KANSAI'
                                            ]
                                        ]
                                    )
                                ]
                            ),
                            example: [
                                [
                                    'region_name' => '近畿',
                                    'prefecture_code' => '28',
                                    'halls' => [
                                        [
                                            'id' => 86,
                                            'name' => 'ZIP大阪 / ZIP OSAKA'
                                        ],
                                        [
                                            'id' => 87,
                                            'name' => 'TAA兵庫 / TAA HYOGO'
                                        ],
                                        [
                                            'id' => 88,
                                            'name' => 'いすゞima神戸 / ISUZU ima KOBE'
                                        ],
                                        [
                                            'id' => 89,
                                            'name' => 'HAA神戸 / HAA KOBE'
                                        ],
                                        [
                                            'id' => 90,
                                            'name' => 'ホンダAA関西 / HONDA AA KANSAI'
                                        ]
                                    ]
                                ]
                            ]
                        ),
                        new OA\Property(
                            property: 'message',
                            type: 'string',
                            example: null,
                            nullable: true
                        )
                    ]
                )
            ),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function getAaPlaceChikuRegion(Request $request): Response
    {
        $data = $this->AaPlaceService->getAaPlaceChikuRegion($request->all());

        return $this->respond($data);
    }

    #[OA\Get(
        path: '/api/admin/aaplace/auction-dates',
        description: 'Retrieve auction dates for venue selection dropdown. Returns auction dates within a date range (31 days before to 4 days after current date) with place information.',
        summary: 'Get auction dates for venue selection',
        security: [['access_token' => []]],
        tags: ['Auction Place'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful operation',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(
                            property: 'success',
                            type: 'boolean',
                            example: true
                        ),
                        new OA\Property(
                            property: 'data',
                            type: 'array',
                            items: new OA\Items(
                                properties: [
                                    new OA\Property(
                                        property: 'tad_id',
                                        description: 'Auction date ID',
                                        type: 'integer',
                                        example: 123
                                    ),
                                    new OA\Property(
                                        property: 'open_date',
                                        description: 'Auction open date',
                                        type: 'string',
                                        format: 'date',
                                        example: '2024-01-20'
                                    ),
                                    new OA\Property(
                                        property: 'place_name',
                                        description: 'Auction place name',
                                        type: 'string',
                                        example: 'Tokyo Auction Place'
                                    )
                                ]
                            ),
                            example: [
                                [
                                    'tad_id' => 123,
                                    'open_date' => '2024-01-20',
                                    'place_name' => 'Tokyo Auction Place'
                                ],
                                [
                                    'tad_id' => 124,
                                    'open_date' => '2024-01-25',
                                    'place_name' => 'Osaka Auction Place'
                                ]
                            ]
                        ),
                        new OA\Property(
                            property: 'message',
                            type: 'string',
                            example: null,
                            nullable: true
                        )
                    ]
                )
            ),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function getAuctionDates(): Response
    {
        $data = $this->AaPlaceService->getAuctionDates();

        return $this->respond($data);
    }
}
