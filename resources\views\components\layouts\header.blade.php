@props([
    'isHome' => false,
])

@php
    $lang = app()->getLocale();
    $urlOld = env('APP_URL_OLD', '');
    $isJa = $lang === 'ja';

    $localizedRoute = fn ($route) => $isJa ? route($route) : route("localized.$route", ['locale' => 'en']);

    $urlServices = $localizedRoute('service');
    $urlHome = $localizedRoute('home');
    $urlSchedule = $localizedRoute('schedule');
    $urlYardList = $localizedRoute('yard-list');
    $urlCompany = $localizedRoute('company');
    $urlBlogList = $localizedRoute('blog-list');
    $urlFaq = $localizedRoute('question');
    $urlRecruit = $localizedRoute('recruit');
    $urlContact = $localizedRoute('contact');
@endphp

<form action="{{ route('language.switch') }}" method="get">
    <nav
        class="header fixed top-0 right-0 left-0 z-[1030] flex h-15 w-full max-w-480 flex-wrap items-center justify-between border-b border-[#dee2e6] bg-white/80 px-4 py-[0.7em] lg:h-20 lg:flex-nowrap lg:justify-start lg:pr-0 lg:pl-[10px]"
    >
        <input type="hidden" value="{{ $lang }}" name="locale" />
        @csrf
        <a
            href="{{ $urlHome }}"
            data-aos="zoom-in"
            class="aos-init mr-4 inline-block py-[0.3124rem]"
            data-aos-delay="{{ $isHome ? 2800 : 300 }}"
            data-aos-duration="300"
        >
            <img src="{{ asset('images/Autohub-logo-red.png') }}" alt="Autohub Logo" class="w-[170px]" />
        </a>
        <button
            id="navbar-button"
            class="cursor-pointer rounded border border-[#bd0a0a] bg-[#bd0a0a] px-3 py-1 lg:hidden"
            type="button"
        >
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="z-5 hidden flex-1 basis-auto items-center lg:flex">
            <ul class="text-15 mx-auto !mb-0 flex h-full items-center justify-center gap-2 font-bold text-[#343a40]">
                <li
                    class="aos-init flex h-20 items-center justify-center"
                    data-aos="flip-left"
                    data-aos-delay="{{ $isHome ? 1500 : 0 }}"
                    data-aos-duration="2000"
                >
                    <a href="{{ $urlServices }}" class="px-[0.7rem] py-2 text-center">
                        {!! __('layouts.services') !!}
                    </a>
                </li>
                <li
                    class="aos-init flex h-20 items-center justify-center"
                    data-aos="flip-left"
                    data-aos-delay="{{ $isHome ? 1700 : 0 }}"
                    data-aos-duration="2000"
                >
                    <a href="{{ $urlSchedule }}" class="w-fit px-[0.7rem] py-2 text-center">
                        {!! __('layouts.sailing_schedule') !!}
                    </a>
                </li>
                <li
                    class="aos-init flex h-20 items-center justify-center"
                    data-aos="flip-left"
                    data-aos-delay="{{ $isHome ? 1900 : 0 }}"
                    data-aos-duration="2000"
                >
                    <a href="{{ $urlYardList }}" class="w-fit px-[0.7rem] py-2 text-center">
                        {!! __('layouts.stock_yards') !!}
                    </a>
                </li>
                <li
                    class="aos-init flex h-20 items-center justify-center"
                    data-aos="flip-left"
                    data-aos-delay="{{ $isHome ? 2100 : 0 }}"
                    data-aos-duration="2000"
                >
                    <a href="{{ $urlCompany }}" class="w-fit px-[0.7rem] py-2 text-center">
                        {{ __('layouts.aboutUs') }}
                    </a>
                </li>
                <li
                    class="aos-init flex h-20 items-center justify-center"
                    data-aos="flip-left"
                    data-aos-delay="{{ $isHome ? 2300 : 0 }}"
                    data-aos-duration="2000"
                >
                    <a href="{{ $urlBlogList }}" class="w-fit px-[0.7rem] py-2 text-center">
                        {{ __('layouts.blog') }}
                    </a>
                </li>
                <li
                    class="aos-init flex h-20 items-center justify-center"
                    data-aos="flip-left"
                    data-aos-delay="{{ $isHome ? 2500 : 0 }}"
                    data-aos-duration="2000"
                >
                    <a href="{{ $urlFaq }}" class="w-fit px-[0.7rem] py-2 text-center">
                        {{ __('layouts.faq') }}
                    </a>
                </li>
                <li
                    class="aos-init flex h-20 items-center justify-center"
                    data-aos="flip-left"
                    data-aos-delay="{{ $isHome ? 2700 : 0 }}"
                    data-aos-duration="2000"
                >
                    <a href="{{ $urlRecruit }}" class="w-fit px-[0.7rem] py-2 text-center">
                        {{ __('layouts.recruit') }}
                    </a>
                </li>
            </ul>
            <ul class="!mb-0 flex h-20">
                <li
                    class="aos-init mr-4 h-full w-[135px] py-3"
                    data-aos="zoom-in"
                    data-aos-delay="300"
                    data-aos-duration="300"
                >
                    <a
                        class="nav-link-contact group flex h-full w-full items-center justify-center gap-1 rounded-[100vh] border-2 border-red-500 bg-white p-2 text-red-500 transition duration-200 ease-in-out"
                        href="{{ $urlContact }}"
                        target="_blank"
                    >
                        <span
                            class="text-base leading-normal font-bold transition duration-200 ease-in-out group-hover:text-[110%]"
                        >
                            {{ __('layouts.contact') }}
                        </span>
                    </a>
                </li>
                <li class="aos-init h-full w-[135px]" data-aos="zoom-in" data-aos-delay="300" data-aos-duration="300">
                    <a
                        class="flex h-full flex-col items-center justify-center gap-[3px] bg-red-500 p-2 text-white"
                        href="{{ $urlOld }}/hn/login/index.asp?lan={{ $lang }}"
                        target="_blank"
                    >
                        <span class="text-15 font-bold{{ $isJa ? ' tracking-[5px]' : '' }} leading-[1.2em]">
                            {{ __('layouts.signUp') }}
                        </span>
                        <span class="text-11 font-bold tracking-[3px]">-HUBNET-</span>
                    </a>
                </li>
                <li class="aos-init h-full w-[120px]" data-aos="zoom-in" data-aos-delay="300" data-aos-duration="300">
                    <a
                        class="flex h-full flex-col items-center justify-center gap-[3px] bg-black p-2 text-[#f8c239]"
                        href="https://pas-japan.com/{{ $lang === 'en' ? 'en' : '' }}"
                        target="_blank"
                    >
                        <span class="text-15 font-bold{{ $isJa ? ' tracking-[5px]' : '' }} leading-[1.2em]">
                            {{ __('layouts.shipFast') }}
                        </span>
                        <span class="text-11 font-bold tracking-[3px] italic">-PAS-</span>
                    </a>
                </li>
                <li class="aos-init h-full w-[90px]" data-aos="zoom-in" data-aos-delay="300" data-aos-duration="300">
                    <button
                        type="submit"
                        class="flex h-full w-full cursor-pointer flex-col items-center justify-center bg-[#343a40] p-2 font-bold text-white"
                    >
                        <span>
                            {{ $isJa ? __('language.ja') : __('language.en') }}
                        </span>
                        <span class="block">
                            <i class="fas fa-arrows-alt-h text-xs"></i>
                            <span class="text-xs">
                                {{ $isJa ? __('language.en') : __('language.ja') }}
                            </span>
                        </span>
                    </button>
                </li>
            </ul>
        </div>
        <ul id="headerDropdown" class="navbar-dropdown !m-0 hidden cursor-pointer lg:!hidden">
            <li><a href="{{ $urlServices }}" class="navbar-dropdown_item">{!! __('layouts.services') !!}</a></li>
            <li><a href="{{ $urlSchedule }}" class="navbar-dropdown_item">{{ __('layouts.sailingSchedule') }}</a></li>
            <li><a href="{{ $urlYardList }}" class="navbar-dropdown_item">{{ __('layouts.stockYards') }}</a></li>
            <li><a href="{{ $urlCompany }}" class="navbar-dropdown_item">{{ __('layouts.aboutUs') }}</a></li>
            <li><a href="{{ $urlBlogList }}" class="navbar-dropdown_item">{{ __('layouts.blog') }}</a></li>
            <li><a href="{{ $urlFaq }}" class="navbar-dropdown_item">{{ __('layouts.faq') }}</a></li>
            <li><a href="{{ $urlRecruit }}" class="navbar-dropdown_item">{{ __('layouts.recruit') }}</a></li>
            <li class="relative mx-[10px] bg-white px-[10px] py-2">
                <a
                    class="nav-link-contact-min nav-link-contact group flex min-h-[50px] w-full items-center justify-center gap-1 rounded-[100vh] border-2 border-red-500 bg-white/80 px-[10px] py-2 text-red-500 transition duration-200 ease-in-out"
                    href="{{ $urlContact }}"
                    target="_blank"
                >
                    <span
                        class="text-base leading-normal font-bold transition duration-200 ease-in-out group-hover:text-[110%]"
                    >
                        {{ __('layouts.contact') }}
                    </span>
                </a>
            </li>
            <li class="relative mx-[10px] bg-white px-[10px] py-2">
                <a
                    class="nav-link-contact-min flex min-h-[50px] w-full flex-col items-center justify-center rounded-[100vh] border-b border-gray-50 bg-red-500 px-[10px] py-2 text-center font-bold text-white"
                    href="{{ $urlOld }}/hn/login/index.asp?lan={{ $lang }}"
                    target="_blank"
                >
                    <span class="text-base">{{ __('layouts.signUp') }}</span>
                    <span>-HUBNET-</span>
                </a>
            </li>
            <li class="relative mx-[10px] bg-white px-[10px] py-2">
                <a
                    class="nav-link-contact-min flex min-h-[50px] w-full flex-col items-center justify-center rounded-[100vh] border-b border-gray-50 bg-black px-[10px] py-2 text-center font-bold text-[#f8c239]"
                    href="https://pas-japan.com/{{ $lang === 'en' ? 'en' : '' }}"
                    target="_blank"
                >
                    <span class="text-base">{{ __('layouts.shipFast') }}</span>
                    <span>-PAS-</span>
                </a>
            </li>
            <li class="relative mx-[10px] bg-white px-[10px] py-2">
                <button class="w-full cursor-pointer text-base font-bold text-[#343a40]" type="submit">
                    <span class="block">
                        {{ $isJa ? __('language.ja') : __('language.en') }}
                    </span>
                    <span class="block">
                        <i class="fas fa-arrows-alt-h text-xs"></i>
                        {{ $isJa ? __('language.en') : __('language.ja') }}
                    </span>
                    <a
                        data-aos="zoom-in"
                        class="aos-init aos-animate"
                        data-aos-delay="300"
                        data-aos-duration="300"
                        href="https://www.cssdesignawards.com/sites/autohub/38100/"
                        target="_blank"
                    >
                        <img
                            src="{{ asset('images/home/<USER>') }}"
                            alt="CSSDA Special Kudos Award monogram"
                            style="width: 40px; position: absolute; top: 13px; right: 55px"
                        />
                    </a>
                    <a
                        data-aos="zoom-in"
                        class="aos-init aos-animate"
                        data-aos-delay="300"
                        data-aos-duration="300"
                        href="https://www.csswinner.com/details/autohub-coltd/14925"
                        target="_blank"
                    >
                        <img
                            src="{{ asset('images/home/<USER>') }}"
                            alt="CSSW STAR badge"
                            class="absolute top-[13px] right-[10px] w-10"
                        />
                    </a>
                </button>
            </li>
        </ul>
    </nav>
</form>

@once
    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                var btn = document.getElementById('navbar-button')
                var nav = document.getElementById('headerDropdown')
                if (btn && nav) {
                    btn.addEventListener('click', function () {
                        if (nav.classList.contains('hidden')) {
                            nav.classList.remove('hidden')
                            nav.classList.add('flex')
                        } else {
                            nav.classList.add('hidden')
                            nav.classList.remove('flex')
                        }
                    })
                }
            })
        </script>
    @endpush
@endonce
