<?php

declare(strict_types=1);

namespace App\Services\User;

use App\Repositories\sqlServer\UserRepository;

class UserService
{
    public function __construct(private UserRepository $userRepository)
    {
    }

    public function getDataDropdown(array $params, array $columns = [])
    {
        $columns = ['id', 'cus_Name_JP'];
        $params = array_merge($params, [
            'transportation_member' => 1,
            'sort' => 'id|asc',
        ]);
        return $this->userRepository->getList($params, [], $columns);
    }
}
