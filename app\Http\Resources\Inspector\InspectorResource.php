<?php

declare(strict_types=1);

namespace App\Http\Resources\Inspector;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class InspectorResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'name_kana' => $this->name_kana,
            'tel' => $this->tel,
            'mail' => $this->mail,
            'sort' => $this->sort,
            'del_flg' => (bool) $this->del_flg,
            'reg_date' => $this->reg_date?->format('Y-m-d H:i:s'),
            'up_date' => $this->up_date?->format('Y-m-d H:i:s'),
            'up_owner' => $this->up_owner,
        ];
    }
}
