@php
    use Illuminate\Support\Facades\App;
    $isJa = 'ja' === App::getLocale();
@endphp

<div
    class="fixed bottom-[15px] left-0 z-99 w-[300px] sm:top-[100px] sm:right-[15px] sm:bottom-auto sm:left-auto"
    id="hubnet-btn"
>
    <a href="{{ $isJa ? route('introduction') : route('localized.introduction', ['locale' => 'en']) }}" class="group">
        <img
            src="{{ asset($isJa ? 'images/top_hubnet_btn.svg' : 'images/top_hubnet_btn_en.svg') }}"
            alt="HUBNET"
            style="filter: drop-shadow(2px 3px 7px black)"
            class="2md:w-[250px] absolute bottom-0 left-[15px] inline-block w-[180px] group-hover:hidden sm:right-0 sm:bottom-auto sm:left-auto sm:w-[200px] md:w-[230px] lg:w-[300px]"
        />
        <img
            src="{{ asset($isJa ? 'images/top_hubnet_btn_hover.svg' : 'images/top_hubnet_btn_en_hover.svg') }}"
            alt="HUBNET"
            style="filter: drop-shadow(2px 3px 7px black)"
            class="2md:w-[250px] absolute bottom-0 left-[15px] hidden w-[180px] group-hover:inline-block sm:right-0 sm:bottom-auto sm:left-auto sm:w-[200px] md:w-[230px] lg:w-[300px]"
        />
    </a>
    <img
        id="add_line_close"
        class="z-10 hidden w-[50px] md:absolute md:top-[15px] md:right-[32px] md:block"
        src="{{ asset('images/home/<USER>') }}"
        alt=""
    />
    <img
        id="add_line_close_768"
        class="absolute bottom-[100px] left-[150px] z-10 block w-[35px] sm:top-[15px] sm:right-[3px] sm:bottom-auto sm:left-auto md:hidden"
        src="{{ asset('images/home/<USER>') }}"
        alt=""
    />
</div>

<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', () => {
        $('#add_line_close').click(function () {
            $('#hubnet-btn').fadeOut()
        })
        $('#add_line_close_768').click(function () {
            $('#hubnet-btn').fadeOut()
        })
    })
</script>
