<?php

declare(strict_types=1);

namespace App\Services\SaleStaff;

use App\Repositories\sqlServer\SaleStaffRepositories;

class SaleStaffService
{
    private SaleStaffRepositories $saleStaffRepositories;

    public function __construct(SaleStaffRepositories $saleStaffRepositories)
    {
        $this->saleStaffRepositories = $saleStaffRepositories;
    }

    public function getSaleStaffActive()
    {
        return $this->saleStaffRepositories->getSaleStaffActive();
    }

    public function getSaleStaffOrderedByEnglishName()
    {
        return $this->saleStaffRepositories->getSaleStaffOrderedByEnglishName();
    }
}
