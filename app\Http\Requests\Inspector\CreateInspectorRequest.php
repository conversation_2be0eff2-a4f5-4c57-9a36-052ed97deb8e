<?php

declare(strict_types=1);

namespace App\Http\Requests\Inspector;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateInspectorRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $inspectorId = $this->route('id');
        
        return [
            'name' => [
                'required', 
                'string', 
                'max:50', 
                Rule::unique('t_inspector', 'name')
                    ->ignore($inspectorId, 'id')
                    ->where('del_flg', 0)
            ],
            'name_kana' => ['required', 'string', 'max:50'],
            'tel' => ['required', 'string', 'max:20', 'regex:/^[0-9\-]+$/'],
            'mail' => ['required', 'email', 'max:100'],
        ];
    }

    public function messages(): array
    {
        return [
            'name.unique' => '既に同じ名前の検査員が存在します',
            'name.required' => '検査員名は必須です',
            'name_kana.required' => 'カナ名は必須です',
            'tel.required' => '電話番号は必須です',
            'tel.regex' => '電話番号の形式が正しくありません',
            'mail.email' => 'メールアドレスの形式が正しくありません',
            'mail.required' => 'メールアドレスは必須です',
        ];
    }
}
