<?php

declare(strict_types=1);

namespace App\Http\Requests\Transport;

use App\Enums\Transport\DatePaymentTime;
use App\Enums\Transport\Flag;
use App\Enums\Transport\TransIdDefault;
use App\Enums\UserRole;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreTransportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        $rules = [
            'odr_customer_id' => ['required', 'numeric',
                Rule::exists('m_customer', 'id')->where('del_flg', 0)->whereIn('hn_cd', [UserRole::admin->value, UserRole::member->value, UserRole::sameIndustry->value])
            ],
            'car_no' => ['required', 'string', 'max:25'],
            'car_name' => ['required', 'string', 'max:50'],
            'plate_no' => ['nullable', 'string', 'max:20'],
            'note' => ['nullable', 'string', 'max:2000'],
            'fr_place_id' => ['nullable'],
            'to_place_id' => ['nullable'],
            'to_etc_place_id' => ['nullable'],

            'date_of_payment' => ['required', 'date-format:Y-m-d'],
            'auction_date' => ['nullable', 'date-format:Y-m-d'],

            'pos_no' => ['nullable', 'string', 'max:10'],
            'aa_no' => ['nullable', 'string', 'max:10'],
            'agent_ref_no' => ['nullable', 'string', 'max:15'],

            'undrivable_flg' => ['nullable', 'in:0,1'],
            'tall_flg' => ['nullable', 'in:0,1'],
            'lowdown_flg' => ['nullable', 'in:0,1'],
            'long_flg' => ['nullable', 'in:0,1'],
            'old_flg' => ['nullable', 'in:0,1'],
            'luxury_flg' => ['nullable', 'in:0,1'],
            'luxury_flg_insrance' => ['nullable', Rule::in(Flag::getAllValues())],
            'other_flg' => ['nullable', 'in:0,1'],
            'other_flg_txt' => ['nullable', 'string', 'max:255'],
            'tick_no_flg' => ['nullable', 'in:0,1'],
            'optn1_flg' => ['nullable', 'in:0,1'],
            'plate_cut_flg' => ['nullable', Rule::in(Flag::getAllValues())],

            'pos_chk' => ['nullable', 'string'],
            'pos_chk_text' => ['nullable', 'string', 'max:255'],
            'date_of_payment_time' => ['nullable', Rule::in(DatePaymentTime::getAllValues())],
            'auction_chk' => ['nullable', 'in:0,1'],
            'auction_txt' => ['nullable', 'string', 'max:255'],

            // country, port, area
            'country_cd' => ['nullable', 'string'],
            'country_area' => ['nullable', 'string'],
            'country_free' => ['nullable', 'string'],
            'port_cd' => ['nullable', 'string', 'max:10'],

            // sender info
            'plate_send_name' => ['nullable', 'string', 'max:50'],
            'plate_send_zipcd' => ['nullable', 'string', 'max:10'],
            'plate_send_address' => ['nullable', 'string', 'max:255'],
            'plate_send_tel' => ['nullable', 'string', 'max:20'],

            'auc_name' => ['nullable', 'string', 'max:200'],
            'auc_addr' => ['nullable', 'string', 'max:255'],
            'auc_tel' => ['nullable', 'string', 'max:15'],
        ];

        // Rule case fr_aa_place_id is not empty
        if ($this->input('fr_aa_place_id') && '0' != $this->input('fr_aa_place_id')) {
            $rules = array_merge($rules, [
                'fr_aa_place_id' => ['required', 'exists:t_aa_place,id,del_flg,0'],
                'fr_name' => ['nullable'],
                'fr_addr' => ['nullable'],
                'fr_tel1' => ['nullable'],
                'fr_tel2' => ['nullable'],
                'fr_tel3' => ['nullable'],
                'pos_no' => ['required', 'string', 'max:10'],
                'aa_no' => ['required', 'string', 'max:10'],
            ]);
        } else {
            $rules = array_merge($rules, [
                'fr_name' => ['required', 'string', 'max:50'],
                'fr_addr' => ['required', 'string', 'max:50'],
                'fr_tel1' => ['required', 'numeric'],
                'fr_tel2' => ['required', 'numeric'],
                'fr_tel3' => ['required', 'numeric'],
            ]);

            // If optn1_flg is 1 but fr_aa_place_id is empty, return error
            if ('1' == $this->input('optn1_flg')) {
                $rules['optn1_flg'] = ['nullable'];
            }
        }

        // Rule case to_aa_place_id is not empty
        if ($this->input('to_aa_place_id') && '0' != $this->input('to_aa_place_id')) {
            $rules = array_merge($rules, [
                'to_aa_place_id' => ['required', 'exists:t_aa_place,id,del_flg,0'],
                'to_name' => ['nullable'],
                'to_addr' => ['nullable'],
                'to_tel1' => ['nullable'],
                'to_tel2' => ['nullable'],
                'to_tel3' => ['nullable'],
            ]);
        } else {
            $rules = array_merge($rules, [
                'to_name' => ['required', 'string', 'max:50'],
                'to_addr' => ['required', 'string', 'max:50'],
                'to_tel1' => ['required', 'numeric'],
                'to_tel2' => ['required', 'numeric'],
                'to_tel3' => ['required', 'numeric'],
            ]);
        }

        if (Auth::check() && UserRole::admin->value == Auth::user()->hn_cd) {
            $rules = array_merge($rules, [
                'm_trans_id' => ['required', 'not_in:999', Rule::in([
                    TransIdDefault::SHIPPING_EAST_AND_WEST->value,
                    TransIdDefault::TARGET->value,
                    TransIdDefault::J_BRING->value,
                    TransIdDefault::EIKO_SHOUNEN->value,
                ])],
                'inp_ah_name' => ['required', 'string', 'max:50']
            ]);
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'odr_customer_id.exists' => '依頼会員IDを正しく入力してください',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function validationData(): array
    {
        $data = parent::validationData();

        if (!empty($data['fr_tel1']) && !empty($data['fr_tel2']) && !empty($data['fr_tel3'])) {
            $fr_tel = $data['fr_tel1'] . $data['fr_tel2'] . $data['fr_tel3'];
            if (strlen($fr_tel) > 12) {
                $this->getValidatorInstance()->errors()->add('fr_tel1', '引取先電話番号を正しく入力してください。');
            }
        }

        if (!empty($data['to_tel1']) && !empty($data['to_tel2']) && !empty($data['to_tel3'])) {
            $to_tel = $data['to_tel1'] . $data['to_tel2'] . $data['to_tel3'];
            if (strlen($to_tel) > 12) {
                $this->getValidatorInstance()->errors()->add('to_tel1', '搬入先電話番号を正しく入力してください');
            }
        }

        return $data;
    }
}
