body {
    background:#fff;
    height: 100vh;
    color: #333;
}
.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 500;
}
#question01,#question02{
    width:100%;
}
.back_top_btn {
    position: fixed;
    left: 4%;
    bottom: 100px;
    z-index: 20;
    width: 115px;
    cursor: pointer;
    display: none;
}
.back_top_btn img {
    width: 100%;
    height: auto;
}
.hubnet_btn_wrap{
    display:flex;
    align-items:center;
}
.hubnet_btn{
    padding: 15px 20px;
    font-size: 20px;
    width: 45%;
    background: linear-gradient(-10deg, #dc3545,#f93b4d,#dc3545);
    color: #fff;
    border: solid 2px #dc3545;
    border-radius: 0.25rem;
    font-weight: bold;
    margin:0 auto;
    text-align:center;
}
.hubnet_btn:hover{
    text-decoration:none;
    color: #fff;
    border: solid 2px #f93b4d;
    background: linear-gradient(10deg, #dc3545,#f93b4d,#dc3545);
    opacity:.9;
}
.hubnet_link{
    background: linear-gradient(-10deg, #dc3545,#f93b4d,#dc3545);
    color: #fff;
    border: solid 2px #dc3545;
    position: fixed;
    right: 0px;
    bottom:100px;
    z-index: 20;
    text-decoration: none;
    padding: 20px 30px 15px 40px;
    border-radius: 15px 0 0 15px;
    box-shadow: -5px 5px 10px gray;
    width: 350px;
    display:none;
}
.hubnet_link.fadeRight{
    animation: fade-right .5s 1, click-me 2s infinite alternate;
}
@keyframes click-me {
    0% {
        transform: scale(1);
        right: 0px;
        background-position:0% 0%;
        background: linear-gradient(-10deg, #dc3545,#f93b4d,#dc3545);
    }
    10%{
        transform: scale(1.05);
        right: 10px;
        background-position:50% 50%;
        background: linear-gradient(-10deg, #e63d4e,#ff4558,#ea3f50);
        opacity:.9;
    }
    20%,100% {
        transform: scale(1);
        right: 0px;
        background-position:0% 0%;
        background: linear-gradient(-10deg, #dc3545,#f93b4d,#dc3545);
    }
}
@keyframes fade-right {
    0% {
        right: -400px;
        opacity:0;
    }
    100% {
        right: 0px;
        opacity:1;
    }
}
.hubnet_link.fadeLeft{
    animation-name: fade-left;
    animation-duration:.5s;
    animation-iteration-count:1;
}
@keyframes fade-left {
    0% {
        right: 0px;
        opacity:1;
    }
    100% {
        right: -400px;
        opacity:0;
    }
}
.hubnet_link:hover{
    opacity:.9;
    text-decoration:none;
    border: solid 2px #f93b4d;
    background: linear-gradient(10deg, #dc3545,#f93b4d,#dc3545);
}
.hubnet_link:hover h1, .hubnet_link:hover p{
    text-decoration:none;
    color:#fff;
}
.hubnet_link h1{
    font-size:45px;
}
.hubnet_link hr{
    border: solid 1px #fff;
    margin: 5px -10px;
}
.hubnet_link p{
    font-size:20px;
    margin-bottom: 1rem;
}
h2.title{
    font-size:40px;
    text-align:center;
}
p.title_message{
    font-size:18px;
    text-align:center;
    margin-top:40px;
    margin-bottom:40px;
    padding:0 5em;
}
.q_comments{
    position:absolute;
    top:150px;
    font-size:22px;
    text-align:center;
    width:100%;
    display:inline-block;
    font-weight : bold;
    color:#ffffff;
    text-shadow:1px 1px 3px #777, -1px 1px 3px #777, 1px -1px 3px #777, -1px -1px 3px #777;
    transform: translate(0)scale(1)rotate(-7deg)!important;
}
.q_comments span{
    font-size:38px;
    color:#f36767;
    text-shadow:none;
    margin-left:5px;
    background: linear-gradient(transparent 70%, yellow 30%);
}
.question-container {
    box-shadow: #999 0 0 2px;
    background: #f2faff;
    padding: 4em 0;
    margin: 5em auto;
    position: relative;
}
.question-container02 h3 {
    width: 130%;
}
.question-row {
    padding: 3em 0;
    margin: 0 2em;
    border-bottom: #ccc solid 1px;
}
.question-row:last-child{
    border-bottom: none;
}
.question-row ul {
    padding: 1em 0 0;
    font-size: 13px;
    transform: translate(.5em,0);
}
.question-row ul li {
    padding: .4em 0;
    border-bottom: #ccc dashed 1px;
}
.q-wrap-left {
    border-right: #ccc solid 1px;
}
/* チェックボックス */
input[type="checkbox"] {
    display: none;
}
input[type="checkbox"] ~ label p {
    display: none;
}
label {
    margin-bottom: 0;
    width: 100%;
}
li input[type="checkbox"] ~ label i {
    display: none;
}
/* グラフの大きさ */
.question-wrap1,.question-wrap2,.question-wrap3,.question-wrap5,.question-wrap6 {
    width: 100%;
    height: 250px;
}
.question-wrap4 {
    width: 100%;
    height: 270px;
}
.question-wrap7 {
    width: 100%;
    height: 450px;
}
.redpin {
    width: 50px;
    position: absolute;
    left: 48%;
    top: -30px;
}
.redpin img {
    width: 100%;
}
/* サービス紹介 */
.sm-br {
    display: none;
}
.flex {
    display: flex;
}
.container-width img {
    max-width: 100%;
    display: block;
}
.question-container02 {
    width: 70%;
    /* height: 100%; */
    margin: 5em auto 0 auto;
    padding: 0;
    max-width: 900px;
}
.question-container02 .question-container {
    padding: 4em 0 2em;
}
.question-container02 .question-container:first-of-type {
    margin: 0 auto;
}
.question-container02:first-of-type {
    margin: 10em auto 4em auto;
}
.screen02 {
    margin: 0em 0 10em;
}
.screen02 .question-container {
    background: #fff;
    border: double 20px #fcf4c4;
}
.screen02 h2{
    font-size: 35px;
    text-align: center;
    margin-bottom: 30px;
}
.screen02 h3 {
    font-size: 35px;
    text-align: center;
    text-shadow: #333 1px 1px 1px;
    /* transform: rotate(-13deg) translate(3em, -5em); */
    line-height: 2em;
}
.screen02 h3 span:first-of-type,
.screen02 h3 span:last-of-type {
    font-size: 55px;
}
.screen02 .title-wrap {
    margin: 0 1em 1em 4em;

}
.screen02 h4 {
    font-size: 25px;
    font-weight: bold;
    position: relative;
    z-index: 1;
}
.title-wrap p {
    position: relative;
    z-index: 2;
    margin: 0 0 0 2.8em;
    font-size: 15px;
}
.screen02 h4 span {
    font-size: 45px;
    margin: 0 .1em 0;
}
.screen02 h4::after {
    content: "";
    display: block;
    width: 6em;
    height: 6em;
    background: #fcf4c4;
    border-radius: 50%;
    position: absolute;
    top: -20px;
    left: -50px;
    z-index: -1;
}
.trouble-wrap {
    margin: -2em 1em 2em;
    align-items: center;
}
.trouble-left {
    margin: 1em 1em 1em 2em;
    position: relative;
    z-index: 3;
}
.trouble-right {
    margin: 2em 0 0;
    width: 100%;
}
.trouble-right p:first-of-type{
    text-align: center;
    font-weight: bold;
    text-shadow: #fcf4c4 1px 1px 5px;
    font-size: 22px;
    margin: 0 1em 1em 0;
}
.trouble-right li {
    margin: .5em 0;
    font-size: 15px;
}
.plan-wrap {
    justify-content: space-around;
}
.plan-wrap section {
    width: 31%;
}
.plan-wrap section h5{
    font-size: 15px;
    text-align: center;
    font-weight: bold;
}
.plan-wrap section img {
    max-width: 100%;
    width: 100%;
    margin: 0 0 1em;
}
.plan-wrap section p {
    font-size: 13px;
    color: #777;
    margin: 0;
}
.rikusou-img img {
    display: block;
    margin: 2em auto;
    max-width: 100%;
    width: auto;
}
.rikusou-img img:nth-of-type(2) {
    display: none;
}
.trouble-wrap02 {
    margin: 0 1em;
}
.trouble-right02 ul{
    font-size: 13px;
    position: relative;
    z-index: 10;
}
.trouble-right02 p:first-of-type{
    font-weight: bold;
    text-shadow: #fcf4c4 1px 1px 5px;
    font-size: 22px;
    margin: 0 0 .5em;
}
.trouble-right02 label{
    display: block;
}
/* 修理 */
.syuri-wrap {
    background-image: url(img/syuri_03.png);
    background-position: top 50px right 20px;
    background-repeat: no-repeat;
    background-size: 25%;
    margin: 2.5em 0 0;
    background-color:rgba(255,255,255,0.7);
    background-blend-mode:lighten;
    position: relative;
}
/* HUBNET紹介 */
.question-container.hubnet-wrap {
    background-image: linear-gradient(to top, #e6e9f0 0%, #eef1f5 100%);
    border: #fff solid 3px;
    margin: 5em auto 10em auto;
}
.hubnet-wrap .trouble-right {
    margin: 2em 0 0 1em;
}
.hubnet-wrap .trouble-right p {
    text-align: left;
    margin: 0 1em 0 0;
}
.hubnet-img img {
    margin: 0 auto;
}
/* タイトル */
.hubnet-wrap p.title_message {
    margin-bottom: 1em;
}
.menu-wrap {
    width: 50%;
    padding: 1em;
}
.menu-title {
    align-items: center;
    margin: 1em 1.7em;
}
.menu-title p {
    color: #22B573;
    font-weight: bold;
    font-size: 20px;
    margin: 0 0 0 .5em;
}
.menu-wrap:nth-of-type(2) .menu-title p{
    color: #27B2C5;
}
/* タイルメニュー */
.menu-container {
    margin: 1em;
}
.menu-wrap > p {
    font-size: 13px;
    min-height: 5em;
    margin: 1.5em;
    border-bottom: #fff 1px solid;
}
/* 吹き出し */
.menu-text {
    width: 50%;
    position: relative;
}
.menu-text p {
    position: relative;
    z-index: 5;
}
.menu-wrap .flex {
    align-items: center;
}
.menu-text {
    position: relative;
    z-index: 5;
}
.menu-text div {
    position: absolute;
    width: 100%;
    font-size: 15px;
    position: relative;
    padding: 10px;
    color: #555;
    margin: 15px;
    background-color: #fff;
    left: -50px;
    border-radius: 10px;
    border: #aaa solid 1px;
    box-shadow: gray 2px 2px 5px;
    z-index: auto;
}
.menu-text div::before {
    content: "";
    position: absolute;
    width: 21px;
    top: 32%;
    left: -6px;
    height: 20px;
    background: #aaa;
    transform: rotate(45deg);
    border: #aaa solid 1px;
    z-index: -1;
    box-shadow: gray 1px 0px 3px;
}
.menu-text div::after {
    content: "";
    position: absolute;
    width: 21px;
    top: 30%;
    left: -4px;
    height: 21px;
    background: #fff;
    transform: rotate(45deg);
    z-index: 1;
}
.menu-text div p {
    margin: 0;
    padding: 0;
}
/*  */
.tileMenu { width: 50%; display: block;}
.tileMenu .box3 a {
    width: 100%;
    display: block;
}
.tileMenu .box3 p {
    margin: 0;
    padding: 0 .5em 1em .5em;
}
.tileMenu div {
    margin: 0 0 10px; background: #FFF;position: relative;
    -webkit-box-shadow: 2px 2px 2px rgba(0,0,0,0.15); -moz-box-shadow: 2px 2px 2px rgba(0,0,0,0.15); box-shadow: 2px 2px 2px rgba(0,0,0,0.15);
}
/* ラベル青 */
.tileMenu div a { display: block; text-decoration: none; position: relative;}
.tileMenu div:after {
    content: ''; width: 20px; height: 30px;	position: absolute; top: 0; left: 0;
    background: url(img/label_style06.png) no-repeat;
    -webkit-background-size: 20px 30px; -moz-background-size: 20px 30px; background-size: 20px 30px;
}
/* ラベル黄緑 */
.menu-wrap:first-of-type .tileMenu div:after {
    content: ''; width: 20px; height: 30px;	position: absolute; top: 0; left: 0;
    background: url(img/label_style05.png) no-repeat;
    -webkit-background-size: 20px 30px; -moz-background-size: 20px 30px; background-size: 20px 30px;
}
/* ラベル緑 */
.menu-wrap:first-of-type .flex:nth-of-type(2) .tileMenu div:after {
    content: ''; width: 20px; height: 30px;	position: absolute; top: 0; left: 0;
    background: url(img/label_style04.png) no-repeat;
    -webkit-background-size: 20px 30px; -moz-background-size: 20px 30px; background-size: 20px 30px;
}
.tileMenu .box3 { width:70%; text-align: center; min-height: 135px; display: flex; margin: .5em auto;flex-direction: column; justify-content: center;}
.tileMenu .box3 img { width: 60px; margin: 10px auto 0;}
.tileMenu .box3 p { color: #EF4146; font-size: 14px; font-weight: bold;}
.tileMenu .box3 p > span.sub { display: block; font-size: 12px; line-height: 100%;}
.tileMenu .box3 p > span.text { display: block; padding: 5px 20px 0; font-size: 10px; line-height: 140%; color: #666; font-weight: normal; text-align: center;}
.tileMenu .box3 p > span.text span { color: red;}
.container-width {
	margin: 45px auto 0px;
}
#detail_open_btn01{
    border-radius: 0;box-shadow: none;border: none;width: 200px;height: 48px;padding-top: 12px;font-weight: bold;font-size: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    background: repeating-linear-gradient(45deg, #ffffff, #ffffff 3px, #e7e7e7 3px, #e7e7e7 30px);
    color: #333;
    position: relative;
    border-radius: 25px;
    margin: 1em 1em 0 auto;
}
.question-container02:first-of-type #detail_open_btn01 {
    margin: 3em 1em 0 auto;
}
.syuri-wrap #detail_open_btn01 {
    margin: 0 1em 0 auto;
}
#detail_open_btn01 span {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 48px;
    background: #fff;
    border: 1px solid #000;
    box-sizing: border-box;
    position: absolute;
    top: -6px;
    left: -6px;
    transition-duration: 0.2s;
    border-radius: 25px;
}
#detail_open_btn01:hover span {
    left: -1px;
    top: -1px;
}
@media screen and (max-width:1399px) {
    .question-container02 h3 {
        width: 100%;
    }
    .screen02 > .flex {
        display: block;
    }
    .question-container02 {
        width: 70%;
    }
    .screen02 h3 {
        transform: rotate(0deg) translate(0, -2em);
    }
}
@media screen and (max-width:1199px) {
    .back_top_btn {
        bottom: 50px;
    }
    p.title_message {
        padding: 0 2em;
    }
    .question-container {
        width: 95%;
    }
    .question-wrap1,.question-wrap2,.question-wrap3,.question-wrap5 {
        height: 300px;
    }
    .question-wrap4 {
        height: 320px;
    }
    .question-wrap6 {
        height: 285px;
    }
    .question-wrap7 {
        height: 400px;
    }
    .screen02 h3 {
        font-size: 30px;
    }
    .screen02 h3 span:first-of-type, .screen02 h3 span:last-of-type {
        font-size: 45px;
    }
    .tileMenu {
        width: 70%;
    }
}
@media screen and (max-width:991px) {
    .back_top_btn {
        width: 100px;
    }
    .q_comments {
        font-size: 20px;
    }
    .q_comments span {
        font-size: 33px;
    }
    p.title_message {
        margin-bottom: 1em;
    }
    .q-wrap-left {
        border-right: none;border-bottom: 1px #ccc solid;padding: 0 0 3em 0;margin: 0 0 3em 0;
    }
    .improvement-wrap ul {
        font-size: 13px;
    }
    .question-row ul {
        padding: 1em 2em;
    }
    .question-wrap1 {
        height: 340px;
    }
    .question-wrap6 {
        height: 300px;
    }
    .question-wrap4 {
        height: 320px;
    }
    .question-wrap7 {
        height: 350px;
    }
    .redpin {
        width: 40px;top: -20px;
    }
    .question-container02 {
        width: 100%;
    }
    .screen02 h3 {
        font-size: 26px;
    }
    .screen02 h3 span:first-of-type, .screen02 h3 span:last-of-type {
        font-size: 42px;
    }
    .screen02 h4 span {
        font-size: 40px;
    }
    .screen02 h4  {
        font-size: 22px;
    }
    .title-wrap p {
        font-size: 14px;margin: 0 0 0 .5em;
    }
    .trouble-right li {
        font-size: 14px;
    }
    .trouble-right02 ul {
        font-size: 12px;
    }
    .question-container02:first-of-type .trouble-left {
        width: 15%;
    }
    .trouble-left {
        width: 11%;
    }
    .trouble-left img {
        width: 100%;
    }
    .trouble-right p:first-of-type,.trouble-right02 p:first-of-type {
        font-size: 18px;
    }
    .tileMenu {
        width: 100%;
    }
    .tileMenu .box3 {
        margin: .5em auto .5em 1.2em;
    }
    .menu-text div {
        margin: 0;font-size: 13px;width: 130%;
    }
    .menu-wrap {
        padding: 0;
    }
    .menu-wrap > p {
        min-height: 6.5em;
    }
    .menu-title p {
        font-size: 18px;
    }
    .menu-title img {
        width: 40px;
    }
    .hubnet_link h1 {
        font-size: 38px;
    }
    .hubnet_link p {
        font-size: 18px;
    }
}
@media screen and (max-width:767px) {
    body {
        background-image: url(img/cork_board_sm.jpg);
    }
    .back_top_btn {
        width: 90px;
    }
    h2.title {
        font-size: 28px;
    }
    p.title_message {
        font-size: 14px;text-align: left;
    }
    .question-row ul {
        font-size: 12px;
    }
    .question-wrap1 {
        width: 100%;height: 340px;
    }
    .question-wrap2, .question-wrap3, .question-wrap4, .question-wrap5, .question-wrap6, .question-wrap7 {
        padding: 0;
    }
    .question-wrap7 {
        height: 310px;
    }
    .redpin {
        width: 35px;top: -15px;
    }
    .screen02 h2{
        font-size: 32px;margin-bottom: 20px;
    }
    .screen02 {
        margin: 10em 0;
    }
    .screen02 h3 {
        font-size: 24px;
    }
    .screen02 h3 span:first-of-type, .screen02 h3 span:last-of-type {
        font-size: 34px;
    }
    .screen02 .title-wrap {
        margin:  0 1em;
    }
    .screen02 h4::after {
        width: 3em;height: 3em;left: -10px;top: -10px;
    }
    .trouble-wrap.flex,.plan-wrap.flex {
        flex-direction: column;
    }
    .trouble-right p:first-of-type {
        text-align: center;
    }
    .trouble-left {
        width: auto;
    }
    .trouble-right {
        margin: 0;
    }
    .trouble-right02 {
        width: 90%;
    }
    .trouble-right02 ul li{
        padding: .2em 0;
    }
    .trouble-wrap {
        margin: 0 auto 2em auto;
    }
    .plan-wrap section {
        width: 80%;margin: 0 auto 2em auto;
    }
    .trouble-left img {
        width: 80%;
    }
    .plan-wrap section img {
        width: auto;margin: 0 auto;display: block;
    }
    .plan-wrap section:nth-of-type(2) p {
        transform: translate(0 ,-2em);
    }
    .syuri-wrap {
        background-position: top 230px right 20px;
    }
    #detail_open_btn01{
        height:35px;font-size:12px;line-height:23px;margin: 0 auto;padding-top:5px;width:130px;
    }
    .syuri-wrap #detail_open_btn01, .question-container02:first-of-type #detail_open_btn01 {
        margin: 0 auto;
    }
    #detail_open_btn01 span {
        height: 35px;
    }
    .hubnet-wrap p.title_message {
        text-align: center;
    }
    .hubnet-img img {
        width: 80%;margin: 0 auto;
    }
    .hubnet-wrap .trouble-right {
        margin: 0;
    }
    .hubnet-wrap .trouble-right p {
        margin: 0 2em;
    }
    .menu-container.flex {
        flex-direction: column;
    }
    .menu-wrap {
        width: 100%;margin: 1em auto;
    }
    .menu-title {
        margin: 1em;
    }
    .menu-wrap > p {
        min-height: 0;padding: 0 0 1em;
    }
    .tileMenu {
        width: 50%;
    }
    .menu-text div {
        width: 100%;
    }
    .tileMenu .box3 {
        margin: .5em auto;
    }
    .menu-text div {
        margin-left: 8px;
    }
    .hubnet_btn{
        padding: 10px 15px;font-size: 18px;width:50%;
    }
    .hubnet_link{
        padding: 15px 15px 10px 20px;width:250px;
    }
    .hubnet_link h1 {
        font-size: 35px;
    }
    .hubnet_link p {
        font-size: 15px;
    }
}
@media screen and (max-width:576px) {
    .back_top_btn {
        width: 70px;
    }
    h2.title {
        font-size: 25px;
    }
    .screen02 h2{
        font-size: 25px;font-weight:bold;
    }
    p.title_message {
        font-size: 13px;margin: 2em 0 0;
    }
    .question-row ul {
        padding: 1em 0 0 0;
    }
    .q_comments {
        font-size: 15px;top:190px;
    }
    .q_comments span {
        font-size: 25px;
    }
    input[type="checkbox"] ~ label {
        margin: 0;
    }
    input[type="checkbox"] ~ label p {
        display: flex;align-items: center;margin: 0;color: #555;
    }
    input[type="checkbox"] ~ label span {
        display: none;
    }
    input[type="checkbox"]:checked ~ label ul {
        display: block;
    }
    li input[type="checkbox"]:checked ~ label span:last-of-type {
        display: block;width: 100%;font-weight: normal;
    }
    li input[type="checkbox"] ~ label {
        display: flex;flex-flow: wrap;
    }
    li input[type="checkbox"] ~ label i {
        transition: .5s;font-size: 20px;color: #777;margin: 0 0 0 auto;display: block;
    }
    input[type="checkbox"] ~ label p i {
        transition: .5s;font-size: 26px;color: #777;margin: 0 .2em;
    }
    input[type="checkbox"]:checked ~ label p i, input[type="checkbox"]:checked ~ label i {
        transform: rotate(90deg);
    }
    input[type="checkbox"] ~ label ul {
        display: none;
    }
    .question-wrap2, .question-wrap3 {
        height: 315px;
    }
    .question-wrap4 {
        height: 330px;
    }
    .question-wrap6 {
        height: 290px;
    }
    .question-wrap7 {
        height: 380px;
    }
    .improvement-wrap ul li {
        border-bottom: dashed #ccc 1px;padding: .5em 0;
    }
    .redpin {
        width: 30px;top: -10px;
    }
    .sm-br {
        display: block;
    }
    .screen02 h3 {
        font-weight: bold;color: #555;text-shadow: #fff 1px 1px 5px;
    }
    .screen02 .question-container {
        border: double 10px #fcf4c4;
    }
    .screen02 h4 {
        font-size: 18px;background: #fcf4c4;margin: 0 0 1.5em;display: flex;align-items: center;transform: translate(4px, 0);
    }
    .screen02 h4 span {
        font-size: 30px;
    }
    .rikusou-img img:nth-of-type(2) {
        display: block;
    }
    .rikusou-img img:nth-of-type(1) {
        display: none;
    }
    .trouble-wrap ul {
        padding: 0 .5em 0 1.8em;margin: 0;
    }
    .trouble-wrap02 ul {
        padding: 0 .5em;
    }
    .trouble-right02 ul li {
        padding: .5em 0;border-bottom: dashed #ccc 1px;list-style: none;font-weight: bold;
    }
    .trouble-right02 .flex {
        flex-direction: column;
    }
    .question-container02:first-of-type .trouble-left {
        width: auto;
    }
    .trouble-left img {
        display: block;width: 60%;margin: 0 auto;
    }
    .trouble-left {
        margin: .5em;
    }
    .syuri-wrap {
        background-position: right 0% bottom  0%;background-size: 40%;
    }
    .hubnet-img img {
        width: 90%;
    }
    .menu-wrap {
        padding: 1em .5em;
    }
    .menu-wrap > p,.menu-title {
        margin: .5em 0;
    }
    .question-container.hubnet-wrap {
        padding: 4em 0 2em 0;margin: 0em auto 10em auto;
    }
    .tileMenu .box3 {
        width: 110%;
    }
    .menu-text div {
        left: 8px;
    }
    .tileMenu .box3 p {
        font-size: 12px;padding: 5px;
    }
    .tileMenu .box3 p > span.text {
        padding: 5px;
    }
    .tileMenu .box3 {
        min-height: 120px;
    }
    .hubnet_btn{
        padding: 10px 10px;font-size: 14px;width:80%;
    }
    .hubnet_link{
        padding: 15px 15px 0px 20px;width:160px;bottom: 50px;
    }
    .hubnet_link h1 {
        font-size: 20px;
    }
    .hubnet_link p {
        font-size: 10px;
    }
}




