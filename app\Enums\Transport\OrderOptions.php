<?php

declare(strict_types=1);

namespace App\Enums\Transport;

enum OrderOptions: string
{
    case CUS_NAME_JP = '1';
    case POS_NO = '2';
    case AA_NO = '3';
    case CAR_NO = '4';
    case ORD_DATE = '5';
    case TO_PLAN_DATE = '6';
    case TO_DATE = '7';
    case FROM_PLAN_DATE = '8';

    public static function getAllValues(): array
    {
        return array_column(self::cases(), 'value');
    }
}
