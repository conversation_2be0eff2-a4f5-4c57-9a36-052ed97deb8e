<?php

declare(strict_types=1);

namespace App\Enums\LoadingOrder;

enum LoadingOrderType: string
{
    case NEW_ZEALAND_SMART   = '1'; // ニュージーランド（スマートプラン）
    case AUSTRALIA_SMART     = '2'; // オーストラリア（スマートプラン）
    case PREMIUM             = '3'; // プレミアムプラン
    case ADDITIONAL_SUPPORT  = '4'; // 追加サポートサービス
    case LIGHT               = '6'; // ライトプラン
    case CONTAINER           = '7'; // コンテナプラン
    case USA_SMART           = '8'; // アメリカ（スマートプラン）
    case UK_SMART            = '9'; // イギリス（スマートプラン）

    /**
     * Get order type name by code (JP).
     */
    public static function getOrderTypeName(string $code): string
    {
        $orderType = self::tryFrom($code);
        return $orderType?->getJapaneseName() ?? $code;
    }

    /**
     * Get all order types as array for dropdown/select options
     * Format: ['1' => '...', '2' => '...', ...]
     */
    public static function getAllOrderTypes(): array
    {
        $orderTypes = [];
        foreach (self::cases() as $orderType) {
            $orderTypes[$orderType->value] = $orderType->getJapaneseName();
        }
        return $orderTypes;
    }

    /**
     */
    public static function toSelectOptions(): array
    {
        $out = [];
        foreach (self::cases() as $case) {
            $out[] = [
                'value' => $case->value,
                'label' => $case->getJapaneseName(),
            ];
        }
        return $out;
    }

    /**
     * Japanese display name
     */
    public function getJapaneseName(): string
    {
        return match ($this) {
            self::NEW_ZEALAND_SMART  => 'ニュージーランド（スマートプラン）',
            self::AUSTRALIA_SMART    => 'オーストラリア（スマートプラン）',
            self::PREMIUM            => 'プレミアムプラン',
            self::ADDITIONAL_SUPPORT => '追加サポートサービス',
            self::LIGHT              => 'ライトプラン',
            self::CONTAINER          => 'コンテナプラン',
            self::USA_SMART          => 'アメリカ（スマートプラン）',
            self::UK_SMART           => 'イギリス（スマートプラン）',
        };
    }
}
