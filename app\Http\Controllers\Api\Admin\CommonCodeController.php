<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Http\Resources\CommonCode\ServicePlanResource;
use App\Services\CommonCode\CommonCodeService;
use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Response;

class CommonCodeController extends Controller
{
    private CommonCodeService $commonCodeService;

    public function __construct(CommonCodeService $commonCodeService)
    {
        $this->commonCodeService = $commonCodeService;
    }

    #[OA\Get(
        path: '/api/admin/service-plans',
        description: 'Retrieve the list of service plans for dropdown/selection',
        summary: 'Get service plans',
        security: [['access_token' => []]],
        tags: ['Common Code Management'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful operation',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'code', type: 'integer', example: 200),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Success'),
                        new OA\Property(
                            property: 'data',
                            type: 'array',
                            items: new OA\Items(
                                properties: [
                                    new OA\Property(property: 'cd', type: 'string', example: 'S001'),
                                    new OA\Property(property: 'value1', type: 'string', example: 'Basic Plan')
                                ]
                            )
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 401,
                description: 'Unauthorized - Invalid or missing access token'
            ),
            new OA\Response(
                response: 403,
                description: 'Forbidden - Insufficient permissions'
            )
        ]
    )]
    public function getServicePlans(): Response
    {
        return $this->respond(
            ServicePlanResource::collection($this->commonCodeService->getServicePlans())
        );
    }
}
