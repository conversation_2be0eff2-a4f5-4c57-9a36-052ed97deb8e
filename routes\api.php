<?php

declare(strict_types=1);

use App\Enums\UserRole;
use App\Http\Controllers\Api\Admin\AaPlaceController;
use App\Http\Controllers\Api\Admin\AreaController;
use App\Http\Controllers\Api\Admin\AuthController;
use App\Http\Controllers\Api\Admin\BeforeCheckCustomerController;
use App\Http\Controllers\Api\Admin\CheckingRequestController;
use App\Http\Controllers\Api\Admin\CommonCodeController;
use App\Http\Controllers\Api\Admin\ConfirmEditTransportController;
use App\Http\Controllers\Api\Admin\CustomerController;
use App\Http\Controllers\Api\Admin\InspectorController;
use App\Http\Controllers\Api\Admin\LoadingOrderController;
use App\Http\Controllers\Api\Admin\SaleStaffController;
use App\Http\Controllers\Api\Admin\SupportServiceController;
use App\Http\Controllers\Api\Admin\ToPlaceController;
use App\Http\Controllers\Api\Admin\TransportController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::get('admin/transport/export-csv', [TransportController::class, 'exportCsv']);
Route::prefix('admin')->group(function (): void {
    Route::prefix('auth')->group(function (): void {
        Route::post('login', [AuthController::class, 'login']);
        Route::post('refresh-token', [AuthController::class, 'refreshToken']);
        Route::post('logout', [AuthController::class, 'logout'])->middleware('auth.jwt');
        Route::post('authorization', [AuthController::class, 'processAuthorization'])->middleware('auth.jwt');
    });

    Route::middleware([
        'auth.jwt',
        'permission.user:' . implode(',', UserRole::getPermissionAdmin()),
    ])->group(callback: function (): void {
        Route::get('profile', [AuthController::class, 'getProfile']);

        Route::prefix('checking-request')->group(function (): void {
            Route::get('', [CheckingRequestController::class, 'getListCheckingRequest']);
            Route::post('', [CheckingRequestController::class, 'createCheckingRequest']);
            Route::get('statuses', [CheckingRequestController::class, 'getInspectionStatuses']);

            Route::put('bulk-update-status', [CheckingRequestController::class, 'bulkUpdateStatus']);
            Route::put('bulk-assign-inspector', [CheckingRequestController::class, 'bulkAssignInspector']);

            Route::get('{id}', [CheckingRequestController::class, 'getDetailCheckingRequest']);
            Route::put('{id}', [CheckingRequestController::class, 'updateCheckingRequest']);

            Route::post('export-csv/generate-token', [CheckingRequestController::class, 'generateCsvToken']);
            Route::post('confirm-create', [CheckingRequestController::class, 'confirmCreate']);
        });

        // Api inspector
        Route::prefix('inspectors')->group(function (): void {
            Route::get('', [InspectorController::class, 'getAllInspectorsFilter']);
            Route::get('active', [InspectorController::class, 'getListInspectorActive']);
            Route::get('{id}', [InspectorController::class, 'getInspector']);
            Route::put('{id}', [InspectorController::class, 'updateInspector']);
            Route::delete('delete/{id}', [InspectorController::class, 'deleteInspector']);
            Route::post('register', [InspectorController::class, 'registerInspector']);
        });

        // Api transport
        Route::prefix('transport')->group(function (): void {
            // Transport file download routes with file-specific permission
            Route::middleware('permission.user:' . implode(',', UserRole::getPermissionAdminTransportFile()))->group(function (): void {
                Route::get('/bulk-edit-list', [ConfirmEditTransportController::class, 'getListBulkEditTransport']);
                Route::put('/bulk-edit', [ConfirmEditTransportController::class, 'bulkEdit']);

                Route::post('/{id}/confirm-update', [ConfirmEditTransportController::class, 'confirmUpdate']);
                Route::put('/{id}/update', [ConfirmEditTransportController::class, 'updateTransport']);

                Route::get('/download-trans-file-ref', [TransportController::class, 'downloadTransFileRef']);
                Route::post('/del-trans-file-ref', [TransportController::class, 'delTransFileRef']);
            });

            Route::middleware('permission.user:' . implode(',', UserRole::getPermissionAdminTransportStore()))->group(function (): void {
                Route::post('/', [TransportController::class, 'store']);
                Route::post('/confirm-create', [TransportController::class, 'confirmCreate']);
            });

            // Transport routes with basic transport permission
            Route::middleware('permission.user:' . implode(',', UserRole::getPermissionAdminTransport()))->group(function (): void {
                Route::get('/shipping-report-month', [TransportController::class, 'transportVehicleReportMonth']);
                Route::get('/check-download-csv', [TransportController::class, 'checkDownloadCsv']);

                Route::get('/get-aa-to-trans-config', [TransportController::class, 'getAaToTransConfig']);
                Route::get('{id}', [TransportController::class, 'getInfoDetailTransport']);

                Route::post('sync-ref-no', [TransportController::class, 'syncRefNoTransport']);
                Route::get('/', [TransportController::class, 'getListTransport']);

                Route::put('{id}', [TransportController::class, 'updateInfoDetailTransport']);
                Route::post('/update-status', [TransportController::class, 'updateStatusTransport']);

                Route::post('/export-csv/generate-token', [TransportController::class, 'generateCsvToken']);
                Route::post('/download-file-zip', [TransportController::class, 'downloadFileZip']);

                Route::get('/detail-edit/{id}', [TransportController::class, 'getDetailTransport']);
            });
        });

        // Api common
        Route::prefix('aaplace')->group(function (): void {
            Route::get('chiku-region', [AaPlaceController::class, 'getAaPlaceChikuRegion']);
            Route::get('auction-dates', [AaPlaceController::class, 'getAuctionDates']);
        });

        Route::prefix('areas')->group(function (): void {
            Route::get('dropdown', [AreaController::class, 'getDataDropdown']);
        });

        Route::prefix('to-places')->group(function (): void {
            Route::get('dropdown', [ToPlaceController::class, 'getDataDropdown']);
            Route::get('list-active', [ToPlaceController::class, 'getToPlaceActive']);
        });

        Route::prefix('sales-staff')->group(function (): void {
            Route::get('list-active', [SaleStaffController::class, 'getSaleStaffActive']);
            Route::get('list-ordered-by-english-name', [SaleStaffController::class, 'getSaleStaffOrderedByEnglishName']);
        });

        Route::prefix('before-check-customers')->group(function (): void {
            Route::get('', [BeforeCheckCustomerController::class, 'getList']);
        });

        Route::prefix('customers')->group(function (): void {
            Route::get('dropdown', [CustomerController::class, 'getDataDropdown']);
        });

        Route::prefix('loading-orders')->middleware(
            'permission.user:' . implode(',', UserRole::getPermissionLoadingOrder()),
        )->group(function (): void {
            Route::get('', [LoadingOrderController::class, 'getListLoadingOrder']);
            Route::put('bulk-update-status', [LoadingOrderController::class, 'bulkUpdateStatusLoadingOrder']);

            Route::get('print-view', [LoadingOrderController::class, 'getLoadingPrint']);
            Route::put('update-and-print', [LoadingOrderController::class, 'printAndUpdateStatus']);
            Route::get('download-loading-order-file', [LoadingOrderController::class, 'downLoadingOrderFile']);
            Route::post('bulk-download-zip', [LoadingOrderController::class, 'bulkDownloadZip']);
            Route::get('print-view', [LoadingOrderController::class , 'getLoadingPrint']);
            Route::put('update-and-print', [LoadingOrderController::class , 'printAndUpdateStatus']);
            Route::post('/export-csv/generate-token', [LoadingOrderController::class , 'generateCsvToken']);

            Route::get('{id}', [LoadingOrderController::class, 'getDetailLoadingOrder']);
            Route::put('{id}', [LoadingOrderController::class, 'updateLoadingOrder']);
        });

        Route::get('service-plans', [CommonCodeController::class, 'getServicePlans']);

        Route::prefix('support-services')->group(function (): void {
            Route::get('not-deleted', [SupportServiceController::class, 'getSupportServiceNotDeleted']);
        });
    });
});

Route::get('db2data', [App\Http\Controllers\GetAs400Controller::class, 'getData']);
Route::get('sailing-schedule/countries', [App\Http\Controllers\Api\SailingScheduleController::class, 'getCountries']);
Route::get('sailing-schedule/route-schedule', [App\Http\Controllers\Api\SailingScheduleController::class, 'getRouteSchedule']);
Route::get('sailing-schedule/ports', [App\Http\Controllers\Api\SailingScheduleController::class, 'getPort']);
Route::get('sailing-schedule/schedules', [App\Http\Controllers\Api\SailingScheduleController::class, 'getSchedule']);

Route::get('admin/checking-request/export-csv/action', [CheckingRequestController::class, 'exportCsv']);
Route::get('admin/loading-orders/export-csv/action', [LoadingOrderController::class, 'exportCsv']);
