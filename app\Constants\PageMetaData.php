<?php

declare(strict_types=1);

namespace App\Constants;

class PageMetaData
{
    private int $current_page;
    private int $last_page;
    private int $per_page;
    private int $total;

    public function __construct($pagination)
    {
        $this->current_page = $pagination->currentPage();
        $this->last_page = $pagination->lastPage();
        $this->per_page = $pagination->perPage();
        $this->total = $pagination->total();
    }

    public function toArray(): array
    {
        return [
            'current_page' => $this->current_page,
            'last_page' => $this->last_page,
            'per_page' => $this->per_page,
            'total' => $this->total
        ];
    }
}
