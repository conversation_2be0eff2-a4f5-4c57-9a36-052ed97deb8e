FROM minduc30102001/php_odbc_driver

# Set working directory
WORKDIR /var/www/html

# Clear cache
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# Install composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
RUN apt-get update && apt-get install -y gettext util-linux

#Add config
COPY .docker/odbc/as400/db2dsdriver.cfg /tmp/db2dsdriver.cfg
COPY .docker/odbc/as400/odbc.ini /tmp/odbc.ini

COPY .docker/odbc/odbcinst.ini /tmp/odbcinst.ini
RUN echo "" >> /etc/odbcinst.ini && cat /tmp/odbcinst.ini >> /etc/odbcinst.ini && rm /tmp/odbcinst.ini

COPY .docker/script/entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh

#Set env path
ENV DB2DSDRIVER_CFG_PATH=/opt/ibm/clidriver/cfg
ENV IBM_DB_HOME=/opt/ibm/clidriver

#Add odbc config
RUN docker-php-ext-configure pdo_odbc --with-pdo-odbc=unixODBC,/usr
RUN docker-php-ext-install -j$(nproc) pdo_odbc

# Add user for laravel application
RUN groupadd -g 1000 www
RUN useradd -u 1000 -ms /bin/bash -g www www

# Copy application files
COPY . /var/www/html
COPY --chown=www:www . /var/www/html

# Set permissions
RUN chown -R www:www /var/www/html/storage /var/www/html/bootstrap/cache
RUN touch /var/log/php-fpm.log && chown www:www /var/log/php-fpm.log && ln -sf /dev/stderr /var/log/php-fpm.log

# Expose port 9000
EXPOSE 9000

ENTRYPOINT ["entrypoint.sh"]

# Start PHP-FPM server
CMD ["php-fpm"]
