@extends('app')

@section('title', __('home-page/recruit.page_title'))

@push('styles')
    @vite('resources/css/recruit.css')
@endpush

@php
    $recruitSections = [
        [
            'title' => __('home-page/recruit.auto_hub_work'),
            'content' => __('home-page/recruit.auto_hub_work_content'),
            'image' => 'recruit_service_annai.jpg',
        ],
        [
            'title' => __('home-page/recruit.image_of_the_person_we_wish'),
            'content' => __('home-page/recruit.image_of_the_person_we_wish_content'),
            'image' => 'recruit_image02.jpg',
        ],
        [
            'title' => __('home-page/recruit.auto_hub_corporate_culture'),
            'content' => __('home-page/recruit.auto_hub_corporate_culture_content'),
            'image' => 'recruit_image04.jpg',
        ],
    ];
@endphp

@section('content')
    <div class="relative overflow-x-hidden overflow-y-hidden">
        @foreach ($bubbleWidths as $key => $width)
            <img
                id="animation{{ str_pad($key + 1, 2, '0', STR_PAD_LEFT) }}"
                src="{{ asset('images/kirakira01.png') }}"
                class="absolute"
                style="width: {{ $width }}px; z-index: 2"
                alt="#"
            />
        @endforeach

        <div class="container-fluid m-0 p-0 pt-[57px] lg:pt-20" data-aos="zoom-in">
            <div
                class="h-[576px] bg-center bg-no-repeat pt-23"
                style="background-image: url('{{ asset('images/ah_recruit_bg.jpg') }}')"
            >
                <div class="relative h-[322px]">
                    <div class="recruit_bg_02 absolute top-0 left-0"></div>
                    <div
                        class="z-10 mx-auto flex h-full max-w-6xl items-center px-5"
                        data-aos="zoom-in-right"
                        data-aos-duration="1000"
                    >
                        <div data-aos="zoom-in-right" data-aos-duration="1000">
                            <h1 class="mb-2 text-center text-2xl font-bold md:text-left md:text-35">
                                {{ __('home-page/recruit.description_title') }}
                            </h1>
                            <p class="text-sm md:text-19 md:leading-10">
                                {{ __('home-page/recruit.description_content') }}
                            </p>
                        </div>
                    </div>
                </div>
                <div class="mx-auto max-w-6xl px-5 pt-15 pb-5">
                    <h3
                        class="md:text-50 text-[36px] font-bold text-white"
                        style="text-shadow: 4px 2px 2px #555; letter-spacing: 14px"
                    >
                        Recruit
                    </h3>
                </div>
            </div>
        </div>

        <div class="container-fluid bg-content relative">
            <div class="relative z-10 mx-auto max-w-6xl px-6 pt-15 md:pt-32">
                <x-common.tab>
                    <div class="px-5 pb-6">
                        @foreach ($recruitSections as $index => $section)
                            <div
                                class="{{ $index === 0 ? 'mt-20 md:mt-32' : 'mt-4 md:mt-8' }} grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-8"
                            >
                                <div
                                    class="{{ $index % 2 === 0 ? 'md:order-1' : 'md:order-2' }} order-1"
                                    data-aos="slide-in"
                                    data-aos-delay="0"
                                    data-aos-duration="300"
                                >
                                    <h4 class="my-4 text-3xl font-bold md:my-6">{{ $section['title'] }}</h4>
                                    <p class="text-gray-700">
                                        {!! $section['content'] !!}
                                    </p>
                                </div>

                                <div class="{{ $index % 2 === 0 ? 'md:order-2' : 'md:order-1' }} order-2">
                                    <div class="border-5 border-white bg-white shadow-md">
                                        <img src="{{ asset('images/' . $section['image']) }}" class="w-full" alt="#" />
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <x-slot:tab2>
                        <div class="mt-32 px-4 md:px-12">
                            <h3 class="mb-6 text-center text-sm font-bold md:text-xl">
                                {{ __('home-page/recruit.thank_you_for_your_interest') }}
                            </h3>
                        </div>
                        <div class="mx-auto mt-5 px-4 pb-6 md:px-12">
                            <x-accordion
                                :items="[
                                    [
                                        'id' => '1',
                                        'question' => __('home-page/recruit.sales_title'),
                                        'showIcon' => false,
                                        'answer' => view('components.recruit.recruit-sales-inner', ['sales' => $sales])->render()
                                    ],
                                    [
                                        'id' => '2',
                                        'question' => __('home-page/recruit.engineer_title'),
                                        'showIcon' => false,
                                        'answer' => view('components.recruit.recruit-system-engineer', ['data' => $engineer])->render()
                                    ],
                                ]"
                                :defaultOpen="1"
                            />
                        </div>
                    </x-slot>
                </x-common.tab>

                <div class="mt-10 mb-20">
                    <h2 class="mb-4 text-3xl font-bold">{{ __('home-page/recruit.application_flow') }}</h2>
                    <x-common.step :items="$steps" />
                </div>
            </div>
        </div>
    </div>
@endsection
