<?php

declare(strict_types=1);

namespace App\Services\Inspector;

use App\Constants\ApiCodes;
use App\Repositories\sqlServer\InspectorRepository;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response as HttpResponse;
use Symfony\Component\HttpKernel\Exception\HttpException;
use App\Exceptions\ApiException;

class DeleteInspectorService
{
    private InspectorRepository $inspectorRepository;

    public function __construct(InspectorRepository $inspectorRepository)
    {
        $this->inspectorRepository = $inspectorRepository;
    }

    /**
     * @throws Exception
     */
    public function call(int $id): mixed
    {
        if (!$this->inspectorRepository->findDetailById($id)) {
            return null;
        }

        // Check if inspector is being used in t_before_check table
        if ($this->inspectorRepository->checkInspectorUsage($id)) {
            throw new ApiException(ApiCodes::WEB_E_MSG_043, 400);
        }

        $deleteData = $this->prepareDeleteData();
        $inspector = $this->inspectorRepository->update($id, $deleteData);
        return $inspector;
    }

    private function prepareDeleteData(): array
    {
        $now = Carbon::now();

        return [
            'up_date' => $now->format('Y-m-d H:i:s'),
            'up_owner' => Auth::user()?->id ?? 1,
            'del_flg' => true
        ];
    }
}
