<?php

declare(strict_types=1);

return [
    'page_title' => 'Car & Shipping Information｜AUTOHUB Co.,Ltd.',
    'title' => 'Car & Shipping Information　Track&Trace',
    'page_description1' => 'Car & Shipping',
    'page_description2' => 'Information',
    'service_descriptions' => 'You can see the requested car information, photos, shipping status, etc. at any time.',
    'service_list' => [
        'You can easily search and browse car and shipping information.',
        'You can also download all photos on the detail page.',
        'You and our staff share the same information.',
        'You can request the release of D2D service vehicles.'
    ],
    'click_car' => "Click on Car &<br>Shipping Information",
    'list_function' => [
        '1.<span class="2sm:text-xl mx-[0.3em] font-bold text-orange-400">Search filter</span>for destination country, car name, chassis number, ship name, etc.',
        '2. The order of the list can be<span class="2sm:text-xl mx-[0.3em] font-bold text-orange-400">changed</span>freely',
        '3.<span class="2sm:text-xl mx-[0.3em] font-bold text-orange-400">Download</span>search results in CSV',
        '4. Click on the car photo or car name to go to the vehicle details page<br>The detailed page also has an<span class="2sm:text-xl mx-[0.3em] font-bold text-orange-400">upload</span>function such as export deletion copy.',
    ],
    'function_descriptions' => 'Depending on the service you are using, photos, reports, etc.You can check and download (* For details, please contact the sales staff in charge)',
    'hubnet_order' => 'If you are a HUBNET member, you can check here after login in.',
    'hubnet_login' => "HUBNET<br>Login",
    'car_information' => 'Car & Shipping Information',
    'track_trace' => '(Track and Trace)',
    'line_title' => 'You can see car information from <span class="2sm:text-40 mx-[0.2em] text-xl text-lime-500">LINE</span>.',
    'line_search' => 'Search by Chassis number,<br>AH No. and Car Name.',
    'line_bot_1' => 'Interactive <br class="hidden 2sm:block">Response System <br class="hidden 2sm:block">lanched.',
    'line_bot_2' => '「LineBot」',
    'line_bot_3' => 'が登場!!',
    'friend_description' => 'After adding an AUTOHUB<br>official account as<br>a friend,You can use it<br>by linking HUBNET and LINE account.',
    'friend_btn' => 'Click here to be the friend.'
];
