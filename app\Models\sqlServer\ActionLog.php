<?php

declare(strict_types=1);

namespace App\Models\sqlServer;

use Illuminate\Database\Eloquent\Model;

class ActionLog extends Model
{
    public const CREATED_AT = 'reg_date';
    public const UPDATED_AT = 'up_date';

    protected $fillable = [
        'del_flg',
        'reg_date',
        'up_date',
        'up_owner',
        'login_id',
        'acc_page',
        'acc_datetime',
        'acc_ip'
    ];
    protected $table = 't_act_log';
    protected $casts = [
        'del_flg' => 'boolean',
    ];
}
