<?php

declare(strict_types=1);

namespace App\Http\Resources\LoadingOrder;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DetailLoadingOrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'st_cd' => $this->st_cd,
            'ref_no' => $this->ref_no,
            'agent_ref_no' => $this->agent_ref_no,
            'car_no' => $this->car_no,
            'car_name' => $this->car_name,
            'car_year' => $this->car_year,
            'mileage' => $this->mileage,
            'fob_price' => $this->fob_price,
            'price_currency' => $this->price_currency,
            'price_quotation' => $this->price_quotation,
            'to_name' => $this->to_name,
            'destination' => $this->destination,
            'port' => $this->port,
            'to_plan_date' => $this->to_plan_date,
            'consignee_name' => $this->consignee_name,
            'to_plan_flg' => $this->to_plan_flg,
            'consignee_flg' => $this->consignee_flg,
            'odr_kbn' => $this->odr_kbn,
            'odr_date' => $this->odr_date,
            'customs_flg' => $this->customs_flg,
            'part_note' => $this->part_note,
            'note' => $this->note,
            'customer' => [
                'id' => $this->customer->id,
                'cus_Name_JP' => $this->mc_cus_Name_JP,
                'ah_sales_name' => $this->mc_ah_sales_name,
                'cus_name_EN' => $this->cus_name_EN
            ],
            'special_note' => $this->lpn_special_note,

            'optn1_flg' => $this->optn1_flg,
            'optn2_flg' => $this->optn2_flg,
            'optn2_sub_kbn' => $this->optn2_sub_kbn,
            'optn2_sub2_kbn' => $this->optn2_sub2_kbn,
            'optn3_flg' => $this->optn3_flg,
            'optn3_sub_txt' => $this->optn3_sub_txt,
            'optn4_flg' => $this->optn4_flg,
            'optn4_sub_kbn' => $this->optn4_sub_kbn,
            'optn5_flg' => $this->optn5_flg,
            'optn5_sub_txt' => $this->optn5_sub_txt,
            'optn6_flg' => $this->optn6_flg,
            'optn6_sub_kbn' => $this->optn6_sub_kbn,
            'optn7_flg' => $this->optn7_flg,
            'optn8_flg' => $this->optn8_flg,
            'optn9_flg' => $this->optn9_flg,
            'optn10_flg' => $this->optn10_flg,
            'optn11_flg' => $this->optn11_flg,
            'optn14_flg' => $this->optn14_flg,
            'optn17_flg' => $this->optn17_flg,
            'optn18_flg' => $this->optn18_flg,
            'optn21_flg' => $this->optn21_flg
        ];
    }
}
