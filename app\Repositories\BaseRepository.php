<?php

declare(strict_types=1);

namespace App\Repositories;

use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use ReflectionClass;
use RuntimeException;

/**
 * @property $TIME_STAMP
 */
abstract class BaseRepository
{
    public const TIME_STAMP = ['created_at', 'updated_at', 'deleted_at'];

    /** @var $model Model */
    protected $model;

    protected $query = null;

    private $reflection;

    public function __construct()
    {
        $this->setModel();
    }

    /**
     * Specify Model class name
     *
     * @return mixed
     */
    abstract public function model(): mixed;

    /**
     * Store a newly created resource in storage.
     *
     * @param array $attributes
     * @return Model|bool
     * @throws Exception
     */
    public function createOneWithRelations(array $attributes): Model|bool
    {
        $parent = ($this->model)::query()->create($attributes);
        $relations = [];

        foreach (array_filter($attributes, [$this, 'isRelation']) as $key => $models) {
            if (method_exists($parent, $relation = Str::camel($key))) {
                $relations[] = $relation;
                $this->syncRelations($parent->{$relation}(), $models, false);
            }
        }
        if (count($relations)) {
            $parent->load($relations);
        }

        return $parent->push() ? $parent : false;
    }

    /**
     * @param array $attributes
     * @param array $values
     * @return Builder|Model
     */
    public function firstOrCreate(array $attributes, array $values = []): Builder|Model
    {
        return ($this->model)::query()->firstOrCreate($attributes, $values);
    }

    /*
     *
     * @param Builder $query
     * @param array   $filter
     */

    /**
     * Update the specified resource in storage.
     *
     * @param int|Model $parent
     * @param array $attributes
     * @return Model|bool
     *
     * @throws ModelNotFoundException
     * @throws Exception
     */
    public function update(Model|int $parent, array $attributes): Model|bool|int
    {
        if (is_integer($parent)) {
            $parent = ($this->model)::query()->findOrFail($parent);
        }
        $parent->fill($attributes);
        $relations = [];

        foreach (array_filter($attributes, [$this, 'isRelation']) as $key => $models) {
            if (method_exists($parent, $relation = Str::camel($key))) {
                $relations[] = $relation;
                $this->syncRelations($parent->{$relation}(), $models);
            }
        }
        if (count($relations)) {
            $parent->load($relations);
        }

        return $parent->push() ? $parent : false;
    }

    /**
     * @param $id
     * @return Model
     * @throw ModelNotFoundException
     */
    public function findOrFail($id): Model
    {
        return ($this->model)::query()->findOrFail($id);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param string $item
     * @param bool $force
     * @return bool
     *
     */
    public function destroy(string $item, bool $force = false): bool
    {
        $deleteItem = ($this->model)::query()->findOrFail($item);

        return $deleteItem->{$force ? 'forceDelete' : 'delete'}();
    }

    /**
     * @param $id
     * @return bool
     */
    public function restore($id): bool
    {
        return ($this->model)::query()->withTrashed()->findOrFail($id)->restore();
    }

    /**
     * @param array $attrs
     * @return null|object
     */
    public function findBy(array $attrs): object|null
    {
        return ($this->model)::query()->where($attrs)->first();
    }

    /**
     * @param array $attributes
     * @param array $values
     * @return Builder|Model
     */
    public function updateOrCreate(array $attributes, array $values = []): Model|Builder
    {
        return ($this->model)::query()->updateOrCreate($attributes, $values);
    }

    /**
     * Retrieve the specified resource.
     *
     * @param int $id
     * @param array $relations
     * @param array $appends
     * @param array $hidden
     * @param bool $withTrashed
     * @return Model
     */
    public function show(int $id, array $relations = [], array $appends = [], array $hidden = [], bool $withTrashed = false): Model
    {
        $query = ($this->model)::query();

        if ($withTrashed) {
            $query->withTrashed();
        }
        return $query->with($relations)->findOrFail($id)->setAppends($appends)->makeHidden($hidden);
    }

    public function buildSortQuery(Builder $query, $params): void
    {
        $params = $params ?: request()->toArray();

        $rawSort = $params['sort'] ?? null;
        if (blank($rawSort)) {
            return;
        }

        $items = is_array($rawSort) ? $rawSort : explode(',', (string) $rawSort);

        foreach ($items as $item) {
            $item = trim($item);
            if ('' === $item) {
                continue;
            }

            [$col, $dir] = array_pad(explode('|', $item), 2, 'asc');

            $col = trim($col);
            $dir = 'desc' === strtolower($dir) ? 'desc' : 'asc';

            $query->orderBy($col, $dir);
        }
    }

    public function exitWithId($id): bool
    {
        $query = ($this->model)::query();

        return $query->where('id', $id)->exists();
    }

    public function addSoftDeleteCondition(Builder $query): Builder
    {
        return $query->where(($this->model)->getTable() . '.del_flg', '=', 0);
    }

    /**
     * Custom pagination for AS400/IBM DB2 models
     *
     * @param Builder $query
     * @param int $limit
     * @param int $page
     * @param array $columns
     * @param string $pageName
     * @param bool $withCount
     * @return \Illuminate\Pagination\LengthAwarePaginator|\Illuminate\Pagination\Paginator
     */
    public function customsPaginateAs400(Builder $query, $limit = 20, $page = 1, $columns = ['*'], $pageName = 'page', $withCount = true): \Illuminate\Pagination\LengthAwarePaginator|\Illuminate\Pagination\Paginator
    {
        // Ensure minimum values
        $limit = max(1, (int) $limit);
        $page = max(1, (int) $page);

        // Calculate offset
        $offset = ($page - 1) * $limit;

        // Get total count if needed (this might be slow for large AS400 tables)
        $total = 0;
        if ($withCount) {
            try {
                // Clone query for count to avoid affecting the main query
                $countQuery = clone $query;
                $total = $countQuery->count();
            } catch (Exception $e) {
                // If count fails, use simplePaginate approach (no total count)
                $withCount = false;
                Log::warning('AS400 count query failed: ' . $e->getMessage());
            }
        }

        // Get the actual data with limit and offset
        try {
            $items = $query->offset($offset)->limit($limit)->get($columns);
        } catch (Exception $e) {
            // If offset/limit fails, try alternative approach
            Log::error('AS400 pagination query failed: ' . $e->getMessage());
            throw new RuntimeException('Failed to retrieve paginated data from AS400: ' . $e->getMessage());
        }

        // Create paginator based on whether we have total count
        if ($withCount) {
            return new \Illuminate\Pagination\LengthAwarePaginator(
                $items,
                $total,
                $limit,
                $page,
                [
                    'path' => request()->url(),
                    'pageName' => $pageName,
                ]
            );
        }
        // Simple paginator without total count
        return new \Illuminate\Pagination\Paginator(
            $items,
            $limit,
            $page,
            [
                'path' => request()->url(),
                'pageName' => $pageName,
            ]
        );

    }

    /**
     * Alternative pagination method using raw SQL for AS400
     * Use this if the Builder approach doesn't work
     *
     * @param string $table
     * @param array $conditions
     * @param array $bindings
     * @param int $limit
     * @param int $page
     * @param array $columns
     * @param string $orderBy
     * @param string $connection
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function customsPaginateAs400Raw(
        string $table,
        array $conditions = [],
        array $bindings = [],
        int $limit = 20,
        int $page = 1,
        array $columns = ['*'],
        string $orderBy = '',
        string $connection = 'as400'
    ): \Illuminate\Pagination\LengthAwarePaginator {
        $limit = max(1, $limit);
        $page = max(1, $page);
        $offset = ($page - 1) * $limit;

        // Build column string
        $columnString = $columns === ['*'] ? '*' : implode(', ', $columns);

        // Build WHERE clause
        $whereClause = '';
        if (!empty($conditions)) {
            $whereClause = 'WHERE ' . implode(' AND ', $conditions);
        }

        // Build ORDER BY clause
        $orderByClause = '';
        if (!empty($orderBy)) {
            $orderByClause = 'ORDER BY ' . $orderBy;
        }

        // Count query for total records
        $countSql = "SELECT COUNT(*) as total FROM {$table} {$whereClause}";

        try {
            $totalResult = DB::connection($connection)->select($countSql, $bindings);
            $total = $totalResult[0]->total ?? 0;
        } catch (Exception $e) {
            Log::warning('AS400 count query failed: ' . $e->getMessage());
            $total = 0;
        }

        // Data query with pagination
        // Using ROW_NUMBER() for AS400/DB2 compatibility
        $dataSql = "
            SELECT * FROM (
                SELECT {$columnString}, ROW_NUMBER() OVER ({$orderByClause}) as rn
                FROM {$table}
                {$whereClause}
            ) AS numbered
            WHERE rn > {$offset} AND rn <= " . ($offset + $limit);

        try {
            $items = DB::connection($connection)->select($dataSql, $bindings);

            // Remove the row number column
            $items = collect($items)->map(function ($item) {
                $itemArray = (array) $item;
                unset($itemArray['rn']);
                return (object) $itemArray;
            });

        } catch (Exception $e) {
            Log::error('AS400 pagination data query failed: ' . $e->getMessage());
            throw new RuntimeException('Failed to retrieve paginated data from AS400: ' . $e->getMessage());
        }

        return new \Illuminate\Pagination\LengthAwarePaginator(
            $items,
            $total,
            $limit,
            $page,
            [
                'path' => request()->url(),
                'pageName' => 'page',
            ]
        );
    }

    /**
     * Simple pagination without count for better performance on large AS400 tables
     *
     * @param Builder $query
     * @param int $limit
     * @param int $page
     * @param array $columns
     * @param string $pageName
     * @return \Illuminate\Pagination\Paginator
     */
    public function simplePaginateAs400(Builder $query, $limit = 20, $page = 1, $columns = ['*'], $pageName = 'page'): \Illuminate\Pagination\Paginator
    {
        $limit = max(1, (int) $limit);
        $page = max(1, (int) $page);
        $offset = ($page - 1) * $limit;

        try {
            // Get one extra item to determine if there's a next page
            $items = $query->offset($offset)->limit($limit + 1)->get($columns);

            $hasMorePages = $items->count() > $limit;

            // Remove the extra item if it exists
            if ($hasMorePages) {
                $items = $items->slice(0, $limit);
            }

        } catch (Exception $e) {
            Log::error('AS400 simple pagination failed: ' . $e->getMessage());
            throw new RuntimeException('Failed to retrieve paginated data from AS400: ' . $e->getMessage());
        }

        return new \Illuminate\Pagination\Paginator(
            $items,
            $limit,
            $page,
            [
                'path' => request()->url(),
                'pageName' => $pageName,
                'hasMorePagesWhen' => $hasMorePages,
            ]
        );
    }

    /**
     * @param $value
     * @return bool
     */
    protected function checkParamFilter($value): bool
    {
        return '' !== $value && null !== $value;
    }

    protected function applyFilterScope(Model | Builder $lModel, array $params)
    {
        foreach ($params as $funcName => $valueParam) {
            // continue if valueParam is null
            if (null === $valueParam) {
                continue;
            }
            $funcName = Str::studly($funcName);
            if ($this->getReflection()->hasMethod('scope' . $funcName)) {
                $funcName = lcfirst($funcName);
                $lModel = $lModel->{$funcName}($valueParam);
            }
        }
        return $lModel;
    }

    protected function getReflection()
    {
        if ($this->reflection) {
            return $this->reflection;
        }
        $this->reflection = new ReflectionClass($this->model());
        return $this->reflection;
    }

    /**
     * Set Eloquent Model to instantiate
     *
     * @return void
     */
    private function setModel(): void
    {
        $newModel = App::make($this->model());

        if (!$newModel instanceof Model) {
            throw new RuntimeException("Class {$newModel} must be an instance of Illuminate\\Database\\Eloquent\\Model");
        }

        $this->model = $newModel;
    }

    /**
     * @param Relation $relation
     * @param array | Model $conditions
     * @param bool $detaching
     * @return void
     * @throws Exception
     */
    private function syncRelations(Relation $relation, $conditions, bool $detaching = true): void
    {
        $conditions = is_array($conditions) ? $conditions : [$conditions];
        $relatedModels = [];
        foreach ($conditions as $condition) {
            if ($condition instanceof Model) {
                $relatedModels[] = $condition;
            } elseif (is_array($condition)) {
                $relatedModels[] = $relation->firstOrCreate($condition);
            }
        }

        if ($relation instanceof BelongsToMany && method_exists($relation, 'sync')) {
            $relation->sync($this->parseIds($relatedModels), $detaching);
        } elseif ($relation instanceof HasMany | $relation instanceof HasOne) {
            $relation->saveMany($relatedModels);
        } else {
            throw new Exception('Relation not supported');
        }
    }

    /**
     * @param array $models
     * @return array
     */
    private function parseIds(array $models): array
    {
        $ids = [];
        foreach ($models as $model) {
            $ids[] = $model instanceof Model ? $model->getKey() : $model;
        }

        return $ids;
    }

    /**
     * @param $value
     * @return bool
     */
    private function isRelation($value): bool
    {
        return is_array($value) || $value instanceof Model;
    }
}
