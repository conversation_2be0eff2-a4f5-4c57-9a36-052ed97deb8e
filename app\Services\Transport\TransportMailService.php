<?php

declare(strict_types=1);

namespace App\Services\Transport;

use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class TransportMailService
{
    private $transportCompanies = [
        '1005' => [
            'name' => '東西海運',
            'name_note' => '東西海運株式会社',
            'email' => '<EMAIL>'
        ],
        '7875' => [
            'name' => 'キャリーゴール',
            'name_note' => '株式会社キャリーゴール',
            'email' => '<EMAIL>'
        ],
        '17078' => [
            'name' => '栄港商運',
            'name_note' => '株式会社栄港商運',
            'email' => '<EMAIL>'
        ],
        '14192' => [
            'name' => 'ジェイキャリー',
            'name_note' => '株式会社ジェイ・キャリー',
            'email' => '<EMAIL>'
        ]
    ];

    public function processTransportMail($transportData, $lastData, $fileData = []): void
    {
        // ********************メール表示用***************************
        $tickitemsMailData = $this->buildTickitemsMailData($transportData);
        $aucMailData = $this->buildAucMailData($transportData);
        $plateAddressMailData = $this->buildPlateAddressMailData($transportData);
        $dateOfTime = $this->buildDateOfTime($transportData);

        // ***メールを送信する
        $sendMailFlg = 0;
        $changePriceFlg = 0;
        $plateSendFlg = 0;
        $fileMailFlg = 0;
        $cmmntUpdFlg = $transportData['cmmnt_upd_flg'] ?? 0;

        // 価格変更チェック
        if ($lastData['deli_price'] != $transportData['deli_price']) {
            $changePriceFlg = 1;
        }

        // プレート発送チェック
        if ($transportData['plate_send_date'] != $lastData['plate_send_date'] ||
            $transportData['plate_send_co'] != $lastData['plate_send_co'] ||
            $transportData['plate_send_no'] != $lastData['plate_send_no']) {
            $plateSendFlg = 1;
        }

        if (empty($transportData['plate_send_date']) ||
            empty($transportData['plate_send_co']) ||
            empty($transportData['plate_send_no'])) {
            $plateSendFlg = 0;
        }

        // 新規添付ファイル案内メール送信
        if (!empty($fileData[0])) {
            $fileMailFlg = 1;
        }

        if (1 == $changePriceFlg || 1 == $plateSendFlg || 1 == $cmmntUpdFlg || 1 == $fileMailFlg) {
            $transInfo = $this->getTransportCompanyInfo($transportData['m_trans_id']);

            if ($transInfo) {
                $sendMailFlg = 1;
            }

            // 営業担当宛データ取得
            $customerData = $this->getCustomerData($transportData['m_customer_id']);

            // コメント更新通知
            if (1 == $cmmntUpdFlg && 1 == $sendMailFlg) {
                $this->sendCommentUpdateMail($transportData, $customerData, $transInfo, $tickitemsMailData);
            }

            // 価格変更通知
            if (1 == $changePriceFlg) {
                $this->sendPriceChangeMail($transportData, $lastData, $customerData, $transInfo);
            }

            // プレート発送通知
            if (1 == $plateSendFlg) {
                $this->sendPlateSendMail($transportData, $customerData, $transInfo);
            }

            // ファイル添付通知
            if (1 == $fileMailFlg) {
                $this->sendFileAttachmentMail($transportData, $customerData, $transInfo, $fileData);
            }
        }

        // キャンセル通知
        if (in_array($transportData['m_trans_id'], ['7875', '1005', '14192', '17078'])) {
            if ('9' == $transportData['st_cd']) {
                $this->sendCancelMail($transportData);
            }

            // 保留通知
            if ('3' != $lastData['st_cd'] && 3 == $transportData['st_cd']) {
                $this->sendOnHoldMail($transportData);
            }
        }
    }

    private function buildTickitemsMailData($transportData)
    {
        $tickitemsMailData = '';

        if ('1' == $transportData['undrivable_flg']) {
            $tickitemsMailData .= config('language.lan_undrivable') . "<br/>";
        }
        if ('1' == $transportData['tall_flg']) {
            $tickitemsMailData .= config('language.lan_tall') . "<br/>";
        }
        if ('1' == $transportData['lowdown_flg']) {
            $tickitemsMailData .= config('language.lan_lowdown') . "<br/>";
        }
        if ('1' == $transportData['long_flg']) {
            $tickitemsMailData .= config('language.lan_long') . "<br/>";
        }
        if ('1' == $transportData['old_flg']) {
            $tickitemsMailData .= config('language.lan_old_car') . "<br/>";
        }
        if ('1' == $transportData['luxury_flg']) {
            $tickitemsMailData .= config('language.lan_luxury_car');
            if ('1' == $transportData['luxury_flg_insrance']) {
                $tickitemsMailData .= " (" . config('language.lan_luxyry_insurance1') . ")";
            } elseif ('2' == $transportData['luxury_flg_insrance']) {
                $tickitemsMailData .= " (" . config('language.lan_luxyry_insurance2') . ")";
            }
            $tickitemsMailData .= "<br/>";
        }
        if ('1' == $transportData['other_flg']) {
            $tickitemsMailData .= config('language.lan_other_car');
            $tickitemsMailData .= " (" . htmlspecialchars($transportData['other_flg_txt']) . ")";
            $tickitemsMailData .= "<br/>";
        }
        if ('1' == $transportData['tick_no_flg']) {
            $tickitemsMailData .= config('language.lan_gaitou_nasi') . "<br/>";
        }

        return $tickitemsMailData;
    }

    private function buildAucMailData($transportData)
    {
        $aucMail = '';

        if ('1' == $transportData['auction_chk']) {
            $aucMail .= config('language.lan_aucBoth') . "<br/>";
        } else {
            $aucMail .= $transportData['auction_txt'] . "<br/>";
            if (!empty($transportData['auc_name']) || !empty($transportData['auc_addr']) || !empty($transportData['auc_tel'])) {
                $aucMail .= config('language.lan_other_auc') . "：" . $transportData['auc_name'] . "<br/>";
                $aucMail .= config('language.lan_address') . "：" . $transportData['auc_addr'] . "<br/>";
                $aucMail .= config('language.lan_tel') . "：" . $transportData['auc_tel'] . "<br/>";
            }
        }

        return $aucMail;
    }

    private function buildPlateAddressMailData($transportData)
    {
        $plateAddressMail = '';

        if (!empty($transportData['plate_send_name'])) {
            $plateAddressMail = $transportData['plate_send_name'] . "<br/>" .
                " " . "〒" . $transportData['plate_send_zipcd'] . "<br/>" .
                " " . $transportData['plate_send_address'] . "<br/>" .
                " " . $transportData['plate_send_tel'] . "<br/>";
        }

        return $plateAddressMail;
    }

    private function buildDateOfTime($transportData)
    {
        $dateOfTime = '';

        if ('1' == $transportData['date_of_payment_time']) {
            $dateOfTime = "（" . config('language.lan_payment_time1') . "）";
        } elseif ('2' == $transportData['date_of_payment_time']) {
            $dateOfTime = "（" . config('language.lan_payment_time2') . "）";
        } elseif ('3' == $transportData['date_of_payment_time']) {
            $dateOfTime = "（" . config('language.lan_payment_time3') . "）";
        }

        return $dateOfTime;
    }

    private function getTransportCompanyInfo($transId)
    {
        if (in_array($transId, ['1005', '7875', '17078', '14192'])) {
            return $this->transportCompanies[$transId];
        }
        return null;
    }

    private function getCustomerData($customerId)
    {
        return DB::table('m_customer as mc')
            ->leftJoin('m_sales_staff as mss', 'mc.ah_sales_id', '=', 'mss.s_id')
            ->select([
                'mc.id', 'mc.cus_name_JP', 'mc.shipping_prsn_name', 'mc.ah_sales_id',
                'mss.e_mail', 'mss.team', 'mss.s_name',
                'mc.ah_send_mailaddress', 'mc.ah_sales_mail_flg', 'mc.bk_trans_note_mail_flg',
                'mc.bk_trans_price_mail_flg', 'mc.bk_trans_plate_mail_flg', 'mc.bk_trans_file_mail_flg',
                'mc.bk_trans_cancel_mail_flg', 'mc.bk_trans_onhold_mail_flg'
            ])
            ->where('mc.id', $customerId)
            ->where('mc.del_flg', 0)
            ->first();
    }

    private function sendCommentUpdateMail($transportData, $customerData, $transInfo, $tickitemsMailData): void
    {
        if (!$this->shouldSendNotification($customerData, 'bk_trans_note_mail_flg')) {
            return;
        }

        $addresses = $this->buildEmailAddresses($customerData, $transInfo['email'], $transportData['m_trans_id']);

        $mailData = [
            'trans_name' => $transInfo['name_note'],
            'id' => $transportData['tp_id'],
            'customer_name' => $transportData['customer_name'],
            'fr_aa_place_id' => $transportData['fr_aa_place_id'],
            'fr_name' => $transportData['fr_name'],
            'fr_addr' => $transportData['fr_addr'],
            'fr_tel' => $transportData['fr_tel'],
            'to_name' => $transportData['to_name'],
            'to_addr' => $transportData['to_addr'],
            'to_tel' => $transportData['to_tel'],
            'pos_no' => $transportData['pos_no'],
            'aa_no' => $transportData['aa_no'],
            'date_of_payment' => $transportData['date_of_payment'],
            'car_name' => $transportData['car_name'],
            'car_no' => $transportData['car_no'],
            'undrivable_flg' => $this->flagReplace2Ja($transportData['undrivable_flg']),
            'tall_flg' => $this->flagReplace2Ja($transportData['tall_flg']),
            'lowdown_flg' => $this->flagReplace2Ja($transportData['lowdown_flg']),
            'long_flg' => $this->flagReplace2Ja($transportData['long_flg']),
            'plate_cut_flg' => $this->flagReplace($transportData['plate_cut_flg']),
            'plate_no' => $transportData['plate_no'],
            'country' => $transportData['country'],
            'txtcomment_biko' => $transportData['txtcomment_biko']
        ];

        $this->sendMailProgram($addresses, 'admin_transport_note_change', $mailData);
    }

    private function sendPriceChangeMail($transportData, $lastData, $customerData, $transInfo): void
    {
        if (!$this->shouldSendNotification($customerData, 'bk_trans_price_mail_flg')) {
            return;
        }

        $addresses = [
            'to' => [$customerData->e_mail],
            'cc' => ['<EMAIL>'],
            'bcc' => ['<EMAIL>', '<EMAIL>']
        ];

        if (!empty($customerData->ah_send_mailaddress)) {
            $additionalEmails = explode(';', $customerData->ah_send_mailaddress);
            $addresses['to'] = array_merge($addresses['to'], $additionalEmails);
        }

        if ('14192' == $transportData['m_trans_id']) {
            $addresses['cc'][] = '<EMAIL>';
        }

        $mailData = [
            'ah_staff_name' => $customerData->s_name,
            'trans_name' => $transInfo['name'],
            'id' => $transportData['transport_id'],
            'last_deli_price' => $lastData['deli_price'],
            'deli_price' => $transportData['deli_price'],
            'customer_name' => $transportData['customer_name'],
            'fr_aa_place_id' => $transportData['fr_aa_place_id'],
            'fr_name' => $transportData['fr_name'],
            'fr_addr' => $transportData['fr_addr'],
            'fr_tel' => $transportData['fr_tel'],
            'to_name' => $transportData['to_name'],
            'to_addr' => $transportData['to_addr'],
            'to_tel' => $transportData['to_tel'],
            'pos_no' => $transportData['pos_no'],
            'aa_no' => $transportData['aa_no'],
            'date_of_payment' => $transportData['date_of_payment'],
            'car_name' => $transportData['car_name'],
            'car_no' => $transportData['car_no'],
            'undrivable_flg' => $this->flagReplace2Ja($transportData['undrivable_flg']),
            'tall_flg' => $this->flagReplace2Ja($transportData['tall_flg']),
            'lowdown_flg' => $this->flagReplace2Ja($transportData['lowdown_flg']),
            'long_flg' => $this->flagReplace2Ja($transportData['long_flg']),
            'plate_cut_flg' => $this->flagReplace($transportData['plate_cut_flg']),
            'plate_no' => $transportData['plate_no'],
            'country' => $transportData['country'],
            'odr_date' => $transportData['odr_date']
        ];

        $this->sendMailProgram($addresses, 'admin_transport_change_price', $mailData);
    }

    private function sendPlateSendMail($transportData, $customerData, $transInfo): void
    {
        $addresses = [
            'to' => [$customerData->e_mail],
            'cc' => ['<EMAIL>'],
            'bcc' => ['<EMAIL>']
        ];

        if (!empty($customerData->ah_send_mailaddress)) {
            $additionalEmails = explode(';', $customerData->ah_send_mailaddress);
            $addresses['to'] = array_merge($addresses['to'], $additionalEmails);
        }

        if ('14192' == $transportData['m_trans_id']) {
            $addresses['cc'][] = '<EMAIL>';
        }

        $mailData = [
            'ah_staff_name' => $customerData->s_name,
            'trans_name' => $transInfo['name'],
            'id' => $transportData['transport_id'],
            'plate_send_date' => $transportData['plate_send_date'],
            'plate_send_co' => $transportData['plate_send_co'],
            'plate_send_no' => $transportData['plate_send_no'],
            'customer_name' => $transportData['customer_name'],
            'fr_aa_place_id' => $transportData['fr_aa_place_id'],
            'fr_name' => $transportData['fr_name'],
            'fr_addr' => $transportData['fr_addr'],
            'fr_tel' => $transportData['fr_tel'],
            'to_name' => $transportData['to_name'],
            'to_addr' => $transportData['to_addr'],
            'to_tel' => $transportData['to_tel'],
            'pos_no' => $transportData['pos_no'],
            'aa_no' => $transportData['aa_no'],
            'date_of_payment' => $transportData['date_of_payment'],
            'car_name' => $transportData['car_name'],
            'car_no' => $transportData['car_no'],
            'undrivable_flg' => $this->flagReplace2Ja($transportData['undrivable_flg']),
            'tall_flg' => $this->flagReplace2Ja($transportData['tall_flg']),
            'lowdown_flg' => $this->flagReplace2Ja($transportData['lowdown_flg']),
            'long_flg' => $this->flagReplace2Ja($transportData['long_flg']),
            'plate_cut_flg' => $this->flagReplace($transportData['plate_cut_flg']),
            'plate_no' => $transportData['plate_no'],
            'country' => $transportData['country']
        ];

        $this->sendMailProgram($addresses, 'admin_transport_plate', $mailData);
    }

    private function sendFileAttachmentMail($transportData, $customerData, $transInfo, $fileData): void
    {
        if (!$this->shouldSendNotification($customerData, 'bk_trans_file_mail_flg')) {
            return;
        }

        $addresses = [
            'to' => [$transInfo['email']],
            'cc' => [$customerData->e_mail, '<EMAIL>'],
            'bcc' => []
        ];

        if ('14192' == $transportData['m_trans_id']) {
            $addresses['cc'][] = '<EMAIL>';
        }

        $mailData = [
            'id' => $transportData['tp_id'],
            'trans_name_note' => $transInfo['name_note'],
            'customer_name' => $transportData['customer_name'],
            'fr_aa_place_id' => $transportData['fr_aa_place_id'],
            'fr_name' => $transportData['fr_name'],
            'fr_addr' => $transportData['fr_addr'],
            'fr_tel' => $transportData['fr_tel'],
            'to_name' => $transportData['to_name'],
            'to_addr' => $transportData['to_addr'],
            'to_tel' => $transportData['to_tel'],
            'pos_no' => $transportData['pos_no'],
            'aa_no' => $transportData['aa_no'],
            'date_of_payment' => $transportData['date_of_payment'],
            'car_name' => $transportData['car_name'],
            'car_no' => $transportData['car_no'],
            'undrivable_flg' => $this->flagReplace2Ja($transportData['undrivable_flg']),
            'tall_flg' => $this->flagReplace2Ja($transportData['tall_flg']),
            'lowdown_flg' => $this->flagReplace2Ja($transportData['lowdown_flg']),
            'long_flg' => $this->flagReplace2Ja($transportData['long_flg']),
            'plate_cut_flg' => $this->flagReplace($transportData['plate_cut_flg']),
            'plate_no' => $transportData['plate_no'],
            'country' => $transportData['country'],
            'odr_date' => now(),
            'txtcomment_biko' => $transportData['txtcomment_biko']
        ];

        $this->sendMailAttacheFileProgram($addresses, 'admin_transport_file', $mailData, $fileData);
    }

    private function sendCancelMail($transportData): void
    {
        $customerData = $this->getCustomerData($transportData['m_customer_id']);

        if (!$this->shouldSendNotification($customerData, 'bk_trans_cancel_mail_flg')) {
            return;
        }

        $templateMap = [
            '7875' => ['email' => '<EMAIL>', 'template' => 'admin_cg_transport_cancel'],
            '1005' => ['email' => '<EMAIL>', 'template' => 'admin_transport_cancel'],
            '14192' => ['email' => '<EMAIL>', 'template' => 'admin_jc_transport_cancel'],
            '17078' => ['email' => '<EMAIL>', 'template' => 'admin_ei_transport_cancel']
        ];

        $config = $templateMap[$transportData['m_trans_id']];

        $addresses = [
            'to' => [$config['email']],
            'cc' => [$customerData->e_mail, '<EMAIL>'],
            'bcc' => ['<EMAIL>']
        ];

        if ('14192' == $transportData['m_trans_id']) {
            $addresses['cc'][] = '<EMAIL>';
        }

        $mailData = [
            'id' => $transportData['tp_id'],
            'customer_name' => $transportData['customer_name'],
            'fr_aa_place_id' => $transportData['fr_aa_place_id'],
            'fr_name' => $transportData['fr_name'],
            'fr_addr' => $transportData['fr_addr'],
            'fr_tel' => $transportData['fr_tel'],
            'auction_date' => $transportData['auction_date'],
            'auc_mail' => $this->buildAucMailData($transportData),
            'to_name' => $transportData['to_name'],
            'to_addr' => $transportData['to_addr'],
            'to_tel' => $transportData['to_tel'],
            'pos_no' => $transportData['pos_no'],
            'aa_no' => $transportData['aa_no'],
            'date_of_payment' => $transportData['date_of_payment'],
            'date_of_time' => $this->buildDateOfTime($transportData),
            'car_name' => $transportData['car_name'],
            'car_no' => $transportData['car_no'],
            'tickitems_mail' => $this->buildTickitemsMailData($transportData),
            'plate_cut_flg' => $this->flagReplace($transportData['plate_cut_flg']),
            'plate_no' => $transportData['plate_no'],
            'plate_address_mail' => $this->buildPlateAddressMailData($transportData),
            'country' => $transportData['country'],
            'port' => $transportData['port'],
            'odr_date' => now(),
            'note' => $transportData['txtcomment_biko']
        ];

        $this->sendMailProgram($addresses, $config['template'], $mailData);
    }

    private function sendOnHoldMail($transportData): void
    {
        $customerData = $this->getCustomerData($transportData['m_customer_id']);

        if (!$this->shouldSendNotification($customerData, 'bk_trans_onhold_mail_flg')) {
            return;
        }

        $templateMap = [
            '7875' => ['email' => '<EMAIL>', 'template' => 'admin_cg_transport_onhold'],
            '1005' => ['email' => '<EMAIL>', 'template' => 'admin_transport_onhold'],
            '14192' => ['email' => '<EMAIL>', 'template' => 'admin_jc_transport_onhold'],
            '17078' => ['email' => '<EMAIL>', 'template' => 'admin_ei_transport_onhold']
        ];

        $config = $templateMap[$transportData['m_trans_id']];

        $addresses = [
            'to' => [$config['email']],
            'cc' => [$customerData->e_mail, '<EMAIL>'],
            'bcc' => []
        ];

        if ('14192' == $transportData['m_trans_id']) {
            $addresses['cc'][] = '<EMAIL>';
        }

        $mailData = [
            'id' => $transportData['tp_id'],
            'customer_name' => $transportData['customer_name'],
            'fr_aa_place_id' => $transportData['fr_aa_place_id'],
            'fr_name' => $transportData['fr_name'],
            'fr_addr' => $transportData['fr_addr'],
            'fr_tel' => $transportData['fr_tel'],
            'to_name' => $transportData['to_name'],
            'to_addr' => $transportData['to_addr'],
            'to_tel' => $transportData['to_tel'],
            'pos_no' => $transportData['pos_no'],
            'aa_no' => $transportData['aa_no'],
            'date_of_payment' => $transportData['date_of_payment'],
            'car_name' => $transportData['car_name'],
            'car_no' => $transportData['car_no'],
            'undrivable_flg' => $this->flagReplace2Ja($transportData['undrivable_flg']),
            'tall_flg' => $this->flagReplace2Ja($transportData['tall_flg']),
            'lowdown_flg' => $this->flagReplace2Ja($transportData['lowdown_flg']),
            'long_flg' => $this->flagReplace2Ja($transportData['long_flg']),
            'plate_cut_flg' => $this->flagReplace($transportData['plate_cut_flg']),
            'plate_no' => $transportData['plate_no'],
            'odr_date' => now(),
            'txtcomment_biko' => $transportData['txtcomment_biko']
        ];

        $this->sendMailProgram($addresses, $config['template'], $mailData);
    }

    private function buildEmailAddresses($customerData, $transportEmail, $transId)
    {
        $addresses = [
            'to' => [$transportEmail],
            'cc' => ['<EMAIL>'],
            'bcc' => ['<EMAIL>']
        ];

        if (!empty($customerData->e_mail)) {
            $addresses['to'][] = $customerData->e_mail;
        }

        if (!empty($customerData->ah_send_mailaddress)) {
            $additionalEmails = explode(';', $customerData->ah_send_mailaddress);
            $addresses['to'] = array_merge($addresses['to'], $additionalEmails);
        }

        if ('14192' == $transId) {
            $addresses['cc'][] = '<EMAIL>';
        }

        return $addresses;
    }

    private function shouldSendNotification($customerData, $flagField)
    {
        return 1 == $customerData->ah_sales_mail_flg &&
               1 == $customerData->{$flagField};
    }

    private function flagReplace($flag)
    {
        return '1' == $flag ? 'あり' : 'なし';
    }

    private function flagReplace2Ja($flag)
    {
        return '1' == $flag ? 'あり' : '';
    }

    private function sendMailProgram($addresses, $template, $mailData)
    {
        try {
            Mail::send("emails.transport.{$template}", $mailData, function ($message) use ($addresses): void {
                $message->to($addresses['to'])
                    ->cc($addresses['cc'])
                    ->bcc($addresses['bcc'])
                    ->from(config('mail.from.address'));
            });
            return 'send_ok';
        } catch (Exception $e) {
            Log::error('Mail send failed: ' . $e->getMessage());
            return 'send_NG-> ' . $e->getMessage();
        }
    }

    private function sendMailAttacheFileProgram($addresses, $template, $mailData, $fileData)
    {
        try {
            Mail::send("emails.transport.{$template}", $mailData, function ($message) use ($addresses, $fileData): void {
                $message->to($addresses['to'])
                    ->cc($addresses['cc'])
                    ->bcc($addresses['bcc'])
                    ->from(config('mail.from.address'));

                // ファイル添付
                foreach ($fileData as $file) {
                    if (!empty($file)) {
                        $message->attach($file);
                    }
                }
            });
            return 'send_ok';
        } catch (Exception $e) {
            Log::error('Mail with attachment send failed: ' . $e->getMessage());
            return 'send_NG-> ' . $e->getMessage();
        }
    }
}
