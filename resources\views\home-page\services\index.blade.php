@extends('app')

@section('title')
    {{ __('home-page/services.page_title') }}
@endsection

@push('styles')
    @vite('resources/css/services.css')
@endpush

@section('content')
    <div id="page-top"></div>
    <div class="aos-init w-full" data-aos="zoom-in">
        <div class="2md:h-20 h-[50px] w-full"></div>
        <div
            class="absolute -z-1 w-full bg-[length:auto] bg-fixed bg-[position:top_left]"
            style="background: url('{{ asset('images/services/service_bg_kirara.gif') }}')"
        >
            <div id="service-bg-01" class="2md:h-10 h-[37px] w-full"></div>
            <div
                id="service-bg-02"
                class="2sm:h-20 h-[45px] w-full bg-[rgba(255,255,255,0.6)] shadow-[1px_1px_10px_0px_#adb5bd,-1px_-1px_10px_0px_#adb5bd]"
            ></div>
            <div id="service-bg-03" class="2sm:h-[55px] 2md:h-[75px] h-[45px] w-full"></div>
        </div>
    </div>
    <div class="w-full px-0 md:px-[15px]">
        <div class="mx-auto mt-[45px] flex max-w-6xl">
            <div
                class="aos-init aos-animate relative w-full px-[15px]"
                data-aos="zoom-in-right"
                data-aos-duration="1000"
            >
                <h1
                    class="2sm:text-32 2sm:mt-[7px] 2md:text-40 2md:mt-[14px] 2md:pl-0 -mt-2 pl-[10%] text-xl font-bold md:mt-1 md:text-[35px]"
                >
                    {{ __('home-page/services.title') }}
                </h1>
                <p
                    class="2sm:text-base 2sm:-top-3 2md:text-lg 2md:top-[-47px] 2md:ml-[50px] 2md:text-center text-11 relative top-[-10px] mb-4 text-left md:top-[-10px] lg:ml-0 lg:text-xl"
                >
                    {{ __('home-page/services.content') }}
                </p>
            </div>
        </div>
        <x-common.hubnet-button />
        <x-services.group-animation />
        <div class="2sm:mt-[50px] mt-[-9px]"></div>
        <div class="2sm:mt-[45px] mx-auto mt-[5px] max-w-6xl">
            <div class="flex w-full flex-wrap">
                @foreach ($pageMenus as $index => $menu)
                    <x-services.page-menu
                        classColor="{{ $menu['color'] }}"
                        href="#page{{ $index + 1 }}"
                        content="{!! __('home-page/services.page_menu_' . ($index + 1)) !!}"
                    />
                @endforeach
            </div>
        </div>
        <img
            id="ah_service_road"
            alt="ah_service_road"
            class="hidden w-full xl:block"
            src="{{ asset('images/services/ah_service_road.png') }}"
        />
        <img
            id="ah_service_road_m"
            alt="ah_service_road_m"
            class="mt-[40px] hidden w-full md:block lg:mt-0 xl:hidden"
            src="{{ asset('images/services/ah_service_road_m.png') }}"
        />
        <img
            id="ah_service_road_s"
            alt="ah_service_road_s"
            class="2sm:mt-0 mt-[25px] block w-full md:hidden"
            src="{{ asset('images/services/ah_service_road_s.png') }}"
        />
        <div id="page1"></div>
        <div class="mb-[250px] w-full">
            @foreach ($services as $index => $service)
                <x-services.service-item
                    :index="$index + 1"
                    :title="__('home-page/services.service_title_' . ($index + 1))"
                    :description="__('home-page/services.service_description_' . ($index + 1))"
                    :link="$service['link']"
                    :bgImage="$service['bgImage']"
                    :bgBack="$service['bgBack']"
                    :bgContainer="$service['bgContainer']"
                />
            @endforeach
        </div>
    </div>
    <a href="#page-top">
        <img
            class="2sm:w-[85px] 2sm:bottom-[210px] 2sm:right-[15px] 2md:none fixed right-[15px] bottom-[20px] z-10 w-[65px] md:right-auto md:bottom-[195px] md:w-[100px] lg:w-[115px]"
            src="{{ asset('images/services/ah_service_backtop.gif') }}"
            alt="ah_service_backtop"
        />
    </a>
@endsection

@once
    @push('scripts')
        @vite('resources/js/service.js')
        <script>
            $(function () {
                $('#add_line_close').click(function () {
                    $('#hubnet-btn').fadeOut()
                })
                $('#add_line_close_768').click(function () {
                    $('#hubnet-btn').fadeOut()
                })
            })
        </script>
    @endpush
@endonce
