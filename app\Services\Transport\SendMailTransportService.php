<?php

declare(strict_types=1);

namespace App\Services\Transport;

use App\Enums\Transport\Statuses;
use App\Enums\Transport\TransIdDefault;
use App\Enums\UserRole;
use App\Jobs\SendMailAdminTransportChangeCommentJob;
use App\Jobs\SendMailAdminTransportChangePlateJob;
use App\Jobs\SendMailAdminTransportChangePriceJob;
use App\Jobs\SendMailTransportChangeStatusJob;
use App\Repositories\sqlServer\TransportRepository;
use App\Repositories\sqlServer\UserRepository;
use App\Traits\CommonTrait;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class SendMailTransportService
{
    use CommonTrait;

    public function __construct(
        private TransportRepository $transportRepository,
        private UserRepository $userRepository
    ) {
    }

    /**
     * Send mail notification cho tất cả trường hợp
     *
     * @param string $type // 'update_info', 'comment_change', 'status_change'
     * @param array $params // Parameters cho từng loại mail
     * @return bool
     */
    public function sendMailNotification(string $type, array $params = []): void
    {
        match ($type) {
            'update_info' => $this->sendMailNotifyByUpdateInfo($params['newRecord'], $params['originRecord']),
            'comment_change' => $this->sendMailNotifyByCommentChange($params['transport'], $params['comment']),
            'status_change' => $this->sendMailNotifyByUpdateStatus($params['idListTransport'], $params['status']),
            default => null,
        };
    }

    /**
     * Send mail notify
     *
     * @param object $newRecord // transport record updated
     * @param object $originRecord // transport record original
     * @return void
     */
    public function sendMailNotifyByUpdateInfo($newRecord, $originRecord): void
    {
        $flagChangePrice = false;
        $flagChangePlateSend = false;
        if ($newRecord->deli_price != $originRecord->deli_price) {
            $flagChangePrice = true;
        }   
        if ($newRecord->plate_send_no != $originRecord->plate_send_no || $newRecord->plate_send_co != $originRecord->plate_send_co) {
            $flagChangePlateSend = true;
        }
        if (!$newRecord->plate_send_date || !$newRecord->plate_send_co || !$newRecord->plate_send_no) {
            $flagChangePlateSend = false;
        }
        // get info mail
        $conditions = [
            't_transport.del_flg' => 0,
        ];

        if (Auth::user()->hn_cd == UserRole::admin->value) {
            $conditions['t_transport.id'] = $originRecord->id;
        } else {
            $conditions['t_transport.tp_id'] = $originRecord->id;
        }
        $transportCustomer = $this->transportRepository->getInfoTransportCustomer($conditions);
        $customerSales = $this->userRepository->getListCustomerSales([
            'm_customer.id' => Auth::user()->id,
            'm_customer.del_flg' => 0,
        ]);
        $ahStaffMailAddress = [];
        if ($customerSales) {
            if ($customerSales?->ah_sales_mail_flg
            && $customerSales?->bk_trans_price_mail_flg
            && $customerSales?->ah_send_mailaddress) {
                $ahStaffMailAddress = array_merge([$customerSales?->e_mail], explode(';', $customerSales?->ah_send_mailaddress ?? ''));
            }

            if ($customerSales?->ah_sales_mail_flg
            && $customerSales?->bk_trans_plate_mail_flg
            && $customerSales?->ah_send_mailaddress) {
                $ahStaffMailAddress = array_merge([$customerSales?->e_mail], explode(';', $customerSales?->ah_send_mailaddress ?? ''));
            }
        }
        if (0 == count($ahStaffMailAddress)) {
            return;
        }
        $mailInfoConfig = Arr::get(config('mail.mail_info'), config('app.env'));
        if ($flagChangePrice || $flagChangePlateSend) {
            if ($flagChangePrice) {
                $toAddress = $ahStaffMailAddress;
                $ccAddress = [Arr::get($mailInfoConfig, 'trans_mail')];
                $bccAddress = [
                    Arr::get($mailInfoConfig, 'bcc'),
                    Arr::get($mailInfoConfig, 'ptrans_mail'),
                ];

                if (TransIdDefault::J_BRING == $newRecord->m_trans_id) {
                    $ccAddress = array_merge($ccAddress, [Arr::get($mailInfoConfig, 'shipping_mail')]);
                }

                $dataMail = [
                    'mail_from' => [
                        'name' => Arr::get(config('mail.mail_name'), 'from'),
                        'mail' => Arr::get($mailInfoConfig, 'from'),
                    ],
                    'mail_address' => $ahStaffMailAddress,
                    'mail_cc' => $ccAddress,
                    'mail_bcc' => $bccAddress,
                    'mail_to' => $toAddress,
                    'data_info' => $this->getDataMailChangePriceTransport($newRecord, $customerSales, $transportCustomer),
                ];

                // Send multiple mail
                foreach ($ahStaffMailAddress as $mailAddress) {
                    SendMailAdminTransportChangePriceJob::dispatchSync(
                        $mailAddress,
                        $dataMail
                    );
                }

            }
        }

        if ($flagChangePlateSend) {
            $ccAddress = [];
            $bccAddress = Arr::get($mailInfoConfig, 'bcc');
            $toAddress = $ahStaffMailAddress;
            if (TransIdDefault::J_BRING == $newRecord->m_trans_id) {
                $ccAddress = array_merge($ccAddress, [Arr::get($mailInfoConfig, 'shipping_mail')]);
            }

            $dataMail = [
                'mail_from' => [
                    'name' => Arr::get(config('mail.mail_name'), 'from'),
                    'mail' => Arr::get($mailInfoConfig, 'from'),
                ],
                'mail_address' => $ahStaffMailAddress,
                'mail_cc' => $ccAddress,
                'mail_bcc' => $bccAddress,
                'mail_to' => $toAddress,
                'data_info' => $this->getDataMailChangePlateSendTransport($newRecord, $customerSales, $transportCustomer),
            ];

            // Send multiple mail
            foreach ($ahStaffMailAddress as $mailAddress) {
                SendMailAdminTransportChangePlateJob::dispatchSync(
                    $mailAddress,
                    $dataMail
                );
            }
        }

    }

    public function sendMailNotifyByCommentChange($transport, $comment): void
    {
        // Get transport customer info
        $conditions = [
            't_transport.del_flg' => 0,
        ];

        if (Auth::user()->hn_cd == UserRole::admin->value) {
            $conditions['t_transport.id'] = $transport->id;
        } else {
            $conditions['t_transport.tp_id'] = $transport->id;
        }
        $transportCustomer = $this->transportRepository->getInfoTransportCustomer($conditions);
        
        // Get customer sales info
        $customerSales = $this->userRepository->getListCustomerSales([
            'm_customer.id' => Auth::user()->id,
            'm_customer.del_flg' => 0,
        ]);

        $ahStaffMailAddress = [];
        if ($customerSales && $customerSales?->ah_sales_mail_flg && $customerSales?->ah_send_mailaddress) {
            $ahStaffMailAddress = array_merge([$customerSales?->e_mail], explode(';', $customerSales?->ah_send_mailaddress ?? ''));
        }

        if (0 == count($ahStaffMailAddress)) {
            return;
        }

        $mailInfoConfig = Arr::get(config('mail.mail_info'), config('app.env'));
        
        // Xác định email công ty vận chuyển dựa trên m_trans_id
        $transCompanyEmail = match ((string) $transport->m_trans_id) {
            TransIdDefault::TARGET->value => Arr::get($mailInfoConfig, 'mail_address_order'), // Carry Goal: <EMAIL>
            TransIdDefault::SHIPPING_EAST_AND_WEST->value => Arr::get($mailInfoConfig, 'mail_address_oosaka'), // Touzaikaiun: <EMAIL>
            TransIdDefault::J_BRING->value => Arr::get($mailInfoConfig, 'mail_address_transport'), // J-Carry: <EMAIL>
            TransIdDefault::EIKO_SHOUNEN->value => Arr::get($mailInfoConfig, 'mail_address_yamashita'), // Eiko Shoun: <EMAIL>
            default => null, // Các công ty khác: Không có email
        };

        $toAddress = [];
        if ($transCompanyEmail) {
            $toAddress[] = $transCompanyEmail;
        }
        $toAddress = array_merge($toAddress, $ahStaffMailAddress);

        $ccAddress = [];
        $bccAddress = Arr::get($mailInfoConfig, 'bcc');

        $dataMail = [
            'mail_from' => [
                'name' => Arr::get(config('mail.mail_name'), 'from'),
                'mail' => Arr::get($mailInfoConfig, 'from'),
            ],
            'mail_address' => $toAddress,
            'mail_cc' => $ccAddress,
            'mail_bcc' => $bccAddress,
            'mail_to' => $toAddress,
            'data_info' => $this->getDataMailChangeCommentTransport($transport, $customerSales, $transportCustomer, $comment),
        ];

        foreach ($toAddress as $mailAddress) {
            SendMailAdminTransportChangeCommentJob::dispatchSync(
                $mailAddress,
                $dataMail
            );
        }
    }

    public function sendMailNotifyByUpdateStatus(array $idListTransport, $status): void
    {
        // Get transports
        $transports = $this->transportRepository->getTransportsByIdList($idListTransport);

        $transports = $this->transportRepository->getInfoTransportByIdList($idListTransport);

        $customerTransIds = $transports->pluck('m_trans_id')->unique()->values()->toArray();

        $salesCustomerTrans = $this->userRepository->getListCustomerSalesByIdList(
            $customerTransIds,
            [
                'm_customer.del_flg' => 0,
            ]
        );

        $transports->each(function ($item) use ($status, $salesCustomerTrans): void {
            $mTransId = $item->m_trans_id;

            // Get customer sales
            $customerSales = $salesCustomerTrans->where('id', $mTransId)?->first();

            // 同時に内勤営業担当を取得（アドミン宛メールを送信する）
            $ahStaffEmail = $customerSales->e_mail;

            $dataMappingMTransId = match ((string) $mTransId) {
                TransIdDefault::SHIPPING_EAST_AND_WEST->value => [
                    'trans_name' => '東西海運',
                    'trans_name_note' => '東西海運株式会社',
                    'trans_note_email' => '<EMAIL>',
                    'send_mail_flg' => 1,
                ],
                TransIdDefault::TARGET->value => [
                    'trans_name' => 'キャリーゴール',
                    'trans_name_note' => '株式会社キャリーゴール',
                    'trans_note_email' => '<EMAIL>',
                    'send_mail_flg' => 1,
                ],
                TransIdDefault::J_BRING->value => [
                    'trans_name' => 'ジェイキャリー',
                    'trans_name_note' => '株式会社ジェイ・キャリー',
                    'trans_note_email' => '<EMAIL>',
                    'send_mail_flg' => 1,
                ],
                TransIdDefault::EIKO_SHOUNEN->value => [
                    'trans_name' => '栄港商運',
                    'trans_name_note' => '株式会社栄港商運',
                    'trans_note_email' => '<EMAIL>',
                    'send_mail_flg' => 1,
                ],
                default => [
                    'trans_name' => '',
                    'trans_name_note' => '',
                    'trans_note_email' => '',
                    'send_mail_flg' => 0,
                ],
            };

            // メール通知設定取得（AH）
            $repAddress = trim($customerSales?->e_mail ?? '');
            $ahSendMailAddress = trim($customerSales?->ah_send_mailaddress ?? '');
            $ahSalesMailFlg = trim($customerSales?->ah_sales_mail_flg ?? '');
            $bkTransCancelMailFlg = $customerSales?->bk_trans_cancel_mail_flg ?? '';
            $bkTransOnholdMailFlg = $customerSales?->bk_trans_onhold_mail_flg ?? '';

            // キャンセル通知
            if ($ahSalesMailFlg && $bkTransCancelMailFlg && $ahSendMailAddress) {
                $ahSendMailAddress = array_merge([$ahStaffEmail], explode(';', $ahSendMailAddress));
            }

            // 保留通知
            if ($ahSalesMailFlg && $bkTransOnholdMailFlg && $ahSendMailAddress) {
                $ahSendMailAddress = array_merge([$ahStaffEmail], explode(';', $ahSendMailAddress));
            }

            // メール送信
            if (in_array((string) $mTransId, [
                TransIdDefault::SHIPPING_EAST_AND_WEST->value,
                TransIdDefault::TARGET->value,
                TransIdDefault::J_BRING->value,
                TransIdDefault::EIKO_SHOUNEN->value
            ])) {

                $tickItemMail = "";
                if ((int) ($item->undrivable_flg)) {
                    $tickItemMail .= trans('mail.lan_undrivable') . "<br/>";
                }
                if ((int) ($item->tall_flg)) {
                    $tickItemMail .= trans('mail.lan_tall') . "<br/>";
                }
                if ((int) ($item->lowdown_flg)) {
                    $tickItemMail .= trans('mail.lan_lowdown') . "<br/>";
                }
                if ((int) ($item->long_flg)) {
                    $tickItemMail .= trans('mail.lan_long') . "<br/>";
                }
                if ((int) ($item->old_flg)) {
                    $tickItemMail .= trans('mail.lan_old_car') . "<br/>";
                }
                if ((int) ($item->luxury_flg)) {
                    $tickItemMail .= trans('mail.lan_luxury_car');
                    if ('1' == $item->luxury_flg_insrance) {
                        $tickItemMail .= '(' . trans('mail.lan_luxyry_insurance1') . ')';
                    } elseif ('2' == $item->luxury_flg_other) {
                        $tickItemMail .= '(' . trans('mail.lan_luxyry_insurance2') . ')';
                    } else {
                        $tickItemMail .= "<br/>";
                    }
                }
                if ((int) ($item->other_flg)) {
                    $tickItemMail .= trans('mail.lan_other_car') . ' (' . $item->other_flg_txt . ") <br/>";
                }
                if ((int) ($item->tick_no_flg)) {
                    $tickItemMail .= trans('mail.lan_gaitou_nasi') . "<br/>";
                }

                $aucMail = '';
                if ((int) ($item->auction_chk)) {
                    $aucMail .= trans('mail.lan_aucBoth') . "<br/>";
                } else {
                    $aucMail .= trans('mail.lan_auc') . "<br/>";
                    if ($item->auc_name && $item->auc_tel && $item->auc_addr) {
                        $aucMail .= trans('mail.lan_other_auc') . ': ' . $item->auc_name . "<br/>";
                        $aucMail .= trans('mail.lan_other_auc_addr') . ': ' . $item->auc_addr . "<br/>";
                        $aucMail .= trans('mail.lan_other_auc_tel') . ': ' . $item->auc_tel . "<br/>";
                    }
                }

                $plateAddressMail = '';
                if (trim($item->plate_send_name ?? '')) {
                    $plateAddressMail .= "<br/> 〒" . $item->plate_send_zipcd . "<br/> " . $item->plate_send_address . "<br/> " . $item->plate_send_tel . "<br/>";
                }

                $dateOfTime = match ((string) ($item->date_of_payment_time)) {
                    '1' => ' (' . trans('mail.lan_payment_time1') . ')',
                    '2' => ' (' . trans('mail.lan_payment_time2') . ')',
                    '3' => ' (' . trans('mail.lan_payment_time3') . ')',
                    default => '',
                };

                // Cancel notification
                $mailInfoByEnv = config('mail.mail_info.' . env('APP_ENV'));
                if ($status == Statuses::CANCELED->value) {
                    $dataMail = match ((string) $mTransId) {
                        TransIdDefault::SHIPPING_EAST_AND_WEST->value => [
                            'to_address' => Arr::get($mailInfoByEnv, 'mail_address_oosaka'),
                            'cc_address' => [
                                $repAddress,
                                Arr::get($mailInfoByEnv, 'trans_mail'),
                            ],
                            'file_name_template' => "admin-transport-cancel",
                        ],
                        TransIdDefault::TARGET->value => [
                            'to_address' => Arr::get($mailInfoByEnv, 'mail_address_order'),
                            'cc_address' => [
                                $repAddress,
                                Arr::get($mailInfoByEnv, 'trans_mail'),
                            ],
                            'file_name_template' => "admin-cg-transport-cancel",
                        ],
                        TransIdDefault::J_BRING->value => [
                            'to_address' => Arr::get($mailInfoByEnv, 'mail_address_transport'),
                            'cc_address' => [
                                $repAddress,
                                Arr::get($mailInfoByEnv, 'trans_mail'),
                            ],
                            'file_name_template' => "admin-jc-transport-cancel",
                        ],
                        TransIdDefault::EIKO_SHOUNEN->value => [
                            'to_address' => Arr::get($mailInfoByEnv, 'mail_address_yamashita'),
                            'cc_address' => [
                                $repAddress,
                                Arr::get($mailInfoByEnv, 'trans_mail'),
                            ],
                            'file_name_template' => "admin-ei-transport-cancel",
                        ],
                        default => [
                            'to_address' => '',
                            'cc_address' => [],
                            'file_name_template' => '',
                        ],
                    };

                    if ($mTransId == TransIdDefault::J_BRING->value) {
                        $dataMail['cc_address'] = array_merge($dataMail['cc_address'], [Arr::get($mailInfoByEnv, 'shipping_mail')]);
                    }


                    $dataMail['cc_address'] = collect($dataMail['cc_address'])->filter(fn ($item) => $item)->values()->toArray();
                    $dataMail['mail_from'] = [
                        'name' => config('mail.mail_name.from'),
                        'mail' => Arr::get($mailInfoByEnv, 'from'),
                    ];
                    $dataMail['mail_bcc'] = Arr::get($mailInfoByEnv, 'bcc');

                    SendMailTransportChangeStatusJob::dispatchSync(
                        $dataMail['to_address'],
                        [
                            ...$dataMail,
                            'data_mail' => [
                                ...$dataMappingMTransId,
                                'id' => $item->tp_id,
                                'ah_staff_name' => $item->s_name,
                                'transport_id' => $item->tp_id,
                                'plate_send_date' => $item->plate_send_date,
                                'plate_send_co' => $item->plate_send_co,
                                'plate_send_no' => $item->plate_send_no,
                                'customer_name' => $customerSales?->cus_name_JP ?? '',
                                'fr_aa_place_id' => $item->fr_aa_place_id,
                                'fr_name' => $item->fr_name,
                                'fr_addr' => $item->fr_addr,
                                'fr_tel' => $item->fr_tel,
                                'auction_date' => $item->auction_date,
                                'auc_mail' => $aucMail,
                                'to_name' => $item->to_name,
                                'to_addr' => $item->to_addr,
                                'to_tel' => $item->to_tel,
                                'pos_no' => $item->pos_no,
                                'aa_no' => $item->aa_no,
                                'date_of_payment' => $item->date_of_payment,
                                'date_of_time' => $dateOfTime,
                                'car_name' => $item->car_name,
                                'car_no' => $item->car_no,
                                'tickitems_mail' => $tickItemMail,
                                'plate_cut_flg' => $this->getFlagReplace2Ja((string) ($item->plate_cut_flg)),
                                'undrivable_flg' => $this->getFlagReplace2Ja((string) ($item->undrivable_flg)),
                                'tall_flg' => $this->getFlagReplace2Ja((string) ($item->tall_flg)),
                                'lowdown_flg' => $this->getFlagReplace2Ja((string) ($item->lowdown_flg)),
                                'long_flg' => $this->getFlagReplace2Ja((string) ($item->long_flg)),
                                'plate_no' => $item->plate_no,
                                'plate_address_mail' => $plateAddressMail,
                                'country' => $item->country,
                                'port' => $item->port,
                                'odr_date' => now(),
                                'note' => $item->note,
                                'status' => $status,
                            ]
                        ]
                    );
                }

                // On-hold notification
                if ($status == Statuses::ON_HOLD->value) {
                    Log::info('On-hold mail debug', [
                        'mTransId' => $mTransId,
                        'status' => $status,
                        'ahSalesMailFlg' => $ahSalesMailFlg,
                        'bkTransOnholdMailFlg' => $bkTransOnholdMailFlg,
                        'ahSendMailAddress' => $ahSendMailAddress,
                        'ahStaffEmail' => $ahStaffEmail
                    ]);
                    
                    if ($ahSalesMailFlg && $bkTransOnholdMailFlg && $ahSendMailAddress) {
                        $ahSendMailAddress = array_merge([$ahStaffEmail], explode(';', $ahSendMailAddress));
                    }

                    $dataMail = match ((string) $mTransId) {
                        TransIdDefault::SHIPPING_EAST_AND_WEST->value => [
                            'to_address' => Arr::get($mailInfoByEnv, 'mail_address_oosaka'),
                            'cc_address' => [
                                $repAddress,
                                Arr::get($mailInfoByEnv, 'trans_mail'),
                            ],
                            'file_name_template' => "admin-transport-onhold",
                        ],
                        TransIdDefault::TARGET->value => [
                            'to_address' => Arr::get($mailInfoByEnv, 'mail_address_order'),
                            'cc_address' => [
                                $repAddress,
                                Arr::get($mailInfoByEnv, 'trans_mail'),
                            ],
                            'file_name_template' => "admin-cg-transport-onhold",
                        ],
                        TransIdDefault::J_BRING->value => [
                            'to_address' => Arr::get($mailInfoByEnv, 'mail_address_transport'),
                            'cc_address' => [
                                $repAddress,
                                Arr::get($mailInfoByEnv, 'trans_mail'),
                            ],
                            'file_name_template' => "admin-jc-transport-onhold",
                        ],
                        TransIdDefault::EIKO_SHOUNEN->value => [
                            'to_address' => Arr::get($mailInfoByEnv, 'mail_address_yamashita'),
                            'cc_address' => [
                                $repAddress,
                                Arr::get($mailInfoByEnv, 'trans_mail'),
                            ],
                            'file_name_template' => "admin-ei-transport-onhold",
                        ],
                        default => [
                            'to_address' => '',
                            'cc_address' => [],
                            'file_name_template' => '',
                        ],
                    };

                    // For J-Carry, also send to Kitagawa-san
                    if ($mTransId == TransIdDefault::J_BRING->value) {
                        $dataMail['cc_address'] = array_merge($dataMail['cc_address'], [Arr::get($mailInfoByEnv, 'shipping_mail')]);
                    }

                    $dataMail['cc_address'] = collect($dataMail['cc_address'])->filter(fn ($item) => $item)->values()->toArray();
                    $dataMail['mail_from'] = [
                        'name' => config('mail.mail_name.from'),
                        'mail' => Arr::get($mailInfoByEnv, 'from'),
                    ];
                    $dataMail['mail_bcc'] = Arr::get($mailInfoByEnv, 'bcc_mail_address_transport_onhold');

                    SendMailTransportChangeStatusJob::dispatchSync(
                        $dataMail['to_address'],
                        [
                            ...$dataMail,
                            'data_mail' => [
                                ...$dataMappingMTransId,
                                'id' => $item->tp_id,
                                'ah_staff_name' => $item->s_name,
                                'transport_id' => $item->tp_id,
                                'plate_send_date' => $item->plate_send_date,
                                'plate_send_co' => $item->plate_send_co,
                                'plate_send_no' => $item->plate_send_no,
                                'customer_name' => $customerSales?->cus_name_JP ?? '',
                                'fr_aa_place_id' => $item->fr_aa_place_id,
                                'fr_name' => $item->fr_name,
                                'fr_addr' => $item->fr_addr,
                                'fr_tel' => $item->fr_tel,
                                'auction_date' => $item->auction_date,
                                'auc_mail' => $aucMail,
                                'to_name' => $item->to_name,
                                'to_addr' => $item->to_addr,
                                'to_tel' => $item->to_tel,
                                'pos_no' => $item->pos_no,
                                'aa_no' => $item->aa_no,
                                'date_of_payment' => $item->date_of_payment,
                                'date_of_time' => $dateOfTime,
                                'car_name' => $item->car_name,
                                'car_no' => $item->car_no,
                                'tickitems_mail' => $tickItemMail,
                                'plate_cut_flg' => $this->getFlagReplace2Ja((string) ($item->plate_cut_flg)),
                                'undrivable_flg' => $this->getFlagReplace2Ja((string) ($item->undrivable_flg)),
                                'tall_flg' => $this->getFlagReplace2Ja((string) ($item->tall_flg)),
                                'lowdown_flg' => $this->getFlagReplace2Ja((string) ($item->lowdown_flg)),
                                'long_flg' => $this->getFlagReplace2Ja((string) ($item->long_flg)),
                                'plate_no' => $item->plate_no,
                                'plate_address_mail' => $plateAddressMail,
                                'country' => $item->country,
                                'port' => $item->port,
                                'odr_date' => now(),
                                'note' => $item->note,
                                'status' => $status,
                            ]
                        ]
                    );

                }

            }
        });
    }

    private function getDataMailChangePlateSendTransport($transport, $customerSales, $transportCustomer): array
    {
        $transportId = Auth::user()->hn_cd == UserRole::admin->value ? $transport->id : $transport->tp_id;
        $mTransName = TransIdDefault::getNameTransId($transport->m_trans_id);
        return [
            'id' => $transportId,
            'ah_staff_name' => $customerSales?->s_name,
            'trans_name' => $mTransName,
            'transport_id' => $transportId,
            'plate_send_date' => $transport->plate_send_date,
            'plate_send_co' => $transport->plate_send_co,
            'plate_send_no' => $transport->plate_send_no,
            'customer_name' => $customerSales?->cus_name_JP,
            'fr_name' => $transportCustomer->fr_name,
            'fr_addr' => $transportCustomer->fr_addr,
            'fr_tel' => $transportCustomer->fr_tel,
            'pos_no' => $transportCustomer->pos_no,
            'aa_no' => $transportCustomer->aa_no,
            'date_of_payment' => $customerSales->date_of_payment,
            'car_no' => $transportCustomer->car_no,
            'undrivable_flg' => $this->getFlagReplace2Ja($transportCustomer->undrivable_flg),
            'tall_flg' => $this->getFlagReplace2Ja($transportCustomer->tall_flg),
            'lowdown_flg' => $this->getFlagReplace2Ja($transportCustomer->lowdown_flg),
            'long_flg' => $this->getFlagReplace2Ja($transportCustomer->long_flg),
            'car_name' => $transportCustomer->car_name,
            'plate_cut_flg' => $this->getFlagReplace2Ja($transportCustomer->plate_cut_flg),
            'plate_no' => $transportCustomer->plate_no,
            'country' => $transportCustomer->country,
            'to_name' => $transportCustomer->to_name,
            'to_addr' => $transportCustomer->to_addr,
            'to_tel' => $transportCustomer->to_tel,
        ];
    }

    private function getDataMailChangePriceTransport($transport, $customerSales, $transportCustomer): array
    {
        $transportId = Auth::user()->hn_cd == UserRole::admin->value ? $transport->id : $transport->tp_id;
        $mTransName = TransIdDefault::getNameTransId($transport->m_trans_id);
        return [
            'id' => $transportId,
            'ah_staff_name' => $customerSales->s_name,
            'trans_name' => $mTransName,
            'transport_id' => $transportId,
            'last_deli_price' => $transport->deli_price,
            'deli_price' => $transport->deli_price,
            'customer_name' => $customerSales->cus_name_JP,
            'fr_aa_place_id' => $transportCustomer->fr_aa_place_id,
            'fr_name' => $transportCustomer->fr_name,
            'fr_addr' => $transportCustomer->fr_addr,
            'fr_tel' => $transportCustomer->fr_tel,
            'to_name' => $transportCustomer->to_name,
            'to_addr' => $transportCustomer->to_addr,
            'to_tel' => $transportCustomer->to_tel,
            'pos_no' => $transportCustomer->pos_no,
            'aa_no' => $transportCustomer->aa_no,
            'date_of_payment' => $customerSales->date_of_payment,
            'car_name' => $transportCustomer->car_name,
            'car_no' => $transportCustomer->car_no,
            'undrivable_flg' => $this->getFlagReplace2Ja($transportCustomer->undrivable_flg),
            'tall_flg' => $this->getFlagReplace2Ja($transportCustomer->tall_flg),
            'lowdown_flg' => $this->getFlagReplace2Ja($transportCustomer->lowdown_flg),
            'long_flg' => $this->getFlagReplace2Ja($transportCustomer->long_flg),
            'plate_cut_flg' => $this->getFlagReplace2Ja($transportCustomer->plate_cut_flg),
            'plate_no' => $transportCustomer->plate_no,
            'country' => $transportCustomer->country,
            'note' => $transportCustomer->note,
            'odr_date' => $transportCustomer->odr_date,
        ];
    }

    private function getDataMailChangeCommentTransport($transport, $customerSales, $transportCustomer, $comment): array
    {
        $transportId = Auth::user()->hn_cd == UserRole::admin->value ? $transport->id : $transport->tp_id;
        $mTransName = TransIdDefault::getNameTransId($transport->m_trans_id);
        return [
            'id' => $transportId,
            'ah_staff_name' => $customerSales?->s_name,
            'trans_name' => $mTransName,
            'transport_id' => $transportId,
            'note' => $comment,
            'customer_name' => $customerSales?->cus_name_JP,
            'fr_name' => $transportCustomer->fr_name,
            'fr_addr' => $transportCustomer->fr_addr,
            'fr_tel' => $transportCustomer->fr_tel,
            'pos_no' => $transportCustomer->pos_no,
            'aa_no' => $transportCustomer->aa_no,
            'date_of_payment' => $customerSales?->date_of_payment,
            'car_no' => $transportCustomer->car_no,
            'undrivable_flg' => $this->getFlagReplace2Ja($transportCustomer->undrivable_flg),
            'tall_flg' => $this->getFlagReplace2Ja($transportCustomer->tall_flg),
            'lowdown_flg' => $this->getFlagReplace2Ja($transportCustomer->lowdown_flg),
            'long_flg' => $this->getFlagReplace2Ja($transportCustomer->long_flg),
            'car_name' => $transportCustomer->car_name,
            'plate_cut_flg' => $this->getFlagReplace2Ja($transportCustomer->plate_cut_flg),
            'plate_no' => $transportCustomer->plate_no,
            'country' => $transportCustomer->country,
            'to_name' => $transportCustomer->to_name,
            'to_addr' => $transportCustomer->to_addr,
            'to_tel' => $transportCustomer->to_tel,
        ];
    }
}
