<?php

declare(strict_types=1);

namespace App\Modules\SailingSchedule\Repositories;

use App\Models\as400\VesselSchedule;
use App\Modules\SailingSchedule\Constants\PortAreaOrder;
use App\Repositories\BaseRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;

class VesselScheduleRepository extends BaseRepository
{
    public function model(): mixed
    {
        return VesselSchedule::class;
    }

    public function getSchedules(array $params, array $ports): array
    {
        $countryCode = $params['country'] ?? null;
        $portCode = $params['port'] ?? null;
        $areaCode = $params['route'] ?? null;
        $pol = $params['pol'] ?? null;

        $startDateInput = $params['startDate'] ?? null;
        $startDateParsed = $this->transformDate($startDateInput);
        $startDate = ($startDateParsed < date('Ymd')) ? date('Ymd') : $startDateParsed;

        $endDateInput = $params['endDate'] ?? null;
        $endDateParsed = $endDateInput ? $this->transformDate($endDateInput) : null;
        $endDate = !$endDateParsed ? null : ($endDateParsed < date('Ymd') ? date('Ymd') : $endDateParsed);

        $paramSchedule = [];
        for ($i = 1; $i <= PortAreaOrder::NUMBER_OF_POL; $i++) {
            $paramSchedule["startDate{$i}"] = $startDate;
        }

        $select = [
            'MFSCDP.FSFBNO AS "shipping_number"',
            'MFSCDP.FSSPCD AS "ship_code"',
            'MFSCDP.FSVYNO AS "voy_no"'
        ];
        for ($i = 1; $i <= PortAreaOrder::NUMBER_OF_POL; $i++) {
            $index = $this->formatNumberToString($i);
            $select[] = 'MFSCDP.FSSD' . $index . ' AS "date_arrival_date_' . $i . '"';
            $select[] = 'MFSCDP.FSNP' . $index . ' AS "port_in_' . $i . '"';
        }
        for ($i = 1; $i <= PortAreaOrder::NUMBER_OF_POD; $i++) {
            $index = $this->formatNumberToString($i);
            $select[] = 'MFSCDP.FSET' . $index . ' AS "except_date_' . $i . '"';
            $select[] = 'MFSCDP.FSGP' . $index . ' AS "port_out_' . $i . '"';
        }
        $select[] = 'MVESLP.VSNN47 AS "ship_name"';
        $select[] = 'MSHAIPP.SHNO38 AS "company_name"';

        $sql = "SELECT " . implode(', ', $select) . "
        FROM HCDLIB.MFSCDP AS MFSCDP
        LEFT JOIN ABDLIB.MVESLP AS MVESLP ON CAST(MVESLP.VSCC72 AS DECIMAL(4,0)) = MFSCDP.FSSPCD
        LEFT JOIN ABDLIB.MSHIPP AS MSHAIPP ON MSHAIPP.SHCS18 = MFSCDP.FSCMCD
        LEFT JOIN HCDLIB.MKOROP AS MKOROP ON MKOROP.KRKRCD = MFSCDP.FSKRCD
        WHERE MFSCDP.FSFL01 = '1'";

        $bindings = [];
        if ($areaCode) {
            $portsInArea = array_filter($ports, fn ($port) => (string)($port['route_code']) == (string)($areaCode));

            $scheduleByRoute = $this->buildQueryFilterByRoute(implode(',', array_keys($portsInArea)), $endDate);
            $sql .= $scheduleByRoute['query'];
            $bindings = array_merge($bindings, $scheduleByRoute['paramEndDate']);
        }

        // Filter by POL
        $internalPort = $this->buildQueryFilterByInternalPort($pol);
        $sql .= $internalPort['query'];

        $bindings = array_merge($bindings, $internalPort['paramPort']);

        // Filter by POD
        if ($portCode) {
            for ($i = 1; $i <= PortAreaOrder::NUMBER_OF_POD; $i++) {
                $paramSchedule["portCode{$i}"] = $portCode;
            }
        }
        $externalPort = $this->buildQueryFilterByPort($portCode, $endDate, $startDate);
        $sql .= $externalPort['query'];
        $bindings = array_merge($bindings, $externalPort['paramEndDate']);

        $bindings = array_merge($paramSchedule, $bindings);

        // Execute query
        $results = DB::connection('as400')->select($sql, $bindings);

        // Mapping port name
        $schedules = array_map(fn ($item) => (array)$item, $results);

        foreach ($schedules as $key => $schedule) {
            $schedules[$key] = $this->mappingPortName($schedule, $ports);
        }

        // Filter by country if needed
        if (!$portCode && $countryCode) {
            $schedules = array_filter($schedules, function ($value) use ($countryCode, $endDate) {
                for ($i = 1; $i <= PortAreaOrder::NUMBER_OF_POD; $i++) {
                    if (!isset($value['sc_external_' . $i]['country_code']) || $value['sc_external_' . $i]['country_code'] != $countryCode) {
                        continue;
                    }
                    if (!$endDate || ($endDate && $value['sc_external_' . $i]['date'] <= $endDate)) {
                        return true;
                    }
                }
                return false;
            });
        }

        // Sort by date
        usort($schedules, fn ($a, $b) => $a['sort_date'] <=> $b['sort_date']);

        // Limit if no filter
        $isHaveStartDate = !empty($params['startDate']);

        if (
            !$countryCode &&
            !$portCode &&
            !$areaCode &&
            !$pol &&
            !$endDate &&
            !$isHaveStartDate &&
            // check exist params query
            !count($params)
        ) {
            return array_slice($schedules, 0, 16);
        }

        return $schedules;
    }

    private function buildQueryFilterByRoute($portsInArea, $endDate): array
    {
        $query = '';
        $paramEndDate = [];

        if (!$endDate) {
            $query .= ' AND ((MFSCDP.FSGP01 IN (' . $portsInArea . ') AND MFSCDP.FSET01 <> \'0\')';
            for ($i = 1; $i <= PortAreaOrder::NUMBER_OF_POD; $i++) {
                $index = $this->formatNumberToString($i);
                $query .= ' OR (MFSCDP.FSGP' . $index . ' IN (' . $portsInArea . ') AND MFSCDP.FSET' . $index . ' <> \'0\')';
            }
            $query .= ')';
        } else {
            $query .= ' AND ((MFSCDP.FSGP01 IN (' . $portsInArea . ') AND  MFSCDP.FSET01 <= :scEndDate1 AND MFSCDP.FSET01 <> \'0\')';
            $paramEndDate['scEndDate1'] = $endDate;
            for ($i = 2; $i <= PortAreaOrder::NUMBER_OF_POD; $i++) {
                $index = $this->formatNumberToString($i);
                $query .= ' OR (MFSCDP.FSGP' . $index . ' IN (' . $portsInArea . ') AND MFSCDP.FSET' . $index . ' <= :scEndDate' . $i . ' AND MFSCDP.FSET' . $index . ' <> \'0\')';
                $paramEndDate['scEndDate' . $i] = $endDate;
            }
            $query .= ')';
        }

        return [
            'query' => $query,
            'paramEndDate' => $paramEndDate
        ];
    }

    private function buildQueryFilterByInternalPort($port): array
    {
        $query = '';
        $paramPort = [];
        if ($port) {
            $query .= ' AND ((MFSCDP.FSNP01 = :fromPortCode1 AND  MFSCDP.FSSD01 >= :startDate1 )';
            for ($i = 1; $i <= PortAreaOrder::NUMBER_OF_POL; $i++) {
                $paramPort['fromPortCode' . $i] = $port;
                if (1 === $i) {
                    continue;
                }
                $index = $this->formatNumberToString($i);
                $query .= ' OR (MFSCDP.FSNP' . $index . ' = :fromPortCode' . $i . ' AND  MFSCDP.FSSD' . $index . ' >= :startDate' . $i . ' )';
            }
            $query .= ')';
        } else {
            $query .= ' AND ((MFSCDP.FSSD01 >= :startDate1 )';
            for ($i = 2; $i <= PortAreaOrder::NUMBER_OF_POL; $i++) {
                $index = $this->formatNumberToString($i);
                $query .= ' OR (MFSCDP.FSSD' . $index . ' >= :startDate' . $i . ' )';
            }
            $query .= ')';
        }
        return [
            'query' => $query,
            'paramPort' => $paramPort
        ];
    }

    private function buildQueryFilterByPort($port, $endDate, $startDate): array
    {
        $query = '';
        $params = [];

        if ($startDate) {
            $params['startDateSc1'] = $startDate;
            $query .= ' AND ((MFSCDP.FSET01 >= :startDateSc1)';
            for ($i = 2; $i <= PortAreaOrder::NUMBER_OF_POD; $i++) {
                $index = $this->formatNumberToString($i);
                $params['startDateSc' . $i] = $startDate;
                $query .= ' OR (MFSCDP.FSET' . $index . ' >= :startDateSc' . $i . ')';
            }
            $query .= ')';
        }

        if ($endDate) {
            for ($i = 1; $i <= PortAreaOrder::NUMBER_OF_POD; $i++) {
                $params['endDate' . $i] = $endDate;
            }

            if ($port) {
                $query .= ' AND ((MFSCDP.FSGP01 = :portCode1 AND  MFSCDP.FSET01 <= :endDate1 AND MFSCDP.FSET01 <> \'0\')';
                for ($i = 2; $i <= PortAreaOrder::NUMBER_OF_POD; $i++) {
                    $index = $this->formatNumberToString($i);
                    $query .= ' OR (MFSCDP.FSGP' . $index . ' = :portCode' . $i . ' AND MFSCDP.FSET' . $index . ' <= :endDate' . $i . ' AND MFSCDP.FSET' . $index . ' <> \'0\')';
                }
                $query .= ')';
            } else {
                $query .= ' AND ((MFSCDP.FSET01 <= :endDate1 AND MFSCDP.FSET01 <> \'0\')';
                for ($i = 2; $i <= PortAreaOrder::NUMBER_OF_POD; $i++) {
                    $index = $this->formatNumberToString($i);
                    $query .= ' OR (MFSCDP.FSET' . $index . ' <= :endDate' . $i . ' AND MFSCDP.FSET' . $index . ' <> \'0\')';
                }
                $query .= ')';
            }
        } else {
            if ($port) {
                $query .= ' AND ((MFSCDP.FSGP01 = :portCode1 )';
                for ($i = 2; $i <= PortAreaOrder::NUMBER_OF_POD; $i++) {
                    $index = $this->formatNumberToString($i);
                    $query .= ' OR (MFSCDP.FSGP' . $index . ' = :portCode' . $i . ' )';
                }
                $query .= ')';
            }
        }

        return [
            'query' => $query,
            'paramEndDate' => $params
        ];
    }


    private function mappingPortName($schedule, $ports): array
    {
        $internalPort = [];
        $externalPort = [];

        for ($i = 1; $i <= PortAreaOrder::NUMBER_OF_POL; $i++) {
            if ($schedule['port_in_' . $i]) {
                $internalPort[$i] = (int)$schedule['date_arrival_date_' . $i];
            }
        }
        for ($i = 1; $i <= PortAreaOrder::NUMBER_OF_POD; $i++) {
            if ($schedule['port_out_' . $i]) {
                $externalPort[$i] = (int)$schedule['except_date_' . $i];
            }
        }

        asort($externalPort);
        asort($internalPort);

        $i = 1;
        foreach ($internalPort as $key => $value) {
            $schedule['sc_internal_' . $i]['port'] = $schedule['port_in_' . $key];
            $schedule['sc_internal_' . $i]['date'] = $value;
            $schedule['sc_internal_' . $i]['port_name'] = $ports[$schedule['port_in_' . $key]]['name'] ?? '';
            $schedule['sc_internal_' . $i]['country_code'] = $ports[$schedule['port_in_' . $key]]['country_code'] ?? '';
            $i++;
        }
        $j = 1;
        foreach ($externalPort as $key => $value) {
            $schedule['sc_external_' . $j]['port'] = $schedule['port_out_' . $key];
            $schedule['sc_external_' . $j]['date'] = $value;
            $schedule['sc_external_' . $j]['port_name'] = $ports[$schedule['port_out_' . $key]]['name'] ?? '';
            $schedule['sc_external_' . $j]['country_code'] = $ports[$schedule['port_out_' . $key]]['country_code'] ?? '';
            $j++;
        }
        $schedule['sort_date'] = $schedule['sc_internal_1']['date'] ?? null;
        return $schedule;
    }

    private function formatNumberToString($number): string
    {
        return $number <= 9 ? '0' . $number : (string) $number;
    }

    /**
     * Transform a date string from format 'Y-m-d' (e.g. '2025-07-22') to 'Ymd' (e.g. '20250722').
     *
     * @param string|null $date Date string in format 'Y-m-d' or null for current date.
     * @return string Date in 'Ymd' format.
     */
    private function transformDate(?string $date): string
    {
        try {
            $dateTime = Carbon::parse($date ?? 'now');
        } catch (Exception $e) {
            $dateTime = Carbon::now();
        }

        return $dateTime->format('Ymd');
    }
}
