@extends('app')

@section('title')
    {{ __('home-page/hubnet-introduction.page_title') }}
@endsection

@push('styles')
    @vite('resources/css/hubnet_intro.css')
@endpush

@section('content')
    <div class="m-0 mx-auto w-full max-w-6xl">
        <div class="h-20 w-full"></div>
        <div class="back_top_btn">
            <img src="{{ asset('images/services/ah_service_backtop.gif') }}" alt="back top" />
        </div>
        <a target="_blank" href="{{ $urlOld }}/hn/login/index.asp?lan={{ $lang }}" class="hubnet_link">
            <h1>HUBNET</h1>
            <hr />
            <p>{!! __('home-page/hubnet-introduction.click_hubnet02') !!}</p>
        </a>
        <div class="w-full">
            <div class="question-container hubnet-wrap">
                <h2 class="title">{{ __('home-page/hubnet-introduction.lang_Introduction_HN') }}</h2>
                <p class="title_message">
                    {!! __('home-page/hubnet-introduction.lang_Intro_HN_p') !!}
                </p>
                <br />
                <div class="hubnet_btn_wrap">
                    <a target="_blank" href="{{ $urlOld }}/hn/login/index.asp?lan={{ $lang }}" class="hubnet_btn">
                        {{ __('home-page/hubnet-introduction.click_hubnet01') }} &nbsp;
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </div>
                <div class="trouble-wrap flex">
                    <div class="trouble-left">
                        <img src="{{ asset('images/services/pict_04.png') }}" alt="" />
                    </div>
                    <div class="trouble-right">
                        <p>{{ __('home-page/hubnet-introduction.lang_computer_phone') }}</p>
                    </div>
                </div>
                <div class="hubnet-img">
                    @if ($lang == 'en')
                        <img src="{{ asset('images/services/hubnet_scr_en.jpg') }}" alt="" />
                    @else
                        <img src="{{ asset('images/services/hubnet_scr.jpg') }}" alt="" />
                    @endif
                </div>
                <div class="menu-container flex flex-wrap">
                    <div class="menu-wrap">
                        <div class="menu-title flex items-center">
                            <img src="{{ asset('images/services/icon_menu_title_01.png') }}" alt="発注したい" />
                            <p>{{ __('home-page/hubnet-introduction.lang_order') }}</p>
                        </div>
                        <p>{{ __('home-page/hubnet-introduction.lang_HN_canorder') }}</p>

                        <x-hubnet-introduction.service-menu
                            href="{{ $urlOld }}/hn/load/tas_lump.asp?lan={{ $lang }}"
                            iconSrc="{{ asset('images/services/icon_menu_01.png') }}"
                            iconAlt="陸送＆シッピングサービス一括発注"
                            title="{!! __('home-page/hubnet-introduction.lang_collectOrder') !!}"
                            description="{!! __('home-page/hubnet-introduction.lang_HN_order') !!}"
                        />

                        <x-hubnet-introduction.service-menu
                            href="{{ $urlOld }}/hn/trans?lan={{ $lang }}"
                            iconSrc="{{ asset('images/services/icon_menu_07.png') }}"
                            iconAlt="陸送発注"
                            title="{{ __('home-page/hubnet-introduction.lang_transOrder') }}"
                            description="{!! __('home-page/hubnet-introduction.lang_HN_land') !!}"
                            subDescription="{!! __('home-page/hubnet-introduction.lang_transGuide') !!}"
                        />

                        <x-hubnet-introduction.service-menu
                            href="{{$urlOld}}/hn/load/sup_lump.asp?scd=4&lan={{$lang }}"
                            iconSrc="{{ asset('images/services/icon_menu_08.png') }}"
                            iconAlt="追加サポートサービス"
                            title="{{ __('home-page/hubnet-introduction.lang_Addsupport') }}"
                            description="{!! __('home-page/hubnet-introduction.lang_HN_photo') !!}"
                            subDescription="{!! __('home-page/hubnet-introduction.lang_Addsupport_02') !!}"
                        />
                    </div>
                    <div class="menu-wrap">
                        <div class="menu-title flex items-center">
                            <img src="{{ asset('images/services/icon_menu_title_02.png') }}" alt="発注したい" />
                            <p>{{ __('home-page/hubnet-introduction.lang_wantToConf') }}</p>
                        </div>
                        <p>{{ __('home-page/hubnet-introduction.lang_HN_freight') }}</p>

                        <x-hubnet-introduction.service-menu
                            href="{{ $urlOld}}/hn/tat/list_81_.php?lan={{$lang}}"
                            iconSrc="{{ asset('images/services/icon_menu_09.png') }}"
                            iconAlt="船積情報"
                            title="{{ __('home-page/hubnet-introduction.lang_car_detail') }}"
                            description="{!! __('home-page/hubnet-introduction.lang_HN_BOOKING') !!}"
                            subDescription="{!! __('home-page/hubnet-introduction.lang_TandT') !!}"
                        />

                        <x-hubnet-introduction.service-menu
                            href="{{ $urlOld}}/hn/document/doc_freight_list.asp?lan={{$lang }}"
                            iconSrc="{{ asset('images/services/icon_menu_invoice.png') }}"
                            iconAlt="フレイト請求書"
                            title="{{ __('home-page/hubnet-introduction.lang_freight_invoice') }}"
                            description="{!! __('home-page/hubnet-introduction.lang_HN_Freight') !!}"
                        />
                    </div>
                </div>
                <div class="hubnet_btn_wrap">
                    <a target="_blank" href="{{ $urlOld }}/hn/login/index.asp?lan={{ $lang }}" class="hubnet_btn">
                        {{ __('home-page/hubnet-introduction.click_hubnet01') }} &nbsp;
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="screen02">
        <div class="question-container02">
            <h2>{{ __('home-page/hubnet-introduction.popular_service') }}</h2>
            <div class="question-container">
                <x-hubnet-introduction.title-wrap
                    title="{!! __('home-page/hubnet-introduction.lang_photo_service3') !!}"
                    description="{{ __('home-page/hubnet-introduction.lang_detailed_photos') }}"
                />
                <div class="trouble-wrap flex">
                    <div class="trouble-left">
                        <img src="{{ asset('images/services/pict_01.png') }}" alt="" />
                    </div>
                    <div class="trouble-right">
                        <p>{{ __('home-page/hubnet-introduction.lang_solve_problems') }}</p>
                        <ul class="ml-10 list-disc">
                            <li>{{ __('home-page/hubnet-introduction.lang_problems_01') }}</li>
                            <li>{{ __('home-page/hubnet-introduction.lang_problems_02') }}</li>
                            <li>{{ __('home-page/hubnet-introduction.lang_problems_03') }}</li>
                        </ul>
                    </div>
                </div>
                <div class="plan-wrap flex">
                    <section>
                        <h5>{{ __('home-page/hubnet-introduction.lang_Smart_pictures') }}</h5>
                        <img src="{{ asset('images/services/smart_photo_sm.png') }}" alt="" />
                        <p>{{ __('home-page/hubnet-introduction.lang_Smartp_text') }}</p>
                    </section>
                    <section>
                        <h5>{{ __('home-page/hubnet-introduction.lang_Light_pictures') }}</h5>
                        <img src="{{ asset('images/services/light_photo_sm.png') }}" alt="" />
                        <p>{{ __('home-page/hubnet-introduction.lang_Lightp_text') }}</p>
                    </section>
                </div>
                <a
                    href="{{ $isJa ? route('service.photo-condition') : route('localized.service.photo-condition', ['locale' => 'en']) }}"
                    id="detail_open_btn01"
                    class="btn detail_open_btn"
                >
                    <span>{{ __('home-page/hubnet-introduction.for_detail') }}</span>
                </a>
            </div>
        </div>
        <div class="question-container02">
            <div class="question-container">
                <x-hubnet-introduction.title-wrap
                    title="{!! __('home-page/hubnet-introduction.lang_Land_service') !!}"
                    description="{!! __('home-page/hubnet-introduction.lang_LandS_text') !!}"
                />
                <div class="rikusou-img">
                    @if ($lang == 'en')
                        <img src="{{ asset('images/services/rikusou_01en.png') }}" alt="" />
                        <img src="{{ asset('images/services/rikusou_02en.png') }}" alt="" />
                    @else
                        <img src="{{ asset('images/services/rikusou_01.png') }}" alt="" />
                        <img src="{{ asset('images/services/rikusou_02.png') }}" alt="" />
                    @endif
                </div>
                <a
                    href="{{ $isJa ? route('service.inland-transport') : route('localized.service.inland-transport', ['locale' => 'en']) }}"
                    id="detail_open_btn01"
                    class="btn detail_open_btn"
                >
                    <span>{{ __('home-page/hubnet-introduction.for_detail') }}</span>
                </a>
            </div>
            <div class="question-container">
                <x-hubnet-introduction.title-wrap
                    title="{!! __('home-page/hubnet-introduction.lang_keisagyo2') !!}"
                    description="{!! __('home-page/hubnet-introduction.lang_What_lightWork') !!}"
                />
                <div class="trouble-wrap trouble-wrap02 flex">
                    <div class="trouble-left">
                        <img src="{{ asset('images/services/pict_02.png') }}" alt="" />
                    </div>
                    <div class="trouble-right02">
                        <p>{{ __('home-page/hubnet-introduction.lang_For_example') }}</p>
                        <div class="flex">
                            <ul class="ml-10 list-disc">
                                @foreach (__('home-page/hubnet-introduction.easy_work_list') as $key => $item)
                                    <x-hubnet-introduction.checkbox-list-item
                                        id="check-li-0{{$key}}"
                                        :title="$item['title']"
                                        :description="$item['description']"
                                    />
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="syuri-wrap">
                    <x-hubnet-introduction.title-wrap
                        title="{!! __('home-page/hubnet-introduction.lang_syuri') !!}"
                        description="{!! __('home-page/hubnet-introduction.lang_What_repair') !!}"
                    />
                    <div class="trouble-wrap trouble-wrap02 flex">
                        <div class="trouble-left">
                            <img src="{{ asset('images/services/pict_03.png') }}" alt="" />
                        </div>
                        <div class="trouble-right02">
                            <p>{{ __('home-page/hubnet-introduction.lang_For_example') }}</p>
                            <div class="flex">
                                <ul class="mx-10 list-disc">
                                    @foreach (__('home-page/hubnet-introduction.lang_repair_01') as $item)
                                        <li>{{ $item }}</li>
                                    @endforeach
                                </ul>
                                <ul class="list-disc">
                                    @foreach (__('home-page/hubnet-introduction.lang_repair_02') as $item)
                                        <li>{{ $item }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </div>
                    <a
                        href="{{ $isJa ? route('service.repair-easywork-inspection') : route('localized.service.repair-easywork-inspection', ['locale' => 'en']) }}"
                        id="detail_open_btn01"
                        class="btn detail_open_btn"
                    >
                        <span>{{ __('home-page/hubnet-introduction.for_detail') }}</span>
                    </a>
                </div>
            </div>
        </div>
        <div class="hubnet_btn_wrap">
            <a target="_blank" href="{{ $urlOld }}/hn/login/index.asp?lan={{ $lang }}" class="hubnet_btn">
                {{ __('home-page/hubnet-introduction.click_hubnet01') }} &nbsp;
                <i class="fas fa-chevron-right"></i>
            </a>
        </div>
    </div>

    <div class="m-0 mx-auto w-full max-w-6xl">
        <div class="question-container">
            <div class="redpin"><img src="{{ asset('images/services/redpin.png') }}" alt="" /></div>
            <h2 class="title">{{ __('home-page/hubnet-introduction.lang_Customers_Voices') }}</h2>
            <p class="title_message">
                {!! __('home-page/hubnet-introduction.lang_Qresults_text') !!}
                <br />
            </p>
            <div class="question-row flex flex-wrap justify-between">
                <div class="question-wrap1 q-wrap-left 2md:w-1/2 2md:max-w-1/2 2md:flex-none relative">
                    <canvas id="question01"></canvas>
                    <p data-aos="zoom-in" class="aos-init q_comments" data-aos-delay="300" data-aos-duration="300">
                        {!! __('home-page/hubnet-introduction.lang_Excellent_Good') !!}
                        <span>&nbsp;82％&nbsp;</span>
                    </p>
                </div>
                <div class="question-wrap2 2md:w-1/2 2md:max-w-1/2 2md:flex-none relative">
                    <canvas id="question02"></canvas>
                    <p data-aos="zoom-in" class="aos-init q_comments" data-aos-delay="300" data-aos-duration="300">
                        {!! __('home-page/hubnet-introduction.lang_Excellent_Good') !!}
                        <span>&nbsp;85％&nbsp;</span>
                    </p>
                </div>
            </div>
            <x-hubnet-introduction.pie-chart
                id="question03"
                percentage="91"
                percentageText="{!! __('home-page/hubnet-introduction.lang_Excellent_Good') !!}"
                :comments="__('home-page/hubnet-introduction.lang_question03_comment')"
                checkboxId="checkbox3"
            />

            <x-hubnet-introduction.pie-chart
                id="question04"
                percentage="90"
                percentageText="{!! __('home-page/hubnet-introduction.lang_Excellent_Good') !!}"
                :comments="__('home-page/hubnet-introduction.lang_question04_comment')"
                checkboxId="checkbox4"
            />

            <x-hubnet-introduction.pie-chart
                id="question05"
                percentage="87"
                percentageText="{!! __('home-page/hubnet-introduction.lang_Excellent_Good') !!}"
                :comments="__('home-page/hubnet-introduction.lang_question05_comment')"
                checkboxId="checkbox5"
            />

            <x-hubnet-introduction.pie-chart
                id="question06"
                percentage="96"
                percentageText="{!! __('home-page/hubnet-introduction.lang_Yes_recommend') !!}"
                :comments="__('home-page/hubnet-introduction.lang_question06_comment')"
                checkboxId="checkbox6"
            />

            <x-hubnet-introduction.pie-chart
                id="question07"
                percentage="56"
                percentageText="{!! __('home-page/hubnet-introduction.lang_Many_clients') !!}"
                :comments="__('home-page/hubnet-introduction.lang_question07_comment')"
                checkboxId="checkbox7"
                chartWrapperClass="question-wrap7 relative mx-auto lg:w-11/12"
                commentsWrapperClass="w-full m-auto 2md:w-5/6"
            />
        </div>
    </div>
@endsection

@push('scripts')
    @include('components.hubnet-introduction.questionnaire-charts')
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Back to top button show/hide
            window.addEventListener('scroll', function () {
                var backTopBtn = document.querySelector('.back_top_btn')
                if (window.scrollY > 0) {
                    backTopBtn && (backTopBtn.style.display = 'block')
                } else {
                    backTopBtn && (backTopBtn.style.display = 'none')
                }
            })

            // Back to top button click
            var backTopBtn = document.querySelector('.back_top_btn')
            if (backTopBtn) {
                backTopBtn.addEventListener('click', function () {
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth',
                    })
                })
            }

            window.addEventListener('scroll', function () {
                var hubnetLink = document.querySelector('.hubnet_link')
                if (!hubnetLink) return

                if (window.scrollY > 0) {
                    hubnetLink.classList.remove('fadeLeft')
                    hubnetLink.classList.add('fadeRight')
                    hubnetLink.style.display = 'block'
                } else {
                    hubnetLink.classList.remove('fadeRight')
                    hubnetLink.classList.add('fadeLeft')

                    function onAnimationEnd(e) {
                        if (e.animationName === 'fade-left' && window.scrollY === 0) {
                            hubnetLink.style.display = 'none'
                            hubnetLink.removeEventListener('animationend', onAnimationEnd)
                        }
                    }

                    hubnetLink.addEventListener('animationend', onAnimationEnd)
                }
            })

            // hubnet_link & back_top_btn bottom position
            window.addEventListener('scroll', function () {
                var docHeight = document.documentElement.scrollHeight
                var windowHeight = window.innerHeight
                var pageBottom = docHeight - windowHeight
                var win_w = window.innerWidth
                var scrollTop = window.scrollY
                var hubnetLink = document.querySelector('.hubnet_link')
                var backTopBtn = document.querySelector('.back_top_btn')
                if (pageBottom <= scrollTop) {
                    if (win_w >= 577) {
                        hubnetLink && (hubnetLink.style.bottom = '420px')
                        backTopBtn && (backTopBtn.style.bottom = '420px')
                    } else {
                        hubnetLink && (hubnetLink.style.bottom = '345px')
                        backTopBtn && (backTopBtn.style.bottom = '345px')
                    }
                } else {
                    if (win_w >= 577) {
                        hubnetLink && (hubnetLink.style.bottom = '100px')
                        backTopBtn && (backTopBtn.style.bottom = '100px')
                    } else {
                        hubnetLink && (hubnetLink.style.bottom = '50px')
                        backTopBtn && (backTopBtn.style.bottom = '50px')
                    }
                }
            })
        })
    </script>
@endpush
