<?php

declare(strict_types=1);

namespace App\View\Components\services;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class GoogleMap extends Component
{
    /**
     * Create a new component instance.
     */
    public function __construct(public int $positionX, public int $positionY, public string $id, public string $office, public string $officeDetail)
    {

    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.services.google-map');
    }
}
