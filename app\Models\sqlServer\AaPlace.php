<?php

declare(strict_types=1);

namespace App\Models\sqlServer;

use Illuminate\Database\Eloquent\Model;

class AaPlace extends Model
{
    public const CREATED_AT = 'reg_date';
    public const UPDATED_AT = 'up_date';
    protected $guarded = [];

    protected $table = 't_aa_place';
    protected $fillable = [
        'del_flg',
        'reg_date',
        'up_date',
        'up_owner',
        'id',
        'name',
        'todofuken_cd',
        'addr',
        'tel',
        'base_yobi',
        'sort_no',
    ];
}
