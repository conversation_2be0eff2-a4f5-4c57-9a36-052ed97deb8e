<?php

declare(strict_types=1);

namespace App\Providers;

use App\Database\Connectors\OdbcConnector;
use Illuminate\Database\Connection;
use Illuminate\Support\ServiceProvider;

class OdbcProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->resolving('db', function ($db): void {
            $db->extend('odbc', function ($config, $name) {
                $connector = new OdbcConnector();
                $connection = $connector->connect($config);

                return new Connection($connection, $config['database'], $config['prefix'] ?? '', $config);
            });
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {

    }
}
