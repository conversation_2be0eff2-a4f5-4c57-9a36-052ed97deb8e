<?php

declare(strict_types=1);

namespace App\Services\BeforeCheckCustomer;

use App\Models\sqlServer\BeforeCheckCustomer;
use App\Repositories\sqlServer\BeforeCheckCustomerRepository;
use App\Repositories\sqlServer\CheckingRequestRepository;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class GetListBeforeCheckCustomerService
{
    private BeforeCheckCustomerRepository $beforeCheckCustomerRepository;
    private CheckingRequestRepository $checkingRequestRepository;

    public function __construct(BeforeCheckCustomerRepository $beforeCheckCustomerRepository, CheckingRequestRepository $checkingRequestRepository)
    {
        $this->beforeCheckCustomerRepository = $beforeCheckCustomerRepository;
        $this->checkingRequestRepository = $checkingRequestRepository;
    }

    public function call(array $param): Collection
    {
        $update = $param['update'] ?? null;
        if ('go_update' === $update) {
            $this->reloadTableBeforeCheckCustomer();
        }

        return $this->beforeCheckCustomerRepository->listBeforeCheckCustomer();
    }

    private function reloadTableBeforeCheckCustomer(): void
    {
        DB::transaction(function (): void {
            $this->truncateBeforeCheckCustomer();
            $this->reInsertBeforeCheckCustomer();
        });
    }

    private function truncateBeforeCheckCustomer(): void
    {
        $this->beforeCheckCustomerRepository->truncateBeforeCheckCustomer();
    }

    /**
     * @throws Exception
     */
    private function reInsertBeforeCheckCustomer(): void
    {
        $dataInsert = $this->checkingRequestRepository->getDataForBeforeCheckCustomer();

        $groupedData = $dataInsert->groupBy(fn ($item) => $item->m_customer_id)
            ->map(fn ($group) => $group->first())
            ->values();

        $groupedData->chunk(100)->each(function ($chunk): void {
            BeforeCheckCustomer::insert($chunk->toArray());
        });
    }
}
