<?php

declare(strict_types=1);

namespace App\Services\CheckingRequest;

use App\Repositories\sqlServer\CheckingRequestRepository;

class DetailCheckingRequestService
{
    private CheckingRequestRepository $checkingRequestRepository;

    public function __construct(CheckingRequestRepository $checkingRequestRepository)
    {
        $this->checkingRequestRepository = $checkingRequestRepository;
    }

    public function call(int $id)
    {
        return $this->checkingRequestRepository->findDetailById($id);
    }
}
