<?php

declare(strict_types=1);

namespace App\Models\sqlServer;

use Illuminate\Database\Eloquent\Model;

/**
 * Inspector Model
 *
 * Handles list of inspectors/inspection staff
 * Maps to database table: t_inspector
 */
class SalesStaff extends Model
{
    public const CREATED_AT = 'reg_date';
    public const UPDATED_AT = 'up_date';

    protected $fillable = [
        'del_flg',
        'idx',
        's_name',
        's_name_en',
        's_mail',
        'note',
        's_name_jp',
        's_id',
        'e_mail',
        'team',
        'office',
        'office_cd',
        'm_ah_staff_id',
    ];
    protected $table = 'm_sales_staff';
}
