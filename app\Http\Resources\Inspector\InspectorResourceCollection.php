<?php

declare(strict_types=1);

namespace App\Http\Resources\Inspector;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class InspectorResourceCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
        ];
    }
}
