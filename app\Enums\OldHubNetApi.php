<?php

declare(strict_types=1);

namespace App\Enums;

enum OldHubNetApi: string
{
    case TRANS_FILE_CHECK_ENDPOINT = '/backend/order/trans_file_check_api_81_.php';
    case LOADING_ORDER_CHECK_ENDPOINT = '/backend/order/loading_file_check_api_81_.php';
    case STORE_FILE_IMAGE_HUBNET_ENDPOINT = '/backend/order/api_save_trans_file_81_.php';
    case TIMEOUT = '30';

    /**
     * Get the value for the configuration
     */
    public function getValue(): string|int
    {
        return match ($this) {
            self::TRANS_FILE_CHECK_ENDPOINT => $this->value,
            self::TIMEOUT => (int) $this->value,
        };
    }
}
