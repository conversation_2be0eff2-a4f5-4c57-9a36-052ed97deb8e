<?php

declare(strict_types=1);

namespace App\View\Components\services;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class HowToUseContent extends Component
{
    /**
     * Create a new component instance.
     */
    public function __construct(public string $page)
    {

    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.services.how-to-use-content');
    }
}
