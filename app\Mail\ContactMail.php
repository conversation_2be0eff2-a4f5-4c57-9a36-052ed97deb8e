<?php

declare(strict_types=1);

namespace App\Mail;

use App\Constants\EnvironmentParams;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;

class ContactMail extends Mailable
{
    use Queueable;
    use SerializesModels;

    public string $contact_type;
    public string $company_name;
    public string $name;
    public string $mail_address;
    public string $tel;
    public string $contact_message;
    public string $lang;
    public string $company_url;

    public function __construct(
        string $contact_type,
        string $company_name,
        string $name,
        string $mail_address,
        string $tel,
        ?string $contact_message,
        string $lang = 'ja'
    ) {
        $this->contact_type = $contact_type;
        $this->company_name = $company_name;
        $this->name = $name;
        $this->mail_address = $mail_address;
        $this->tel = $tel;
        $this->contact_message = $contact_message ?? '';
        $this->lang = $lang;

        $this->company_url = $this->getCompanyUrl();
    }

    public function build(): ContactMail
    {
        app()->setLocale($this->lang);

        $subject = "[" . __('home-page/contact.lang_autohub') . "] " . __('home-page/contact.lang_contact_ok01');

        $mailByEnv = Arr::get(config('mail.mail_contact_env'), config('app.env'));

        return $this
            ->from($mailByEnv['from'], '株式会社AUTOHUB')
            ->cc($mailByEnv['cc'])
            ->bcc($mailByEnv['bcc'])
            ->subject($subject)
            ->view("emails.{$this->lang}.contact")
            ->with([
                'contact_type' => $this->contact_type,
                'company_name' => $this->company_name,
                'name' => $this->name,
                'mail_address' => $this->mail_address,
                'tel' => $this->tel,
                'contact_message' => $this->contact_message,
                'lang' => $this->lang,
                'company_url' => $this->company_url,
            ]);
    }

    private function getCompanyUrl(): string
    {
        $environment = env('APP_ENV', EnvironmentParams::DEFAULT_ENVIRONMENT);

        return match ($environment) {
            EnvironmentParams::ENVIRONMENT_STAGING,
            EnvironmentParams::ENVIRONMENT_LOCAL => EnvironmentParams::COMPANY_URL_STAGING,
            EnvironmentParams::ENVIRONMENT_PRODUCTION => EnvironmentParams::COMPANY_URL_PRODUCTION,
            default => EnvironmentParams::COMPANY_URL_STAGING,
        };
    }
}
