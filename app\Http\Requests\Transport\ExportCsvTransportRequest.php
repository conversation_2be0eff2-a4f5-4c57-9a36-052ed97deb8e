<?php

declare(strict_types=1);

namespace App\Http\Requests\Transport;

use App\Enums\SortType;
use App\Enums\Transport\DateMode;
use App\Enums\Transport\OrderOptions;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ExportCsvTransportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules()
    {
        return [
            'token' => 'required|string', // Token parameter
            'transport_ids' => ['nullable', 'string'],
            'tp_ids' => ['nullable', 'string'],
            'aa_place_id' => ['nullable', 'string'],
            'to_place_id' => ['nullable', 'string'],
            'date_mode' => ['nullable', Rule::in(DateMode::getAllValues())],
            'ref_nos' => ['nullable', 'string'],
            'fr_name' => ['nullable', 'string'],
            'to_name' => ['nullable', 'string'],
            'customer_name' => ['nullable', 'string'],
            'car_name' => ['nullable', 'string'],
            'car_nos' => ['nullable', 'string'],
            'aa_nos' => ['nullable', 'string'],
            'st_cd' => ['nullable', 'string'],
            'plate_cut' => ['nullable', 'string'],
            'm_trans_id' => ['nullable', 'string'],
            'customer_id' => ['nullable', 'string'],
            'charge_sale_customer' => ['nullable', 'string'],
            'start_date' => ['nullable', 'date_format:Y-m-d'],
            'end_date' => ['nullable', 'date_format:Y-m-d'],
            'order_option' => ['nullable' , Rule::in(OrderOptions::getAllValues())],
            'sort_type' => ['nullable', Rule::in(SortType::getAllValues())],
        ];
    }
}
