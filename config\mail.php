<?php

declare(strict_types=1);

return [

    /*
    |--------------------------------------------------------------------------
    | Default Mailer
    |--------------------------------------------------------------------------
    |
    | This option controls the default mailer that is used to send all email
    | messages unless another mailer is explicitly specified when sending
    | the message. All additional mailers can be configured within the
    | "mailers" array. Examples of each type of mailer are provided.
    |
    */

    'default' => env('MAIL_MAILER', 'log'),

    /*
    |--------------------------------------------------------------------------
    | Mailer Configurations
    |--------------------------------------------------------------------------
    |
    | Here you may configure all of the mailers used by your application plus
    | their respective settings. Several examples have been configured for
    | you and you are free to add your own as your application requires.
    |
    | Laravel supports a variety of mail "transport" drivers that can be used
    | when delivering an email. You may specify which one you're using for
    | your mailers below. You may also add additional mailers if needed.
    |
    | Supported: "smtp", "sendmail", "mailgun", "ses", "ses-v2",
    |            "postmark", "log", "array", "failover", "roundrobin"
    |
    */

    'mailers' => [

        'smtp' => [
            'transport' => 'smtp',
            'url' => env('MAIL_URL'),
            'host' => env('MAIL_HOST', '127.0.0.1'),
            'port' => env('MAIL_PORT', 2525),
            'encryption' => env('MAIL_ENCRYPTION', 'tls'),
            'username' => env('MAIL_USERNAME'),
            'password' => env('MAIL_PASSWORD'),
            'timeout' => null,
            'local_domain' => env('MAIL_EHLO_DOMAIN'),
        ],

        'ses' => [
            'transport' => 'ses',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION'),
        ],

        'postmark' => [
            'transport' => 'postmark',
            // 'message_stream_id' => env('POSTMARK_MESSAGE_STREAM_ID'),
            // 'client' => [
            //     'timeout' => 5,
            // ],
        ],

        'sendmail' => [
            'transport' => 'sendmail',
            'path' => env('MAIL_SENDMAIL_PATH', '/usr/sbin/sendmail -bs -i'),
        ],

        'log' => [
            'transport' => 'log',
            'channel' => env('MAIL_LOG_CHANNEL'),
        ],

        'array' => [
            'transport' => 'array',
        ],

        'failover' => [
            'transport' => 'failover',
            'mailers' => [
                'smtp',
                'log',
            ],
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Global "From" Address
    |--------------------------------------------------------------------------
    |
    | You may wish for all emails sent by your application to be sent from
    | the same address. Here you may specify a name and address that is
    | used globally for all emails that are sent by your application.
    |
    */

    'from' => [
        'address' => env('MAIL_FROM_ADDRESS', '<EMAIL>'),
        'name' => env('MAIL_FROM_NAME', 'Example'),
    ],

    'mail_contact_env' => [
        'production' => [
            'from' => '<EMAIL>',
            'cc' => '<EMAIL>',
            'bcc' => '<EMAIL>',
        ],
        'staging' => [
            'from' => '<EMAIL>',
            'cc' => '<EMAIL>',
            'bcc' => '<EMAIL>',
        ],
        'development' => [
            'from' => '<EMAIL>',
            'cc' => '<EMAIL>',
            'bcc' => '<EMAIL>',
        ],
        'local' => [
            'from' => '<EMAIL>',
            'cc' => '<EMAIL>',
            'bcc' => '<EMAIL>',
        ],
    ],

    // mail config site admin and user
    'mail_info' => [
        'production' => [
            'from' => '<EMAIL>',
            'cc' => '<EMAIL>',
            'bcc' => '<EMAIL>',
            'shipping_mail' => '<EMAIL>',
            'trans_mail' => '<EMAIL>',
            'ptrans_mail' => '<EMAIL>',
            'ltrans_mail' => '<EMAIL>',
            'center_mail' => '<EMAIL>',
            'rep_mail' => '<EMAIL>',
            'test_admin_mail' => '<EMAIL>',
            'mail_address_order' => '<EMAIL>',
            'mail_address_oosaka' => '<EMAIL>',
            'mail_address_transport' => '<EMAIL>',
            'mail_address_yamashita' => '<EMAIL>',
            'bcc_mail_address_transport_cancel' => '<EMAIL>',
            'bcc_mail_address_transport_onhold' => '<EMAIL>',
        ],
        'staging' => [
            'from' => '<EMAIL>',
            'cc' => '<EMAIL>',
            'bcc' => '<EMAIL>',
            'shipping_mail' => '<EMAIL>',
            'trans_mail' => '<EMAIL>',
            'ptrans_mail' => '<EMAIL>',
            'ltrans_mail' => '<EMAIL>',
            'center_mail' => '<EMAIL>',
            'rep_mail' => '<EMAIL>',
            'test_admin_mail' => '<EMAIL>',
            'mail_address_order' => '<EMAIL>',
            'mail_address_oosaka' => '<EMAIL>',
            'mail_address_transport' => '<EMAIL>',
            'mail_address_yamashita' => '<EMAIL>',
            'bcc_mail_address_transport_cancel' => '<EMAIL>',
            'bcc_mail_address_transport_onhold' => '<EMAIL>',
        ],
        'development' => [
            'from' => '<EMAIL>',
            'cc' => '<EMAIL>',
            'bcc' => '<EMAIL>',
            'shipping_mail' => '<EMAIL>',
            'trans_mail' => '<EMAIL>',
            'ptrans_mail' => '<EMAIL>',
            'ltrans_mail' => '<EMAIL>',
            'center_mail' => '<EMAIL>',
            'rep_mail' => '<EMAIL>',
            'test_admin_mail' => '<EMAIL>',
            'mail_address_order' => '<EMAIL>',
            'mail_address_oosaka' => '<EMAIL>',
            'mail_address_transport' => '<EMAIL>',
            'mail_address_yamashita' => '<EMAIL>',
            'bcc_mail_address_transport_cancel' => '<EMAIL>',
            'bcc_mail_address_transport_onhold' => '<EMAIL>',
        ],
        'local' => [
            'from' => '<EMAIL>',
            'cc' => '<EMAIL>',
            'bcc' => '<EMAIL>',
            'shipping_mail' => '<EMAIL>',
            'trans_mail' => '<EMAIL>',
            'ptrans_mail' => '<EMAIL>',
            'ltrans_mail' => '<EMAIL>',
            'center_mail' => '<EMAIL>',
            'rep_mail' => '<EMAIL>',
            'test_admin_mail' => '<EMAIL>',
            'mail_address_order' => '<EMAIL>',
            'mail_address_oosaka' => '<EMAIL>',
            'mail_address_transport' => '<EMAIL>',
            'mail_address_yamashita' => '<EMAIL>',
            'bcc_mail_address_transport_cancel' => '<EMAIL>',
            'bcc_mail_address_transport_onhold' => '<EMAIL>',
        ],
    ],

    'mail_name' => [
        'from' => '株式会社AUTOHUB',
        'cc' => '株式会社AUTOHUB',
        'bcc' => '株式会社AUTOHUB',
    ],

    'mail_domain_allow' => env('MAIL_DOMAIN_ALLOW', ''),

    'mail_subjects' => [
        'admin-lg-transport' => '【新規 / 陸送依頼】 $car_no $customer_name',
        'admin-cg-transport' => '【新規 / 陸送依頼】 $car_no $customer_name',
        'admin-ei-transport' => '【新規 / 陸送依頼】 $car_no $customer_name',
        'admin-jc-transport' => '【新規 / 陸送依頼】 $car_no $customer_name',
        'admin-transport' => '【新規 / 陸送依頼】 $car_no $customer_name',
        'transport-from-backend' => ' [HUBNET]　陸送依頼受付いたしました',
        'admin-transport-cancel' => '【キャンセル / 陸送依頼】 $car_no $customer_name',
        'admin-cg-transport-cancel' => '【キャンセル / 陸送依頼】 $car_no $customer_name',
        'admin-ei-transport-cancel' => '【キャンセル / 陸送依頼】 $car_no $customer_name',
        'admin-jc-transport-cancel' => '【キャンセル / 陸送依頼】 $car_no $customer_name',
        'admin-transport-change-price' => '【原価更新 / 陸送料金】 $car_no $customer_name',
        'admin-transport-change-plate' => '【プレート発送完了 / 陸送依頼】 $car_no $customer_name',
        'admin-transport-change-comment' => '【備考欄更新 / 陸送依頼】 $car_no $customer_name',
        'admin-cg-transport-onhold' => '【保留 / 陸送依頼】 $car_no $customer_name',
        'admin-transport-onhold' => '【保留 / 陸送依頼】 $car_no $customer_name',
        'admin-jc-transport-onhold' => '【保留 / 陸送依頼】 $car_no $customer_name',
        'admin-ei-transport-onhold' => '【保留 / 陸送依頼】 $car_no $customer_name',
    ]

];
