<?php

declare(strict_types=1);

namespace App\Http\Resources\CheckingRequest;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CheckingRequestResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'st_cd' => $this->st_cd,
            'aa_no' => $this->aa_no,
            'optn1_flg' => $this->optn1_flg,
            'optn2_flg' => $this->optn2_flg,
            'note' => $this->note,
            'photo_flg' => $this->photo_flg,
            'cc_note' => $this->cc_note,
            'car_name' => $this->car_name,
            'check_req_date' => $this->check_req_date,
            'odr_date' => $this->odr_date,
            'open_date' => $this->open_date,
            'odr_person' => $this->odr_person,
            'odr_mail' => $this->odr_mail,
            'odr_tel' => $this->odr_tel,
            'inspector' => (($this->inspector) && (0 === (int)$this->inspector->del_flg)) ? [
                'id' => $this->inspector->id,
                'name' => $this->inspector->name,
            ] : null,
            'customer' => $this->customer ? [
                'id' => $this->customer->id,
                'cus_Name_JP' => $this->customer->cus_Name_JP,
                'cus_Name_EN' => $this->customer->cus_Name_EN,
            ] : null,
            'aaDate' => $this->aaDate ? [
                'id' => $this->aaDate->id,
                'open_date' => $this->aaDate->open_date,
                'odr_date' => $this->aaDate->odr_date,
                'check_req_date' => $this->aaDate->check_req_date,
                'aaPlace' => $this->aaDate->aaPlace ? [
                    'id' => $this->aaDate->aaPlace->id,
                    'name' => $this->aaDate->aaPlace->name,
                ] : null,
            ] : null
        ];
    }
}
