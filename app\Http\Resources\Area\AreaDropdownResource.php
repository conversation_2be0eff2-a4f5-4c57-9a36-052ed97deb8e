<?php

declare(strict_types=1);

namespace App\Http\Resources\Area;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AreaDropdownResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'pcd' => $this->pcd,
            'port' => $this->port,
            'cd' => $this->cd,
            'country' => $this->country,
            'acd' => $this->acd,
            'area' => $this->area,
        ];
    }
}
