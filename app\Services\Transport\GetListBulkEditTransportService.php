<?php

declare(strict_types=1);

namespace App\Services\Transport;

use App\Constants\PageMetaData;
use App\Enums\UserRole;
use App\Http\Resources\Transport\BulkEditTransportResource;
use App\Repositories\sqlServer\TransportRepository;
use Exception;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Pagination\LengthAwarePaginator;

class GetListBulkEditTransportService
{
    private TransportRepository $transportRepository;

    public function __construct(TransportRepository $transportRepository)
    {
        $this->transportRepository = $transportRepository;
    }

    /**
     * Get paginated transport list for bulk edit
     *
     * @param array $params
     * @return array
     */
    public function call(array $params): array
    {
        $params = $this->applyUserFiltering($params);
        
        if (isset($params['id_list']) && is_array($params['id_list']) && !empty($params['id_list'])) {
            $pagination = $this->getTransportByIds($params['id_list']);
        } else {
            $pagination = $this->getTransportPagination($params);
        }
        
        $transportCollection = BulkEditTransportResource::collection($pagination);
        return [
            'items' => $transportCollection,
            'meta' => (new PageMetaData($pagination))->toArray()
        ];
    }

    /**
     * Apply user-specific filtering based on user role
     *
     * @param array $params
     * @return array
     */
    private function applyUserFiltering(array $params): array
    {
        if (Auth::user()->hn_cd != UserRole::admin->value) {
            $params['customer_id'] = Auth::user()->id;
        }

        return $params;
    }

    /**
     * Get paginated transport data from repository
     *
     * @param array $params
     * @return mixed
     */
    private function getTransportPagination(array $params): mixed
    {
        return $this->transportRepository->getPaginate($params);
    }

    /**
     * Get transport records by specific IDs
     *
     * @param array $ids
     * @return mixed
     */
    private function getTransportByIds(array $ids): mixed
    {
        $transports = $this->transportRepository->getTransportsByIdList($ids);
        
        // Convert collection to pagination-like structure
        $perPage = 20;
        $currentPage = 1;
        $total = $transports->count();
        
        return new LengthAwarePaginator(
            $transports,
            $total,
            $perPage,
            $currentPage,
            [
                'path' => request()->url(),
                'pageName' => 'page',
            ]
        );
    }
} 