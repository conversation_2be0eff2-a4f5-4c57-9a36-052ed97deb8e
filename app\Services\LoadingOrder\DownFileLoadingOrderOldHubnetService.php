<?php

declare(strict_types=1);

namespace App\Services\LoadingOrder;

use App\Constants\ApiCodes;
use App\Enums\ApiKey;
use App\Exceptions\ApiException;
use http\Exception\RuntimeException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class DownFileLoadingOrderOldHubnetService
{
    private const API_ENDPOINT = '/backend/order/loading_file_download_api_81_.php';
    private const HTTP_TIMEOUT = 30;
    private const USER_AGENT = 'Laravel-Transport-API/1.0';

    public function call(array $params)
    {
        $customerId = (int) $params['customer_id'];
        $loadingId = (int) $params['loading_id'];
        $filename = (string) $params['fname'];

        $apiUrl = $this->buildApiUrl();
        $requestParams = $this->buildRequestParams($customerId, $loadingId, $filename);

        $response = $this->makeApiRequest($apiUrl, $requestParams);

        if ($response->successful()) {
            return response($response->body(), 200, $response->headers());
        }

        throw new ApiException(ApiCodes::FILE_DOWN_LOAD_ERROR, '400');

    }

    /**
     * Build the complete API URL
     *
     * @return string
     * @throws \RuntimeException
     */
    private function buildApiUrl(): string
    {
        $oldApiUrl = config('app.APP_URL_OLD', env('APP_URL_OLD'));

        if (empty($oldApiUrl)) {
            Log::error('APP_URL_OLD is not configured');
            throw new RuntimeException('External API configuration error');
        }

        return rtrim($oldApiUrl, '/') . self::API_ENDPOINT;
    }


    /**
     * Build request parameters
     *
     * @param int|string $customerId
     * @param int|string $transportId
     * @param string $filename
     * @return array
     */
    private function buildRequestParams($customerId, $loadingId, string $filename): array
    {
        return [
            'customer_id' => $customerId,
            'loading_id' => $loadingId,
            'fname' => $filename,
        ];
    }


    /**
     * Make HTTP request to external API
     *
     * @param string $apiUrl
     * @param array $params
     * @return \Illuminate\Http\Client\Response
     */
    private function makeApiRequest(string $apiUrl, array $params): \Illuminate\Http\Client\Response
    {
        return Http::timeout(self::HTTP_TIMEOUT)
            ->withHeaders([
                'Accept' => 'application/json',
                'User-Agent' => self::USER_AGENT,
                'X-Token-Validate' => ApiKey::API_OLD_HUBNET->value
            ])
            ->get($apiUrl, $params);
    }

}
