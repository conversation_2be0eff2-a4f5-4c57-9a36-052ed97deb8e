<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\Admin;

use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Http\Requests\LoadingOrder\BulkDownloadFileZipLoadingOrderRequest;
use App\Http\Requests\LoadingOrder\BulkUpdateStatusLoadingOrderRequest;
use App\Http\Requests\LoadingOrder\DownFileLoadingOrderOldHubnetRequest;
use App\Http\Requests\LoadingOrder\ExportCSVLoadingOrderRequest;
use App\Http\Requests\LoadingOrder\ListLoadingOrderRequest;
use App\Http\Requests\LoadingOrder\LoadingOrderPrintRequest;
use App\Http\Requests\LoadingOrder\LoadingOrderUpdateAndPrintRequest;
use App\Http\Requests\LoadingOrder\UpdateLoadingOrderRequest;
use App\Http\Resources\LoadingOrder\DetailLoadingOrderResource;
use App\Services\LoadingOrder\BulkDownloadFileZipLoadingOrderService;
use App\Services\LoadingOrder\BulkUpdateStatusLoadingOrderService;
use App\Services\LoadingOrder\DetailLoadingOrderService;
use App\Services\LoadingOrder\DownFileLoadingOrderOldHubnetService;
use App\Services\LoadingOrder\ExportCsvGenerateActionService;
use App\Services\LoadingOrder\ExportCsvLoadingOrderService;
use App\Services\LoadingOrder\GetListLoadingOrderService;
use App\Services\LoadingOrder\GetLoadingOrderPrintService;
use App\Services\LoadingOrder\UpdateAndPrintLoadingOrderService;
use App\Services\LoadingOrder\UpdateLoadingOrderService;
use Exception;
use MarcinOrlowski\ResponseBuilder\Exceptions\ArrayWithMixedKeysException;
use MarcinOrlowski\ResponseBuilder\Exceptions\ConfigurationNotFoundException;
use MarcinOrlowski\ResponseBuilder\Exceptions\IncompatibleTypeException;
use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Response;

class LoadingOrderController extends Controller
{
    private GetListLoadingOrderService $getListLoadingOrderService;
    private BulkUpdateStatusLoadingOrderService $bulkUpdateStatusLoadingOrderService;
    private DetailLoadingOrderService  $detailLoadingOrderService;
    private GetLoadingOrderPrintService  $getLoadingOrderPrintService;
    private UpdateAndPrintLoadingOrderService  $updateAndPrintLoadingOrderService;
    private UpdateLoadingOrderService $updateLoadingOrderService;
    private DownFileLoadingOrderOldHubnetService $downFileLoadingOrderOldHubnetService;
    private ExportCsvGenerateActionService $exportCsvGenerateActionService;
    private ExportCsvLoadingOrderService  $exportCsvLoadingOrderService;
    private BulkDownloadFileZipLoadingOrderService $bulkDownloadFileZipLoadingOrderService;

    public function __construct(
        GetListLoadingOrderService $getListLoadingOrderService,
        BulkUpdateStatusLoadingOrderService $bulkUpdateStatusLoadingOrderService,
        DetailLoadingOrderService $detailLoadingOrderService,
        GetLoadingOrderPrintService $getLoadingOrderPrintService,
        UpdateAndPrintLoadingOrderService $updateAndPrintLoadingOrderService,
        UpdateLoadingOrderService  $updateLoadingOrderService,
        DownFileLoadingOrderOldHubnetService $downFileLoadingOrderOldHubnetService,
        ExportCsvGenerateActionService $exportCsvGenerateActionService,
        ExportCsvLoadingOrderService  $exportCsvLoadingOrderService,
        BulkDownloadFileZipLoadingOrderService $bulkDownloadFileZipLoadingOrderService,
    ) {
        $this->getListLoadingOrderService = $getListLoadingOrderService;
        $this->bulkUpdateStatusLoadingOrderService = $bulkUpdateStatusLoadingOrderService;
        $this->detailLoadingOrderService = $detailLoadingOrderService;
        $this->getLoadingOrderPrintService = $getLoadingOrderPrintService;
        $this->updateAndPrintLoadingOrderService = $updateAndPrintLoadingOrderService;
        $this->updateLoadingOrderService = $updateLoadingOrderService;
        $this->downFileLoadingOrderOldHubnetService = $downFileLoadingOrderOldHubnetService;
        $this->exportCsvGenerateActionService = $exportCsvGenerateActionService;
        $this->exportCsvLoadingOrderService = $exportCsvLoadingOrderService;
        $this->bulkDownloadFileZipLoadingOrderService = $bulkDownloadFileZipLoadingOrderService;
    }

    #[OA\Get(
        path: '/api/admin/loading-orders',
        description: 'API to get list of loading orders with filtering and pagination capabilities. For array parameters like m_customer-ah_sales_id-in, send as JSON array format (e.g., ?m_customer-ah_sales_id-in=[1,2,3])',
        summary: 'Get list of loading orders',
        security: [['access_token' => []]],
        tags: ['Loading order Management'],
        parameters: [
            new OA\Parameter(
                name: 'limit',
                description: 'Number of records per page (default: 20)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'integer', example: 20)
            ),
            new OA\Parameter(
                name: 'page',
                description: 'Page number (default: 1)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'integer', example: 1)
            ),
            new OA\Parameter(
                name: 't_loading-id-equal',
                description: 'Filter by loading request ID',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'integer', example: 123)
            ),
            new OA\Parameter(
                name: 't_loading-odr_date-date_later',
                description: 'Filter order date from this date onwards (format: YYYY-MM-DD)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', format: 'date', example: '2025-01-15')
            ),
            new OA\Parameter(
                name: 't_loading-odr_date-date_earlier',
                description: 'Filter order date up to this date (format: YYYY-MM-DD)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', format: 'date', example: '2025-01-15')
            ),
            new OA\Parameter(
                name: 't_loading-consignee_name-like',
                description: 'Filter by consignee name (partial match)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'Yamada')
            ),
            new OA\Parameter(
                name: 't_loading-to_name-like',
                description: 'Filter by delivery address name (partial match)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'Tokyo')
            ),
            new OA\Parameter(
                name: 't_loading-destination-like',
                description: 'Filter by destination (partial match)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'Osaka')
            ),
            new OA\Parameter(
                name: 't_loading-car_no-like',
                description: 'Filter by car number (partial match)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '123-456')
            ),
            new OA\Parameter(
                name: 't_loading-car_name-like',
                description: 'Filter by car name (partial match)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'Toyota')
            ),
            new OA\Parameter(
                name: 'customerName',
                description: 'Filter by customer name (searches both Japanese and English names)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '田中商事')
            ),
            new OA\Parameter(
                name: 't_loading-st_cd-equal',
                description: 'Filter by status code',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '01')
            ),
            new OA\Parameter(
                name: 't_loading-odr_kbn-equal',
                description: 'Filter by order classification',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '1')
            ),
            new OA\Parameter(
                name: 'm_customer-ah_sales_id-in',
                description: 'Filter by sales person ID list (JSON array format, e.g., [1,2,3])',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '[1,2,3]')
            ),
            new OA\Parameter(
                name: 'sort',
                description: 'Sort configuration. Format: "column|direction" (e.g., "cus_Name_JP|desc", "odr_date|desc", "car_no|asc"). For multi-level sorting, use comma separator (e.g., "odr_date|desc,id|desc"). Available columns: cus_Name_JP (Customer name), odr_date (Order date), car_no (Car number), id (ID), etc. Default sorting: "odr_date|desc,id|desc" (Order date descending, then ID descending).',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'odr_date|desc,id|desc')
            ),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful operation',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(
                            property: 'data',
                            properties: [
                                new OA\Property(
                                    property: 'data',
                                    type: 'array',
                                    items: new OA\Items(
                                        properties: [
                                            new OA\Property(property: 'id', description: 'Loading order ID', type: 'integer', example: 123),
                                            new OA\Property(property: 'odr_date', description: 'Order date', type: 'string', format: 'date', example: '2025-01-15'),
                                            new OA\Property(property: 'consignee_name', description: 'Consignee name', type: 'string', example: 'Yamada Taro'),
                                            new OA\Property(property: 'to_name', description: 'Delivery address name', type: 'string', example: 'Tokyo Office'),
                                            new OA\Property(property: 'destination', description: 'Destination', type: 'string', example: 'Osaka'),
                                            new OA\Property(property: 'car_no', description: 'Car number', type: 'string', example: '123-456'),
                                            new OA\Property(property: 'car_name', description: 'Car name', type: 'string', example: 'Toyota Camry'),
                                            new OA\Property(property: 'st_cd', description: 'Status code', type: 'string', example: '01'),
                                            new OA\Property(property: 'odr_kbn', description: 'Order classification', type: 'string', example: '1'),
                                            new OA\Property(
                                                property: 'customer',
                                                description: 'Customer information',
                                                properties: [
                                                    new OA\Property(property: 'id', type: 'integer', example: 100),
                                                    new OA\Property(property: 'cus_name_JP', type: 'string', example: '田中商事'),
                                                    new OA\Property(property: 'cus_name_EN', type: 'string', example: 'Tanaka Trading'),
                                                    new OA\Property(property: 'ah_sales_id', type: 'integer', example: 5)
                                                ],
                                                type: 'object'
                                            )
                                        ]
                                    )
                                ),
                                new OA\Property(property: 'current_page', type: 'integer', example: 1),
                                new OA\Property(property: 'last_page', type: 'integer', example: 5),
                                new OA\Property(property: 'per_page', type: 'integer', example: 20),
                                new OA\Property(property: 'total', type: 'integer', example: 100)
                            ],
                            type: 'object'
                        ),
                        new OA\Property(property: 'message', type: 'string', example: null, nullable: true)
                    ]
                )
            ),
            new OA\Response(response: 400, description: 'Bad request - validation failed'),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function getListLoadingOrder(ListLoadingOrderRequest $request): Response
    {
        return $this->respond(
            $this->getListLoadingOrderService->call($request->validated())
        );
    }

    #[OA\Put(
        path: '/api/admin/loading-orders/bulk-update-status',
        description: 'Bulk update the status of multiple loading orders.',
        summary: 'Bulk update loading order status',
        security: [['access_token' => []]],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(
                required: ['ids', 'status'],
                properties: [
                    new OA\Property(
                        property: 'ids',
                        description: 'Array of loading order IDs to update',
                        type: 'array',
                        items: new OA\Items(type: 'integer', example: 123),
                        example: [123, 124, 125]
                    ),
                    new OA\Property(
                        property: 'status',
                        description: 'New status code to set for all selected loading orders',
                        type: 'integer',
                        example: 1
                    )
                ]
            )
        ),
        tags: ['Loading order Management'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Status updated successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'data', type: 'object', example: null, nullable: true),
                        new OA\Property(property: 'message', type: 'string', example: 'Status updated successfully', nullable: true)
                    ]
                )
            ),
            new OA\Response(response: 400, description: 'Bad request - validation failed'),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function bulkUpdateStatusLoadingOrder(BulkUpdateStatusLoadingOrderRequest $request): Response
    {
        $this->bulkUpdateStatusLoadingOrderService->call($request->validated());

        return $this->respond();
    }

    #[OA\Get(
        path: '/api/admin/loading-orders/{id}',
        description: 'API to get detailed information of a specific loading order by ID',
        summary: 'Get loading order detail',
        security: [['access_token' => []]],
        tags: ['Loading order Management'],
        parameters: [
            new OA\Parameter(
                name: 'id',
                description: 'Loading order ID',
                in: 'path',
                required: true,
                schema: new OA\Schema(type: 'integer', example: 123)
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Loading order detail retrieved successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(
                            property: 'data',
                            description: 'Loading order detail information',
                            properties: [
                                new OA\Property(property: 'id', type: 'integer', example: 123),
                                new OA\Property(property: 'st_cd', description: 'Status code', type: 'string', example: '01'),
                                new OA\Property(property: 'ref_no', description: 'Reference number', type: 'string', example: 'REF001'),
                                new OA\Property(property: 'agent_ref_no', description: 'Agent reference number', type: 'string', example: 'AGENT001'),
                                new OA\Property(property: 'car_no', description: 'Car number', type: 'string', example: 'ABC123'),
                                new OA\Property(property: 'car_name', description: 'Car name', type: 'string', example: 'Toyota Camry'),
                                new OA\Property(property: 'car_year', description: 'Car year', type: 'string', example: '2020'),
                                new OA\Property(property: 'mileage', description: 'Car mileage', type: 'string', example: '50000'),
                                new OA\Property(property: 'fob_price', description: 'FOB price', type: 'string', example: '15000'),
                                new OA\Property(property: 'price_currency', description: 'Price currency', type: 'string', example: 'USD'),
                                new OA\Property(property: 'price_quotation', description: 'Price quotation', type: 'string', example: '16000'),
                                new OA\Property(property: 'to_name', description: 'Delivery address name', type: 'string', example: 'Tokyo Port'),
                                new OA\Property(property: 'destination', description: 'Destination', type: 'string', example: 'Tokyo'),
                                new OA\Property(property: 'port', description: 'Port', type: 'string', example: 'Tokyo Port'),
                                new OA\Property(property: 'to_plan_date', description: 'Planned delivery date', type: 'string', example: '2025-02-15'),
                                new OA\Property(property: 'consignee_name', description: 'Consignee name', type: 'string', example: 'Yamada Trading'),
                                new OA\Property(property: 'to_plan_flg', description: 'Delivery plan flag', type: 'string', example: '1'),
                                new OA\Property(property: 'consignee_flg', description: 'Consignee flag', type: 'string', example: '1'),
                                new OA\Property(property: 'odr_kbn', description: 'Order classification', type: 'string', example: '1'),
                                new OA\Property(property: 'odr_date', description: 'Order date', type: 'string', example: '2025-01-15'),
                                new OA\Property(property: 'customs_flg', description: 'Customs flag', type: 'string', example: '0'),
                                new OA\Property(property: 'part_note', description: 'Part note', type: 'string', example: 'Special handling required'),
                                new OA\Property(property: 'note', description: 'General note', type: 'string', example: 'Handle with care'),
                                new OA\Property(
                                    property: 'customer',
                                    description: 'Customer information',
                                    properties: [
                                        new OA\Property(property: 'cus_Name_JP', type: 'string', example: '田中商事'),
                                        new OA\Property(property: 'ah_sales_name', type: 'string', example: 'John Smith'),
                                        new OA\Property(property: 'cus_name_EN', type: 'string', example: 'Tanaka Trading')
                                    ],
                                    type: 'object'
                                ),
                                new OA\Property(property: 'special_note', description: 'Special note', type: 'string', example: 'Urgent delivery'),
                                new OA\Property(property: 'optn1_flg', description: 'Option 1 flag', type: 'string', example: '1'),
                                new OA\Property(property: 'optn2_flg', description: 'Option 2 flag', type: 'string', example: '0'),
                                new OA\Property(property: 'optn2_sub_kbn', description: 'Option 2 sub classification', type: 'string', example: 'A'),
                                new OA\Property(property: 'optn2_sub2_kbn', description: 'Option 2 sub2 classification', type: 'string', example: 'B'),
                                new OA\Property(property: 'optn3_flg', description: 'Option 3 flag', type: 'string', example: '1'),
                                new OA\Property(property: 'optn3_sub_txt', description: 'Option 3 sub text', type: 'string', example: 'Special instruction'),
                                new OA\Property(property: 'optn4_flg', description: 'Option 4 flag', type: 'string', example: '0'),
                                new OA\Property(property: 'optn4_sub_kbn', description: 'Option 4 sub classification', type: 'string', example: 'C'),
                                new OA\Property(property: 'optn5_flg', description: 'Option 5 flag', type: 'string', example: '1'),
                                new OA\Property(property: 'optn5_sub_txt', description: 'Option 5 sub text', type: 'string', example: 'Additional service'),
                                new OA\Property(property: 'optn6_flg', description: 'Option 6 flag', type: 'string', example: '0'),
                                new OA\Property(property: 'optn6_sub_kbn', description: 'Option 6 sub classification', type: 'string', example: 'D'),
                                new OA\Property(property: 'optn7_flg', description: 'Option 7 flag', type: 'string', example: '1'),
                                new OA\Property(property: 'optn8_flg', description: 'Option 8 flag', type: 'string', example: '0'),
                                new OA\Property(property: 'optn9_flg', description: 'Option 9 flag', type: 'string', example: '1'),
                                new OA\Property(property: 'optn10_flg', description: 'Option 10 flag', type: 'string', example: '0'),
                                new OA\Property(property: 'optn11_flg', description: 'Option 11 flag', type: 'string', example: '1'),
                                new OA\Property(property: 'optn14_flg', description: 'Option 14 flag', type: 'string', example: '0'),
                                new OA\Property(property: 'optn17_flg', description: 'Option 17 flag', type: 'string', example: '1'),
                                new OA\Property(property: 'optn18_flg', description: 'Option 18 flag', type: 'string', example: '0'),
                                new OA\Property(property: 'optn21_flg', description: 'Option 21 flag', type: 'string', example: '1')
                            ],
                            type: 'object'
                        ),
                        new OA\Property(property: 'message', type: 'string', example: null, nullable: true)
                    ]
                )
            ),
            new OA\Response(response: 400, description: 'Bad request - invalid ID format'),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 404, description: 'Loading order not found'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function getDetailLoadingOrder($id): Response
    {
        return $this->respond(
            DetailLoadingOrderResource::make(
                $this->detailLoadingOrderService->call($id)
            )
        );
    }

    #[OA\Get(
        path: '/api/admin/loading-orders/print-view',
        description: 'API to get loading orders for printing purposes. Accepts a JSON array of loading order IDs to retrieve detailed information for printing.',
        summary: 'Get loading orders for printing',
        security: [['access_token' => []]],
        tags: ['Loading order Management'],
        parameters: [
            new OA\Parameter(
                name: 'ids',
                description: 'JSON array of loading order IDs to print (e.g., [1,2,3])',
                in: 'query',
                required: false,
                schema: new OA\Schema(
                    description: 'JSON array of integer IDs',
                    type: 'string',
                    example: '[1,2,3]'
                )
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Loading orders for printing retrieved successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(
                            property: 'data',
                            description: 'Array of loading order details for printing',
                            type: 'array',
                            items: new OA\Items(
                                properties: [
                                    new OA\Property(property: 'id', type: 'integer', example: 123),
                                    new OA\Property(property: 'st_cd', description: 'Status code', type: 'string', example: '01'),
                                    new OA\Property(property: 'ref_no', description: 'Reference number', type: 'string', example: 'REF001'),
                                    new OA\Property(property: 'agent_ref_no', description: 'Agent reference number', type: 'string', example: 'AGENT001'),
                                    new OA\Property(property: 'car_no', description: 'Car number', type: 'string', example: 'ABC123'),
                                    new OA\Property(property: 'car_name', description: 'Car name', type: 'string', example: 'Toyota Camry'),
                                    new OA\Property(property: 'car_year', description: 'Car year', type: 'string', example: '2020'),
                                    new OA\Property(property: 'mileage', description: 'Car mileage', type: 'string', example: '50000'),
                                    new OA\Property(property: 'fob_price', description: 'FOB price', type: 'string', example: '15000'),
                                    new OA\Property(property: 'price_currency', description: 'Price currency', type: 'string', example: 'USD'),
                                    new OA\Property(property: 'price_quotation', description: 'Price quotation', type: 'string', example: '16000'),
                                    new OA\Property(property: 'to_name', description: 'Delivery address name', type: 'string', example: 'Tokyo Port'),
                                    new OA\Property(property: 'destination', description: 'Destination', type: 'string', example: 'Tokyo'),
                                    new OA\Property(property: 'port', description: 'Port', type: 'string', example: 'Tokyo Port'),
                                    new OA\Property(property: 'to_plan_date', description: 'Planned delivery date', type: 'string', example: '2025-02-15'),
                                    new OA\Property(property: 'consignee_name', description: 'Consignee name', type: 'string', example: 'Yamada Trading'),
                                    new OA\Property(property: 'to_plan_flg', description: 'Delivery plan flag', type: 'string', example: '1'),
                                    new OA\Property(property: 'consignee_flg', description: 'Consignee flag', type: 'string', example: '1'),
                                    new OA\Property(property: 'odr_kbn', description: 'Order classification', type: 'string', example: '1'),
                                    new OA\Property(property: 'odr_date', description: 'Order date', type: 'string', example: '2025-01-15'),
                                    new OA\Property(property: 'customs_flg', description: 'Customs flag', type: 'string', example: '0'),
                                    new OA\Property(property: 'part_note', description: 'Part note', type: 'string', example: 'Special handling required'),
                                    new OA\Property(property: 'note', description: 'General note', type: 'string', example: 'Handle with care'),
                                    new OA\Property(
                                        property: 'customer',
                                        description: 'Customer information',
                                        properties: [
                                            new OA\Property(property: 'cus_Name_JP', type: 'string', example: '田中商事'),
                                            new OA\Property(property: 'ah_sales_name', type: 'string', example: 'John Smith'),
                                            new OA\Property(property: 'cus_name_EN', type: 'string', example: 'Tanaka Trading')
                                        ],
                                        type: 'object'
                                    ),
                                    new OA\Property(property: 'special_note', description: 'Special note', type: 'string', example: 'Urgent delivery'),
                                    new OA\Property(property: 'optn1_flg', description: 'Option 1 flag', type: 'string', example: '1'),
                                    new OA\Property(property: 'optn2_flg', description: 'Option 2 flag', type: 'string', example: '0'),
                                    new OA\Property(property: 'optn2_sub_kbn', description: 'Option 2 sub classification', type: 'string', example: 'A'),
                                    new OA\Property(property: 'optn2_sub2_kbn', description: 'Option 2 sub2 classification', type: 'string', example: 'B'),
                                    new OA\Property(property: 'optn3_flg', description: 'Option 3 flag', type: 'string', example: '1'),
                                    new OA\Property(property: 'optn3_sub_txt', description: 'Option 3 sub text', type: 'string', example: 'Special instruction'),
                                    new OA\Property(property: 'optn4_flg', description: 'Option 4 flag', type: 'string', example: '0'),
                                    new OA\Property(property: 'optn4_sub_kbn', description: 'Option 4 sub classification', type: 'string', example: 'C'),
                                    new OA\Property(property: 'optn5_flg', description: 'Option 5 flag', type: 'string', example: '1'),
                                    new OA\Property(property: 'optn5_sub_txt', description: 'Option 5 sub text', type: 'string', example: 'Additional service'),
                                    new OA\Property(property: 'optn6_flg', description: 'Option 6 flag', type: 'string', example: '0'),
                                    new OA\Property(property: 'optn6_sub_kbn', description: 'Option 6 sub classification', type: 'string', example: 'D'),
                                    new OA\Property(property: 'optn7_flg', description: 'Option 7 flag', type: 'string', example: '1'),
                                    new OA\Property(property: 'optn8_flg', description: 'Option 8 flag', type: 'string', example: '0'),
                                    new OA\Property(property: 'optn9_flg', description: 'Option 9 flag', type: 'string', example: '1'),
                                    new OA\Property(property: 'optn10_flg', description: 'Option 10 flag', type: 'string', example: '0'),
                                    new OA\Property(property: 'optn11_flg', description: 'Option 11 flag', type: 'string', example: '1'),
                                    new OA\Property(property: 'optn14_flg', description: 'Option 14 flag', type: 'string', example: '0'),
                                    new OA\Property(property: 'optn17_flg', description: 'Option 17 flag', type: 'string', example: '1'),
                                    new OA\Property(property: 'optn18_flg', description: 'Option 18 flag', type: 'string', example: '0'),
                                    new OA\Property(property: 'optn21_flg', description: 'Option 21 flag', type: 'string', example: '1')
                                ],
                                type: 'object'
                            )
                        ),
                        new OA\Property(property: 'message', type: 'string', example: null, nullable: true)
                    ]
                )
            ),
            new OA\Response(response: 400, description: 'Bad request - invalid JSON format'),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 404, description: 'No loading orders found'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function getLoadingPrint(LoadingOrderPrintRequest $params): Response
    {
        return $this->respond(
            DetailLoadingOrderResource::collection(
                $this->getLoadingOrderPrintService->call($params->validated())
            )
        );
    }

    #[OA\Post(
        path: '/api/admin/loading-orders/update-and-print',
        description: 'API to update status of loading orders and get them for printing. This endpoint accepts an array of loading order IDs, updates their status, and returns the updated loading order details for printing purposes.',
        summary: 'Update loading order status and get for printing',
        security: [['access_token' => []]],
        requestBody: new OA\RequestBody(
            description: 'Loading order IDs to update and print',
            required: true,
            content: new OA\JsonContent(
                required: ['ids'],
                properties: [
                    new OA\Property(
                        property: 'ids',
                        description: 'Array of loading order IDs to update and print',
                        type: 'array',
                        items: new OA\Items(type: 'integer', example: 123),
                        example: [1, 2, 3],
                        minItems: 1
                    )
                ]
            )
        ),
        tags: ['Loading order Management'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Loading orders updated and retrieved for printing successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(
                            property: 'data',
                            description: 'Array of updated loading order details for printing',
                            type: 'array',
                            items: new OA\Items(
                                properties: [
                                    new OA\Property(property: 'id', type: 'integer', example: 123),
                                    new OA\Property(property: 'st_cd', description: 'Status code', type: 'string', example: '02'),
                                    new OA\Property(property: 'ref_no', description: 'Reference number', type: 'string', example: 'REF001'),
                                    new OA\Property(property: 'agent_ref_no', description: 'Agent reference number', type: 'string', example: 'AGENT001'),
                                    new OA\Property(property: 'car_no', description: 'Car number', type: 'string', example: 'ABC123'),
                                    new OA\Property(property: 'car_name', description: 'Car name', type: 'string', example: 'Toyota Camry'),
                                    new OA\Property(property: 'car_year', description: 'Car year', type: 'string', example: '2020'),
                                    new OA\Property(property: 'mileage', description: 'Car mileage', type: 'string', example: '50000'),
                                    new OA\Property(property: 'fob_price', description: 'FOB price', type: 'string', example: '15000'),
                                    new OA\Property(property: 'price_currency', description: 'Price currency', type: 'string', example: 'USD'),
                                    new OA\Property(property: 'price_quotation', description: 'Price quotation', type: 'string', example: '16000'),
                                    new OA\Property(property: 'to_name', description: 'Delivery address name', type: 'string', example: 'Tokyo Port'),
                                    new OA\Property(property: 'destination', description: 'Destination', type: 'string', example: 'Tokyo'),
                                    new OA\Property(property: 'port', description: 'Port', type: 'string', example: 'Tokyo Port'),
                                    new OA\Property(property: 'to_plan_date', description: 'Planned delivery date', type: 'string', example: '2025-02-15'),
                                    new OA\Property(property: 'consignee_name', description: 'Consignee name', type: 'string', example: 'Yamada Trading'),
                                    new OA\Property(property: 'to_plan_flg', description: 'Delivery plan flag', type: 'string', example: '1'),
                                    new OA\Property(property: 'consignee_flg', description: 'Consignee flag', type: 'string', example: '1'),
                                    new OA\Property(property: 'odr_kbn', description: 'Order classification', type: 'string', example: '1'),
                                    new OA\Property(property: 'odr_date', description: 'Order date', type: 'string', example: '2025-01-15'),
                                    new OA\Property(property: 'customs_flg', description: 'Customs flag', type: 'string', example: '0'),
                                    new OA\Property(property: 'part_note', description: 'Part note', type: 'string', example: 'Special handling required'),
                                    new OA\Property(property: 'note', description: 'General note', type: 'string', example: 'Handle with care'),
                                    new OA\Property(
                                        property: 'customer',
                                        description: 'Customer information',
                                        properties: [
                                            new OA\Property(property: 'cus_Name_JP', type: 'string', example: '田中商事'),
                                            new OA\Property(property: 'ah_sales_name', type: 'string', example: 'John Smith'),
                                            new OA\Property(property: 'cus_name_EN', type: 'string', example: 'Tanaka Trading')
                                        ],
                                        type: 'object'
                                    ),
                                    new OA\Property(property: 'special_note', description: 'Special note', type: 'string', example: 'Urgent delivery'),
                                    new OA\Property(property: 'optn1_flg', description: 'Option 1 flag', type: 'string', example: '1'),
                                    new OA\Property(property: 'optn2_flg', description: 'Option 2 flag', type: 'string', example: '0'),
                                    new OA\Property(property: 'optn2_sub_kbn', description: 'Option 2 sub classification', type: 'string', example: 'A'),
                                    new OA\Property(property: 'optn2_sub2_kbn', description: 'Option 2 sub2 classification', type: 'string', example: 'B'),
                                    new OA\Property(property: 'optn3_flg', description: 'Option 3 flag', type: 'string', example: '1'),
                                    new OA\Property(property: 'optn3_sub_txt', description: 'Option 3 sub text', type: 'string', example: 'Special instruction'),
                                    new OA\Property(property: 'optn4_flg', description: 'Option 4 flag', type: 'string', example: '0'),
                                    new OA\Property(property: 'optn4_sub_kbn', description: 'Option 4 sub classification', type: 'string', example: 'C'),
                                    new OA\Property(property: 'optn5_flg', description: 'Option 5 flag', type: 'string', example: '1'),
                                    new OA\Property(property: 'optn5_sub_txt', description: 'Option 5 sub text', type: 'string', example: 'Additional service'),
                                    new OA\Property(property: 'optn6_flg', description: 'Option 6 flag', type: 'string', example: '0'),
                                    new OA\Property(property: 'optn6_sub_kbn', description: 'Option 6 sub classification', type: 'string', example: 'D'),
                                    new OA\Property(property: 'optn7_flg', description: 'Option 7 flag', type: 'string', example: '1'),
                                    new OA\Property(property: 'optn8_flg', description: 'Option 8 flag', type: 'string', example: '0'),
                                    new OA\Property(property: 'optn9_flg', description: 'Option 9 flag', type: 'string', example: '1'),
                                    new OA\Property(property: 'optn10_flg', description: 'Option 10 flag', type: 'string', example: '0'),
                                    new OA\Property(property: 'optn11_flg', description: 'Option 11 flag', type: 'string', example: '1'),
                                    new OA\Property(property: 'optn14_flg', description: 'Option 14 flag', type: 'string', example: '0'),
                                    new OA\Property(property: 'optn17_flg', description: 'Option 17 flag', type: 'string', example: '1'),
                                    new OA\Property(property: 'optn18_flg', description: 'Option 18 flag', type: 'string', example: '0'),
                                    new OA\Property(property: 'optn21_flg', description: 'Option 21 flag', type: 'string', example: '1')
                                ],
                                type: 'object'
                            )
                        ),
                        new OA\Property(property: 'message', type: 'string', example: null, nullable: true)
                    ]
                )
            ),
            new OA\Response(response: 400, description: 'Bad request - invalid request data or validation failed'),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 404, description: 'One or more loading orders not found'),
            new OA\Response(response: 422, description: 'Validation error'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function printAndUpdateStatus(LoadingOrderUpdateAndPrintRequest $request): Response
    {
        return $this->respond(
            DetailLoadingOrderResource::collection(
                $this->updateAndPrintLoadingOrderService->call($request->validated())
            )
        );
    }

    /**
     * @throws ArrayWithMixedKeysException
     * @throws IncompatibleTypeException
     * @throws ConfigurationNotFoundException
     * @throws Exception
     */
    #[OA\Put(
        path: '/api/admin/loading-orders/{id}',
        description: 'API to update an existing loading order by ID',
        summary: 'Update loading order',
        security: [['access_token' => []]],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\MediaType(
                mediaType: 'application/json',
                schema: new OA\Schema(
                    properties: [
                        new OA\Property(property: 'odr_kbn', description: 'Order classification', type: 'string', maxLength: 50, example: '1'),
                        new OA\Property(property: 'm_customer_id', description: 'Customer ID', type: 'integer', example: 12345),
                        new OA\Property(property: 'ref_no', description: 'Reference number', type: 'string', maxLength: 100, example: 'REF-20250817-A'),
                        new OA\Property(property: 'agent_ref_no', description: 'Agent reference number', type: 'string', maxLength: 100, example: 'AGENT-9999'),
                        new OA\Property(property: 'st_cd', description: 'Status code', type: 'numeric', example: 2),
                        new OA\Property(property: 'odr_date', description: 'Order date', type: 'string', format: 'date', example: '2025-08-17'),
                        new OA\Property(property: 'car_year', description: 'Car year', type: 'integer', example: 2020),
                        new OA\Property(property: 'car_no', description: 'Car number', type: 'string', maxLength: 50, example: 'ABC123'),
                        new OA\Property(property: 'car_name', description: 'Car name', type: 'string', maxLength: 255, example: 'Toyota Corolla'),
                        new OA\Property(property: 'mileage', description: 'Car mileage', type: 'integer', example: 56000),
                        new OA\Property(property: 'fob_price', description: 'FOB price', type: 'numeric', example: 15000.5),
                        new OA\Property(property: 'customs_flg', description: 'Customs flag', type: 'boolean', example: true),
                        new OA\Property(property: 'consignee_name', description: 'Consignee name', type: 'string', maxLength: 255, example: 'John Doe'),
                        new OA\Property(property: 'consignee_flg', description: 'Consignee flag', type: 'boolean', example: false),
                        new OA\Property(property: 'destination', description: 'Destination', type: 'string', maxLength: 255, example: 'Yokohama Port'),
                        new OA\Property(property: 'port', description: 'Port', type: 'string', maxLength: 255, example: 'Tokyo'),
                        new OA\Property(property: 'to_name', description: 'Delivery address name', type: 'string', maxLength: 255, example: 'Tanaka Co., Ltd.'),
                        new OA\Property(property: 'to_plan_date', description: 'Planned delivery date', type: 'string', format: 'date', example: '2025-08-25'),
                        new OA\Property(property: 'to_plan_flg', description: 'Delivery plan flag', type: 'boolean', example: true),
                        new OA\Property(property: 'special_note', description: 'Special note', type: 'string', example: 'Urgent shipment'),
                        new OA\Property(property: 'part_note', description: 'Part note', type: 'string', example: 'Replace left mirror'),
                        new OA\Property(property: 'note', description: 'General note', type: 'string', example: 'Urgent shipment'),
                        new OA\Property(property: 'optn2_sub_kbn', description: 'Option 2 sub classification', type: 'string', example: '3'),
                        new OA\Property(property: 'optn2_sub2_kbn', description: 'Option 2 sub2 classification', type: 'string', example: '2'),
                        new OA\Property(property: 'optn3_flg', description: 'Option 3 flag', type: 'string', example: '1'),
                        new OA\Property(property: 'optn3_sub_txt', description: 'Option 3 sub text', type: 'string', example: 'GPS'),
                        new OA\Property(property: 'optn4_flg', description: 'Option 4 flag', type: 'string', example: '1'),
                        new OA\Property(property: 'optn4_sub_kbn', description: 'Option 4 sub classification', type: 'string', example: '2'),
                        new OA\Property(property: 'optn5_flg', description: 'Option 5 flag', type: 'string', example: '1'),
                        new OA\Property(property: 'optn6_flg', description: 'Option 6 flag', type: 'string', example: '0'),
                        new OA\Property(property: 'optn6_sub_kbn', description: 'Option 6 sub classification', type: 'string', example: '1'),
                        new OA\Property(property: 'optn7_flg', description: 'Option 7 flag', type: 'string', example: '1'),
                        new OA\Property(property: 'optn8_flg', description: 'Option 8 flag', type: 'string', example: '0'),
                        new OA\Property(property: 'optn10_flg', description: 'Option 10 flag', type: 'string', example: '1'),
                        new OA\Property(property: 'optn11_flg', description: 'Option 11 flag', type: 'string', example: '0'),
                        new OA\Property(property: 'optn14_flg', description: 'Option 14 flag', type: 'string', example: '1'),
                        new OA\Property(property: 'optn17_flg', description: 'Option 17 flag', type: 'string', example: '0'),
                        new OA\Property(property: 'optn18_flg', description: 'Option 18 flag', type: 'boolean', example: true),
                        new OA\Property(property: 'optn18_sub_kbn', description: 'Option 18 sub classification', type: 'integer', example: 1),
                        new OA\Property(property: 'optn21_flg', description: 'Option 21 flag', type: 'string', example: '1')
                    ],
                    type: 'object'
                )
            )
        ),
        tags: ['Loading order Management'],
        parameters: [
            new OA\Parameter(
                name: 'id',
                description: 'Loading order ID',
                in: 'path',
                required: true,
                schema: new OA\Schema(type: 'integer', example: 123)
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Loading order updated successfully',
                content: new OA\MediaType(
                    mediaType: 'application/json',
                    schema: new OA\Schema(
                        properties: [
                            new OA\Property(property: 'success', type: 'boolean', example: true),
                            new OA\Property(property: 'data', type: 'object', example: null, nullable: true),
                            new OA\Property(property: 'message', type: 'string', example: 'Loading order updated successfully')
                        ],
                        type: 'object'
                    )
                )
            ),
            new OA\Response(response: 400, description: 'Bad request - invalid request data'),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 404, description: 'Loading order not found'),
            new OA\Response(response: 422, description: 'Validation error'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function updateLoadingOrder($id, UpdateLoadingOrderRequest $request): Response
    {
        $this->updateLoadingOrderService->call((int)$id, $request->validated());

        return $this->respond();
    }

    /**
     * Download loading order file from old Hubnet system
     */
    #[OA\Get(
        path: '/api/admin/loading-orders/download-loading-order-file',
        description: 'Download loading order file from old Hubnet system',
        summary: 'Download loading order file',
        security: [['access_token' => []]],
        tags: ['Loading order Management'],
        parameters: [
            new OA\Parameter(
                name: 'customer_id',
                description: 'Customer ID',
                in: 'query',
                required: true,
                schema: new OA\Schema(type: 'integer', example: 12492)
            ),
            new OA\Parameter(
                name: 'loading_id',
                description: 'Loading order ID',
                in: 'query',
                required: true,
                schema: new OA\Schema(type: 'integer', example: 88518)
            ),
            new OA\Parameter(
                name: 'fname',
                description: 'File name to download',
                in: 'query',
                required: true,
                schema: new OA\Schema(type: 'string', example: '2F.png')
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Loading order file downloaded successfully',
                content: new OA\MediaType(
                    mediaType: 'application/octet-stream',
                    schema: new OA\Schema(type: 'string', format: 'binary')
                )
            ),
            new OA\Response(
                response: 400,
                description: 'Bad request - File download failed',
                content: new OA\MediaType(
                    mediaType: 'application/json',
                    schema: new OA\Schema(
                        properties: [
                            new OA\Property(property: 'error', type: 'string', example: 'FILE_DOWN_LOAD_ERROR'),
                            new OA\Property(property: 'message', type: 'string', example: 'Failed to download loading order file')
                        ],
                        type: 'object'
                    )
                )
            ),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 422, description: 'Validation error'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function downLoadingOrderFile(DownFileLoadingOrderOldHubnetRequest $request)
    {
        return $this->downFileLoadingOrderOldHubnetService->call($request->validated());
    }

    /**
     * @throws ApiException
     */
    #[OA\Get(
        path: '/api/admin/loading-orders/export-csv/action',
        description: 'Export loading orders data to CSV format. This endpoint requires a valid token generated from the generate-csv-token endpoint. All filters are passed as query parameters.',
        summary: 'Export loading orders to CSV',
        security: [['access_token' => []]],
        tags: ['Loading order Management'],
        parameters: [
            new OA\Parameter(
                name: 'token',
                description: 'CSV export token (required)',
                in: 'query',
                required: true,
                schema: new OA\Schema(type: 'string', example: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...')
            ),
            new OA\Parameter(
                name: 'limit',
                description: 'Number of records per page (default: 20)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'integer', example: 20)
            ),
            new OA\Parameter(
                name: 'page',
                description: 'Page number (default: 1)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'integer', example: 1)
            ),
            new OA\Parameter(
                name: 't_loading-id-equal',
                description: 'Filter by loading request ID',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'integer', example: 123)
            ),
            new OA\Parameter(
                name: 't_loading-odr_date-date_later',
                description: 'Filter order date from this date onwards (format: YYYY-MM-DD)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', format: 'date', example: '2025-01-15')
            ),
            new OA\Parameter(
                name: 't_loading-odr_date-date_earlier',
                description: 'Filter order date up to this date (format: YYYY-MM-DD)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', format: 'date', example: '2025-01-15')
            ),
            new OA\Parameter(
                name: 't_loading-consignee_name-like',
                description: 'Filter by consignee name (partial match)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'Yamada')
            ),
            new OA\Parameter(
                name: 't_loading-to_name-like',
                description: 'Filter by delivery address name (partial match)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'Tokyo')
            ),
            new OA\Parameter(
                name: 't_loading-destination-like',
                description: 'Filter by destination (partial match)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'Osaka')
            ),
            new OA\Parameter(
                name: 't_loading-car_no-like',
                description: 'Filter by car number (partial match)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '123-456')
            ),
            new OA\Parameter(
                name: 't_loading-car_name-like',
                description: 'Filter by car name (partial match)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'Toyota')
            ),
            new OA\Parameter(
                name: 'customerName',
                description: 'Filter by customer name (searches both Japanese and English names)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '田中商事')
            ),
            new OA\Parameter(
                name: 't_loading-st_cd-equal',
                description: 'Filter by status code',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '01')
            ),
            new OA\Parameter(
                name: 't_loading-odr_kbn-equal',
                description: 'Filter by order classification',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '1')
            ),
            new OA\Parameter(
                name: 'm_customer-ah_sales_id-in',
                description: 'Filter by sales person ID list (JSON array format, e.g., [1,2,3])',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '[1,2,3]')
            ),
            new OA\Parameter(
                name: 'sort',
                description: 'Sort configuration. Format: "column|direction" (e.g., "cus_Name_JP|desc", "odr_date|desc", "car_no|asc"). For multi-level sorting, use comma separator (e.g., "odr_date|desc,id|desc"). Available columns: cus_Name_JP (Customer name), odr_date (Order date), car_no (Car number), id (ID), etc. Default sorting: "odr_date|desc,id|desc" (Order date descending, then ID descending).',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'odr_date|desc,id|desc')
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'CSV file generated successfully',
                headers: [
                    new OA\Header(
                        header: 'Content-Disposition',
                        description: 'File attachment header',
                        schema: new OA\Schema(
                            type: 'string',
                            example: 'attachment; filename="loading_orders_20250115.csv"'
                        )
                    )
                ],
                content: new OA\MediaType(
                    mediaType: 'text/csv',
                    schema: new OA\Schema(
                        description: 'CSV file content',
                        type: 'string',
                        format: 'binary'
                    )
                )
            ),
            new OA\Response(
                response: 400,
                description: 'Bad request - invalid token or request data',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 400),
                        new OA\Property(property: 'message', type: 'string', example: 'Invalid token or request data')
                    ]
                )
            ),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 422, description: 'Validation error'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function exportCsv(ExportCSVLoadingOrderRequest $request): Response
    {
        return $this->exportCsvLoadingOrderService->call($request->validated());
    }

    #[OA\Post(
        path: '/api/admin/loading-orders/generate-csv-token',
        description: 'Generate a token for CSV export operation. This token is required for the actual CSV export process.',
        summary: 'Generate CSV export token',
        security: [['access_token' => []]],
        tags: ['Loading order Management'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'CSV export token generated successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'code', type: 'integer', example: 200),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Success'),
                        new OA\Property(
                            property: 'data',
                            properties: [
                                new OA\Property(
                                    property: 'token',
                                    description: 'CSV export token',
                                    type: 'string',
                                    example: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...'
                                )
                            ],
                            type: 'object'
                        )
                    ]
                )
            ),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function generateCsvToken(): Response
    {
        return $this->respond(
            [
                'token' => $this->exportCsvGenerateActionService->call()
            ]
        );
    }

    /**
     * @throws ApiException
     */
    #[OA\Post(
        path: '/api/admin/loading-orders/bulk-download-zip',
        description: 'Download multiple loading-order files as a single ZIP archive.',
        summary: 'Bulk download loading-order files (ZIP)',
        security: [['access_token' => []]],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(
                required: ['ids'],
                properties: [
                    new OA\Property(
                        property: 'ids',
                        description: 'List of loading-order IDs to include in the ZIP',
                        type: 'array',
                        items: new OA\Items(type: 'integer', example: 88518),
                        example: [88518, 88517, 88516]
                    )
                ]
            )
        ),
        tags: ['Loading order Management'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'ZIP file returned',
                headers: [
                    new OA\Header(
                        header: 'Content-Disposition',
                        description: 'attachment; filename="file_081742.zip"',
                        schema: new OA\Schema(type: 'string')
                    )
                ],
                content: new OA\MediaType(
                    mediaType: 'application/zip',
                    schema: new OA\Schema(type: 'string', format: 'binary')
                )
            ),
            new OA\Response(response: 400, description: 'Bad request – validation failed'),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function bulkDownloadZip(BulkDownloadFileZipLoadingOrderRequest $request): Response
    {
        return $this->bulkDownloadFileZipLoadingOrderService->call($request->validated());
    }
}
