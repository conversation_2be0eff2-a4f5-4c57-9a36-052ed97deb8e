<?php

declare(strict_types=1);

namespace App\Enums\LoadingOrder;

enum LoadingOrderStatuses: string
{
    case CONFIRMING = '0'; // 確認中
    case RECEIVED = '1'; // 受付済
    case CANCELED = '9'; // キャンセル

    public function toJapaneseLabel(): string
    {
        return match ($this) {
            self::CONFIRMING => '確認中',
            self::RECEIVED => '受付済',
            self::CANCELED => 'キャンセル',
        };
    }
}
