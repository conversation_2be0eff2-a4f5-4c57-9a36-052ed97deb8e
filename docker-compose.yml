version: '3.8'

services:
    app:
        build:
            context: .
            dockerfile: .docker/DockerFile/dev/Dockerfile
        container_name: laravel-app
        restart: unless-stopped
        networks:
            - autom-dev
        env_file:
            - ./.env
        volumes:
            - ./:/var/www/html
        ports:
            - '8080:8080'

    nginx:
        build:
            context: .
            dockerfile: .docker/nginx/Dockerfile
        container_name: laravel-nginx
        restart: unless-stopped
        ports:
            - "8082:80"
        volumes:
            - ./:/var/www/html
            - ./.docker/nginx/conf.d:/etc/nginx/conf.d
        networks:
            - autom-dev
        depends_on:
            - app

networks:
    autom-dev:
        driver: bridge
