<?php

declare(strict_types=1);

namespace App\Services\Transport;

use App\Repositories\as400\ShipmentRepository;
use App\Repositories\sqlServer\TransportRepository;
use App\Services\ProcessLock\ProcessLockService;
use Exception;
use Illuminate\Support\Facades\DB;

class SyncRefNoTransportService
{
    private TransportRepository $transportRepository;
    private ProcessLockService $processLockService;
    private ShipmentRepository $shipmentRepository;

    public function __construct(
        TransportRepository $transportRepository,
        ProcessLockService $processLockService,
        ShipmentRepository $shipmentRepository
    ) {
        $this->transportRepository = $transportRepository;
        $this->processLockService = $processLockService;
        $this->shipmentRepository = $shipmentRepository;
    }

    public function syncRefNoFormAS400Database()
    {
        $result = [
            'status' => false,
            'message' => ''
        ];
        try {
            DB::beginTransaction();

            // Lock process
            $lockResult = $this->processLockService->lock('trans_list_update_refno_81_.php');
            if (empty($lockResult['status']) || empty($lockResult['lock_id'])) {
                throw new Exception('使用中のため使用できません');
            }

            // Trim car_no first and last - Bulk update using SQL
            $this->transportRepository->updateCarNoHasSpace();

            // Get list not have ref_no
            $targets = $this->transportRepository->getListTransportNotRefNo();

            foreach ($targets as $row) {
                // Check car_no valid
                if (!preg_match('/^[a-zA-Z0-9\-\(\)\[\]]{6,}$/', $row->car_no)) {
                    continue;
                }
                $chkCarNo = str_replace('-', '', $row->car_no);

                $asResult = $this->shipmentRepository->getShipmentByCarNo($chkCarNo);

                if ($asResult->count()) {
                    $this->transportRepository->updateOneByTransportId($row->id, ['ref_no' => $asResult->first()->CMCC03]);
                }
            }

            // Unlock process
            $this->processLockService->unlock($lockResult['lock_id']);

            DB::commit();
            $result['status'] = true;
            return $result;

        } catch (Exception $e) {
            DB::rollBack();
            $result['message'] = $e->getMessage();
            return $result;
        }
    }
}
