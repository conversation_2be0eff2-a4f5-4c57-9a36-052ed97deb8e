<?php

declare(strict_types=1);

namespace App\Models\as400;

use Illuminate\Database\Eloquent\Model;

class Ports extends Model
{
    public $timestamps = false;
    public $incrementing = false;
    protected $connection = 'as400';
    protected $table = 'ABDLIB.PTCMST';

    protected $primaryKey = null;

    protected $fillable = [
        'PTCC20',  // Location/Port code
        'PTN074',  // Location/Port name
        'PTKNCD',  // Country code (numeric)
        'PTKNNM',  // Country name
        'PTRYAK',  // Abbreviation (short code for port)
        'PTRKCD',  // Status flag (e.g. active/inactive)
    ];
}
