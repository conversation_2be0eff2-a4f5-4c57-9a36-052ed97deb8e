<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Transport\CheckDownloadCsvTransportRequest;
use App\Http\Requests\Transport\DelTransFileRefRequest;
use App\Http\Requests\Transport\DownloadFileZipRequest;
use App\Http\Requests\Transport\DownloadTransFileRefRequest;
use App\Http\Requests\Transport\ExportCsvTransportRequest;
use App\Http\Requests\Transport\GetAaToTransConfigRequest;
use App\Http\Requests\Transport\GetListTransportRequest;
use App\Http\Requests\Transport\ShippingReportMonthRequest;
use App\Http\Requests\Transport\StoreTransportRequest;
use App\Http\Requests\Transport\UpdateInfoDetailTransportRequest;
use App\Http\Requests\Transport\UpdateStatusTransportRequest;
use App\Http\Resources\Transport\TransportDetailResource;
use App\Http\Resources\Transport\TransportEditDetailResource;
use App\Http\Resources\Transport\TransportResource;
use App\Services\Transport\DelTransFileRefService;
use App\Services\Transport\DownloadTransFileRefService;
use App\Services\Transport\ExportCsvFileTransportService;
use App\Services\Transport\ExportCsvTokenService;
use App\Services\Transport\GetListTransportService;
use App\Services\Transport\StoreTransportService;
use App\Services\Transport\SyncRefNoTransportService;
use App\Services\Transport\TransportAgentConfigService;
use App\Services\Transport\TransportService;
use App\Services\VehicleTransport\VehicleTransportService;
use Exception;
use Illuminate\Support\Facades\Auth;
use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Response;

class TransportController extends Controller
{
    private GetListTransportService $getListTransportService;

    private SyncRefNoTransportService $syncRefNoTransportService;

    private TransportService $transportService;

    private ExportCsvFileTransportService $exportCsvFileTransportService;

    private ExportCsvTokenService $exportCsvTokenService;

    private TransportAgentConfigService $transportAgentConfigService;

    private StoreTransportService $storeTransportService;

    private DownloadTransFileRefService $downloadTransFileRefService;

    private DelTransFileRefService $delTransFileRefService;

    private VehicleTransportService $vehicleTransportService;

    public function __construct(
        GetListTransportService $getListTransportService,
        SyncRefNoTransportService $syncRefNoTransportService,
        TransportService $transportService,
        ExportCsvFileTransportService $exportCsvFileTransportService,
        ExportCsvTokenService $exportCsvTokenService,
        TransportAgentConfigService $transportAgentConfigService,
        StoreTransportService $storeTransportService,
        DownloadTransFileRefService $downloadTransFileRefService,
        DelTransFileRefService $delTransFileRefService,
        VehicleTransportService $vehicleTransportService
    ) {
        $this->getListTransportService = $getListTransportService;
        $this->syncRefNoTransportService = $syncRefNoTransportService;
        $this->transportService = $transportService;
        $this->exportCsvFileTransportService = $exportCsvFileTransportService;
        $this->exportCsvTokenService = $exportCsvTokenService;
        $this->transportAgentConfigService = $transportAgentConfigService;
        $this->storeTransportService = $storeTransportService;
        $this->downloadTransFileRefService = $downloadTransFileRefService;
        $this->delTransFileRefService = $delTransFileRefService;
        $this->vehicleTransportService = $vehicleTransportService;
    }

    #[OA\Get(
        path: '/api/admin/transport',
        description: 'Retrieve a paginated list of transport records with comprehensive filtering and sorting options. This endpoint supports multiple filter parameters for detailed search functionality.',
        summary: 'Get transport records list',
        security: [['access_token' => []]],
        tags: ['Transport Management'],
        parameters: [
            new OA\Parameter(
                name: 'limit',
                description: 'Number of items per page (default: 20)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'integer', minimum: 1, maximum: 100, example: 20)
            ),
            new OA\Parameter(
                name: 'page',
                description: 'Page number for pagination',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'integer', minimum: 1, example: 1)
            ),
            new OA\Parameter(
                name: 'transport_ids',
                description: 'Filter by specific transport IDs (space-separated)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '1 2 3')
            ),
            new OA\Parameter(
                name: 'tp_ids',
                description: 'Filter by transport point IDs (space-separated)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '100 101 102')
            ),
            new OA\Parameter(
                name: 'aa_place_id',
                description: 'Filter by auction place ID',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '1')
            ),
            new OA\Parameter(
                name: 'to_place_id',
                description: 'Filter by destination place ID',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '2')
            ),
            new OA\Parameter(
                name: 'date_mode',
                description: 'Date filter mode (1: 受注日 Order Date, 2: 搬入予定 Planned Delivery, 3: 搬入実績 Actual Delivery, 4: 搬出予定 Planned Pickup)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'integer', enum: [1, 2, 3, 4], example: 1)
            ),
            new OA\Parameter(
                name: 'ref_nos',
                description: 'Filter by reference numbers (space-separated)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'REF001 REF002')
            ),
            new OA\Parameter(
                name: 'fr_name',
                description: 'Filter by from location name',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '東京港')
            ),
            new OA\Parameter(
                name: 'to_name',
                description: 'Filter by to location name',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '大阪港')
            ),
            new OA\Parameter(
                name: 'customer_name',
                description: 'Search by customer name (partial match)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '田中太郎')
            ),
            new OA\Parameter(
                name: 'car_name',
                description: 'Search by car/vehicle name (partial match)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'Toyota Camry')
            ),
            new OA\Parameter(
                name: 'car_nos',
                description: 'Filter by car numbers (space-separated)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'ABC123 DEF456')
            ),
            new OA\Parameter(
                name: 'aa_nos',
                description: 'Filter by auction numbers (space-separated)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'AU001 AU002')
            ),
            new OA\Parameter(
                name: 'st_cd',
                description: 'Filter by status code',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '1')
            ),
            new OA\Parameter(
                name: 'plate_cut',
                description: 'Filter by plate cut flag',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '1')
            ),
            new OA\Parameter(
                name: 'm_trans_id',
                description: 'Filter by transport company ID',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '1')
            ),
            new OA\Parameter(
                name: 'customer_id',
                description: 'Filter by customer ID',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '123')
            ),
            new OA\Parameter(
                name: 'charge_sale_customer',
                description: 'Filter by charge sale customer',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '1')
            ),
            new OA\Parameter(
                name: 'start_date',
                description: 'Start date for date range filtering (Y-m-d format)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', format: 'date', example: '2024-01-01')
            ),
            new OA\Parameter(
                name: 'end_date',
                description: 'End date for date range filtering (Y-m-d format)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', format: 'date', example: '2024-12-31')
            ),
            new OA\Parameter(
                name: 'order_option',
                description: 'Sort field option (1: 顧客名 Customer Name, 2: POS番号 POS Number, 3: AA番号 Auction Number, 4: 車両番号 Car Number, 5: 受注日 Order Date, 6: 搬入予定 Planned Delivery, 7: 搬入実績 Actual Delivery, 8: 搬出予定 Planned Pickup)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', enum: ['1', '2', '3', '4', '5', '6', '7', '8'], example: '5')
            ),
            new OA\Parameter(
                name: 'sort_type',
                description: 'Sort direction (asc: ascending, desc: descending)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', enum: ['asc', 'desc'], example: 'desc')
            ),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful operation',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(
                            property: 'success',
                            type: 'boolean',
                            example: true
                        ),
                        new OA\Property(
                            property: 'data',
                            properties: [
                                new OA\Property(
                                    property: 'data',
                                    type: 'array',
                                    items: new OA\Items(
                                        properties: [
                                            new OA\Property(property: 'id', description: 'Transport record ID', type: 'integer', example: 1),
                                            new OA\Property(property: 'tp_id', description: 'Transport point ID (null for LOGICO and DREAM_INTERNATIONAL)', type: 'integer', example: 123, nullable: true),
                                            new OA\Property(property: 'transport_id', description: 'Transport ID (same as id)', type: 'integer', example: 1),
                                            new OA\Property(property: 'st_cd', description: 'Status code', type: 'string', example: '1'),
                                            new OA\Property(property: 'common_value', description: 'Status description from common inspection table', type: 'string', example: '配送中', nullable: true),
                                            new OA\Property(property: 'customer_name_jp', description: 'Customer name in Japanese', type: 'string', example: '田中太郎'),
                                            new OA\Property(property: 'customer_id', description: 'Customer ID', type: 'integer', example: 123),
                                            new OA\Property(property: 'pos_no', description: 'POS number', type: 'string', example: 'POS001'),
                                            new OA\Property(property: 'aa_no', description: 'Auction number', type: 'string', example: 'AA001'),
                                            new OA\Property(property: 'fr_name', description: 'From location name', type: 'string', example: '東京港'),
                                            new OA\Property(property: 'car_name', description: 'Vehicle name', type: 'string', example: 'Toyota Camry'),
                                            new OA\Property(property: 'car_no', description: 'Vehicle number', type: 'string', example: 'ABC123'),
                                            new OA\Property(property: 'deli_price', description: 'Delivery price (raw value)', type: 'integer', example: 50000, nullable: true),
                                            new OA\Property(property: 'deli_price_format', description: 'Formatted delivery price with currency', type: 'string', example: '50,000円'),
                                            new OA\Property(property: 'sales_price', description: 'Sales price (raw value)', type: 'integer', example: 45000, nullable: true),
                                            new OA\Property(property: 'sales_price_format', description: 'Formatted sales price with currency', type: 'string', example: '45,000円'),
                                            new OA\Property(property: 'm_trans_name', description: 'Transport company name', type: 'string', example: 'ロジコ'),
                                            new OA\Property(property: 'm_trans_id', description: 'Transport company ID', type: 'integer', example: 1),
                                            new OA\Property(property: 'd2d_kbn', description: 'Door-to-door service flag (YES if d2d_kbn=2, empty otherwise)', type: 'string', example: 'YES'),
                                            new OA\Property(property: 'plate_cut_flg', description: 'Plate cutting flag (有 if plate_cut_flg in [1,3], empty otherwise)', type: 'string', example: '有'),
                                            new OA\Property(property: 'plate_cut_fin', description: 'Plate cutting completion status', type: 'string', example: '完了', nullable: true),
                                            new OA\Property(property: 'plate_send_date', description: 'Plate sending date', type: 'string', format: 'date', example: '2024-01-15', nullable: true),
                                            new OA\Property(property: 'name1', description: 'Note author 1', type: 'string', example: '備考者1', nullable: true),
                                            new OA\Property(property: 'name2', description: 'Note author 2', type: 'string', example: '備考者2', nullable: true),
                                            new OA\Property(property: 'name3', description: 'Note author 3', type: 'string', example: '備考者3', nullable: true),
                                            new OA\Property(property: 'note1', description: 'Note content 1', type: 'string', example: '配送前確認', nullable: true),
                                            new OA\Property(property: 'note2', description: 'Note content 2', type: 'string', example: '車両点検完了', nullable: true),
                                            new OA\Property(property: 'note3', description: 'Note content 3', type: 'string', example: '配送準備OK', nullable: true),
                                            new OA\Property(property: 'note_date1', description: 'Note date 1', type: 'string', format: 'date', example: '2024-01-10', nullable: true),
                                            new OA\Property(property: 'note_date2', description: 'Note date 2', type: 'string', format: 'date', example: '2024-01-11', nullable: true),
                                            new OA\Property(property: 'note_date3', description: 'Note date 3', type: 'string', format: 'date', example: '2024-01-12', nullable: true),
                                            new OA\Property(property: 'odr_date', description: 'Order date', type: 'string', format: 'date', example: '2024-01-01'),
                                            new OA\Property(property: 'from_plan_date', description: 'Planned pickup date', type: 'string', format: 'date', example: '2024-01-05', nullable: true),
                                            new OA\Property(property: 'to_plan_date', description: 'Planned delivery date', type: 'string', format: 'date', example: '2024-01-10', nullable: true),
                                            new OA\Property(property: 'to_date', description: 'Actual delivery date', type: 'string', format: 'date', example: '2024-01-10', nullable: true),
                                            new OA\Property(property: 'ref_no', description: 'Reference number', type: 'string', example: 'REF001', nullable: true),
                                            new OA\Property(property: 'country', description: 'Country name', type: 'string', example: 'Japan', nullable: true),
                                            new OA\Property(property: 'country_free', description: 'Country name in Japanese', type: 'string', example: '日本', nullable: true),
                                            new OA\Property(property: 'country_cd', description: 'Country code', type: 'string', example: 'JP', nullable: true),
                                            new OA\Property(property: 'service_name', description: 'Service name', type: 'string', example: '配送サービス', nullable: true),
                                            new OA\Property(property: 'vessel_voy', description: 'Vessel/voyage information', type: 'string', example: 'V001', nullable: true),
                                            new OA\Property(property: 'etd', description: 'Estimated time of departure', type: 'string', format: 'date', example: '2024-01-15', nullable: true),
                                            new OA\Property(property: 'eta', description: 'Estimated time of arrival', type: 'string', format: 'date', example: '2024-01-20', nullable: true),
                                            new OA\Property(property: 'fin_ship', description: 'Ship completion status', type: 'string', example: '完了', nullable: true),
                                            new OA\Property(property: 'to_name', description: 'Destination location name', type: 'string', example: '大阪港', nullable: true)
                                        ]
                                    )
                                ),
                                new OA\Property(
                                    property: 'current_page',
                                    type: 'integer',
                                    example: 1
                                ),
                                new OA\Property(
                                    property: 'last_page',
                                    type: 'integer',
                                    example: 5
                                ),
                                new OA\Property(
                                    property: 'per_page',
                                    type: 'integer',
                                    example: 10
                                ),
                                new OA\Property(
                                    property: 'total',
                                    type: 'integer',
                                    example: 50
                                )
                            ],
                            type: 'object'
                        ),
                        new OA\Property(
                            property: 'message',
                            type: 'string',
                            example: null,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'locale',
                            type: 'string',
                            example: null,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'code',
                            type: 'integer',
                            example: null,
                            nullable: true
                        )
                    ]
                )
            ),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 403, description: 'Forbidden - Insufficient transport admin permissions'),
            new OA\Response(response: 422, description: 'Validation Error - Invalid request parameters'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function getListTransport(GetListTransportRequest $request): Response
    {
        return $this->respond(
            $this->getListTransportService->getListPaginate($request->validated())
        );
    }

    #[OA\Post(
        path: '/api/admin/transport/sync-ref-no',
        description: 'Synchronize reference numbers from AS400 database to the main system. This operation updates transport records with the latest reference number information from the AS400 system.',
        summary: 'Sync transport reference numbers from AS400',
        security: [['access_token' => []]],
        tags: ['Transport Management'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Synchronization completed successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(
                            property: 'success',
                            type: 'boolean',
                            example: true
                        ),
                        new OA\Property(
                            property: 'data',
                            properties: [
                                new OA\Property(
                                    property: 'synced_count',
                                    description: 'Number of transport records synchronized',
                                    type: 'integer',
                                    example: 150
                                ),
                                new OA\Property(
                                    property: 'updated_count',
                                    description: 'Number of records that were actually updated',
                                    type: 'integer',
                                    example: 75
                                ),
                                new OA\Property(
                                    property: 'sync_time',
                                    description: 'Timestamp when synchronization was completed',
                                    type: 'string',
                                    format: 'datetime',
                                    example: '2024-01-15T10:30:00Z'
                                )
                            ],
                            type: 'object'
                        ),
                        new OA\Property(
                            property: 'message',
                            type: 'string',
                            example: 'Reference numbers synchronized successfully',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'locale',
                            type: 'string',
                            example: null,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'code',
                            type: 'integer',
                            example: null,
                            nullable: true
                        )
                    ]
                )
            ),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 403, description: 'Forbidden - Insufficient transport admin permissions'),
            new OA\Response(response: 500, description: 'Internal server error - AS400 connection or synchronization failed')
        ]
    )]
    public function syncRefNoTransport()
    {
        return $this->respond($this->syncRefNoTransportService->syncRefNoFormAS400Database());
    }

    #[OA\Get(
        path: '/api/admin/transport/{id}',
        description: 'Retrieve detailed information for a specific transport record. This endpoint provides comprehensive transport details including delivery information, pricing, plate sending details, and address information.',
        summary: 'Get detailed transport information',
        security: [['access_token' => []]],
        tags: ['Transport Management'],
        parameters: [
            new OA\Parameter(
                name: 'id',
                description: 'Transport ID',
                in: 'path',
                required: true,
                schema: new OA\Schema(type: 'integer', minimum: 1, example: 123)
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful operation',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(
                            property: 'success',
                            type: 'boolean',
                            example: true
                        ),
                        new OA\Property(
                            property: 'data',
                            properties: [
                                new OA\Property(
                                    property: 'id',
                                    type: 'integer',
                                    description: 'Transport record ID',
                                    example: 123
                                ),
                                new OA\Property(
                                    property: 'tp_id',
                                    type: 'string',
                                    description: 'Transport point ID',
                                    example: 'TP001'
                                ),
                                new OA\Property(
                                    property: 'm_trans_id',
                                    type: 'string',
                                    description: 'Master transport ID',
                                    example: 'MT001'
                                ),
                                new OA\Property(
                                    property: 'st_cd',
                                    type: 'string',
                                    description: 'Status code',
                                    example: 'ACTIVE'
                                ),
                                new OA\Property(
                                    property: 'deli_price',
                                    type: 'number',
                                    format: 'float',
                                    description: 'Delivery price',
                                    example: 150000.50
                                ),
                                new OA\Property(
                                    property: 'sales_price',
                                    type: 'number',
                                    format: 'float',
                                    description: 'Sales price',
                                    example: 180000.00
                                ),
                                new OA\Property(
                                    property: 'plate_send_date',
                                    type: 'string',
                                    format: 'date',
                                    description: 'Plate sending date',
                                    example: '2024-01-15'
                                ),
                                new OA\Property(
                                    property: 'plate_send_no',
                                    type: 'string',
                                    description: 'Plate sending number',
                                    example: 'PSN001'
                                ),
                                new OA\Property(
                                    property: 'plate_send_co',
                                    type: 'string',
                                    description: 'Plate sending company',
                                    example: 'ABC Transport Co.'
                                ),
                                new OA\Property(
                                    property: 'to_plan_date',
                                    type: 'string',
                                    format: 'date',
                                    description: 'Planned delivery date',
                                    example: '2024-01-20'
                                ),
                                new OA\Property(
                                    property: 'to_date',
                                    type: 'string',
                                    format: 'date',
                                    description: 'Actual delivery date',
                                    example: '2024-01-20'
                                ),
                                new OA\Property(
                                    property: 'car_no',
                                    type: 'string',
                                    description: 'Vehicle number',
                                    example: 'ABC-1234'
                                ),
                                new OA\Property(
                                    property: 'from_plan_date',
                                    type: 'string',
                                    format: 'date',
                                    description: 'Planned pickup date',
                                    example: '2024-01-10'
                                ),
                                new OA\Property(
                                    property: 'fr_name',
                                    type: 'string',
                                    description: 'Sender name',
                                    example: 'Yamada Taro'
                                ),
                                new OA\Property(
                                    property: 'fr_addr',
                                    type: 'string',
                                    description: 'Sender address',
                                    example: 'Tokyo, Shibuya 1-1-1'
                                ),
                                new OA\Property(
                                    property: 'fr_tel',
                                    type: 'string',
                                    description: 'Sender telephone',
                                    example: '03-1234-5678'
                                ),
                                new OA\Property(
                                    property: 'to_name',
                                    type: 'string',
                                    description: 'Recipient name',
                                    example: 'Suzuki Hanako'
                                ),
                                new OA\Property(
                                    property: 'to_addr',
                                    type: 'string',
                                    description: 'Recipient address',
                                    example: 'Osaka, Namba 2-2-2'
                                ),
                                new OA\Property(
                                    property: 'to_tel',
                                    type: 'string',
                                    description: 'Recipient telephone',
                                    example: '06-1234-5678'
                                )
                            ],
                            type: 'object'
                        )
                    ]
                )
            ),
            new OA\Response(response: 401, description: 'Unauthorized - Invalid or missing access token'),
            new OA\Response(response: 403, description: 'Forbidden - Insufficient permissions'),
            new OA\Response(response: 404, description: 'Not Found - Transport record not found'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function getInfoDetailTransport(int $id)
    {
        $data = $this->transportService->getInfoDetailTransport($id);
        return $this->respond(TransportDetailResource::make($data));
    }

    #[OA\Put(
        path: '/api/admin/transport/{id}',
        description: 'Update detailed information for a specific transport record. This endpoint allows updating delivery dates, plate sending information, and pricing data. Admin users have full access to all fields including sales price, while transport company users can only update delivery price.',
        summary: 'Update detailed transport information',
        security: [['access_token' => []]],
        tags: ['Transport Management'],
        parameters: [
            new OA\Parameter(
                name: 'id',
                description: 'Transport record ID (for admin users) or Transport Point ID (for regular users)',
                in: 'path',
                required: true,
                schema: new OA\Schema(type: 'integer', minimum: 1, example: 123)
            )
        ],
        requestBody: new OA\RequestBody(
            required: true,
            description: 'Transport detail information to update',
            content: new OA\MediaType(
                mediaType: 'application/json',
                schema: new OA\Schema(
                    properties: [
                        new OA\Property(
                            property: 'to_plan_date',
                            description: 'Planned delivery date (搬入予定日)',
                            type: 'string',
                            format: 'date',
                            example: '2024-01-15',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'to_date',
                            description: 'Actual delivery date (搬入日)',
                            type: 'string',
                            format: 'date',
                            example: '2024-01-16',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'from_plan_date',
                            description: 'Planned pickup date (搬出予定日)',
                            type: 'string',
                            format: 'date',
                            example: '2024-01-10',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'plate_send_date',
                            description: 'Plate sending date (プレート発送日)',
                            type: 'string',
                            format: 'date',
                            example: '2024-01-12',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'plate_send_no',
                            description: 'Plate sending number (プレート発送番号)',
                            type: 'string',
                            maxLength: 255,
                            example: 'PLATE001',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'plate_send_co',
                            description: 'Plate sending company (プレート発送会社)',
                            type: 'string',
                            maxLength: 255,
                            example: '佐川急便',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'deli_price',
                            description: 'Delivery price in yen (陸送代) - Available to all users',
                            type: 'number',
                            minimum: 0,
                            example: 50000,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'sales_price',
                            description: 'Sales price in yen (陸送代販売) - Only available to admin users (hn_cd=1)',
                            type: 'number',
                            minimum: 0,
                            example: 45000,
                            nullable: true
                        )
                    ]
                )
            )
        ),
        responses: [
            new OA\Response(
                response: 200,
                description: 'Transport information updated successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(
                            property: 'success',
                            type: 'boolean',
                            example: true
                        ),
                        new OA\Property(
                            property: 'data',
                            properties: [
                                new OA\Property(
                                    property: 'transport_id',
                                    description: 'Transport ID (id for admin users, tp_id for regular users)',
                                    type: 'integer',
                                    example: 123
                                ),
                                new OA\Property(
                                    property: 'sub_title',
                                    description: 'Location type label',
                                    type: 'string',
                                    example: '出荷元'
                                ),
                                new OA\Property(
                                    property: 'place_name',
                                    description: 'Location name',
                                    type: 'string',
                                    example: '東京港第一ターミナル',
                                    nullable: true
                                ),
                                new OA\Property(
                                    property: 'place_addr',
                                    description: 'Location address',
                                    type: 'string',
                                    example: '東京都港区海岸1-16-1',
                                    nullable: true
                                ),
                                new OA\Property(
                                    property: 'place_tel',
                                    description: 'Location phone number',
                                    type: 'string',
                                    example: '03-1234-5678',
                                    nullable: true
                                ),
                                new OA\Property(
                                    property: 'tp_id',
                                    description: 'Transport Point ID',
                                    type: 'integer',
                                    example: 456,
                                    nullable: true
                                )
                            ],
                            type: 'object'
                        ),
                        new OA\Property(
                            property: 'message',
                            type: 'string',
                            example: '陸送情報が正常に更新されました',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'locale',
                            type: 'string',
                            example: null,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'code',
                            type: 'integer',
                            example: null,
                            nullable: true
                        )
                    ]
                )
            ),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 403, description: 'Forbidden - Insufficient transport admin permissions'),
            new OA\Response(response: 404, description: 'Transport record not found'),
            new OA\Response(
                response: 422,
                description: 'Validation Error - Invalid request data',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(
                            property: 'message',
                            type: 'string',
                            example: 'The given data was invalid.'
                        ),
                        new OA\Property(
                            property: 'errors',
                            properties: [
                                new OA\Property(
                                    property: 'deli_price',
                                    type: 'array',
                                    items: new OA\Items(type: 'string'),
                                    example: ['陸送代は半角数字で入力してください']
                                ),
                                new OA\Property(
                                    property: 'to_plan_date',
                                    type: 'array',
                                    items: new OA\Items(type: 'string'),
                                    example: ['搬入予定日の形式が正しくありません']
                                )
                            ],
                            type: 'object'
                        )
                    ]
                )
            ),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function updateInfoDetailTransport(int $id, UpdateInfoDetailTransportRequest $request)
    {
        $data = $this->transportService->updateInfoDetailTransport($id, $request->validated());
        return $this->respond(TransportDetailResource::make($data));
    }

    #[OA\Post(
        path: '/api/admin/transport/update-status',
        description: 'Update status for multiple transport records. This endpoint allows bulk status updates for transport records. Admin users can update by transport ID, while regular users can only update by transport point ID.',
        summary: 'Update transport status',
        security: [['access_token' => []]],
        tags: ['Transport Management'],
        requestBody: new OA\RequestBody(
            required: true,
            description: 'Transport status update information',
            content: new OA\MediaType(
                mediaType: 'application/json',
                schema: new OA\Schema(
                    required: ['status', 'id_list'],
                    properties: [
                        new OA\Property(
                            property: 'status',
                            description: 'New status code for transport records',
                            type: 'string',
                            example: '1',
                            maxLength: 10
                        ),
                        new OA\Property(
                            property: 'id_list',
                            description: 'Array of transport IDs (for admin users) or transport point IDs (for regular users) to update',
                            type: 'array',
                            items: new OA\Items(type: 'integer'),
                            example: [123, 456, 789],
                            minItems: 1
                        )
                    ]
                )
            )
        ),
        responses: [
            new OA\Response(
                response: 200,
                description: 'Transport status updated successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(
                            property: 'success',
                            type: 'boolean',
                            example: true
                        ),
                        new OA\Property(
                            property: 'data',
                            properties: [
                                new OA\Property(
                                    property: 'status',
                                    description: 'Operation status',
                                    type: 'boolean',
                                    example: true
                                ),
                                new OA\Property(
                                    property: 'message',
                                    description: 'Success message',
                                    type: 'string',
                                    example: 'Transport status updated successfully',
                                    nullable: true
                                )
                            ],
                            type: 'object'
                        ),
                        new OA\Property(
                            property: 'message',
                            type: 'string',
                            example: null,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'locale',
                            type: 'string',
                            example: null,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'code',
                            type: 'integer',
                            example: null,
                            nullable: true
                        )
                    ]
                )
            ),
            new OA\Response(response: 401, description: 'Unauthorized - Invalid or missing access token'),
            new OA\Response(response: 403, description: 'Forbidden - Insufficient transport admin permissions'),
            new OA\Response(
                response: 422,
                description: 'Validation Error - Invalid request data',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(
                            property: 'message',
                            type: 'string',
                            example: 'The given data was invalid.'
                        ),
                        new OA\Property(
                            property: 'errors',
                            properties: [
                                new OA\Property(
                                    property: 'status',
                                    type: 'array',
                                    items: new OA\Items(type: 'string'),
                                    example: ['Status is required']
                                ),
                                new OA\Property(
                                    property: 'id_list',
                                    type: 'array',
                                    items: new OA\Items(type: 'string'),
                                    example: ['ID list must be an array', 'Selected transport records are invalid']
                                )
                            ],
                            type: 'object'
                        )
                    ]
                )
            ),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function updateStatusTransport(UpdateStatusTransportRequest $request)
    {
        $data = $this->transportService->updateStatusTransport($request->validated());
        return $this->respond($data);
    }

    #[OA\Post(
        path: '/api/admin/transport/export-csv/generate-token',
        summary: 'Generate CSV Export Token',
        description: 'Generate a one-time use token for CSV export. This token is required to access the CSV export endpoint.',
        security: [['access_token' => []]],
        tags: ['Transport Management'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Token generated successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'message', type: 'string', example: 'Success'),
                        new OA\Property(
                            property: 'data',
                            properties: [
                                new OA\Property(
                                    property: 'token',
                                    type: 'string',
                                    description: 'UUID token for CSV export (one-time use)',
                                    example: '550e8400-e29b-41d4-a716-************'
                                )
                            ],
                            type: 'object'
                        )
                    ]
                )
            ),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function generateCsvToken()
    {
        return $this->respond([
            'token' => $this->exportCsvTokenService->generateCsvToken()
        ]);
    }

    #[OA\Get(
        path: '/api/admin/transport/check-download-csv',
        summary: 'Check Download CSV Transport',
        description: 'Check before downloading CSV to confirm valid data. Check for duplicate REF numbers.',
        security: [['BearerAuth' => []]],
        tags: ['Transport Management'],
        parameters: [
            new OA\Parameter(
                name: 'transport_ids',
                description: 'Comma-separated list of transport IDs',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'tp_ids',
                description: 'Comma-separated list of TP IDs',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'aa_place_id',
                description: 'AA Place ID',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'to_place_id',
                description: 'To Place ID',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'date_mode',
                description: 'Date mode (1: date range, 2: by month)',
                in: 'query',
                schema: new OA\Schema(type: 'string', enum: ['1', '2'])
            ),
            new OA\Parameter(
                name: 'ref_nos',
                description: 'Comma-separated list of REF numbers',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'fr_name',
                description: 'From location name',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'to_name',
                description: 'To location name',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'customer_name',
                description: 'Customer name',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'car_name',
                description: 'Car name',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'car_nos',
                description: 'Comma-separated list of car numbers',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'aa_nos',
                description: 'Comma-separated list of AA numbers',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'st_cd',
                description: 'Status code',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'plate_cut',
                description: 'Plate cut',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'm_trans_id',
                description: 'Master transport ID',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'customer_id',
                description: 'Customer ID',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'charge_sale_customer',
                description: 'Charge sale customer',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'start_date',
                description: 'Start date (format: Y-m-d)',
                in: 'query',
                schema: new OA\Schema(type: 'string', format: 'date')
            ),
            new OA\Parameter(
                name: 'end_date',
                description: 'End date (format: Y-m-d)',
                in: 'query',
                schema: new OA\Schema(type: 'string', format: 'date')
            ),
            new OA\Parameter(
                name: 'order_option',
                description: 'Sort option',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'sort_type',
                description: 'Sort type (asc/desc)',
                in: 'query',
                schema: new OA\Schema(type: 'string', enum: ['asc', 'desc'])
            ),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Check completed successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'status', type: 'string', enum: ['OK', 'NG'], description: 'Check status'),
                        new OA\Property(property: 'message', type: 'string', description: 'Error message if any'),
                    ]
                )
            ),
            new OA\Response(
                response: 401,
                description: 'Unauthorized'
            ),
            new OA\Response(
                response: 422,
                description: 'Validation Error'
            ),
        ]
    )]
    public function checkDownloadCsv(CheckDownloadCsvTransportRequest $request)
    {
        $data = $this->exportCsvFileTransportService->checkDownloadCsv($request->validated());
        return $this->respond($data);
    }

    #[OA\Get(
        path: '/api/admin/transport/export-csv',
        summary: 'Export CSV Transport Data',
        description: 'Export transport data to CSV file. Requires valid token generated from generate-token endpoint.',
        security: [['BearerAuth' => []]],
        tags: ['Transport Management'],
        parameters: [
            new OA\Parameter(
                name: 'token',
                description: 'Token generated from generate-token endpoint, required for CSV download',
                in: 'query',
                required: true,
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'transport_ids',
                description: 'Comma-separated list of transport IDs to filter',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'tp_ids',
                description: 'Comma-separated list of TP IDs to filter',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'aa_place_id',
                description: 'AA Place ID to filter by location',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'to_place_id',
                description: 'To Place ID to filter by destination',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'date_mode',
                description: 'Date filter mode: 1 (date range), 2 (by month)',
                in: 'query',
                schema: new OA\Schema(type: 'string', enum: ['1', '2'])
            ),
            new OA\Parameter(
                name: 'ref_nos',
                description: 'Comma-separated list of REF numbers to filter',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'fr_name',
                description: 'From location name to filter',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'to_name',
                description: 'To location name to filter',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'customer_name',
                description: 'Customer name to filter',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'car_name',
                description: 'Car name to filter',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'car_nos',
                description: 'Comma-separated list of car numbers to filter',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'aa_nos',
                description: 'Comma-separated list of AA numbers to filter',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'st_cd',
                description: 'Status code to filter by status',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'plate_cut',
                description: 'Plate cut to filter',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'm_trans_id',
                description: 'Master transport ID to filter',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'customer_id',
                description: 'Customer ID to filter by customer',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'charge_sale_customer',
                description: 'Charge sale customer to filter',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'start_date',
                description: 'Start date to filter (format: Y-m-d)',
                in: 'query',
                schema: new OA\Schema(type: 'string', format: 'date')
            ),
            new OA\Parameter(
                name: 'end_date',
                description: 'End date to filter (format: Y-m-d)',
                in: 'query',
                schema: new OA\Schema(type: 'string', format: 'date')
            ),
            new OA\Parameter(
                name: 'order_option',
                description: 'Sort option for CSV data',
                in: 'query',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'sort_type',
                description: 'Sort type: asc (ascending) or desc (descending)',
                in: 'query',
                schema: new OA\Schema(type: 'string', enum: ['asc', 'desc'])
            ),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'CSV download successful',
                content: new OA\MediaType(
                    mediaType: 'text/csv',
                    schema: new OA\Schema(
                        type: 'string',
                        format: 'binary'
                    )
                ),
                headers: [
                    new OA\Header(
                        header: 'Content-Disposition',
                        description: 'Attachment filename',
                        schema: new OA\Schema(type: 'string')
                    ),
                    new OA\Header(
                        header: 'Content-Type',
                        description: 'MIME type of the file',
                        schema: new OA\Schema(type: 'string', example: 'text/csv; charset=UTF-8')
                    ),
                ]
            ),
            new OA\Response(
                response: 400,
                description: 'Token is invalid or expired',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'message', type: 'string', example: 'Token is invalid or expired'),
                    ]
                )
            ),
            new OA\Response(
                response: 401,
                description: 'Unauthorized'
            ),
            new OA\Response(
                response: 422,
                description: 'Validation Error'
            ),
        ]
    )]
    public function exportCsv(ExportCsvTransportRequest $request)
    {
        return $this->exportCsvFileTransportService->exportCsv($request->validated());
    }

    #[OA\Post(
        path: '/api/admin/transport',
        description: 'Create a new transport record. This endpoint allows creating transport records with comprehensive information including delivery details, pricing, and customer information.',
        summary: 'Create new transport record',
        security: [['access_token' => []]],
        tags: ['Transport Management'],
        requestBody: new OA\RequestBody(
            required: true,
            description: 'Transport information to create',
            content: new OA\MediaType(
                mediaType: 'application/json',
                schema: new OA\Schema(
                    required: ['odr_customer_id', 'car_no', 'car_name', 'date_of_payment'],
                    properties: [
                        new OA\Property(
                            property: 'odr_customer_id',
                            description: 'Order customer ID (must exist in m_customer table)',
                            type: 'integer',
                            minimum: 1,
                            example: 12345
                        ),
                        new OA\Property(
                            property: 'car_no',
                            description: 'Vehicle number',
                            type: 'string',
                            maxLength: 25,
                            example: 'ABC-1234'
                        ),
                        new OA\Property(
                            property: 'car_name',
                            description: 'Vehicle name/model',
                            type: 'string',
                            maxLength: 50,
                            example: 'Toyota Camry'
                        ),
                        new OA\Property(
                            property: 'plate_no',
                            description: 'License plate number',
                            type: 'string',
                            maxLength: 20,
                            example: '品川123あ',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'note',
                            description: 'General note',
                            type: 'string',
                            maxLength: 2000,
                            example: 'Special handling required',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'fr_place_id',
                            description: 'From place ID',
                            type: 'integer',
                            example: 1,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'to_place_id',
                            description: 'To place ID',
                            type: 'integer',
                            example: 2,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'to_etc_place_id',
                            description: 'To etc place ID',
                            type: 'integer',
                            example: 3,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'date_of_payment',
                            description: 'Date of payment (Y-m-d format)',
                            type: 'string',
                            format: 'date',
                            example: '2024-01-15'
                        ),
                        new OA\Property(
                            property: 'auction_date',
                            description: 'Auction date (Y-m-d format)',
                            type: 'string',
                            format: 'date',
                            example: '2024-01-10',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'pos_no',
                            description: 'POS number (required if fr_aa_place_id is provided)',
                            type: 'string',
                            maxLength: 10,
                            example: 'POS001',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'aa_no',
                            description: 'Auction number (required if fr_aa_place_id is provided)',
                            type: 'string',
                            maxLength: 10,
                            example: 'AA001',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'agent_ref_no',
                            description: 'Agent reference number',
                            type: 'string',
                            maxLength: 15,
                            example: 'REF001',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'undrivable_flg',
                            description: 'Undrivable flag (0: No, 1: Yes)',
                            type: 'integer',
                            enum: [0, 1],
                            example: 0,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'tall_flg',
                            description: 'Tall vehicle flag (0: No, 1: Yes)',
                            type: 'integer',
                            enum: [0, 1],
                            example: 0,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'lowdown_flg',
                            description: 'Lowdown vehicle flag (0: No, 1: Yes)',
                            type: 'integer',
                            enum: [0, 1],
                            example: 0,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'long_flg',
                            description: 'Long vehicle flag (0: No, 1: Yes)',
                            type: 'integer',
                            enum: [0, 1],
                            example: 0,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'old_flg',
                            description: 'Old vehicle flag (0: No, 1: Yes)',
                            type: 'integer',
                            enum: [0, 1],
                            example: 0,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'luxury_flg',
                            description: 'Luxury vehicle flag (0: No, 1: Yes)',
                            type: 'integer',
                            enum: [0, 1],
                            example: 0,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'luxury_flg_insrance',
                            description: 'Luxury vehicle insurance flag (0: No, 1: Yes, 2: Other)',
                            type: 'integer',
                            enum: [0, 1, 2],
                            example: 0,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'other_flg',
                            description: 'Other flag (0: No, 1: Yes)',
                            type: 'integer',
                            enum: [0, 1],
                            example: 0,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'other_flg_txt',
                            description: 'Other flag text',
                            type: 'string',
                            maxLength: 255,
                            example: 'Special requirements',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'tick_no_flg',
                            description: 'Ticket number flag (0: No, 1: Yes)',
                            type: 'integer',
                            enum: [0, 1],
                            example: 0,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'optn1_flg',
                            description: 'Option 1 flag (0: No, 1: Yes)',
                            type: 'integer',
                            enum: [0, 1],
                            example: 0,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'plate_cut_flg',
                            description: 'Plate cut flag (0: No, 1: Yes, 2: Other)',
                            type: 'integer',
                            enum: [0, 1, 2],
                            example: 0,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'pos_chk',
                            description: 'POS check',
                            type: 'string',
                            example: 'checked',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'pos_chk_text',
                            description: 'POS check text',
                            type: 'string',
                            maxLength: 255,
                            example: 'POS verification completed',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'date_of_payment_time',
                            description: 'Date of payment time',
                            enum: [1, 2, 3],
                            example: 1,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'auction_chk',
                            description: 'Auction check (0: No, 1: Yes)',
                            type: 'integer',
                            enum: [0, 1],
                            example: 0,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'auction_txt',
                            description: 'Auction text',
                            type: 'string',
                            maxLength: 255,
                            example: 'Auction details',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'country_cd',
                            description: 'Country code',
                            type: 'string',
                            example: 'JP',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'country_area',
                            description: 'Country area',
                            type: 'string',
                            example: 'Asia',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'country_free',
                            description: 'Country free text',
                            type: 'string',
                            example: '日本',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'port_cd',
                            description: 'Port code',
                            type: 'string',
                            maxLength: 10,
                            example: 'TYO',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'plate_send_name',
                            description: 'Plate send name',
                            type: 'string',
                            maxLength: 50,
                            example: 'Yamada Taro',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'plate_send_zipcd',
                            description: 'Plate send zip code',
                            type: 'string',
                            maxLength: 10,
                            example: '100-0001',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'plate_send_address',
                            description: 'Plate send address',
                            type: 'string',
                            maxLength: 255,
                            example: 'Tokyo, Shibuya 1-1-1',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'plate_send_tel',
                            description: 'Plate send telephone',
                            type: 'string',
                            maxLength: 20,
                            example: '03-1234-5678',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'fr_aa_place_id',
                            description: 'From AA place ID (if provided, fr_name, fr_addr, fr_tel1-3 become optional)',
                            type: 'integer',
                            example: 1,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'fr_name',
                            description: 'From name (required if fr_aa_place_id is not provided)',
                            type: 'string',
                            maxLength: 50,
                            example: 'Yamada Taro',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'fr_addr',
                            description: 'From address (required if fr_aa_place_id is not provided)',
                            type: 'string',
                            maxLength: 50,
                            example: 'Tokyo, Shibuya 1-1-1',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'fr_tel1',
                            description: 'From telephone part 1 (required if fr_aa_place_id is not provided)',
                            type: 'string',
                            example: '03',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'fr_tel2',
                            description: 'From telephone part 2 (required if fr_aa_place_id is not provided)',
                            type: 'string',
                            example: '1234',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'fr_tel3',
                            description: 'From telephone part 3 (required if fr_aa_place_id is not provided)',
                            type: 'string',
                            example: '5678',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'to_aa_place_id',
                            description: 'To AA place ID (if provided, to_name, to_addr, to_tel1-3 become optional)',
                            type: 'integer',
                            example: 2,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'to_name',
                            description: 'To name (required if to_aa_place_id is not provided)',
                            type: 'string',
                            maxLength: 50,
                            example: 'Suzuki Hanako',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'to_addr',
                            description: 'To address (required if to_aa_place_id is not provided)',
                            type: 'string',
                            maxLength: 50,
                            example: 'Osaka, Namba 2-2-2',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'to_tel1',
                            description: 'To telephone part 1 (required if to_aa_place_id is not provided)',
                            type: 'string',
                            example: '06',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'to_tel2',
                            description: 'To telephone part 2 (required if to_aa_place_id is not provided)',
                            type: 'string',
                            example: '1234',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'to_tel3',
                            description: 'To telephone part 3 (required if to_aa_place_id is not provided)',
                            type: 'string',
                            example: '5678',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'm_trans_id',
                            description: 'Master transport ID (required for admin users, cannot be 999)',
                            type: 'integer',
                            example: 1,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'inp_ah_name',
                            description: 'Input AH name (required for admin users)',
                            type: 'string',
                            maxLength: 50,
                            example: 'Admin User',
                            nullable: true
                        )
                    ]
                )
            )
        ),
        responses: [
            new OA\Response(
                response: 200,
                description: 'Transport record created successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(
                            property: 'success',
                            type: 'boolean',
                            example: true
                        ),
                        new OA\Property(
                            property: 'data',
                            properties: [
                                new OA\Property(property: 'id', description: 'Transport record ID', type: 'integer', example: 1),
                                new OA\Property(property: 'tp_id', description: 'Transport point ID (null for LOGICO and DREAM_INTERNATIONAL)', type: 'integer', example: 123, nullable: true),
                                new OA\Property(property: 'transport_id', description: 'Transport ID (same as id)', type: 'integer', example: 1),
                                new OA\Property(property: 'st_cd', description: 'Status code', type: 'string', example: '1'),
                                new OA\Property(property: 'common_value', description: 'Status description from common inspection table', type: 'string', example: '配送中', nullable: true),
                                new OA\Property(property: 'customer_name_jp', description: 'Customer name in Japanese', type: 'string', example: '田中太郎'),
                                new OA\Property(property: 'customer_id', description: 'Customer ID', type: 'integer', example: 123),
                                new OA\Property(property: 'pos_no', description: 'POS number', type: 'string', example: 'POS001'),
                                new OA\Property(property: 'aa_no', description: 'Auction number', type: 'string', example: 'AA001'),
                                new OA\Property(property: 'fr_name', description: 'From location name', type: 'string', example: '東京港'),
                                new OA\Property(property: 'car_name', description: 'Vehicle name', type: 'string', example: 'Toyota Camry'),
                                new OA\Property(property: 'car_no', description: 'Vehicle number', type: 'string', example: 'ABC123'),
                                new OA\Property(property: 'deli_price', description: 'Delivery price (raw value)', type: 'integer', example: 50000, nullable: true),
                                new OA\Property(property: 'deli_price_format', description: 'Formatted delivery price with currency', type: 'string', example: '50,000円'),
                                new OA\Property(property: 'sales_price', description: 'Sales price (raw value)', type: 'integer', example: 45000, nullable: true),
                                new OA\Property(property: 'sales_price_format', description: 'Formatted sales price with currency', type: 'string', example: '45,000円'),
                                new OA\Property(property: 'm_trans_name', description: 'Transport company name', type: 'string', example: 'ロジコ'),
                                new OA\Property(property: 'm_trans_id', description: 'Transport company ID', type: 'integer', example: 1),
                                new OA\Property(property: 'd2d_kbn', description: 'Door-to-door service flag (YES if d2d_kbn=2, empty otherwise)', type: 'string', example: 'YES'),
                                new OA\Property(property: 'plate_cut_flg', description: 'Plate cutting flag (有 if plate_cut_flg in [1,3], empty otherwise)', type: 'string', example: '有'),
                                new OA\Property(property: 'plate_cut_fin', description: 'Plate cutting completion status', type: 'string', example: '完了', nullable: true),
                                new OA\Property(property: 'plate_send_date', description: 'Plate sending date', type: 'string', format: 'date', example: '2024-01-15', nullable: true),
                                new OA\Property(property: 'name1', description: 'Note author 1', type: 'string', example: '備考者1', nullable: true),
                                new OA\Property(property: 'name2', description: 'Note author 2', type: 'string', example: '備考者2', nullable: true),
                                new OA\Property(property: 'name3', description: 'Note author 3', type: 'string', example: '備考者3', nullable: true),
                                new OA\Property(property: 'note1', description: 'Note content 1', type: 'string', example: '配送前確認', nullable: true),
                                new OA\Property(property: 'note2', description: 'Note content 2', type: 'string', example: '車両点検完了', nullable: true),
                                new OA\Property(property: 'note3', description: 'Note content 3', type: 'string', example: '配送準備OK', nullable: true),
                                new OA\Property(property: 'note_date1', description: 'Note date 1', type: 'string', format: 'date', example: '2024-01-10', nullable: true),
                                new OA\Property(property: 'note_date2', description: 'Note date 2', type: 'string', format: 'date', example: '2024-01-11', nullable: true),
                                new OA\Property(property: 'note_date3', description: 'Note date 3', type: 'string', format: 'date', example: '2024-01-12', nullable: true),
                                new OA\Property(property: 'odr_date', description: 'Order date', type: 'string', format: 'date', example: '2024-01-01'),
                                new OA\Property(property: 'from_plan_date', description: 'Planned pickup date', type: 'string', format: 'date', example: '2024-01-05', nullable: true),
                                new OA\Property(property: 'to_plan_date', description: 'Planned delivery date', type: 'string', format: 'date', example: '2024-01-10', nullable: true),
                                new OA\Property(property: 'to_date', description: 'Actual delivery date', type: 'string', format: 'date', example: '2024-01-10', nullable: true),
                                new OA\Property(property: 'ref_no', description: 'Reference number', type: 'string', example: 'REF001', nullable: true),
                                new OA\Property(property: 'country', description: 'Country name', type: 'string', example: 'Japan', nullable: true),
                                new OA\Property(property: 'country_free', description: 'Country name in Japanese', type: 'string', example: '日本', nullable: true),
                                new OA\Property(property: 'country_cd', description: 'Country code', type: 'string', example: 'JP', nullable: true),
                                new OA\Property(property: 'service_name', description: 'Service name', type: 'string', example: '配送サービス', nullable: true),
                                new OA\Property(property: 'vessel_voy', description: 'Vessel/voyage information', type: 'string', example: 'V001', nullable: true),
                                new OA\Property(property: 'etd', description: 'Estimated time of departure', type: 'string', format: 'date', example: '2024-01-15', nullable: true),
                                new OA\Property(property: 'eta', description: 'Estimated time of arrival', type: 'string', format: 'date', example: '2024-01-20', nullable: true),
                                new OA\Property(property: 'fin_ship', description: 'Ship completion status', type: 'string', example: '完了', nullable: true),
                                new OA\Property(property: 'to_name', description: 'Destination location name', type: 'string', example: '大阪港', nullable: true)
                            ],
                            type: 'object'
                        ),
                        new OA\Property(
                            property: 'message',
                            type: 'string',
                            example: null,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'locale',
                            type: 'string',
                            example: null,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'code',
                            type: 'integer',
                            example: null,
                            nullable: true
                        )
                    ]
                )
            ),
            new OA\Response(response: 400, description: 'Bad Request - Invalid data or business logic error'),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 403, description: 'Forbidden - Insufficient transport admin permissions'),
            new OA\Response(
                response: 422,
                description: 'Validation Error - Invalid request data',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(
                            property: 'message',
                            type: 'string',
                            example: 'The given data was invalid.'
                        ),
                        new OA\Property(
                            property: 'errors',
                            properties: [
                                new OA\Property(
                                    property: 'odr_customer_id',
                                    type: 'array',
                                    items: new OA\Items(type: 'string'),
                                    example: ['Order customer ID is required']
                                ),
                                new OA\Property(
                                    property: 'car_no',
                                    type: 'array',
                                    items: new OA\Items(type: 'string'),
                                    example: ['Vehicle number is required']
                                ),
                                new OA\Property(
                                    property: 'date_of_payment',
                                    type: 'array',
                                    items: new OA\Items(type: 'string'),
                                    example: ['Date of payment is required']
                                )
                            ],
                            type: 'object'
                        )
                    ]
                )
            ),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function store(StoreTransportRequest $request): Response
    {
        try {
            $result = $this->storeTransportService->store($request->all());
            return $this->respond(new TransportResource($result));
        } catch (Exception $e) {
            return $this->respond($e->getMessage(), Response::HTTP_BAD_REQUEST);
        }
    }

    #[OA\Post(
        path: '/api/admin/transport/confirm-create',
        description: 'Confirm transport record creation without actually creating the record. This endpoint validates the transport data and returns success if the data is valid for creation.',
        summary: 'Confirm transport record creation',
        security: [['access_token' => []]],
        tags: ['Transport Management'],
        requestBody: new OA\RequestBody(
            required: true,
            description: 'Transport information to validate',
            content: new OA\MediaType(
                mediaType: 'application/json',
                schema: new OA\Schema(
                    required: ['odr_customer_id', 'car_no', 'car_name', 'date_of_payment'],
                    properties: [
                        new OA\Property(
                            property: 'odr_customer_id',
                            description: 'Order customer ID (must exist in m_customer table)',
                            type: 'integer',
                            minimum: 1,
                            example: 12345
                        ),
                        new OA\Property(
                            property: 'car_no',
                            description: 'Vehicle number',
                            type: 'string',
                            maxLength: 25,
                            example: 'ABC-1234'
                        ),
                        new OA\Property(
                            property: 'car_name',
                            description: 'Vehicle name/model',
                            type: 'string',
                            maxLength: 50,
                            example: 'Toyota Camry'
                        ),
                        new OA\Property(
                            property: 'plate_no',
                            description: 'License plate number',
                            type: 'string',
                            maxLength: 20,
                            example: '品川123あ',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'note',
                            description: 'General note',
                            type: 'string',
                            maxLength: 2000,
                            example: 'Special handling required',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'fr_place_id',
                            description: 'From place ID',
                            type: 'integer',
                            example: 1,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'to_place_id',
                            description: 'To place ID',
                            type: 'integer',
                            example: 2,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'to_etc_place_id',
                            description: 'To etc place ID',
                            type: 'integer',
                            example: 3,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'date_of_payment',
                            description: 'Date of payment (Y-m-d format)',
                            type: 'string',
                            format: 'date',
                            example: '2024-01-15'
                        ),
                        new OA\Property(
                            property: 'auction_date',
                            description: 'Auction date (Y-m-d format)',
                            type: 'string',
                            format: 'date',
                            example: '2024-01-10',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'pos_no',
                            description: 'POS number (required if fr_aa_place_id is provided)',
                            type: 'string',
                            maxLength: 10,
                            example: 'POS001',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'aa_no',
                            description: 'Auction number (required if fr_aa_place_id is provided)',
                            type: 'string',
                            maxLength: 10,
                            example: 'AA001',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'agent_ref_no',
                            description: 'Agent reference number',
                            type: 'string',
                            maxLength: 15,
                            example: 'REF001',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'undrivable_flg',
                            description: 'Undrivable flag (0: No, 1: Yes)',
                            type: 'integer',
                            enum: [0, 1],
                            example: 0,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'tall_flg',
                            description: 'Tall vehicle flag (0: No, 1: Yes)',
                            type: 'integer',
                            enum: [0, 1],
                            example: 0,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'lowdown_flg',
                            description: 'Lowdown vehicle flag (0: No, 1: Yes)',
                            type: 'integer',
                            enum: [0, 1],
                            example: 0,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'long_flg',
                            description: 'Long vehicle flag (0: No, 1: Yes)',
                            type: 'integer',
                            enum: [0, 1],
                            example: 0,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'old_flg',
                            description: 'Old vehicle flag (0: No, 1: Yes)',
                            type: 'integer',
                            enum: [0, 1],
                            example: 0,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'luxury_flg',
                            description: 'Luxury vehicle flag (0: No, 1: Yes)',
                            type: 'integer',
                            enum: [0, 1],
                            example: 0,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'luxury_flg_insrance',
                            description: 'Luxury vehicle insurance flag (0: No, 1: Yes, 2: Other)',
                            type: 'integer',
                            enum: [0, 1, 2],
                            example: 0,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'other_flg',
                            description: 'Other flag (0: No, 1: Yes)',
                            type: 'integer',
                            enum: [0, 1],
                            example: 0,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'other_flg_txt',
                            description: 'Other flag text',
                            type: 'string',
                            maxLength: 255,
                            example: 'Special requirements',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'tick_no_flg',
                            description: 'Ticket number flag (0: No, 1: Yes)',
                            type: 'integer',
                            enum: [0, 1],
                            example: 0,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'optn1_flg',
                            description: 'Option 1 flag (0: No, 1: Yes)',
                            type: 'integer',
                            enum: [0, 1],
                            example: 0,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'plate_cut_flg',
                            description: 'Plate cut flag (0: No, 1: Yes, 2: Other)',
                            type: 'integer',
                            enum: [0, 1, 2],
                            example: 0,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'pos_chk',
                            description: 'POS check',
                            type: 'string',
                            example: 'checked',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'pos_chk_text',
                            description: 'POS check text',
                            type: 'string',
                            maxLength: 255,
                            example: 'POS verification completed',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'date_of_payment_time',
                            description: 'Date of payment time',
                            enum: [1, 2, 3],
                            example: 1,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'auction_chk',
                            description: 'Auction check (0: No, 1: Yes)',
                            type: 'integer',
                            enum: [0, 1],
                            example: 0,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'auction_txt',
                            description: 'Auction text',
                            type: 'string',
                            maxLength: 255,
                            example: 'Auction details',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'country_cd',
                            description: 'Country code',
                            type: 'string',
                            example: 'JP',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'country_area',
                            description: 'Country area',
                            type: 'string',
                            example: 'Asia',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'country_free',
                            description: 'Country free text',
                            type: 'string',
                            example: '日本',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'port_cd',
                            description: 'Port code',
                            type: 'string',
                            maxLength: 10,
                            example: 'TYO',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'plate_send_name',
                            description: 'Plate send name',
                            type: 'string',
                            maxLength: 50,
                            example: 'Yamada Taro',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'plate_send_zipcd',
                            description: 'Plate send zip code',
                            type: 'string',
                            maxLength: 10,
                            example: '100-0001',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'plate_send_address',
                            description: 'Plate send address',
                            type: 'string',
                            maxLength: 255,
                            example: 'Tokyo, Shibuya 1-1-1',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'plate_send_tel',
                            description: 'Plate send telephone',
                            type: 'string',
                            maxLength: 20,
                            example: '03-1234-5678',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'fr_aa_place_id',
                            description: 'From AA place ID (if provided, fr_name, fr_addr, fr_tel1-3 become optional)',
                            type: 'integer',
                            example: 1,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'fr_name',
                            description: 'From name (required if fr_aa_place_id is not provided)',
                            type: 'string',
                            maxLength: 50,
                            example: 'Yamada Taro',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'fr_addr',
                            description: 'From address (required if fr_aa_place_id is not provided)',
                            type: 'string',
                            maxLength: 50,
                            example: 'Tokyo, Shibuya 1-1-1',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'fr_tel1',
                            description: 'From telephone part 1 (required if fr_aa_place_id is not provided)',
                            type: 'string',
                            example: '03',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'fr_tel2',
                            description: 'From telephone part 2 (required if fr_aa_place_id is not provided)',
                            type: 'string',
                            example: '1234',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'fr_tel3',
                            description: 'From telephone part 3 (required if fr_aa_place_id is not provided)',
                            type: 'string',
                            example: '5678',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'to_aa_place_id',
                            description: 'To AA place ID (if provided, to_name, to_addr, to_tel1-3 become optional)',
                            type: 'integer',
                            example: 2,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'to_name',
                            description: 'To name (required if to_aa_place_id is not provided)',
                            type: 'string',
                            maxLength: 50,
                            example: 'Suzuki Hanako',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'to_addr',
                            description: 'To address (required if to_aa_place_id is not provided)',
                            type: 'string',
                            maxLength: 50,
                            example: 'Osaka, Namba 2-2-2',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'to_tel1',
                            description: 'To telephone part 1 (required if to_aa_place_id is not provided)',
                            type: 'string',
                            example: '06',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'to_tel2',
                            description: 'To telephone part 2 (required if to_aa_place_id is not provided)',
                            type: 'string',
                            example: '1234',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'to_tel3',
                            description: 'To telephone part 3 (required if to_aa_place_id is not provided)',
                            type: 'string',
                            example: '5678',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'm_trans_id',
                            description: 'Master transport ID (required for admin users, cannot be 999)',
                            type: 'integer',
                            example: 1,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'inp_ah_name',
                            description: 'Input AH name (required for admin users)',
                            type: 'string',
                            maxLength: 50,
                            example: 'Admin User',
                            nullable: true
                        )
                    ]
                )
            )
        ),
        responses: [
            new OA\Response(
                response: 200,
                description: 'Transport data validation successful',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(
                            property: 'success',
                            type: 'boolean',
                            example: true
                        ),
                        new OA\Property(
                            property: 'data',
                            type: 'object',
                            example: null,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'message',
                            type: 'string',
                            example: 'Transport data is valid for creation',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'locale',
                            type: 'string',
                            example: null,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'code',
                            type: 'integer',
                            example: null,
                            nullable: true
                        )
                    ]
                )
            ),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 403, description: 'Forbidden - Insufficient transport admin permissions'),
            new OA\Response(
                response: 422,
                description: 'Validation Error - Invalid request data',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(
                            property: 'message',
                            type: 'string',
                            example: 'The given data was invalid.'
                        ),
                        new OA\Property(
                            property: 'errors',
                            properties: [
                                new OA\Property(
                                    property: 'customer_id',
                                    type: 'array',
                                    items: new OA\Items(type: 'string'),
                                    example: ['Customer ID is required']
                                ),
                                new OA\Property(
                                    property: 'car_no',
                                    type: 'array',
                                    items: new OA\Items(type: 'string'),
                                    example: ['Vehicle number is required']
                                )
                            ],
                            type: 'object'
                        )
                    ]
                )
            ),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function confirmCreate(StoreTransportRequest $request): Response
    {
        $validated = $request->validated();
        return $this->respond($this->transportService->confirmCreate($validated));
    }

    #[OA\Get(
        path: '/api/admin/transport/get-aa-to-trans-config',
        summary: 'Get AA to Transport Configuration',
        description: 'Get transport agent configuration for AA place to To place transport. Migrated from ASP function getAaToTransConfig.',
        security: [['access_token' => []]],
        tags: ['Transport Management'],
        parameters: [
            new OA\Parameter(
                name: 'aa_place_id',
                description: 'AA place ID',
                in: 'query',
                required: true,
                schema: new OA\Schema(type: 'integer', minimum: 1, example: 1)
            ),
            new OA\Parameter(
                name: 'to_place_id',
                description: 'To place ID',
                in: 'query',
                required: true,
                schema: new OA\Schema(type: 'integer', minimum: 1, example: 2)
            ),
            new OA\Parameter(
                name: 'customer_id',
                description: 'Customer ID',
                in: 'query',
                required: true,
                schema: new OA\Schema(type: 'integer', minimum: 1, example: 12345)
            ),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful operation',
                content: new OA\JsonContent(
                    properties: [
                        'status' => new OA\Property(property: 'status', type: 'boolean', example: true),
                        'data' => new OA\Property(
                            property: 'data',
                            type: 'object',
                            properties: [
                                'trans_agent_id' => new OA\Property(property: 'trans_agent_id', type: 'string', example: '1005'),
                                'message' => new OA\Property(property: 'message', type: 'string', example: 'Success'),
                                'status' => new OA\Property(property: 'status', type: 'string', example: 'OK'),
                            ]
                        ),
                    ]
                )
            ),
            new OA\Response(response: 401, description: 'Unauthorized'),
            new OA\Response(response: 403, description: 'Forbidden - Insufficient transport admin permissions'),
            new OA\Response(response: 422, description: 'Validation Error'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function getAaToTransConfig(GetAaToTransConfigRequest $request): Response
    {
        $validated = $request->validated();

        $data = $this->transportAgentConfigService->getAaToTransConfig(
            $validated['aa_place_id'],
            $validated['to_place_id'],
            Auth::user()->id
        );

        return $this->respond($data);
    }

    public function downloadTransFileRef(DownloadTransFileRefRequest $request): Response
    {
        $validated = $request->validated();

        return $this->downloadTransFileRefService->downloadTransFileRef(
            $validated['customer_id'],
            $validated['transport_id'],
            $validated['fname']
        );
    }

    public function delTransFileRef(DelTransFileRefRequest $request): Response
    {
        $validated = $request->validated();

        return $this->delTransFileRefService->delTransFileRef(
            $validated['customer_id'],
            $validated['transport_id'],
            $validated['fname']
        );
    }

    #[OA\Post(
        path: '/api/admin/transport/download-file-zip',
        description: 'Create a ZIP file containing transport-related documents for a specific reference number. This endpoint generates a compressed archive with all relevant transport files and documents.',
        summary: 'Create ZIP file for transport documents',
        security: [['access_token' => []]],
        tags: ['Transport Management'],
        requestBody: new OA\RequestBody(
            required: true,
            description: 'Reference number for ZIP file creation',
            content: new OA\MediaType(
                mediaType: 'application/json',
                schema: new OA\Schema(
                    required: ['ref_no'],
                    properties: [
                        new OA\Property(
                            property: 'ref_no',
                            description: 'Reference number to create ZIP file for',
                            type: 'string',
                            maxLength: 50,
                            example: 'REF001234'
                        )
                    ]
                )
            )
        ),
        responses: [
            new OA\Response(
                response: 200,
                description: 'ZIP file created successfully',
                content: new OA\MediaType(
                    mediaType: 'application/zip',
                    schema: new OA\Schema(
                        type: 'string',
                        format: 'binary',
                        description: 'ZIP file containing transport documents'
                    )
                ),
                headers: [
                    new OA\Header(
                        header: 'Content-Disposition',
                        description: 'Attachment filename',
                        schema: new OA\Schema(type: 'string', example: 'attachment; filename="transport_documents_REF001234.zip"')
                    ),
                    new OA\Header(
                        header: 'Content-Type',
                        description: 'MIME type of the file',
                        schema: new OA\Schema(type: 'string', example: 'application/zip')
                    ),
                ]
            ),
            new OA\Response(
                response: 400,
                description: 'Bad Request - Invalid reference number or no documents found',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(
                            property: 'success',
                            type: 'boolean',
                            example: false
                        ),
                        new OA\Property(
                            property: 'message',
                            type: 'string',
                            example: 'No documents found for the specified reference number',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'data',
                            type: 'object',
                            example: null,
                            nullable: true
                        )
                    ]
                )
            ),
            new OA\Response(response: 401, description: 'Unauthorized - Invalid or missing access token'),
            new OA\Response(response: 403, description: 'Forbidden - Insufficient transport admin permissions'),
            new OA\Response(
                response: 422,
                description: 'Validation Error - Invalid request data',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(
                            property: 'message',
                            type: 'string',
                            example: 'The given data was invalid.'
                        ),
                        new OA\Property(
                            property: 'errors',
                            properties: [
                                new OA\Property(
                                    property: 'ref_no',
                                    type: 'array',
                                    items: new OA\Items(type: 'string'),
                                    example: ['Reference number is required']
                                )
                            ],
                            type: 'object'
                        )
                    ]
                )
            ),
            new OA\Response(response: 500, description: 'Internal server error - ZIP file creation failed')
        ]
    )]
    public function downloadFileZip(DownloadFileZipRequest $request): Response
    {
        return $this->transportService->downloadFileZip($request->validated()['ref_no']);
    }

    #[OA\Get(
        path: '/api/admin/transport/detail-edit/{id}',
        operationId: 'getDetailTransport',
        tags: ['Transport Management'],
        summary: 'Get transport detail for editing',
        description: 'Retrieve detailed transport information including vehicle data and transport notes for editing purposes',
        security: [['bearerAuth' => []]],
        parameters: [
            new OA\Parameter(
                name: 'id',
                description: 'Transport ID',
                in: 'path',
                required: true,
                schema: new OA\Schema(type: 'integer', example: 1)
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Transport detail retrieved successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'message', type: 'string', example: 'Transport detail retrieved successfully'),
                        new OA\Property(
                            property: 'data',
                            type: 'object',
                            properties: [
                                // Transport basic data
                                new OA\Property(
                                    property: 'transport',
                                    type: 'object',
                                    properties: [
                                        new OA\Property(property: 'undrivable_flg', type: 'boolean', example: false, nullable: true),
                                        new OA\Property(property: 'tall_flg', type: 'boolean', example: false, nullable: true),
                                        new OA\Property(property: 'lowdown_flg', type: 'boolean', example: false, nullable: true),
                                        new OA\Property(property: 'long_flg', type: 'boolean', example: false, nullable: true),
                                        new OA\Property(property: 'ref_no', type: 'string', example: 'REF001234', nullable: true),
                                        new OA\Property(property: 'm_customer_id', type: 'integer', example: 1, nullable: true),
                                        new OA\Property(property: 'm_trans_id', type: 'integer', example: 1, nullable: true),
                                        new OA\Property(property: 'st_cd', type: 'string', example: 'ACTIVE', nullable: true),
                                        new OA\Property(property: 'deli_price', type: 'number', format: 'float', example: 1000.00, nullable: true),
                                        new OA\Property(property: 'sales_price', type: 'number', format: 'float', example: 1200.00, nullable: true),
                                        new OA\Property(property: 'plate_send_date', type: 'string', format: 'date', example: '2024-01-15', nullable: true),
                                        new OA\Property(property: 'plate_send_no', type: 'string', example: 'PSN001', nullable: true),
                                        new OA\Property(property: 'plate_send_co', type: 'string', example: 'Company Name', nullable: true),
                                        new OA\Property(property: 'to_plan_date', type: 'string', format: 'date', example: '2024-01-20', nullable: true),
                                        new OA\Property(property: 'to_date', type: 'string', format: 'date', example: '2024-01-25', nullable: true),
                                        new OA\Property(property: 'from_plan_date', type: 'string', format: 'date', example: '2024-01-10', nullable: true),
                                        new OA\Property(property: 'fr_name', type: 'string', example: 'Sender Name', nullable: true),
                                        new OA\Property(property: 'fr_addr', type: 'string', example: 'Sender Address', nullable: true),
                                        new OA\Property(property: 'fr_tel', type: 'string', example: '+81-3-1234-5678', nullable: true),
                                        new OA\Property(property: 'to_name', type: 'string', example: 'Receiver Name', nullable: true),
                                        new OA\Property(property: 'to_addr', type: 'string', example: 'Receiver Address', nullable: true),
                                        new OA\Property(property: 'to_tel', type: 'string', example: '+81-3-8765-4321', nullable: true),
                                        new OA\Property(property: 'pos_no', type: 'string', example: 'POS001', nullable: true),
                                        new OA\Property(property: 'aa_no', type: 'string', example: 'AA001', nullable: true),
                                        new OA\Property(property: 'luxury_money', type: 'number', format: 'float', example: 500.00, nullable: true),
                                        new OA\Property(property: 'car_name', type: 'string', example: 'Toyota Camry', nullable: true),
                                        new OA\Property(property: 'car_no', type: 'string', example: 'CAR001', nullable: true),
                                        new OA\Property(property: 'plate_cut_flg', type: 'boolean', example: false, nullable: true),
                                        new OA\Property(property: 'plate_no', type: 'string', example: 'ABC-1234', nullable: true),
                                        new OA\Property(property: 'country', type: 'string', example: 'Japan', nullable: true),
                                        new OA\Property(property: 'odr_date', type: 'string', format: 'date', example: '2024-01-01', nullable: true),
                                        new OA\Property(property: 'note', type: 'string', example: 'Transport notes', nullable: true),
                                        new OA\Property(property: 'port_cd', type: 'string', example: 'YOK', nullable: true),
                                        new OA\Property(property: 'port', type: 'string', example: 'Yokohama Port', nullable: true),
                                        new OA\Property(property: 'country_cd', type: 'string', example: 'JP', nullable: true),
                                        new OA\Property(property: 'country_area', type: 'string', example: 'Kanto', nullable: true),
                                        new OA\Property(property: 'country_free', type: 'string', example: 'Free zone', nullable: true),
                                        new OA\Property(property: 'auction_date', type: 'string', format: 'date', example: '2024-01-05', nullable: true),
                                        new OA\Property(property: 'auction_chk', type: 'boolean', example: true, nullable: true),
                                        new OA\Property(property: 'auction_txt', type: 'string', example: 'Auction text', nullable: true),
                                        new OA\Property(property: 'auc_name', type: 'string', example: 'Auction House Name', nullable: true),
                                        new OA\Property(property: 'auc_addr', type: 'string', example: 'Auction House Address', nullable: true),
                                        new OA\Property(property: 'auc_tel', type: 'string', example: '+81-3-1111-2222', nullable: true),
                                        new OA\Property(property: 'pos_chk', type: 'boolean', example: false, nullable: true),
                                        new OA\Property(property: 'pos_chk_text', type: 'string', example: 'POS check text', nullable: true),
                                        new OA\Property(property: 'date_of_payment_time', type: 'string', format: 'date', example: '2024-01-30', nullable: true),
                                        new OA\Property(property: 'old_flg', type: 'boolean', example: false, nullable: true),
                                        new OA\Property(property: 'luxury_flg', type: 'boolean', example: false, nullable: true),
                                        new OA\Property(property: 'luxury_flg_insrance', type: 'boolean', example: false, nullable: true),
                                        new OA\Property(property: 'other_flg', type: 'boolean', example: false, nullable: true),
                                        new OA\Property(property: 'other_flg_txt', type: 'string', example: 'Other flag text', nullable: true),
                                        new OA\Property(property: 'tick_no_flg', type: 'boolean', example: false, nullable: true),
                                        new OA\Property(property: 'plate_send_name', type: 'string', example: 'Plate Sender Name', nullable: true),
                                        new OA\Property(property: 'plate_send_zipcd', type: 'string', example: '123-4567', nullable: true),
                                        new OA\Property(property: 'plate_send_address', type: 'string', example: 'Plate Sender Address', nullable: true),
                                        new OA\Property(property: 'plate_send_tel', type: 'string', example: '+81-3-3333-4444', nullable: true),
                                        new OA\Property(property: 'narikiri_trans_flg', type: 'boolean', example: false, nullable: true),
                                        new OA\Property(property: 'cus_name_jp', type: 'string', example: 'Customer Name', nullable: true),
                                        new OA\Property(property: 'to_aa_place_id', type: 'integer', example: 1, nullable: true),
                                        new OA\Property(property: 'fr_aa_place_id', type: 'integer', example: 1, nullable: true),
                                        new OA\Property(property: 'fr_place_id', type: 'integer', example: 1, nullable: true),
                                        new OA\Property(property: 'to_place_id', type: 'integer', example: 1, nullable: true),
                                        new OA\Property(property: 'to_etc_place_id', type: 'integer', example: 1, nullable: true),
                                        new OA\Property(property: 'naika_out_date', type: 'string', example: 1, nullable: true),
                                    ],
                                ),

                                // AS400 data
                                new OA\Property(
                                    property: 'as_data',
                                    type: 'object',
                                    properties: [
                                        new OA\Property(property: 'as_ref_no', type: 'string', example: 'ASREF001', nullable: true),
                                        new OA\Property(property: 'as_reg_date', type: 'string', format: 'date', example: '2024-01-01', nullable: true),
                                        new OA\Property(property: 'as_veh_name', type: 'string', example: 'Vehicle Name', nullable: true),
                                        new OA\Property(property: 'as_chassis_no', type: 'string', example: 'CHASSIS001', nullable: true),
                                        new OA\Property(property: 'as_carry_destination', type: 'string', example: 'Destination', nullable: true),
                                        new OA\Property(property: 'as_cnty_destination', type: 'string', example: 'Country Destination', nullable: true),
                                        new OA\Property(property: 'as_vessel_name', type: 'string', example: 'Vessel Name', nullable: true),
                                        new OA\Property(property: 'as_voy_no', type: 'string', example: 'VOY001', nullable: true),
                                        new OA\Property(property: 'as_agent_ref_no', type: 'string', example: 'AGENT001', nullable: true),
                                        new OA\Property(property: 'as_agent', type: 'string', example: 'Agent Name', nullable: true),
                                        new OA\Property(property: 'as_buyer', type: 'string', example: 'Buyer Name', nullable: true),
                                        new OA\Property(property: 'as_etd', type: 'string', format: 'date', example: '2024-01-15', nullable: true),
                                        new OA\Property(property: 'as_eta', type: 'string', format: 'date', example: '2024-01-25', nullable: true),
                                        new OA\Property(property: 'as_l_port', type: 'string', example: 'Loading Port', nullable: true),
                                        new OA\Property(property: 'as_d_port', type: 'string', example: 'Discharge Port', nullable: true),
                                        new OA\Property(property: 'as_doc_receiving_date', type: 'string', format: 'date', example: '2024-01-10', nullable: true),
                                        new OA\Property(property: 'as_carrying_date', type: 'string', format: 'date', example: '2024-01-20', nullable: true),
                                        new OA\Property(property: 'as_keep_scheduled_date', type: 'string', format: 'date', example: '2024-01-30', nullable: true),
                                        new OA\Property(property: 'as_veh_location', type: 'string', example: 'Vehicle Location', nullable: true),
                                        new OA\Property(property: 'as_plan_name', type: 'string', example: 'Plan Name', nullable: true),
                                        new OA\Property(property: 'as_maf_rslt', type: 'string', example: 'MAF Result', nullable: true),
                                    ],
                                    nullable: true
                                ),

                                // Transport notes
                                new OA\Property(
                                    property: 'transport_notes',
                                    type: 'array',
                                    items: new OA\Items(
                                        type: 'object',
                                        properties: [
                                            new OA\Property(property: 'id', type: 'integer', example: 1),
                                            new OA\Property(property: 'transport_id', type: 'integer', example: 1),
                                            new OA\Property(property: 'note', type: 'string', example: 'Transport note content'),
                                            new OA\Property(property: 'created_at', type: 'string', format: 'date-time', example: '2024-01-01T00:00:00Z'),
                                            new OA\Property(property: 'updated_at', type: 'string', format: 'date-time', example: '2024-01-01T00:00:00Z'),
                                        ]
                                    ),
                                    nullable: true
                                ),
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 400,
                description: 'Bad Request - Invalid transport ID',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'message', type: 'string', example: 'Invalid transport ID'),
                        new OA\Property(property: 'data', type: 'object', example: null, nullable: true)
                    ]
                )
            ),
            new OA\Response(response: 401, description: 'Unauthorized - Invalid or missing access token'),
            new OA\Response(response: 403, description: 'Forbidden - Insufficient transport admin permissions'),
            new OA\Response(
                response: 404,
                description: 'Not Found - Transport not found',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'message', type: 'string', example: 'Transport not found'),
                        new OA\Property(property: 'data', type: 'object', example: null, nullable: true)
                    ]
                )
            ),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function getDetailTransport(mixed $id): Response
    {
        return $this->respond(new TransportEditDetailResource($this->transportService->getDetailTransport($id)));
    }

    #[OA\Get(
        path: '/api/admin/transport/vehicle-report-month',
        operationId: 'transportVehicleReportMonth',
        tags: ['Transport Management'],
        summary: 'Get vehicle transport report by month',
        description: 'Retrieve paginated vehicle transport data for a specific month with filtering and sorting options',
        security: [['bearerAuth' => []]],
        parameters: [
            new OA\Parameter(
                name: 'month',
                description: 'Month number (1-12)',
                in: 'query',
                required: true,
                schema: new OA\Schema(type: 'integer', minimum: 1, maximum: 12, example: 1)
            ),
            new OA\Parameter(
                name: 'limit',
                description: 'Number of items per page (default: 20)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'integer', minimum: 1, example: 20)
            ),
            new OA\Parameter(
                name: 'page',
                description: 'Page number for pagination (default: 1)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'integer', minimum: 1, example: 1)
            ),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Vehicle transport report retrieved successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: true),
                        new OA\Property(property: 'code', type: 'integer', example: 200),
                        new OA\Property(property: 'locale', type: 'string', example: 'en'),
                        new OA\Property(property: 'message', type: 'string', example: 'Success'),
                        new OA\Property(
                            property: 'data',
                            type: 'object',
                            properties: [
                                new OA\Property(
                                    property: 'items',
                                    type: 'array',
                                    items: new OA\Items(
                                        type: 'object',
                                        properties: [
                                            new OA\Property(property: 'HNCC03', type: 'string', example: '1030651'),
                                            new OA\Property(property: 'HNCC54', type: 'string', example: 'GWS204-0025945'),
                                            new OA\Property(property: 'HNNC25', type: 'string', example: 'SMART PLAN'),
                                            new OA\Property(property: 'HNTD14', type: 'string', example: '20250113'),
                                            new OA\Property(property: 'HNTD19', type: 'string', example: '20250113'),
                                        ]
                                    )
                                ),
                                new OA\Property(
                                    property: 'meta',
                                    type: 'object',
                                    properties: [
                                        new OA\Property(property: 'current_page', type: 'integer', example: 1, description: 'Current page number'),
                                        new OA\Property(property: 'last_page', type: 'integer', example: 5, description: 'Last page number'),
                                        new OA\Property(property: 'per_page', type: 'integer', example: 20, description: 'Items per page'),
                                        new OA\Property(property: 'total', type: 'integer', example: 75, description: 'Total number of records'),
                                        new OA\Property(property: 'from', type: 'integer', example: 1, description: 'First item number on current page'),
                                        new OA\Property(property: 'to', type: 'integer', example: 20, description: 'Last item number on current page')
                                    ]
                                )
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 400,
                description: 'Bad Request - Invalid month parameter',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 400),
                        new OA\Property(property: 'message', type: 'string', example: 'The month must be between 1 and 12.'),
                        new OA\Property(property: 'data', type: 'object', example: null, nullable: true)
                    ]
                )
            ),
            new OA\Response(
                response: 401,
                description: 'Unauthorized - Invalid or missing access token',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 401),
                        new OA\Property(property: 'message', type: 'string', example: 'Unauthorized')
                    ]
                )
            ),
            new OA\Response(
                response: 403,
                description: 'Forbidden - Insufficient permissions',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 403),
                        new OA\Property(property: 'message', type: 'string', example: 'Insufficient permissions')
                    ]
                )
            ),
            new OA\Response(
                response: 422,
                description: 'Validation Error - Invalid request parameters',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 422),
                        new OA\Property(property: 'message', type: 'string', example: 'The given data was invalid.'),
                        new OA\Property(
                            property: 'errors',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'month', type: 'array', items: new OA\Items(type: 'string'), example: ['The month field is required.']),
                                new OA\Property(property: 'limit', type: 'array', items: new OA\Items(type: 'string'), example: ['The limit must be at least 1.'])
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 500,
                description: 'Internal server error',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'success', type: 'boolean', example: false),
                        new OA\Property(property: 'code', type: 'integer', example: 500),
                        new OA\Property(property: 'message', type: 'string', example: 'Internal server error')
                    ]
                )
            )
        ]
    )]
    public function transportVehicleReportMonth(ShippingReportMonthRequest $request): Response
    {
        return $this->respond($this->vehicleTransportService->transportVehicleReportMonth($request->validated()));
    }
}
