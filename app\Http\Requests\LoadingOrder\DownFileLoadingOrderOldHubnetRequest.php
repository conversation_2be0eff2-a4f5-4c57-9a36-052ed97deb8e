<?php

declare(strict_types=1);

namespace App\Http\Requests\LoadingOrder;

use Illuminate\Foundation\Http\FormRequest;

class DownFileLoadingOrderOldHubnetRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'customer_id' => 'required|integer',
            'loading_id' => 'required|integer',
            'fname' => 'required|string',
        ];
    }
}
