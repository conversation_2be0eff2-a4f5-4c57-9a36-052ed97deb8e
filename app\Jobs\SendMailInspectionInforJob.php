<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Mail\InspectionInforMail;
use App\Traits\MailTrait;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Mail;

class SendMailInspectionInfor<PERSON>ob implements ShouldQueue
{
    use MailTrait;
    use Queueable;

    private array $data;
    private string $mail_address;

    /**
     * Create a new job instance.
     */
    public function __construct(string $mail_address, array $data)
    {
        $this->data = $data;
        $this->mail_address = $mail_address;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if (!$this->checkMailValid($this->mail_address)) {
            return;
        }

        Mail::to($this->mail_address)
            ->send(new InspectionInforMail($this->data));
    }
}
