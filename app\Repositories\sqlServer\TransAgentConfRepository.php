<?php

declare(strict_types=1);

namespace App\Repositories\sqlServer;

use App\Models\sqlServer\TransAgentConf;
use App\Repositories\BaseRepository;

class TransAgentConfRepository extends BaseRepository
{
    public function model(): string
    {
        return TransAgentConf::class;
    }

    /**
     * Get transport agent configuration by AA place and To place
     */
    public function getConfigByPlaceIds(string $aaPlaceId, string $toPlaceId): ?TransAgentConf
    {
        return ($this->model)::query()->select('agent_id')
            ->where('del_flg', 0)
            ->where('aa_place_id', $aaPlaceId)
            ->where('to_place_id', $toPlaceId)
            ->first();
    }
}
