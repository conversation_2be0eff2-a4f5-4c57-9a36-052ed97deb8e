@php
    $language = app()->getLocale();
@endphp

@props([
    'id',
    'type' => 'pie',
    'labels' => null,
    'data' => [],
    'colors' => null,
    'title' => '',
    'titleMobile' => null,
])

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const ctx = document.getElementById('{{ $id }}')
        if (!ctx) return

        const win_w = window.innerWidth
        const isDesktop = win_w >= 577

        // Default colors
        const defaultColors = ['#2c5f72', '#548ea6', '#f38c8d', '#f2b8a2', '#f3d2b3']
        const finalColors = @json($colors) || defaultColors
        const finalLabels =
            @json($labels) ||
            (@json($language) === 'ja'
                ? ['期待以上', '満足', '普通', 'やや不満', '不満']
                : ['Excellent', 'Good', 'Fair', 'Poor', 'Very Poor'])

        // Determine title based on screen size
        const titleText = isDesktop ? @json($title) : @json($titleMobile ?? $title)

        // Convert string with \n to array for Chart.js
        let finalTitle
        if (typeof titleText === 'string' && titleText.includes('\n')) {
            finalTitle = titleText.split('\n')
        } else {
            finalTitle = titleText
        }

        // Determine chart type
        let finalChartType = @json($type);
        if (finalChartType === 'bar' && !isDesktop) {
            finalChartType = 'doughnut'
        }

        const chartOptions = {
            type: finalChartType,
            data: {
                labels: finalLabels,
                datasets: [
                    {
                        backgroundColor: finalColors,
                        data: @json($data),
                    },
                ],
            },
            options: {
                title: {
                    display: true,
                    fontSize: isDesktop ? 18 : 15,
                    text: finalTitle,
                },
                maintainAspectRatio: false,
                tooltips: {
                    callbacks: {
                        label: function (tooltipItem, data) {
                            return (
                                data.labels[tooltipItem.index] + ' : ' + data.datasets[0].data[tooltipItem.index] + ' %'
                            )
                        },
                    },
                },
            },
        }

        // Special options for bar chart
        if (finalChartType === 'bar') {
            chartOptions.options.tooltips.enabled = false
            chartOptions.options.legend = {
                display: false,
            }
            chartOptions.options.scales = {
                yAxes: [
                    {
                        display: false,
                    },
                ],
            }
        }

        new Chart(ctx, chartOptions)
    })
</script>
