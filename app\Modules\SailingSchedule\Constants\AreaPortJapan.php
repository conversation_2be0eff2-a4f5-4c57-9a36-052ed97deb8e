<?php

declare(strict_types=1);

namespace App\Modules\SailingSchedule\Constants;

class AreaPortJapan
{
    public const AREA_MAP_EN = [
        '23' => 'Kyushu',
        '12' => 'Hokuriku',
        '13' => 'Kanto',
        '15' => '<PERSON><PERSON>',
        '8'  => 'Kyushu',
        '30' => 'Kanto',
        '9'  => 'Kyushu',
        '6'  => 'Kanto',
        '19' => 'Kanto',
        '3'  => 'Kansai',
        '26' => 'Kansai',
        '25' => 'Kansai',
        '11' => 'Hokuriku',
        '18' => 'Kyushu',
        '4'  => '<PERSON><PERSON>',
        '28' => 'Chubu',
        '14' => 'Kyushu',
        '1'  => 'Kansai',
        '27' => 'Kansai',
        '2'  => 'Kansai',
        '22' => 'Kyushu',
        '29' => 'Kanto',
        '17' => 'Chubu',
        '24' => '<PERSON><PERSON>',
        '5'  => 'Kanto',
    ];


    public const AREA_MAP_JA = [
        '23' => '九州',
        '12' => '北陸',
        '13' => '関東',
        '15' => '中部',
        '8'  => '九州',
        '30' => '関東',
        '9'  => '九州',
        '6'  => '関東',
        '19' => '関東',
        '3'  => '関西',
        '26' => '関西',
        '25' => '関西',
        '11' => '北陸',
        '18' => '九州',
        '4'  => '中部',
        '28' => '中部',
        '14' => '九州',
        '1'  => '関西',
        '27' => '関西',
        '2'  => '関西',
        '22' => '九州',
        '29' => '関東',
        '17' => '中部',
        '24' => '中部',
        '5'  => '関東',
    ];

    public const HIDDEN_PORT_IDS = [
        7, 16, 21, 10, 20, 110, 536, 503, 504, 505,
        506, 507, 510, 511, 232, 512, 514, 516, 517,
        500, 518, 519, 547, 520, 521, 522, 306, 525,
        509, 515, 526, 527, 541, 302, 501, 545, 528,
        498, 499, 529, 546, 530, 213, 303, 539, 531,
        542, 533, 534, 535,
    ];

    public static function getAreaPortJapan(string $code): string | null
    {
        return self::AREA_MAP_JA[$code] ?? null;
    }

    public static function getAreaPortEn(string $code): string | null
    {
        return self::AREA_MAP_EN[$code] ?? null;
    }
}
