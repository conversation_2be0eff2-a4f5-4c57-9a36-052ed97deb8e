<?php

declare(strict_types=1);

namespace App\Repositories\sqlServer;

use App\Models\sqlServer\SupportServiceModel;
use App\Repositories\BaseRepository;
use Illuminate\Database\Eloquent\Collection;

class SupportServiceRepository extends BaseRepository
{
    public function model(): mixed
    {
        return SupportServiceModel::class;
    }

    public function getSupportServiceNotDeleted(array $params): Collection
    {
        $query = ($this->model)::query();

        $query
            ->select('id', 'label_name')
            ->where('del_flg', 0);

        $this->buildSortQuery($query, $params);

        return $query->get();
    }
}
