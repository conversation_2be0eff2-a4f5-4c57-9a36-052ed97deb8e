<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Modules\SailingSchedule\Services\SailingScheduleService;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\ResponseBuilder\Exceptions\ArrayWithMixedKeysException;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\ResponseBuilder\Exceptions\ConfigurationNotFoundException;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\ResponseBuilder\Exceptions\IncompatibleTypeException;
use Symfony\Component\HttpFoundation\Response;

class SailingScheduleController extends Controller
{
    private SailingScheduleService  $sailingScheduleService;

    public function __construct(SailingScheduleService $sailingScheduleService)
    {
        $this->sailingScheduleService = $sailingScheduleService;
    }

    /**
     * @throws ArrayWithMixedKeysException
     * @throws IncompatibleTypeException
     * @throws ConfigurationNotFoundException
     */
    public function getCountries(): Response
    {
        return $this->respond($this->sailingScheduleService->getCountries());
    }

    public function getRouteSchedule(): Response
    {
        return $this->respond($this->sailingScheduleService->getRouteSchedule());
    }

    public function getPort()
    {
        return $this->respond($this->sailingScheduleService->getPort());
    }

    public function getSchedule()
    {
        $params = request()->query();

        return $this->sailingScheduleService->getSchedule($params);
    }
}
