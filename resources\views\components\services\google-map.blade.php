@php
    /**
     * @var int $positionX
     * @var int $positionY
     * @var string $id
     * @var string $office
     * @var string $officeDetail
     */
    $apiKey = env('API_GOOGLE', '');
@endphp

<div id="{{ $id }}" class="mt-[2em] h-50 w-full"></div>
@once
    @push('scripts')
        <script type="text/javascript" src="//maps.google.com/maps/api/js?key={{ $apiKey }}"></script>
    @endpush
@endonce

@push('scripts')
    <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', function () {
            var latlng = new google.maps.LatLng({{ $positionX }}, {{ $positionY }})
            var myOptions = {
                zoom: 14,
                center: latlng,
                mapTypeId: google.maps.MapTypeId.ROADMAP,
            }

            var map = new google.maps.Map(document.getElementById('{{ $id }}'), myOptions)

            var gmarker = new google.maps.Marker({
                position: latlng,
                title: '{{ $office }}',
            })
            gmarker.setMap(map)

            var infoWindow = new google.maps.InfoWindow({
                content: `<center><b>{{ $office }}</b><br/><font size="1">{!! $officeDetail !!}</font></center>`,
            })

            infoWindow.open(map, gmarker)

            google.maps.event.addListener(gmarker, 'click', function () {
                infoWindow.open(map, gmarker)
            })
        })
    </script>
@endpush
