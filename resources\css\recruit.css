.container-width {
  margin: 45px auto 0px;
}
h1.main_title {
  font-size: 35px;
  margin-top: 30px;
}
.recruit_top_p {
  line-height: 40px;
  font-size: 19px;
}
.recruit_main_title {
  font-size: 50px;
  font-weight: bold;
  font-family: "M PLUS Rounded 1c", sans-serif;
  margin-top: 100px;
}
.recruit_top_margin {
  margin-top: 127px;
}


.contact_us {
  color: #495057;
  height: 320px;
  padding: 50px;
  background-image: url("../img/contact_image.jpg");
  background-size: 100%;
  box-shadow: -1px -1px 7px grey;
}
.btn-danger.detail-btn {
  height: 45px;
  line-height: 33px;
  font-size: 12px;
  margin-top: -19px;
}
#animation01 {
    top: 105px;
    left: 124px;
}
#animation02 {
    top: 455px;
    left: 260px;
}
#animation03 {
    top: 425px;
    left: 1150px;
}
#animation04 {
    top: 590px;
    left: 740px;
}
#animation05 {
    top: 240px;
    left: 816px;
}
#animation06 {
    top: 1215px;
    left: 100px;
}
#animation07 {
    top: 715px;
    left: 1600px;
}
#animation08 {
    top: 1415px;
    left: 90px;
}
#animation09 {
    top: 955px;
    left: 1790px;
}
#animation10 {
    top: 819px;
    left: -50px;
}
#animation11 {
    top: 1215px;
    left: 1600px;
}
#animation12 {
    top: 1715px;
    left: 230px;
}
#animation13 {
    top: 1715px;
    left: 1757px;
}
#animation14 {
    top: 715px;
    left: 900px;
}
#animation15 {
    top: 1495px;
    left: 1800px;
}
#animation16 {
    top: 1145px;
    left: 1500px;
}
#animation17 {
    top: 727px;
    left: 1624px;
}
#animation18 {
    top: 1115px;
    left: 90px;
}
#animation19 {
    top: 1415px;
    left: 1500px;
}
#animation20 {
    top: 1215px;
    left: 200px;
}
#animation21 {
    top: 170px;
    left: 1600px;
}
#animation22 {
    top: 1515px;
    left: 190px;
}
#animation23 {
    top: 1245px;
    left: 800px;
}
#animation24 {
    top: 1615px;
    left: 800px;
}
#animation25 {
    top: 265px;
    left: 1740px;
}

.recruit_bg_01 {
    height: 92px;
    width: 100%;
}

.recruit_bg_02 {
    background: rgb(255, 255, 255, 1);
    height: 322px;
    width: 100%;
    transform: skew(0deg, 2deg);
    box-shadow: 1px 1px 10px 0px grey, -1px -1px 10px 0px grey;
    opacity: 0.8;
}

.recruit_bg_03 {
    height: 165px;
    width: 100%;
}

.recruit_bg_04 {
    background: linear-gradient(45deg, #fdf9f4, #f5feff, #fff, #fff5ff, #f5feff, #f6f9eb);
    height: 2372px;
    width: 100%;
}



@media screen and (max-width: 576px) {
    .recruit_table {
        margin-left: -12px;
        font-size: 13px;
    }

    .recruit_table th {
        padding: 0;
    }

    .recruit_main_title {
        margin-top: 0;
    }
}

@media screen and (min-width: 576px) and (max-width: 767px) {
    .recruit_main_title {
        margin-top: 0;
    }
}
