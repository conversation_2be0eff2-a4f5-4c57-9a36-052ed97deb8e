<?php

declare(strict_types=1);

namespace App\Repositories\sqlServer;

use App\Enums\AaPlace\ChikuRegion;
use App\Enums\AaPlace\OrderOptions;
use App\Enums\CommonCode\CommonCodeCategory;
use App\Enums\CommonCode\ExcludeLabel;
use App\Models\sqlServer\AaPlace;
use App\Repositories\BaseRepository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class AaPlaceRepositories extends BaseRepository
{
    public function model(): string
    {
        return AaPlace::class;
    }

    public function getAaPlaceChikuRegion(array $params = [])
    {
        $query = ($this->model)::query();

        $query = $query
            ->from('t_aa_place as tap')
            ->select('tap.id', 'tap.name', 'tc.cd', 'tc.value2', 'tap.addr', 'tap.tel', 'tap.name')
            ->leftJoin('t_com as tc', 'tap.todofuken_cd', '=', 'tc.cd')
            ->where('tc.cd_kbn', CommonCodeCategory::PREFECTURE->value)
            ->where('tc.cd', '<>', ExcludeLabel::LABEL_CODE->value)
            ->whereIn('tc.value2', ChikuRegion::getAllValues())
            ->where('tap.del_flg', 0);

        // Apply order by
        $this->buildSortQuery($query, $params);

        return $query->get();
    }

    public function buildSortQuery(Builder $query, $params): void
    {
        $params = $params ?: request()->toArray();

        $orderOption = $params['order_option'] ?? '';
        $sortType = $params['sort_type'] ?? 'DESC';

        $this->applySorting($query, $orderOption, $sortType);
    }

    public function getAuctionDates(): Collection
    {
        $query = ($this->model)::query();

        $currentDate = now();
        $fromDate = $currentDate->copy()->subDays(31)->format('Y-m-d');
        $toDate = $currentDate->copy()->addDays(4)->format('Y-m-d');

        return $query
            ->from('t_aa_date')
            ->leftJoin('t_aa_place', 't_aa_date.t_aa_place_id', '=', 't_aa_place.id')
            ->select('t_aa_date.id as tad_id', 't_aa_date.open_date', 't_aa_place.name')
            ->where('t_aa_date.open_date', '>=', $fromDate)
            ->where('t_aa_date.open_date', '<=', $toDate)
            ->where('t_aa_date.del_flg', 0)
            ->where('t_aa_date.service1_flg', 1)
            ->orderBy('t_aa_date.open_date', 'ASC')
            ->get();
    }

    private function applySorting(Builder $query, string $orderOption, string $direction): Builder
    {
        $colSortDefault = 'tap.name';
        return match ($orderOption) {
            OrderOptions::NAME->value => $query->orderBy('tap.name', $direction),
            default => $query->orderBy($colSortDefault, 'ASC'),
        };
    }
}
