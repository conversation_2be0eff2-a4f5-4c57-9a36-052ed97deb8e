<?php

declare(strict_types=1);

namespace App\Repositories\sqlServer;

use App\Enums\Area\OrderOptions;
use App\Models\sqlServer\Area;
use App\Repositories\BaseRepository;
use App\Repositories\common\BuilderWhereCondition;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;

class AreaRepository extends BaseRepository
{
    public function model(): mixed
    {
        return Area::class;
    }

    // get all areas
    public function getList($params = [], $relations = [], $columns = ['*'], $withTrashed = false)
    {
        $query = ($this->model)::query();
        if (Arr::get($params, 'select_raw')) {
            $query->selectRaw(Arr::get($params, 'select_raw'));
        } else {
            $query->select($columns);
        }
        $query = BuilderWhereCondition::call($query, $withTrashed, $params);
        $query = $this->addSoftDeleteCondition($query);
        $query = $this->applyFilterScope($query, $params);
        $this->applySorting($query, $params['order_option'] ?? '', $params['sort_type'] ?? 'asc');
        return $query->get();
    }

    private function applySorting(Builder $query, string $orderOption, string $direction): Builder
    {
        $colSortDefault = 'm_area.id';
        return match ($orderOption) {
            OrderOptions::ACD->value => $query->orderBy('m_area.acd', $direction),
            OrderOptions::COUNTRY->value => $query->orderBy('m_area.country', $direction),
            OrderOptions::PORT->value => $query->orderBy('m_area.port', $direction),
            default => $query->orderBy($colSortDefault, 'DESC'),
        };
    }
}
