<?php

declare(strict_types=1);

namespace App\Services\Transport;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class SendMailEditTransportService1
{
    public function run(array $oldData, array $newData): void
    {
        // Lấy biến ra cho ngắn gọn (giữ nguyên tên như ASP)

        //***************************************************************************************************
        //********************メール表示用***************************
        // 車両状態
        $tickitems_mail = "";
        if (($newData['undrivable_flg'] ?? "") === "1") {
            $tickitems_mail .= (trans('mail.lan_undrivable') ?? "") . "<br/>";
        }
        if (($newData['tall_flg'] ?? "") === "1") {
            $tickitems_mail .= (trans('mail.lan_tall') ?? "") . "<br/>";
        }
        if (($newData['lowdown_flg'] ?? "") === "1") {
            $tickitems_mail .= (trans('mail.lan_lowdown') ?? "") . "<br/>";
        }
        if (($newData['long_flg'] ?? "") === "1") {
            $tickitems_mail .= (trans('mail.lan_long') ?? "") . "<br/>";
        }
        if (($newData['old_flg'] ?? "") === "1") {
            $tickitems_mail .= (trans('mail.lan_old_car') ?? "") . "<br/>";
        }
        if (($newData['luxury_flg'] ?? "") === "1") {
            $tickitems_mail .= (trans('mail.lan_luxury_car') ?? "");
            if (($newData['luxury_flg_insrance'] ?? "") === "1") {
                $tickitems_mail .= " (" . (trans('mail.lan_luxyry_insurance1') ?? "") . ")";
            } elseif (($newData['luxury_flg_insrance'] ?? "") === "2") {
                $tickitems_mail .= " (" . (trans('mail.lan_luxyry_insurance2') ?? "") . ")";
            }
            $tickitems_mail .= "<br/>";
        }
        if (($newData['other_flg'] ?? "") === "1") {
            $tickitems_mail .= (trans('mail.lan_other_car') ?? "");
            $tickitems_mail .= " (" . $this->string_escape_new($this->string_escape_delete($newData['other_flg_txt'] ?? "")) . ")";
            $tickitems_mail .= "<br/>";
        }
        if (($newData['tick_no_flg'] ?? "") === "1") {
            $tickitems_mail .= (trans('mail.lan_gaitou_nasi') ?? "") . "<br/>";
        }

        // オークション会場等
        $auc_mail = "";
        if (($newData['auction_chk'] ?? "") === "1") {
            $auc_mail .= (trans('mail.lang_aucBoth') ?? "") . "<br/>";
        } else {
            $auc_mail .= ($newData['auction_txt'] ?? "") . "<br/>";
            if (!empty($newData['auc_name'] ?? "") || !empty($newData['auc_addr'] ?? "") || !empty($newData['auc_tel'] ?? "")) {
                $auc_mail .= (trans('mail.lan_other_auc') ?? "") . "：" . ($newData['auc_name'] ?? "") . "<br/>";
                $auc_mail .= (trans('mail.lan_address') ?? "") . "：" . ($newData['auc_addr'] ?? "") . "<br/>";
                $auc_mail .= (trans('mail.lan_tel') ?? "") . "：" . ($newData['auc_tel'] ?? "") . "<br/>";
            }
        }

        // プレート発送先
        $plate_address_mail = "";
        if (!empty($newData['plate_send_name'] ?? "")) {
            $plate_address_mail  = ($newData['plate_send_name'] ?? "") . "<br/>";
            $plate_address_mail .= " " . "〒" . ($newData['plate_send_zipcd'] ?? "") . "<br/>";
            $plate_address_mail .= " " . ($newData['plate_send_address'] ?? "") . "<br/>";
            $plate_address_mail .= " " . ($newData['plate_send_tel'] ?? "") . "<br/>";
        }

        // 入金予定時間
        $date_of_time = "";
        if (($newData['date_of_payment_time'] ?? "") === "1") {
            $date_of_time = "（" . (trans('mail.lan_payment_time1') ?? "") . "）";
        } elseif (($newData['date_of_payment_time'] ?? "") === "2") {
            $date_of_time = "（" . (trans('mail.lan_payment_time2') ?? "") . "）";
        } elseif (($newData['date_of_payment_time'] ?? "") === "3") {
            $date_of_time = "（" . (trans('mail.lan_payment_time3') ?? "") . "）";
        }

        // ***メールを送信する
        $send_mail_flg   = 0;
        $change_price_flg = 0;
        $plate_send_flg   = 0;
        $file_mail_flg    = 0;

        if (($last_deli_price ?? null) != ($deli_price ?? null)) {
            $change_price_flg = 1;
        }

        if (
            ($newData['plate_send_date'] ?? "") !== ($oldData['plate_send_date'] ?? "") ||
            ($newData['plate_send_co'] ?? "") !== ($oldData['plate_send_co'] ?? "") ||
            ($newData['plate_send_no'] ?? "") !== ($oldData['plate_send_no'] ?? "")
        ) {
            $plate_send_flg = 1;
        }

        if (empty($newData['plate_send_date'] ?? "") || empty($newData['plate_send_co'] ?? "") || empty($newData['plate_send_no'] ?? "")) {
            $plate_send_flg = 0;
        }

        // 新規添付ファイル案内メール送信
        if (!empty(($newData['name_files'] ?? "")[0] ?? "")) {
            $file_mail_flg = 1;
        }

        if (1 == $change_price_flg || 1 == $plate_send_flg || ($newData['cmmnt_upd_flg'] ?? 0) == 1 || 1 == $file_mail_flg) {
            $trans_name = "";
            $trans_name_note = "";
            $trans_note_email = "";
            $m_trans_id = $newData['m_trans_id'] ?? null;

            if (($m_trans_id ?? "") === "1005") {
                $trans_name = "東西海運";
                $trans_name_note = "東西海運株式会社";
                $trans_note_email = "<EMAIL>";
                $send_mail_flg = 1;
            } elseif (($m_trans_id ?? "") === "7875") {
                $trans_name = "キャリーゴール";
                $trans_name_note = "株式会社キャリーゴール";
                $trans_note_email = "<EMAIL>";
                $send_mail_flg = 1;
            } elseif (($m_trans_id ?? "") === "17078") {
                $trans_name = "栄港商運";
                $trans_name_note = "株式会社栄港商運";
                $trans_note_email = "<EMAIL>";
                $send_mail_flg = 1;
            } elseif (($m_trans_id ?? "") === "8107") {
                $trans_name = "ロジコ";
            } elseif (($m_trans_id ?? "") === "8973") {
                $trans_name = "ドリームインターナショナル";
            } elseif (($m_trans_id ?? "") === "14192") {
                $trans_name = "ジェイキャリー";
                $trans_name_note = "株式会社ジェイ・キャリー";
                $trans_note_email = "<EMAIL>";
                $send_mail_flg = 1;
            }

            // 営業担当宛***
            $ah_staff_email = "";
            $ah_staff_team  = "";

            $bk_trans_note_mail_flg   = "";
            $bk_trans_price_mail_flg  = "";
            $bk_trans_plate_mail_flg  = "";
            $bk_trans_file_mail_flg   = "";
            $bk_trans_cancel_mail_flg = "";
            $bk_trans_onhold_mail_flg = "";

            // === Query (giữ nguyên cột/LEFT JOIN) ===
            $custObj = DB::table('m_customer as mc')
                ->leftJoin('m_sales_staff as mss', 'mc.ah_sales_id', '=', 'mss.s_id')
                ->where('mc.id', $oldData['m_customer_id'] ?? null)
                ->where('mc.del_flg', 0)
                ->selectRaw("
                    mc.id, mc.cus_name_JP, mc.shipping_prsn_name, mc.ah_sales_id,
                    mss.e_mail, mss.team, mss.s_name,
                    mc.ah_send_mailaddress, mc.ah_sales_mail_flg,
                    mc.bk_trans_note_mail_flg, mc.bk_trans_price_mail_flg,
                    mc.bk_trans_plate_mail_flg, mc.bk_trans_file_mail_flg,
                    mc.bk_trans_cancel_mail_flg, mc.bk_trans_onhold_mail_flg
                ")
                ->first();

            if ($custObj) {
                $shipping_prsn_name = $custObj->shipping_prsn_name ?? null;
                $ah_sales_id        = $custObj->ah_sales_id ?? null;
                $ah_staff_email     = $custObj->e_mail ?? "";
                $ah_staff_team      = $custObj->team ?? "";
                $ah_staff_name      = $custObj->s_name ?? "";

                // メール通知設定取得（AH）
                $rep_address           = trim($custObj->e_mail ?? "");
                $rep_address_temp      = trim($custObj->e_mail ?? "");
                $ah_staff_name         = trim($custObj->s_name ?? "");
                $ah_sales_mail_flg     = (int)($custObj->ah_sales_mail_flg ?? 0);
                $ah_send_mailaddress   = trim($custObj->ah_send_mailaddress ?? "");

                $bk_trans_note_mail_flg   = (int)($custObj->bk_trans_note_mail_flg ?? 0);
                $bk_trans_price_mail_flg  = (int)($custObj->bk_trans_price_mail_flg ?? 0);
                $bk_trans_plate_mail_flg  = (int)($custObj->bk_trans_plate_mail_flg ?? 0);
                $bk_trans_file_mail_flg   = (int)($custObj->bk_trans_file_mail_flg ?? 0);
                $bk_trans_cancel_mail_flg = (int)($custObj->bk_trans_cancel_mail_flg ?? 0);
                $bk_trans_onhold_mail_flg = (int)($custObj->bk_trans_onhold_mail_flg ?? 0);

                $cmmnt_upd_flg = (int)($newData['cmmnt_upd_flg'] ?? 0);
                // 備考編集通知
                $bk_trans_note_mail_addr = "";
                if (($cmmnt_upd_flg ?? 0) == 1) {
                    if (1 == $ah_sales_mail_flg && 1 == $bk_trans_note_mail_flg) {
                        if ("" !== $ah_send_mailaddress) {
                            $ah_send_mailaddress = str_replace(";", "\t", $ah_send_mailaddress);
                            $ah_staff_email = $rep_address . "\t" . $ah_send_mailaddress;
                        }
                    }
                }

                // 価格更新通知
                $bk_trans_price_mail_addr = "";
                if (1 == $change_price_flg) {
                    if (1 == $ah_sales_mail_flg && 1 == $bk_trans_price_mail_flg) {
                        if ("" !== $ah_send_mailaddress) {
                            $ah_send_mailaddress = str_replace(";", "\t", $ah_send_mailaddress);
                            $ah_staff_email = $rep_address . "\t" . $ah_send_mailaddress;
                        }
                    }
                }

                // プレート発送通知
                $bk_trans_plate_mail_addr = "";
                if (1 == $plate_send_flg) {
                    if (1 == $ah_sales_mail_flg && 1 == $bk_trans_plate_mail_flg) {
                        if ("" !== $ah_send_mailaddress) {
                            $ah_send_mailaddress = str_replace(";", "\t", $ah_send_mailaddress);
                            $ah_staff_email = $rep_address . "\t" . $ah_send_mailaddress;
                        }
                    }
                }

                // 新規ファイル添付通知
                $bk_trans_file_mail_addr = "";
                if (1 == $file_mail_flg) {
                    if (1 == $ah_sales_mail_flg && 1 == $bk_trans_file_mail_flg) {
                        if ("" !== $ah_send_mailaddress) {
                            $ah_send_mailaddress = str_replace(";", "\t", $ah_send_mailaddress);
                            $ah_staff_email = $rep_address . "\t" . $ah_send_mailaddress;
                        }
                    }
                }

                // キャンセル通知
                $bk_trans_cancel_mail_addr = "";
                if (1 == $ah_sales_mail_flg && 1 == $bk_trans_cancel_mail_flg) {
                    if ("" !== $ah_send_mailaddress) {
                        $ah_send_mailaddress = str_replace(";", "\t", $ah_send_mailaddress);
                        $ah_staff_email = $rep_address . "\t" . $ah_send_mailaddress;
                    }
                }

                // 保留通知
                if (1 == $ah_sales_mail_flg && 1 == $bk_trans_onhold_mail_flg) {
                    if ("" !== $ah_send_mailaddress) {
                        $ah_send_mailaddress = str_replace(";", "\t", $ah_send_mailaddress);
                        $ah_staff_email = $rep_address . "\t" . $ah_send_mailaddress;
                    }
                }
            }

            // === 備考編集通知 (cmmnt_upd_flg & send_mail_flg) ===
            if (($cmmnt_upd_flg ?? 0) == 1 && 1 == $send_mail_flg) {
                $toAddress  = $trans_note_email ?? "";
                $ccAddress  = ""; // "<EMAIL>" sẽ set phía dưới
                $bccAddress = "<EMAIL>";
                $fromAddress = $FROM_MAIL ?? null;

                if ("" === $toAddress) {
                    $toAddress = $ah_staff_email;
                } else {
                    $toAddress = $toAddress . "\t" . $ah_staff_email;
                }

                // luôn thêm arrange@
                $ccAddress = "<EMAIL>";

                // ジェイキャリーの場合
                if (($m_trans_id ?? "") === "14192") {
                    if ("" === $ccAddress) {
                        $ccAddress = "<EMAIL>";
                    } else {
                        $ccAddress = $ccAddress . "\t" . "<EMAIL>";
                    }
                }

                $contentFile = "admin_transport_note_change.txt";
                $includeNameArray = [
                    "trans_name","id","customer_name","fr_aa_place_id","fr_name","fr_addr","fr_tel","to_name","to_addr","to_tel","pos_no",
                    "aa_no", "date_of_payment","car_name","car_no", "undrivable_flg", "tall_flg", "lowdown_flg", "long_flg","plate_cut_flg","plate_no","country","txtcomment_biko"
                ];

                $data = [];
                foreach ($includeNameArray as $key => $name) {
                    $data[$name] = $newData[$name] ?? $oldData[$name] ?? null;
                }
                dd([
                    ...$data,
                    'trans_name' => $trans_name_note,

                ]);
                $includeValueArray = [
                    $trans_name_note ?? "", $newData['tp_id'] ?? null, $newData['customer_name'] ?? "", $newData['fr_aa_place_id'] ?? "", $fr_name ?? "", $fr_addr ?? "", $fr_tel ?? "",
                    $to_name ?? "", $to_addr ?? "", $to_tel ?? "", $pos_no ?? "", $aa_no ?? "", $date_of_payment ?? "", $car_name ?? "", $car_no ?? "",
                    $this->flagReplace2_ja($undrivable_flg ?? "0"), $this->flagReplace2_ja($tall_flg ?? "0"), $this->flagReplace2_ja($lowdown_flg ?? "0"),
                    $this->flagReplace2_ja($long_flg ?? "0"), $this->flagReplace($plate_cut_flg ?? "0"), $plate_no ?? "", $country ?? "", $txtcomment_biko ?? ""
                ];

                dd($includeValueArray);

                $mailResult = $this->sendMailProgram($toAddress, $ccAddress, $bccAddress, $fromAddress, $contentFile, $includeNameArray, $includeValueArray);
            }

            // === 価格更新通知 (change_price_flg) ===
            if (1 == $change_price_flg) {
                $ccAddress = ""; // sẽ set arrange@
                $toAddress = ""; // sẽ override bằng ah_staff_email
                $bccAddress = "<EMAIL>" . "\t" . "<EMAIL>";
                $fromAddress = $FROM_MAIL ?? null;

                if ("" === $toAddress) {
                    $toAddress = $ah_staff_email;
                } else {
                    $toAddress = $toAddress . "\t" . $ah_staff_email;
                }

                // luôn thêm arrange@
                $ccAddress = "<EMAIL>";

                // ジェイキャリーの場合
                if (($m_trans_id ?? "") === "14192") {
                    if ("" === $ccAddress) {
                        $ccAddress = "<EMAIL>";
                    } else {
                        $ccAddress = $ccAddress . "\t" . "<EMAIL>";
                    }
                }

                $contentFile = "admin_transport_change_price.txt";
                $includeNameArray = [
                    "ah_staff_name","trans_name","id","last_deli_price","deli_price","customer_name","fr_aa_place_id","fr_name","fr_addr","fr_tel","to_name","to_addr","to_tel","pos_no",
                    "aa_no", "date_of_payment","car_name","car_no", "undrivable_flg", "tall_flg", "lowdown_flg", "long_flg","plate_cut_flg","plate_no","country","odr_date"
                ];
                $includeValueArray = [
                    $ah_staff_name ?? "", $trans_name ?? "", $transport_id ?? ($tp_id ?? null), $last_deli_price ?? "", $deli_price ?? "",
                    $customer_name ?? "", $fr_aa_place_id ?? "", $fr_name ?? "", $fr_addr ?? "", $fr_tel ?? "",
                    $to_name ?? "", $to_addr ?? "", $to_tel ?? "", $pos_no ?? "", $aa_no ?? "", $date_of_payment ?? "",
                    $car_name ?? "", $car_no ?? "", $this->flagReplace2_ja($undrivable_flg ?? "0"), $this->flagReplace2_ja($tall_flg ?? "0"),
                    $this->flagReplace2_ja($lowdown_flg ?? "0"), $this->flagReplace2_ja($long_flg ?? "0"),
                    $this->flagReplace($plate_cut_flg ?? "0"), $plate_no ?? "", $country ?? "", $odr_date ?? ""
                ];

                $mailResult = $this->sendMailProgram($toAddress, $ccAddress, $bccAddress, $fromAddress, $contentFile, $includeNameArray, $includeValueArray);
            }

            // === プレート発送通知 (plate_send_flg) ===
            if (1 == $plate_send_flg) {
                $ccAddress = "<EMAIL>";
                $toAddress = "";
                $bccAddress = "<EMAIL>";
                $fromAddress = $FROM_MAIL ?? null;

                if ("" === $toAddress) {
                    $toAddress = $ah_staff_email;
                } else {
                    $toAddress = $toAddress . "\t" . $ah_staff_email;
                }

                if (($m_trans_id ?? "") === "14192") {
                    if ("" === $ccAddress) {
                        $ccAddress = "<EMAIL>";
                    } else {
                        $ccAddress = $ccAddress . "\t" . "<EMAIL>";
                    }
                }

                $contentFile2 = "admin_transport_plate.txt";
                $includeNameArray2 = [
                    "ah_staff_name","trans_name","id","plate_send_date","plate_send_co","plate_send_no","customer_name","fr_aa_place_id","fr_name","fr_addr","fr_tel","to_name","to_addr","to_tel","pos_no",
                    "aa_no", "date_of_payment","car_name","car_no", "undrivable_flg", "tall_flg", "lowdown_flg", "long_flg","plate_cut_flg","plate_no","country"
                ];
                $includeValueArray2 = [
                    $ah_staff_name ?? "", $trans_name ?? "", $transport_id ?? ($tp_id ?? null),
                    $plate_send_date ?? "", $plate_send_co ?? "", $plate_send_no ?? "",
                    $customer_name ?? "", $fr_aa_place_id ?? "", $fr_name ?? "", $fr_addr ?? "", $fr_tel ?? "",
                    $to_name ?? "", $to_addr ?? "", $to_tel ?? "", $pos_no ?? "", $aa_no ?? "", $date_of_payment ?? "",
                    $car_name ?? "", $car_no ?? "", $this->flagReplace2_ja($undrivable_flg ?? "0"), $this->flagReplace2_ja($tall_flg ?? "0"),
                    $this->flagReplace2_ja($lowdown_flg ?? "0"), $this->flagReplace2_ja($long_flg ?? "0"),
                    $this->flagReplace($plate_cut_flg ?? "0"), $plate_no ?? "", $country ?? ""
                ];

                $mailResult2 = $this->sendMailProgram($toAddress, $ccAddress, $bccAddress, $fromAddress, $contentFile2, $includeNameArray2, $includeValueArray2);
            }

            // === 新規ファイル添付通知 (file_mail_flg) ===
            if (1 == $file_mail_flg) {
                $trans_name = "";
                $trans_name_note = "";
                $trans_note_email = "";

                if (($m_trans_id ?? "") === "1005") {
                    $trans_name = "東西海運";
                    $trans_name_note = "東西海運株式会社";
                    $trans_note_email = "<EMAIL>";
                } elseif (($m_trans_id ?? "") === "7875") {
                    $trans_name = "キャリーゴール";
                    $trans_name_note = "株式会社キャリーゴール";
                    $trans_note_email = "<EMAIL>";
                } elseif (($m_trans_id ?? "") === "14192") {
                    $trans_name = "ジェイキャリー";
                    $trans_name_note = "株式会社ジェイ・キャリー";
                    $trans_note_email = "<EMAIL>";
                } elseif (($m_trans_id ?? "") === "17078") {
                    $trans_name = "栄港商運";
                    $trans_name_note = "株式会社栄港商運";
                    $trans_note_email = "<EMAIL>";
                }

                $toAddress  = $trans_note_email;
                $ccAddress  = ($ah_staff_email ?? "") . "\t" . "<EMAIL>";
                $bccAddress = "";
                $fromAddress = $FROM_MAIL ?? null;

                if (($m_trans_id ?? "") === "14192") {
                    if ("" === $ccAddress) {
                        $ccAddress = "<EMAIL>";
                    } else {
                        $ccAddress = $ccAddress . "\t" . "<EMAIL>";
                    }
                }

                $contentFile3 = "admin_transport_file.txt";
                $includeNameArray3 = [
                    "id","trans_name_note","customer_name","fr_aa_place_id","fr_name","fr_addr","fr_tel","to_name","to_addr","to_tel","pos_no",
                    "aa_no","date_of_payment","car_name","car_no", "undrivable_flg", "tall_flg", "lowdown_flg", "long_flg","plate_cut_flg","plate_no","country","odr_date","txtcomment_biko"
                ];
                $includeValueArray3 = [
                    $tp_id ?? ($transport_id ?? null), $trans_name_note ?? "", $customer_name ?? "", $fr_aa_place_id ?? "", $fr_name ?? "", $fr_addr ?? "", $fr_tel ?? "",
                    $to_name ?? "", $to_addr ?? "", $to_tel ?? "", $pos_no ?? "", $aa_no ?? "", $date_of_payment ?? "", $car_name ?? "", $car_no ?? "",
                    $this->flagReplace2_ja($undrivable_flg ?? "0"), $this->flagReplace2_ja($tall_flg ?? "0"), $this->flagReplace2_ja($lowdown_flg ?? "0"),
                    $this->flagReplace2_ja($long_flg ?? "0"), $this->flagReplace($plate_cut_flg ?? "0"), $plate_no ?? "", $country ?? "",
                    Carbon::now()->format('Y-m-d H:i:s'), $txtcomment_biko ?? ""
                ];

                $mailResult3 = $this->sendMailAttacheFileProgram(
                    $toAddress,
                    $ccAddress,
                    $bccAddress,
                    $fromAddress,
                    $contentFile3,
                    $includeNameArray3,
                    $includeValueArray3,
                    $trans_file_str ?? ""
                );
            }

            if (($mailResult ?? "send_ok") !== "send_ok" && ($mailResult2 ?? "send_ok") !== "send_ok") {
                // メール送信失敗 (giữ chỗ)
                // logger()->warning('mail send failed', compact('mailResult','mailResult2'));
            }
        }

        if (in_array(($m_trans_id ?? ""), ["7875","1005","14192","17078"], true)) {
            // キャンセル通知
            if (($st_cd ?? "") === "9") {
                $ah_staff_email = "";
                $ah_staff_team  = "";

                $bk_trans_cancel_mail_flg = "";

                $custObj = DB::table('m_customer as mc')
                    ->leftJoin('m_sales_staff as mss', 'mc.ah_sales_id', '=', 'mss.s_id')
                    ->where('mc.id', $m_customer_id ?? null)
                    ->where('mc.del_flg', 0)
                    ->selectRaw("
                        mc.id, mc.cus_name_JP, mc.shipping_prsn_name, mc.ah_sales_id,
                        mss.e_mail, mss.team, mss.s_name,
                        mc.ah_send_mailaddress, mc.ah_sales_mail_flg,
                        mc.bk_trans_note_mail_flg, mc.bk_trans_price_mail_flg,
                        mc.bk_trans_plate_mail_flg, mc.bk_trans_file_mail_flg,
                        mc.bk_trans_cancel_mail_flg
                    ")
                    ->first();

                if ($custObj) {
                    $shipping_prsn_name = $custObj->shipping_prsn_name ?? null;
                    $ah_sales_id        = $custObj->ah_sales_id ?? null;
                    $ah_staff_email     = $custObj->e_mail ?? "";
                    $ah_staff_team      = $custObj->team ?? "";
                    $ah_staff_name      = $custObj->s_name ?? "";

                    $rep_address            = trim($custObj->e_mail ?? "");
                    $rep_address_temp       = trim($custObj->e_mail ?? "");
                    $ah_staff_name          = trim($custObj->s_name ?? "");
                    $ah_sales_mail_flg      = (int)($custObj->ah_sales_mail_flg ?? 0);
                    $ah_send_mailaddress    = trim($custObj->ah_send_mailaddress ?? "");
                    $bk_trans_cancel_mail_flg = (int)($custObj->bk_trans_cancel_mail_flg ?? 0);

                    // キャンセル通知
                    $bk_trans_cancel_mail_addr = "";
                    if (1 == $ah_sales_mail_flg && 1 == $bk_trans_cancel_mail_flg) {
                        if ("" !== $ah_send_mailaddress) {
                            $ah_send_mailaddress = str_replace(";", "\t", $ah_send_mailaddress);
                            $ah_staff_email = $rep_address . "\t" . $ah_send_mailaddress;
                        }
                    }
                }

                // 各社にキャンセルメール送信
                if (($m_trans_id ?? "") === "7875") {
                    $toAddress = "<EMAIL>";
                    $ccAddress = ($ah_staff_email ?? "") . "\t" . "<EMAIL>";
                    $contentFile = "admin_cg_transport_cancel.txt";
                } elseif (($m_trans_id ?? "") === "1005") {
                    $toAddress = "<EMAIL>";
                    $ccAddress = ($ah_staff_email ?? "") . "\t" . "<EMAIL>";
                    $contentFile = "admin_transport_cancel.txt";
                } elseif (($m_trans_id ?? "") === "14192") {
                    $toAddress = "<EMAIL>";
                    $ccAddress = ($ah_staff_email ?? "") . "\t" . "<EMAIL>";
                    $contentFile = "admin_jc_transport_cancel.txt";
                } elseif (($m_trans_id ?? "") === "17078") {
                    $toAddress = "<EMAIL>";
                    $ccAddress = ($ah_staff_email ?? "") . "\t" . "<EMAIL>";
                    $contentFile = "admin_ei_transport_cancel.txt";
                }

                if (($m_trans_id ?? "") === "14192") {
                    if ("" === $ccAddress) {
                        $ccAddress = "<EMAIL>";
                    } else {
                        $ccAddress = $ccAddress . "\t" . "<EMAIL>";
                    }
                }

                $bccAddress  = "<EMAIL>";
                $fromAddress = $FROM_MAIL ?? null;

                if (empty($ccAddress ?? "")) {
                    $ccAddress = $REP_MAIL ?? "";
                }

                $includeNameArray = [
                    "id","customer_name","fr_aa_place_id","fr_name","fr_addr","fr_tel","auction_date", "auc_mail","to_name","to_addr","to_tel","pos_no",
                    "aa_no","date_of_payment","date_of_time","car_name","car_no","tickitems_mail","plate_cut_flg","plate_no","plate_address_mail", "country", "port","odr_date","note"
                ];
                $includeValueArray = [
                    $tp_id ?? ($transport_id ?? null), $customer_name ?? "", $fr_aa_place_id ?? "", $fr_name ?? "", $fr_addr ?? "", $fr_tel ?? "",
                    $auction_date ?? "", $auc_mail, $to_name ?? "", $to_addr ?? "", $to_tel ?? "", $pos_no ?? "", $aa_no ?? "", $date_of_payment ?? "",
                    $date_of_time, $car_name ?? "", $car_no ?? "", $tickitems_mail, $this->flagReplace($plate_cut_flg ?? "0"), $plate_no ?? "",
                    $plate_address_mail, $country ?? "", $port ?? "", Carbon::now()->format('Y-m-d H:i:s'), $txtcomment_biko ?? ""
                ];

                $mailResult = $this->sendMailProgram($toAddress, $ccAddress, $bccAddress, $fromAddress, $contentFile, $includeNameArray, $includeValueArray);
                if (($mailResult ?? "send_ok") !== "send_ok") {
                    // メール送信失敗 (giữ chỗ)
                }
            }

            // 保留通知 (last_st_cd <> "3" and st_cd = 3)
            if (($last_st_cd ?? "") !== "3" && "3" === (string)($st_cd ?? "")) {
                $ah_staff_email = "";
                $ah_staff_team  = "";
                $bk_trans_onhold_mail_flg = "";

                $custObj = DB::table('m_customer as mc')
                    ->leftJoin('m_sales_staff as mss', 'mc.ah_sales_id', '=', 'mss.s_id')
                    ->where('mc.id', $m_customer_id ?? null)
                    ->where('mc.del_flg', 0)
                    ->selectRaw("
                        mc.id, mc.cus_name_JP, mc.shipping_prsn_name, mc.ah_sales_id,
                        mss.e_mail, mss.team, mss.s_name,
                        mc.ah_send_mailaddress, mc.ah_sales_mail_flg,
                        mc.bk_trans_note_mail_flg, mc.bk_trans_price_mail_flg,
                        mc.bk_trans_plate_mail_flg, mc.bk_trans_file_mail_flg,
                        mc.bk_trans_onhold_mail_flg
                    ")
                    ->first();

                if ($custObj) {
                    $shipping_prsn_name = $custObj->shipping_prsn_name ?? null;
                    $ah_sales_id        = $custObj->ah_sales_id ?? null;
                    $ah_staff_email     = $custObj->e_mail ?? "";
                    $ah_staff_team      = $custObj->team ?? "";
                    $ah_staff_name      = $custObj->s_name ?? "";

                    $rep_address         = trim($custObj->e_mail ?? "");
                    $rep_address_temp    = trim($custObj->e_mail ?? "");
                    $ah_staff_name       = trim($custObj->s_name ?? "");
                    $ah_sales_mail_flg   = (int)($custObj->ah_sales_mail_flg ?? 0);
                    $ah_send_mailaddress = trim($custObj->ah_send_mailaddress ?? "");
                    $bk_trans_onhold_mail_flg = (int)($custObj->bk_trans_onhold_mail_flg ?? 0);

                    if (1 == $ah_sales_mail_flg && 1 == $bk_trans_onhold_mail_flg) {
                        if ("" !== $ah_send_mailaddress) {
                            $ah_send_mailaddress = str_replace(";", "\t", $ah_send_mailaddress);
                            $ah_staff_email = $rep_address . "\t" . $ah_send_mailaddress;
                        }
                    }
                }

                if (($m_trans_id ?? "") === "7875") {
                    $toAddress = "<EMAIL>";
                    $ccAddress = ($ah_staff_email ?? "") . "\t" . "<EMAIL>";
                    $contentFile = "admin_cg_transport_onhold.txt";
                } elseif (($m_trans_id ?? "") === "1005") {
                    $toAddress = "<EMAIL>";
                    $ccAddress = ($ah_staff_email ?? "") . "\t" . "<EMAIL>";
                    $contentFile = "admin_transport_onhold.txt";
                } elseif (($m_trans_id ?? "") === "14192") {
                    $toAddress = "<EMAIL>";
                    $ccAddress = ($ah_staff_email ?? "") . "\t" . "<EMAIL>";
                    $contentFile = "admin_jc_transport_onhold.txt";
                } elseif (($m_trans_id ?? "") === "17078") {
                    $toAddress = "<EMAIL>";
                    $ccAddress = ($ah_staff_email ?? "") . "\t" . "<EMAIL>";
                    $contentFile = "admin_ei_transport_onhold.txt";
                }

                if (($m_trans_id ?? "") === "14192") {
                    if ("" === $ccAddress) {
                        $ccAddress = "<EMAIL>";
                    } else {
                        $ccAddress = $ccAddress . "\t" . "<EMAIL>";
                    }
                }

                $fromAddress = $FROM_MAIL ?? null;
                if (empty($ccAddress ?? "")) {
                    $ccAddress = $REP_MAIL ?? "";
                }

                $includeNameArray = [
                    "id","customer_name","fr_aa_place_id","fr_name","fr_addr","fr_tel","to_name","to_addr","to_tel","pos_no",
                    "aa_no","date_of_payment","car_name","car_no","undrivable_flg", "tall_flg", "lowdown_flg", "long_flg","plate_cut_flg","plate_no","odr_date","txtcomment_biko"
                ];
                $includeValueArray = [
                    $tp_id ?? ($transport_id ?? null), $customer_name ?? "", $fr_aa_place_id ?? "", $fr_name ?? "", $fr_addr ?? "", $fr_tel ?? "",
                    $to_name ?? "", $to_addr ?? "", $to_tel ?? "", $pos_no ?? "", $aa_no ?? "", $date_of_payment ?? "",
                    $car_name ?? "", $car_no ?? "", $this->flagReplace2_ja($undrivable_flg ?? "0"), $this->flagReplace2_ja($tall_flg ?? "0"),
                    $this->flagReplace2_ja($lowdown_flg ?? "0"), $this->flagReplace2_ja($long_flg ?? "0"),
                    $this->flagReplace($plate_cut_flg ?? "0"), $plate_no ?? "", Carbon::now()->format('Y-m-d H:i:s'), $txtcomment_biko ?? ""
                ];

                $mailResult = $this->sendMailProgram($toAddress, $ccAddress, $bccAddress ?? "", $fromAddress, $contentFile, $includeNameArray, $includeValueArray);
                if (($mailResult ?? "send_ok") !== "send_ok") {
                    // メール送信失敗 (giữ chỗ)
                }
            }
        }
    }

    // ===== Helpers tương đương trong ASP =====

    private function string_escape_delete(string $s): string
    {
        // loại thẻ & ký tự nguy hiểm cơ bản
        return strip_tags($s);
    }

    private function string_escape_new(string $s): string
    {
        // thay newline thành space
        return trim(preg_replace('/\r\n|\r|\n/', ' ', $s));
    }

    private function flagReplace(string $flag): string
    {
        return "1" === $flag ? "1" : "0";
    }

    private function flagReplace2_ja(string $flag): string
    {
        return "1" === $flag ? "有" : "無";
    }

    // ===== Wrappers tương đương sendMailProgram / sendMailAttacheFileProgram =====
    // Bạn có thể thay thế bằng Mailables + Blade; dưới đây mình giữ giao diện tham số
    private function sendMailProgram(
        string $toTab,
        string $ccTab,
        string $bccTab,
        ?string $from,
        string $contentFile,
        array $includeNames,
        array $includeValues
    ): string {
        $to  = $this->splitTab($toTab);
        $cc  = $this->splitTab($ccTab);
        $bcc = $this->splitTab($bccTab);

        // map biến -> giá trị cho view
        $data = [];
        foreach ($includeNames as $i => $name) {
            $data[$name] = $includeValues[$i] ?? null;
        }

        // ví dụ: dùng 1 view theo tên file (đổi .txt -> blade)
        $view = $this->mapContentFileToView($contentFile);

        Mail::send($view, $data, function ($m) use ($to, $cc, $bcc, $from): void {
            if (!empty($from)) {
                $m->from($from);
            }
            if ($to) {
                $m->to($to);
            }
            if ($cc) {
                $m->cc($cc);
            }
            if ($bcc) {
                $m->bcc($bcc);
            }
            $m->subject(''); // giữ trống như ASP (file template có thể hiển thị tiêu đề)
        });

        return "send_ok";
    }

    private function sendMailAttacheFileProgram(
        string $toTab,
        string $ccTab,
        string $bccTab,
        ?string $from,
        string $contentFile,
        array $includeNames,
        array $includeValues,
        string $trans_file_str
    ): string {
        $to  = $this->splitTab($toTab);
        $cc  = $this->splitTab($ccTab);
        $bcc = $this->splitTab($bccTab);

        $data = [];
        foreach ($includeNames as $i => $name) {
            $data[$name] = $includeValues[$i] ?? null;
        }

        $view = $this->mapContentFileToView($contentFile);

        Mail::send($view, $data, function ($m) use ($to, $cc, $bcc, $from, $trans_file_str): void {
            if (!empty($from)) {
                $m->from($from);
            }
            if ($to) {
                $m->to($to);
            }
            if ($cc) {
                $m->cc($cc);
            }
            if ($bcc) {
                $m->bcc($bcc);
            }
            // đính kèm file theo chuỗi tab
            foreach ($this->splitTab($trans_file_str) as $path) {
                if ('' !== $path) {
                    $m->attach($path);
                }
            }
            $m->subject('');
        });

        return "send_ok";
    }

    private function splitTab(?string $s): array
    {
        if (!$s) {
            return [];
        }
        return array_values(array_filter(array_map('trim', preg_split('/\t+/', $s))));
    }

    private function mapContentFileToView(string $contentFile): string
    {
        // ví dụ: admin_transport_change_price.txt -> emails.admin_transport_change_price
        $base = preg_replace('/\.txt$/', '', $contentFile);
        return 'emails.' . $base;
    }
}
