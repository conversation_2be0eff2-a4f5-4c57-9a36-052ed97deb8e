version: '3.8'

services:
    app:
        build:
            context: .
            dockerfile: .docker/DockerFile/dev/Dockerfile
        container_name: laravel-app
        restart: unless-stopped
        networks:
            - autom-dev
        volumes:
            - ./:/var/www/html
        env_file:
            - ./.env
        ports:
            - '8080:8080'

    sqlserver:
        image: mcr.microsoft.com/mssql/server:2019-latest
        container_name: sqlserver2019
        environment:
            - ACCEPT_EULA=Y
            - MSSQL_SA_PASSWORD=Your_Str0ng@Password
            # - MSSQL_PID=Developer        # Optional: Express, Enterprise, etc.
        ports:
            - '1433:1433'
        volumes:
            - sqlserver_data:/var/opt/mssql
        networks:
            - autom-dev

    db2:
        image: ibmcom/db2
        container_name: db2
        privileged: true
        ports:
            - "50000:50000"
        environment:
            - LICENSE=accept
            - DB2INST1_PASSWORD=Passw0rd
            - DBNAME=TESTDB
        volumes:
            - db2data:/database
        restart: unless-stopped
        networks:
            - autom-dev
        depends_on:
            -   app

    nginx:
        image: nginx:alpine
        container_name: laravel-nginx
        restart: unless-stopped
        ports:
            - "8000:80"
        volumes:
            - ./:/var/www/html
            - ./.docker/nginx/conf.d:/etc/nginx/conf.d
        networks:
            - autom-dev
        depends_on:
            - app

    mailhog:
        image: mailhog/mailhog
        logging:
            driver: 'none'
        ports:
            - 1025:1025
            - 8025:8025
        networks:
            - autom-dev

volumes:
    db2data:
    sqlserver_data:

networks:
    autom-dev:
        driver: bridge
