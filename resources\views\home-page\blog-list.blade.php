@extends('app')

@push('styles')
    @vite('resources/css/blog-list.css')
@endpush

@section('title', $pageTitle)

@section('content')
    <div class="content w-full overflow-hidden px-[15px]">
        <div
            class="2sm:max-w-[720px] mx-auto mt-[100px] mb-[60px] flex max-w-[540px] flex-wrap md:max-w-[960px] lg:mt-[150px] lg:mb-[90px] lg:max-w-[1140px]"
        >
            <div class="relative w-full px-[15px]">
                <h1 class="blog_title">{{ $headingTitle }}</h1>
            </div>
            <div class="blog-contents relative px-[15px]">
                <div class="aos-init contents_info w-full pt-5" data-aos="fade-right" data-aos-duration="1000">
                    {!! $blogContent !!}
                </div>
                <div class="blog-tags aos-init" data-aos="fade-left" data-aos-duration="1000">
                    <div class="srch_area">
                        <input
                            type="text"
                            id="blog-searchWord"
                            value="{{ in_array($searchTag, $blogTags) ? '' : $searchTag }}"
                            placeholder="ブログ内検索"
                            class="bg-white placeholder-gray-200"
                        />
                        <button id="blog-searchBtn"><i class="fas fa-search"></i></button>
                    </div>
                    <ul>
                        @foreach ($blogTags as $val)
                            <li class="{{ $searchTag === $val ? 'active' : '' }}">
                                @php
                                    $locale = app()->getLocale();
                                    $url =
                                        $locale === 'ja'
                                            ? route('blog-list', ['tags' => $val])
                                            : route('localized.blog-list', ['locale' => $locale, 'tags' => $val]);
                                @endphp

                                <a href="{{ $url }}">{{ $val }}</a>
                            </li>
                        @endforeach

                        @if (! empty($searchTag))
                            <li class="tag-clear">
                                <a href="?tags=">タグ解除</a>
                            </li>
                        @endif
                    </ul>
                </div>
            </div>
        </div>
    </div>
@endsection

@once
    @push('scripts')
        <script>
            $(function () {
                document.querySelectorAll('.blog-tags ul li').forEach(function (button) {
                    button.addEventListener('click', function () {
                        var link = this.querySelector('a')
                        if (link) link.click()
                    })
                })
                $('#blog-searchBtn').click(function () {
                    var searchWord = $('#blog-searchWord').val().trim()
                    if (searchWord) {
                        var currentUrl = window.location.href.split('?')[0]
                        window.location.href = currentUrl + '?tags=' + encodeURIComponent(searchWord)
                    }
                })
                $('.news-item').click(function () {
                    var link = this.querySelector('a')
                    if (link) {
                        window.location.href = link.href
                    }
                })
            })
        </script>
    @endpush
@endonce
