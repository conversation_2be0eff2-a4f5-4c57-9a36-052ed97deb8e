<?php

declare(strict_types=1);

namespace App\Http\Controllers\HomePage;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\App;

class CompanyController extends Controller
{
    public function index()
    {
        $isJa = 'ja' === App::getLocale();
        $slogans = [
            1 => __('home-page/company.slogan_1'),
            2 => __('home-page/company.slogan_2'),
            3 => __('home-page/company.slogan_3'),
            4 => __('home-page/company.slogan_4'),
        ];
        $staffBoxes = [
            ['en' => 'Accurate', 'ja' => '正確性', 'content' => __('home-page/company.accurate_content')],
            ['en' => 'Understand', 'ja' => '理解', 'content' => __('home-page/company.understand_content')],
            ['en' => 'Try Hard', 'ja' => '頑張り', 'content' => __('home-page/company.try_hard_content')],
            ['en' => 'Opportunity', 'ja' => 'チャンス', 'content' => __('home-page/company.opportunity_content')],
            ['en' => 'Helpful', 'ja' => '親切', 'content' => __('home-page/company.helpful_content')],
            ['en' => 'Useful', 'ja' => '役立つ', 'content' => __('home-page/company.useful_content')],
            ['en' => 'Best', 'ja' => '高める', 'content' => __('home-page/company.best_content')],
        ];
        $offices = [
            ['title' => __('home-page/company.office_title_1'), 'office' => __('home-page/company.office_company_1'), 'address' => __('home-page/company.office_address_1'), 'url' => 'https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d13151.053910312057!2d135.3960426!3d34.5088801!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x6000c44106eaaaab%3A0xf1d180d120078b8c!2z44GN44KJ44KJ44K744Oz44K_44O844OT44Or!5e0!3m2!1sja!2sjp!4v1722330642888!5m2!1sja!2sjp'],
            ['title' => __('home-page/company.office_title_1'), 'office' => __('home-page/company.office_company_2'), 'address' => __('home-page/company.office_address_2'), 'url' => 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3261.440773952163!2d136.90011117708917!3d35.17056515785503!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x6003772accf80791%3A0xeffc7330c666742b!2z44Ki44O844Kv5qCE6Yym44OL44Ol44O844OT44K444ON44K544OT44Or!5e0!3m2!1sja!2sjp!4v1722331001030!5m2!1sja!2sjp'],
            ['title' => __('home-page/company.office_title_3'), 'office' => __('home-page/company.office_company_3'), 'address' => __('home-page/company.office_address_3'), 'url' => 'https://www.google.com/maps/embed?pb=!1m17!1m12!1m3!1d798.621598855986!2d174.63436926969692!3d-36.80686292449685!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m2!1m1!2zMzbCsDQ4JzI0LjciUyAxNzTCsDM4JzA2LjEiRQ!5e0!3m2!1sja!2sjp!4v1722331394115!5m2!1sja!2sjp'],
            ['title' => __('home-page/company.office_title_4'), 'office' => __('home-page/company.office_company_4'), 'address' => __('home-page/company.office_address_4'), 'url' => 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3310.1645436853973!2d150.99562917733115!3d-33.93689582259191!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x6b12be607ad7c6a9%3A0x34b5f8b18093c209!2zOC80MCBNYXJpZ29sZCBTdCwgUmV2ZXNieSBOU1cgMjIxMiDjgqrjg7zjgrnjg4jjg6njg6rjgqI!5e0!3m2!1sja!2sjp!4v1722331545098!5m2!1sja!2sjp'],
            ['title' => __('home-page/company.office_title_5'), 'office' => __('home-page/company.office_company_5'), 'address' => __('home-page/company.office_address_5'), 'url' => 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3530.5352819491186!2d153.22949497716547!3d-27.76247632924728!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x6b916acbf068f72b%3A0x55d5ebf21f5a278!2zMTkgQWxsb3kgU3QsIFlhdGFsYSBRTEQgNDIwNyDjgqrjg7zjgrnjg4jjg6njg6rjgqI!5e0!3m2!1sja!2sjp!4v1730335433123!5m2!1sja!2sjp'],
            ['title' => __('home-page/company.office_title_6'), 'office' => __('home-page/company.office_company_6'), 'address' => __('home-page/company.office_address_6'), 'url' => 'https://www.google.com/maps/embed?pb=!1m17!1m12!1m3!1d595.504047249623!2d-2.74516023027229!3d53.34296639893938!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m2!1m1!2zNTPCsDIwJzM0LjciTiAywrA0NCc0MC4zIlc!5e0!3m2!1sja!2sjp!4v1730335718366!5m2!1sja!2sjp'],
            ['title' => __('home-page/company.office_title_7'), 'office' => __('home-page/company.office_company_7'), 'address' => __('home-page/company.office_address_7'), 'url' => 'https://www.google.com/maps/embed?pb=!1m17!1m12!1m3!1d3355.611343873324!2d-97.05094702296354!3d32.74950938538901!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m2!1m1!2zMzLCsDQ0JzU4LjIiTiA5N8KwMDInNTQuMSJX!5e0!3m2!1sja!2sjp!4v1730336075903!5m2!1sja!2sjp'],
            ['title' => __('home-page/company.office_title_8'), 'office' => __('home-page/company.office_company_8'), 'address' => __('home-page/company.office_address_8'), 'url' => 'https://www.google.com/maps/embed?pb=!1m17!1m12!1m3!1d3988.746767485441!2d103.75069707674947!3d1.3279373616421881!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m2!1m1!2zMcKwMTknNDAuNiJOIDEwM8KwNDUnMTEuOCJF!5e0!3m2!1sja!2sjp!4v1722331639731!5m2!1sja!2sjp'],
        ];
        return view('home-page.company', compact('offices', 'slogans', 'staffBoxes', 'isJa'));
    }
}
