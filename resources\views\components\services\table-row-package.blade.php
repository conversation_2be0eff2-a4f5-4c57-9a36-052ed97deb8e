@php
    /**
     * @var string $title
     * @var string $description
     */
    $contentCommon = '<p class="mx-auto my-[0.5em] md:py-[0.3em]"><i class="fas fa-check-circle text-15 lg:text-xl"></i></p>';
    $content1 = $contentCommon;
    $content2 = $contentCommon;
    $content3 = $contentCommon;
    $content4 = $contentCommon;
    $contentMinus = '<p class="mx-auto my-[0.5em] md:py-[0.3em]"><i class="fas fa-minus text-15 lg:text-xl text-gray-200"></i></p>';
    $contentCoin = '<p class="mx-auto my-[0.5em] md:py-[0.3em]"><i class="fas fa-coins text-15 lg:text-xl text-gray-200"></i></p>';
    $localizedSchedule = fn ($country) => app()->getLocale() === 'ja' ? route('schedule', ['country' => $country]) : route('localized.schedule', ['locale' => 'en', 'country' => $country]);
    switch ($title) {
        case __('home-page/d2d_package.quarantine'):
            $content3 = $contentMinus;
            $content4 = $contentMinus;
            break;
        case __('home-page/d2d_package.nzta'):
            $content2 = $contentMinus;
            $content3 = $contentMinus;
            $content4 = $contentMinus;
            break;
        case __('home-page/d2d_package.odo_meter'):
            $content2 = $contentMinus;
            $content3 = $contentMinus;
            $content4 = $contentMinus;
            break;
        case __('home-page/d2d_package.import_inland'):
            $content2 = $contentCoin;
            $content3 = $contentCoin;
            $content4 = $contentCoin;
            break;
        case __('home-page/d2d_package.isf'):
            $content1 = $contentMinus;
            $content2 = $contentMinus;
            $content3 = $contentMinus;
            break;
        case '':
            $content1 = '<a href="' . $localizedSchedule(6400) . '" class="2md:text-15 2md:pt-[1em] 2md:px-[1em] 2md:pb-[0.5em] leading-loose hover:opacity-80 text-white bg-[#ff8600] pt-[1.8em] pr-[0.5em] pb-[1.5em] pl-[0.5em] text-10 text-center 2sm:rounded-[15px] 2sm:text-xs 2sm:pt-[0.5em] 2sm:px-[0.5em] 2sm:pb-[0.3em] 2sm:my-[0.5em] 2sm:mx-auto"><i class="fas fa-ship text-15 lg:text-xl"></i><br>' . __('home-page/d2d_package.schedule') . '</a>';
            $content2 = '<a href="' . $localizedSchedule(6100) . '" class="2md:text-15 2md:pt-[1em] 2md:px-[1em] 2md:pb-[0.5em] leading-loose hover:opacity-80 text-white bg-[#40992d] pt-[1.8em] pr-[0.5em] pb-[1.5em] pl-[0.5em] text-10 text-center 2sm:rounded-[15px] 2sm:text-xs 2sm:pt-[0.5em] 2sm:px-[0.5em] 2sm:pb-[0.3em] 2sm:my-[0.5em] 2sm:mx-auto"><i class="fas fa-ship text-15 lg:text-xl"></i><br>' . __('home-page/d2d_package.schedule') . '</a>';
            $content3 = '<a href="' . $localizedSchedule(4400) . '" class="2md:text-15 2md:pt-[1em] 2md:px-[1em] 2md:pb-[0.5em] leading-loose hover:opacity-80 text-white bg-[#27B2C5] pt-[1.8em] pr-[0.5em] pb-[1.5em] pl-[0.5em] text-10 text-center 2sm:rounded-[15px] 2sm:text-xs 2sm:pt-[0.5em] 2sm:px-[0.5em] 2sm:pb-[0.3em] 2sm:my-[0.5em] 2sm:mx-auto"><i class="fas fa-ship text-15 lg:text-xl"></i><br>' . __('home-page/d2d_package.schedule') . '</a>';
            $content4 = '<a href="' . $localizedSchedule(1202) . '" class="2md:text-15 2md:pt-[1em] 2md:px-[1em] 2md:pb-[0.5em] leading-loose hover:opacity-80 text-white bg-red-450 pt-[1.8em] pr-[0.5em] pb-[1.5em] pl-[0.5em] text-10 text-center 2sm:rounded-[15px] 2sm:text-xs 2sm:pt-[0.5em] 2sm:px-[0.5em] 2sm:pb-[0.3em] 2sm:my-[0.5em] 2sm:mx-auto"><i class="fas fa-ship text-15 lg:text-xl"></i><br>' . __('home-page/d2d_package.schedule') . '</a>';
            break;
    }
@endphp

<div class="flex w-full flex-wrap justify-between border-b border-gray-100 text-base text-gray-700">
    <div class="flex w-2/5 items-center bg-[#FFD9D7] font-bold md:w-1/5 md:bg-transparent">
        @if ($title)
            <p class="m-[0.5em] text-xs md:py-[0.3em] md:text-base">{!! $title !!}</p>
        @endif
    </div>
    <div class="border-gray-25 flex w-[15%] items-center border-r text-[1.3em] text-[#ff8600] md:border-none">
        {!! $content1 !!}
    </div>
    <div class="flex w-[15%] items-center text-[1.3em] text-[#40992d]">
        {!! $content2 !!}
    </div>
    <div class="flex w-[15%] items-center text-[1.3em] text-[#27B2C5]">
        {!! $content3 !!}
    </div>
    <div class="text-red-450 flex w-[15%] items-center text-[1.3em]">
        {!! $content4 !!}
    </div>
    <div class="inline-block w-full pl-[0.5em] text-sm md:w-1/5">
        @if ($description || ! $title)
            <p
                class="md: border-t border-dashed border-gray-100 py-[0.5em] text-xs leading-normal md:my-[0.5em] md:border-none md:py-[0.3em] md:text-sm"
            >
                {!! $description !!}
            </p>
        @endif
    </div>
</div>
