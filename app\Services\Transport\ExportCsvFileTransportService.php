<?php

declare(strict_types=1);

namespace App\Services\Transport;

use App\Constants\ApiCodes;
use App\Enums\Transport\Statuses;
use App\Enums\UserRole;
use App\Exceptions\ApiException;
use App\Jobs\StoreActionLogJob;
use App\Repositories\sqlServer\TransportRepository;
use App\Repositories\sqlServer\UserRepository;
use App\Traits\CommonTrait;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\StreamedResponse;

class ExportCsvFileTransportService
{
    use CommonTrait;

    private TransportRepository $transportRepository;
    private UserRepository $userRepository;

    public function __construct(
        TransportRepository $transportRepository,
        UserRepository $userRepository,
    ) {
        $this->transportRepository = $transportRepository;
        $this->userRepository = $userRepository;
    }

    public function checkDownloadCsv(array $data): array
    {
        $hasDuplicateRefNo = $this->transportRepository->builderQueryCheckDuplicateRefNo($data)->exists();

        if ($hasDuplicateRefNo) {
            return [
                'status' => 'NG',
                'message' => '同一車台番号でREFの複数登録が存在します。',
            ];
        }

        return [
            'status' => 'OK',
            'message' => '',
        ];
    }

    public function exportCsv(array $params): StreamedResponse
    {
        // Validate token
        $dataDecodeToken = $this->validateToken($params['token'] ?? '');

        $user = $this->userRepository->findBy([
            'id' => $dataDecodeToken['user_id']
        ]);

        // Set user for Auth
        Auth::setUser($user);
        $query = $this->transportRepository->builderQueryExportCSV($params);

        // Generate filename
        $timestamp = Carbon::now()->format('nYjgisA');
        $filename = "rikusou{$timestamp}.txt";

        $headers = [
            'Content-Type' => 'text/plain; charset=Shift_JIS',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0'
        ];

        $callback = function () use ($query, $params): void {
            $file = fopen('php://output', 'w');

            // Write CSV header
            $this->writeCsvHeader($file, $params);

            // Process data in chunks
            $query->chunk(500, function ($transports) use ($file, $params): void {
                $this->processCsvChunk($transports, $file, $params);
            });

            fclose($file);
        };

        // Insert action log
        dispatch(new StoreActionLogJob(
            pageName: '陸送依頼ダウンロード',
            dateTime: now(),
            userId: (int) ($dataDecodeToken['user_id']),
            ipAddress: request()->ip()
        ));

        return new StreamedResponse($callback, 200, $headers);
    }

    private function validateToken(string $token)
    {
        $cacheData = Cache::store('database')->pull($token);

        if (!$cacheData) {
            throw new ApiException((string)ApiCodes::INVALID_TOKEN);
        }

        return $cacheData;
    }

    private function writeCsvHeader($file, array $params): void
    {
        $isAdmin = Auth::user()->hn_cd == UserRole::admin->value;
        $customerId = Auth::user()->id;

        $headers = [];

        if ($isAdmin) {
            $headers = [
                '陸送会社', '申込日時', '陸送ID', '業者ID', '引取先名'
            ];
        } else {
            $headers = [
                '申込日時', '陸送ID', '引取先名'
            ];
        }

        $headers = array_merge($headers, [
            '搬入先名', 'POS番号', '申込会社', '出品番号', '車名', '車台番号', '担当者'
        ]);

        if ($isAdmin) {
            $headers[] = 'コメント';
            $headers[] = 'AUTOHUBメモ';
        } else {
            $headers[] = 'コメント';
        }

        $headers = array_merge($headers, [
            '引取先住所', '引取先電話番号', '搬入先住所', '搬入先電話番号', '現在状態（ステータス）'
        ]);

        if ($isAdmin) {
            $headers = array_merge($headers, [
                '陸送代金（原価）', '陸送代金（販売）', '搬出予定日', '搬入予定日', '搬入日', '引取先AA会場ID',
                'プレート切', 'プレート番号', '発送日', '発送業者', '伝票番号', '整理番号', '仕向国',
                'プラン名', 'D2D', '船名[VOY]', 'ETD', 'ETA', '船積完了日', '複数REFNOあり', '会員ID'
            ]);
        } elseif (in_array($customerId, ['7875', '1005', '17078'])) {
            $headers = array_merge($headers, [
                '陸送代金', '搬出予定日', '搬入予定日', '搬入日', '引取先AA会場ID',
                'プレート切', 'プレート番号', '発送日', '発送業者', '伝票番号', '仕向国'
            ]);
        } else {
            $headers = array_merge($headers, [
                '陸送代金', '搬出予定日', '搬入予定日', '搬入日', '引取先AA会場ID',
                'プレート切', 'プレート番号', '発送日', '発送業者', '伝票番号'
            ]);
        }

        fputcsv($file, $headers, "\t");
    }

    /**
     * Process CSV chunk
     */
    private function processCsvChunk(Collection $transports, $file, array $params): void
    {
        $isAdmin = Auth::user()->hn_cd == UserRole::admin->value;
        $customerId = Auth::user()->id;

        foreach ($transports as $transport) {
            $row = $this->mapTransportToCsvRow($transport, $isAdmin, $customerId);
            fputcsv($file, $row, "\t");
        }
    }

    /**
     * Map transport object to CSV row
     */
    private function mapTransportToCsvRow($transport, bool $isAdmin, $customerId): array
    {
        $row = [];

        // AA Place ID formatting
        $frAaPlaceId = 0 == $transport->fr_aa_place_id ? '' : $transport->fr_aa_place_id;

        // Clean memo and notes
        $ahMemo = $this->cleanText($transport->note ?? '');
        $notes = $this->cleanNotes($transport->notes ?? '');

        // Transport company name
        $mTransName = $this->getTransportCompanyName($transport->m_trans_id);

        if ($isAdmin) {
            $row = [
                $mTransName,
                $this->formatDate($transport->odr_date),
                $transport->id,
                $transport->tp_id,
                $transport->fr_name
            ];
        } else {
            $row = [
                $transport->odr_date,
                $transport->tp_id,
                $transport->fr_name
            ];
        }

        $row = array_merge($row, [
            $transport->to_name,
            $transport->pos_no,
            $transport->cus_name_JP,
            $transport->aa_no,
            $transport->car_name,
            $transport->car_no,
            $transport->ah_sales_name
        ]);

        if ($isAdmin) {
            $row[] = $notes;
            $row[] = $ahMemo;
        } else {
            $row[] = $notes;
        }

        // dump($transport->st_cd);
        $row = array_merge($row, [
            $transport->fr_addr,
            $transport->fr_tel,
            $transport->to_addr,
            $transport->to_tel,
            $this->getStatusText($transport->st_cd)
        ]);

        if ($isAdmin) {
            $d2dKbn = '2' == $transport->d2d_kbn ? 'YES' : '';
            $tvRegCnt = $transport->tv_reg_cnt > 1 ? '〇' : '';

            $row = array_merge($row, [
                $transport->deli_price,
                $transport->sales_price,
                $transport->from_plan_date,
                $transport->to_plan_date,
                $transport->to_date,
                $frAaPlaceId,
                $this->flagReplace($transport->plate_cut_flg),
                $transport->plate_no,
                $transport->plate_send_date,
                $transport->plate_send_co,
                $transport->plate_send_no,
                $transport->ref_no,
                $transport->country,
                $transport->service_name,
                $d2dKbn,
                $transport->vessel_voy,
                $transport->etd,
                $transport->eta,
                $transport->fin_ship,
                $tvRegCnt,
                $transport->m_customer_id
            ]);
        } elseif (in_array($customerId, ['7875', '1005', '17078'])) {
            $row = array_merge($row, [
                $transport->deli_price,
                $transport->from_plan_date,
                $transport->to_plan_date,
                $transport->to_date,
                $frAaPlaceId,
                $this->flagReplace($transport->plate_cut_flg),
                $transport->plate_no,
                $transport->plate_send_date,
                $transport->plate_send_co,
                $transport->plate_send_no,
                $transport->country
            ]);
        } else {
            $row = array_merge($row, [
                $transport->deli_price,
                $transport->from_plan_date,
                $transport->to_plan_date,
                $transport->to_date,
                $frAaPlaceId,
                $this->flagReplace($transport->plate_cut_flg),
                $transport->plate_no,
                $transport->plate_send_date,
                $transport->plate_send_co,
                $transport->plate_send_no
            ]);
        }

        return $row;
    }

    /**
     * Get transport company name
     */
    private function getTransportCompanyName($mTransId): string
    {
        return match ($mTransId) {
            '1005' => '東西海運',
            '7875' => 'キャリーゴール',
            '8107' => 'ロジコ',
            '8973' => 'ドリームインターナショナル',
            '14192' => 'ジェイキャリー',
            '17078' => '栄港商運',
            default => ''
        };
    }

    /**
     * Format date như trong ASP
     */
    private function formatDate($date): string
    {
        if (!$date) {
            return '';
        }

        $carbon = Carbon::parse($date);
        return $carbon->format('Y/n/j');
    }

    /**
     * Clean text - remove line breaks
     */
    private function cleanText(string $text): string
    {
        return str_replace(["\r\n", "\r", "\n"], '', $text);
    }

    /**
     * Clean notes - remove various types of line breaks
     */
    private function cleanNotes(string $notes): string
    {
        if (empty($notes)) {
            return '';
        }

        $cleaned = str_replace([
            "\r\n", "\r", "\n",
            "&#x0D;", "&#x0A;",
            "\\r\\n", "\\r", "\\n"
        ], '', $notes);

        return $cleaned;
    }

    /**
     * Replace flag values
     */
    private function flagReplace($flag): string
    {
        return $flag ? 'あり' : 'なし';
    }

    private function getStatusText($statusCode): string
    {
        try {
            $status = Statuses::from($statusCode);
            return $status->toJapaneseLabel();
        } catch (Exception $e) {
            return '';
        }
    }
}
