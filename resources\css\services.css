body {
    overflow-x: hidden;
}

.linebot-bg {
    background: linear-gradient(to bottom right, #2af598, #009efd);
    width: 100%;
    height: auto;
    display: flex;
    flex-direction: column;
    padding: 1em 0.5em;
    border-radius: 3px;
    margin: 0 auto 3em auto;
    position: relative;
    color: white;
    font-weight: bold;
}

@media (min-width: 576px) {
    .linebot-bg {
        background: url("../../public/images/services/linebot_back.png") no-repeat;
        background-size: contain;
        background-position: center;
        width: 510px;
        height: 350px;
        padding: 0;
        display: block;
    }

    #section2, #section7 {
        background: linear-gradient(0deg, #fff, #f7fdf2, #fff, transparent);
    }

    #section3, #section8, #section11 {
        background: linear-gradient(0deg, #fff, #eef4f9, #fff, transparent);
    }

    #section4, #section9 {
        background: linear-gradient(0deg, #fff, #fff7f7, #fff, transparent);
    }

    #section5, #section10 {
        background: linear-gradient(0deg, #fff, #efedfd, #fff, transparent);
    }

    #section1, #section6 {
        background: linear-gradient(0deg, #fff, #fdfaf1, #fff, transparent);
    }
}

@media (min-width: 768px) {
    .linebot-bg {
        width: 700px;
        height: 500px;
    }
}

@media (min-width: 992px) {
    .linebot-bg {
        width: 100%;
        height: 600px;
        margin: 0 auto 5em auto;
    }
}

@media screen and (max-width: 576px) {
    input[type="checkbox"]:checked ~ label i {
        transform: rotate(90deg);
    }

    li input[type="checkbox"]:checked ~ label span:last-of-type {
        display: block;
        width: 100%;
        font-weight: normal;
    }
}
