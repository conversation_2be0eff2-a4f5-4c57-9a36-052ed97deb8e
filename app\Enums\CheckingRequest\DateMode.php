<?php

declare(strict_types=1);

namespace App\Enums\CheckingRequest;

enum DateMode: int
{
    case eventDate = 1;
    case orderDate = 2;
    case requestDate = 3;

    public static function getAllValues(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function getName(): string
    {
        return match ($this) {
            self::eventDate => '開催日',
            self::orderDate => '受注日',
            self::requestDate => '検査依頼日',
        };
    }
}
