<?php

declare(strict_types=1);

namespace App\Models\sqlServer\Trait\User;

use App\Enums\UserRole;
use Illuminate\Database\Eloquent\Builder;

trait FilterTrait
{
    public function scopeCustomerName(Builder $query, mixed $strCustomerName): Builder
    {
        return $query->where('m_customer.cus_Name_JP', 'like', '%' . $strCustomerName . '%')
            ->orWhere('m_customer.cus_Name_kana', 'like', '%' . $strCustomerName . '%');
    }

    public function scopeCustomerNameEn(Builder $query, mixed $strCustomerNameEn): Builder
    {
        return $query->where('m_customer.cus_Name_EN', 'like', '%' . $strCustomerNameEn . '%');
    }

    public function scopeCustomerId(Builder $query, mixed $customerId): Builder
    {
        return $query->where('m_customer.id', $customerId);
    }

    public function scopeChChargeSale(Builder $query, mixed $chChargeSale): Builder
    {
        $optionOther = [0, 99];
        $query = $query->whereIn('m_customer.exchange_flg', $optionOther);
        // option 0: all, option 99: not charge sale
        if ($chChargeSale && !in_array($chChargeSale, $optionOther)) {
            return $query->where('m_customer.ah_sales_id', $chChargeSale);
        }
        return $query->where(function ($query) {
            $query->where('m_customer.ah_sales_id', 0)->orWhereNull('m_customer.ah_sales_id');
        });
    }

    public function scopeTransportationMember(Builder $query, mixed $transportationMember): Builder
    {
        if ((bool) $transportationMember) {
            return $query->whereIn('m_customer.hn_cd', [UserRole::admin->value, UserRole::member->value, UserRole::sameIndustry->value]);
        }
        return $query;
    }
}
