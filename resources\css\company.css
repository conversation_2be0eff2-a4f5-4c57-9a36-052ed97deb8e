.slogan {
    position: relative;
    background: linear-gradient(-135deg, #fdf6f7, #fff, #fdf6f7);
    padding: 20px 25px;
    border-left: solid 77px #dc3545;
    font-size: 20px;
    font-weight: bold;
    margin: 10px;
    box-shadow: 5px 5px 10px -2px grey;
    border-top: solid 1px #dc3545;
    border-bottom: solid 1px #dc3545;
    border-right: solid 8px #dc3545;
}

.slogan:before {
    position: absolute;
    color: white;
    left: -52px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 28px;
}

.slogan.slogan_1:before {
    content: "１";
}

.slogan.slogan_2:before {
    content: "２";
}

.slogan.slogan_3:before {
    content: "３";
}

.slogan.slogan_4:before {
    content: "４";
}

@media screen and (max-width: 576px) {
    .slogan {
        font-size: 16px;
    }
}

@media screen and (min-width: 576px) and (max-width: 767px) {
    .slogan {
        font-size: 16px;
    }
}

.staff_box {
    border: solid 1px #aaa;
    padding: 20px 15px;
    height: 230px;
    margin-bottom: 30px;
    box-shadow: 7px 7px 10px -2px grey;
    border-top: solid 1px #dc3545;
    border-bottom: solid 1px #dc3545;
    background: linear-gradient(-135deg, #fff, #f2f2f2, #fff);
    border-radius: 5px;
}

@media screen and (max-width: 767px) {
    .staff_box {
        padding: 15px 10px;
        height: 180px;
        margin-bottom: 10px;
    }
}

@media screen and (min-width: 768px) and (max-width: 991px) {
    .staff_box {
        padding: 15px 10px;
        height: 225px;
        margin-bottom: 10px;
    }
}

@media screen and (min-width: 992px) and (max-width: 1199px) {
    .staff_box {
        padding: 20px 10px;
    }
}

#company_profile01 {
    background-color: #fff;
    padding: 2em 2em 4em 2em;
    position: relative;
    z-index: 1;
    color: #444;
    margin-bottom: 40px;
    border-radius: 5px;
}

#company_profile01::before {
    background-color: rgba(255, 255, 255, .5);
    left: 0;
    top: 0;
    z-index: -1;
}

#company_profile01::after {
    background: linear-gradient(-135deg, #94bdea, #b0d0f3, #d0e4fb, #b0d0f3, #94bdea);
    top: 5px;
    left: 5px;
    z-index: -2;
}

#company_profile01::before, #company_profile01::after {
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, .3);
    content: "";
    height: 100%;
    position: absolute;
    width: 100%;
}

#company_profile02 {
    background-color: #fff;
    padding: 2em 2em 4em 2em;
    position: relative;
    z-index: 1;
    color: #444;
    margin-bottom: 40px;
}

#company_profile02::before {
    background-color: rgba(255, 255, 255, .5);
    left: 0;
    top: 0;
    z-index: -1;
}

#company_profile02::after {
    background: linear-gradient(-135deg, #f99ba4, #f9bec4, #fdd2d6, #f9bec4, #f99ba4);
    top: 5px;
    left: 5px;
    z-index: -2;
}

#company_profile02::before, #company_profile02::after {
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, .3);
    content: "";
    height: 100%;
    position: absolute;
    width: 100%;
}
