<?php

declare(strict_types=1);

namespace App\Enums\Transport;

enum DatePaymentTime: int
{
    case AM = 1;
    case PM = 2;
    case CAN_BE_CARRIED_OUT_BEFORE_PAYMENT = 3;

    public static function getNameTransId(string $mTransId): string
    {
        return match ($mTransId) {
            self::AM->value => '午前',
            self::PM->value => '午後',
            self::CAN_BE_CARRIED_OUT_BEFORE_PAYMENT->value => '入金前搬出可能',
        };
    }

    public static function getAllValues(): array
    {
        return array_column(self::cases(), 'value');
    }
}
