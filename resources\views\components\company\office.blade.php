@php
    /**
     * @var int $index
     * @var string $title
     * @var string $office
     * @var string $address
     * @var string $url
     */
    $classDashed = $index !== 0 ? 'border-t border-dashed border-[#adb5bd] !mt-[10px] pt-[25px] ' : '';
@endphp

<div class="{{ $classDashed }} 2sm:mt-5 mx-[-15px] mt-[10px] flex flex-wrap md:mt-[30px]">
    <div class="relative w-full px-[15px] md:max-w-1/2 md:flex-none md:basis-1/2">
        <h4 class="text-lg font-bold md:text-2xl">{{ $title }}</h4>
        <h4 class="text-lg font-bold md:text-2xl">{{ $office }}</h4>
        <br />
        {!! $address !!}
        @if ($index === 2)
            URL :
            <a
                href="https://www.autohub.co/"
                class="bg-transparent text-blue-500 no-underline hover:text-[#0056b3] hover:underline"
                target="_blank"
            >
                https://www.autohub.co/
            </a>
            <br />
        @endif
    </div>
    <div class="relative w-full px-[15px] md:max-w-1/2 md:flex-none md:basis-1/2">
        <p class="mb-4">
            <iframe
                src="{{ $url }}"
                class="mt-5 h-[290px] w-full max-w-[480px] border-0 md:mt-0"
                allowfullscreen=""
                loading="lazy"
                referrerpolicy="no-referrer-when-downgrade"
            ></iframe>
        </p>
    </div>
</div>
