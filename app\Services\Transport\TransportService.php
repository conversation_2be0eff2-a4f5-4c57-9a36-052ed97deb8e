<?php

declare(strict_types=1);

namespace App\Services\Transport;

use App\Constants\ApiCodes;
use App\Enums\ApiKey;
use App\Enums\Transport\Statuses;
use App\Enums\Transport\TransIdDefault;
use App\Enums\UserRole;
use App\Exceptions\ApiException;
use App\Models\as400\VehicleTransport;
use App\Models\sqlServer\TransportNote;
use App\Repositories\sqlServer\TransportRepository;
use App\Repositories\sqlServer\UserRepository;
use App\Traits\CommonTrait;
use Illuminate\Http\Response;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;

class TransportService
{
    use CommonTrait;

    private TransportRepository $transportRepository;
    private UserRepository $userRepository;
    private SendMailTransportService $sendMailTransportService;

    public function __construct(
        TransportRepository $transportRepository,
        UserRepository $userRepository,
        SendMailTransportService $sendMailTransportService,
        private VehicleTransport $vehicleTransportModel,
        private TransportNote $transportNoteModel,
        private CheckingFileOldHubNetService $checkingFileOldHubNetService
    ) {
        $this->transportRepository = $transportRepository;
        $this->userRepository = $userRepository;
        $this->sendMailTransportService = $sendMailTransportService;
    }

    public function getInfoDetailTransport(int $id)
    {
        $transport = $this->transportRepository->getInfoDetailTransport($id);
        if ($transport) {
            return $transport;
        }
        throw new ApiException(httpStatus: Response::HTTP_NOT_FOUND, errorCode: (string)ApiCodes::HTTP_NOT_FOUND, messageCustom: '陸送情報の取得に失敗しました。');

    }

    public function updateInfoDetailTransport(int $id, array $params = [])
    {
        $transportOrigin = $this->transportRepository->getInfoDetailTransport($id);

        // Transform data
        $data = array_merge(Arr::only($params, [
            'to_plan_date',
            'to_date',
            'from_plan_date',
            'plate_send_date',
            'plate_send_co',
            'plate_send_no',
            'deli_price',
            'sales_price'
        ]), [
            'up_owner' => Auth::user()->id,
        ]);

        if (Arr::get($data, 'deli_price') && !Arr::get($data, 'sales_price')) {
            $data['sales_price'] = Arr::get($data, 'deli_price');
        }

        // Clear sales_price if id in TransIdDefault::LOGICO or TransIdDefault::DREAM_INTERNATIONAL
        if (in_array($id, [TransIdDefault::TARGET->value, TransIdDefault::J_BRING->value, TransIdDefault::EIKO_SHOUNEN->value])) {
            unset($data['sales_price']);
        }

        // update data
        if (Auth::user()->hn_cd == UserRole::admin->value) {
            $transport = $this->transportRepository->update($id, $data);
        } else {
            $transport = $this->transportRepository->updateByTpId($id, $data);
        }

        // Send mail notify
        $this->sendMailTransportService->sendMailNotifyByUpdateInfo($transport, $transportOrigin);

        if ($transport) {
            return $transport;
        }
        throw new ApiException(httpStatus: Response::HTTP_NOT_FOUND, errorCode: (string)ApiCodes::HTTP_NOT_FOUND, messageCustom: '陸送情報の更新に失敗しました。');
    }

    public function updateStatusTransport(array $data): array
    {
        // init status update
        $dataUpdate = [
            'st_cd' => Arr::get($data, 'status'),
            'up_owner' => Auth::user()->id,
            'up_date' => now(),
        ];

        // update status
        $result = $this->transportRepository->updateStatusTransports($data['id_list'], $dataUpdate);

        // send mail notify
        $status = Arr::get($data, 'status');
        if ($result && in_array($status, [Statuses::ON_HOLD->value, Statuses::CANCELED->value])) {
            $this->sendMailTransportService->sendMailNotifyByUpdateStatus($data['id_list'], $status);
        }

        // return result
        return [
            'status' => true,
            'message' => '',
        ];
    }

    public function downloadFileZip(string $refNo)
    {
        // call api get data from backend
        $url = env('APP_URL_OLD') . '/backend/tat/api_zipfile_output.asp';

        $response = Http::withHeaders([
            'Accept' => 'application/zip',
        ])->get($url, [
            'customer_id' => Auth::user()->id,
            'key' => ApiKey::API_OLD_HUBNET->getValue(),
            'autohub_id' => $refNo,
        ]);

        if ($response->successful()) {
            // Get filename from Content-Disposition header if available
            $contentDisposition = $response->header('Content-Disposition');
            $filename = 'transport_' . $refNo . '.zip'; // default filename

            if ($contentDisposition && preg_match('/filename="([^"]+)"/', $contentDisposition, $matches)) {
                $filename = $matches[1];
            }

            // Use streamDownload to avoid loading large files into memory
            return response()->streamDownload(
                function () use ($response): void {
                    // Stream the response body in chunks
                    $stream = $response->toPsrResponse()->getBody();
                    while (!$stream->eof()) {
                        echo $stream->read(8192); // Read 8KB chunks
                        flush();
                    }
                },
                $filename,
                [
                    'Content-Type' => 'application/zip',
                    'Cache-Control' => 'private',
                    'Pragma' => 'private',
                ]
            );
        }

        // If failed, throw exception
        throw new ApiException(
            httpStatus: Response::HTTP_BAD_REQUEST,
            errorCode: (string)Response::HTTP_BAD_REQUEST,
            messageCustom: 'ZIPファイルのダウンロードに失敗しました。'
        );
    }

    public function getDetailTransport($id)
    {
        $transport = $this->transportRepository->getInfoDetailTransport((int) $id, ['m_customer'], 't_transport.*, m_customer.cus_name_JP, m_customer.ah_sales_name');

        $vehicleTransport = $this->vehicleTransportModel
            ->selectRaw("HNTD07,HNNN54,HNCC54,HNNO68,HNSKNM,HNNN47,HNNR13,HNNO79,HNFCSN,HNTD14,HNASRN,HNTD79,HNNPNM,HNNO25,HNTD44,HNTD24,HNFL95,HNNC25,HNMFKK,HNFL71,HNCC03")
            ->where('HNCC03', $transport->ref_no)
            ->where('HNCY15', '<>', '1')
            ->where('HNYCKB', '<>', '1')
            ->where('HNFL01', '<>', '1')
            ->first();

        if ($vehicleTransport) {
            $transport->vehicleTransport = $vehicleTransport;
        }

        // get data transport note
        if ($transport->transportNotes->count()) {
            $transport->transportNotes = $transport->transportNotes->where('del_flg', 0);
        }

        // fetch file info
        $dataFile = $this->checkingFileOldHubNetService->call([
            [
                'm_customer_id' => $transport->m_customer_id,
                'transport_id' => $transport->id,
            ]
        ]);

        // add data file in
        if ($transport) {
            if ($dataFile) {
                $transport->file_info = collect($dataFile)->map(function ($item) {
                    return collect($item['fileList'])?->flatten()?->values();
                })->first();
            }
            return $transport;
        }
        throw new ApiException(httpStatus: Response::HTTP_NOT_FOUND, errorCode: (string)ApiCodes::HTTP_NOT_FOUND, messageCustom: '陸送情報の取得に失敗しました。');
    }

    public function getImageTransportFromAutohub(int $id)
    {
        $transport = $this->transportRepository->getInfoDetailTransport($id);

        // fecth
        $url = env('APP_URL_OLD') . '/backend/tat/api_image_output.asp';

        $response = Http::withHeaders([
            'Accept' => 'application/zip',
        ])->get($url, [
            'customer_id' => Auth::user()->id,
            'key' => ApiKey::API_OLD_HUBNET->getValue(),
            'autohub_id' => $transport->ref_no,
        ]);

        if ($response->successful()) {
            return $response->body();
        }

        throw new ApiException(
            httpStatus: Response::HTTP_BAD_REQUEST,
            errorCode: (string)Response::HTTP_BAD_REQUEST,
            messageCustom: '画像の取得に失敗しました。'
        );
    }

    public function confirmCreate(array $params = [])
    {
        $query = $this->userRepository->findBy([
            'id' => $params['odr_customer_id']
        ]);
        return [
            'id' => $query?->id,
            'cus_name_jp' => $query?->cus_Name_JP,
        ];
    }
}
