<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Area\GetDataDropdownAreaRequest;
use App\Services\Area\AreaService;
use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Response;

class AreaController extends Controller
{
    public function __construct(private AreaService $areaService)
    {
    }

    #[OA\Get(
        path: '/api/admin/areas/dropdown',
        summary: 'Get area dropdown data',
        description: 'Retrieve area data for dropdown selection. Supports filtering and sorting options.',
        security: [['access_token' => []]],
        tags: ['Area Management'],
        parameters: [
            new OA\Parameter(
                name: 'order_option',
                description: 'Order option for sorting (1: ACD, 2: COUNTRY, 3: PORT)',
                in: 'query',
                required: false,
                schema: new OA\Schema(
                    type: 'string',
                    enum: ['1', '2', '3'],
                    example: '1'
                )
            ),
            new OA\Parameter(
                name: 'sort_type',
                description: 'Sort direction (asc or desc)',
                in: 'query',
                required: false,
                schema: new OA\Schema(
                    type: 'string',
                    enum: ['asc', 'desc'],
                    example: 'asc'
                )
            ),
            new OA\Parameter(
                name: 'is_acd_not_empty',
                description: 'Filter by area code (ACD) - exact match',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'numeric', example: 1)
            ),
            new OA\Parameter(
                name: 'is_cd_not_empty',
                description: 'Filter by country code (CD) - exact match',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'numeric', example: 1)
            ),
            new OA\Parameter(
                name: 'is_pcd_not_empty',
                description: 'Filter by port code (PCD) - exact match',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'numeric', example: 1)
            ),
            new OA\Parameter(
                name: 'distinct',
                description: 'Filter by port code (PCD) - exact match',
                in: 'query',
                required: false,
                schema: new OA\Schema(
                    type: 'string',
                    enum: ['port', 'country', 'area'],
                    example: 'port'
                )
            ),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Area dropdown data retrieved successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(
                            property: 'status',
                            type: 'boolean',
                            example: true
                        ),
                        new OA\Property(
                            property: 'data',
                            type: 'array',
                            items: new OA\Items(
                                properties: [
                                    new OA\Property(
                                        property: 'id',
                                        description: 'Area ID',
                                        type: 'integer',
                                        example: 1
                                    ),
                                    new OA\Property(
                                        property: 'pcd',
                                        description: 'Port code',
                                        type: 'string',
                                        example: 'YOKOHAMA'
                                    ),
                                    new OA\Property(
                                        property: 'port',
                                        description: 'Port name',
                                        type: 'string',
                                        example: '横浜港'
                                    ),
                                    new OA\Property(
                                        property: 'cd',
                                        description: 'Country code',
                                        type: 'string',
                                        example: 'JPN'
                                    ),
                                    new OA\Property(
                                        property: 'country',
                                        description: 'Country name',
                                        type: 'string',
                                        example: 'Japan'
                                    ),
                                    new OA\Property(
                                        property: 'acd',
                                        description: 'Area code',
                                        type: 'string',
                                        example: 'JP'
                                    ),
                                    new OA\Property(
                                        property: 'area',
                                        description: 'Area name',
                                        type: 'string',
                                        example: '日本'
                                    ),
                                ],
                                type: 'object'
                            )
                        ),
                        new OA\Property(
                            property: 'message',
                            type: 'string',
                            example: 'Area dropdown data retrieved successfully',
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'locale',
                            type: 'string',
                            example: null,
                            nullable: true
                        ),
                        new OA\Property(
                            property: 'code',
                            type: 'integer',
                            example: null,
                            nullable: true
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 400,
                description: 'Bad Request - Invalid parameters',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(
                            property: 'status',
                            type: 'boolean',
                            example: false
                        ),
                        new OA\Property(
                            property: 'message',
                            type: 'string',
                            example: 'The given data was invalid.'
                        ),
                        new OA\Property(
                            property: 'errors',
                            properties: [
                                new OA\Property(
                                    property: 'order_option',
                                    type: 'array',
                                    items: new OA\Items(type: 'string'),
                                    example: ['The selected order option is invalid.']
                                ),
                                new OA\Property(
                                    property: 'sort_type',
                                    type: 'array',
                                    items: new OA\Items(type: 'string'),
                                    example: ['The selected sort type is invalid.']
                                ),
                            ],
                            type: 'object'
                        )
                    ]
                )
            ),
            new OA\Response(response: 401, description: 'Unauthorized - Invalid or missing access token'),
            new OA\Response(response: 403, description: 'Forbidden - Insufficient permissions'),
            new OA\Response(response: 500, description: 'Internal server error')
        ]
    )]
    public function getDataDropdown(GetDataDropdownAreaRequest $request): Response
    {
        return $this->respond($this->areaService->getDataDropdown($request->validated()));
    }
}
