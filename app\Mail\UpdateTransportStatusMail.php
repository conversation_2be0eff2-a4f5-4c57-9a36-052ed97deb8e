<?php

declare(strict_types=1);

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use App\Traits\MailTrait;

class UpdateTransportStatusMail extends Mailable
{
    use MailTrait;
    use Queueable;
    use SerializesModels;

    private array $data;

    /**
     * Create a new message instance.
     */
    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function build(): self
    {
        $subject = str_replace('$car_no', Arr::get($this->data, 'data_mail.car_no'), config('mail.mail_subjects.' . Arr::get($this->data, 'file_name_template')));
        $subject = str_replace('$customer_name', Arr::get($this->data, 'data_mail.customer_name'), $subject);

        $mailer = $this
            ->from($this->data['mail_from']['mail'], $this->data['mail_from']['name'])
            ->bcc($this->transformMail($this->data['mail_bcc']))
            ->cc($this->transformMail($this->data['cc_address'] ?? ''))
            ->subject($subject);

        if ('admin-ei-transport-cancel' == Arr::get($this->data, 'file_name_template')) {
            $mailer->view('emails.ja.admin-ei-transport-cancel');
        } else {
            $mailer->text('emails.ja.' . Arr::get($this->data, 'file_name_template'));
        }

        return $mailer
            ->with([
                'id' => Arr::get($this->data, 'data_mail.id'),
                'ah_staff_name' => Arr::get($this->data, 'data_mail.ah_staff_name'),
                'trans_name' => Arr::get($this->data, 'data_mail.trans_name'),
                'transport_id' => Arr::get($this->data, 'data_mail.transport_id'),
                'plate_send_date' => Arr::get($this->data, 'data_mail.plate_send_date'),
                'plate_send_co' => Arr::get($this->data, 'data_mail.plate_send_co'),
                'plate_send_no' => Arr::get($this->data, 'data_mail.plate_send_no'),
                'customer_name' => Arr::get($this->data, 'data_mail.customer_name'),
                'fr_name' => Arr::get($this->data, 'data_mail.fr_name'),
                'fr_addr' => Arr::get($this->data, 'data_mail.fr_addr'),
                'fr_tel' => Arr::get($this->data, 'data_mail.fr_tel'),
                'pos_no' => Arr::get($this->data, 'data_mail.pos_no'),
                'aa_no' => Arr::get($this->data, 'data_mail.aa_no'),
                'date_of_payment' => Arr::get($this->data, 'data_mail.date_of_payment'),
                'car_no' => Arr::get($this->data, 'data_mail.car_no'),
                'undrivable_flg' => Arr::get($this->data, 'data_mail.undrivable_flg'),
                'tall_flg' => Arr::get($this->data, 'data_mail.tall_flg'),
                'lowdown_flg' => Arr::get($this->data, 'data_mail.lowdown_flg'),
                'long_flg' => Arr::get($this->data, 'data_mail.long_flg'),
                'car_name' => Arr::get($this->data, 'data_mail.car_name'),
                'plate_cut_flg' => Arr::get($this->data, 'data_mail.plate_cut_flg'),
                'plate_no' => Arr::get($this->data, 'data_mail.plate_no'),
                'country' => Arr::get($this->data, 'data_mail.country'),
                'to_name' => Arr::get($this->data, 'data_mail.to_name'),
                'to_addr' => Arr::get($this->data, 'data_mail.to_addr'),
                'to_tel' => Arr::get($this->data, 'data_mail.to_tel'),
                'odr_date' => Arr::get($this->data, 'data_mail.odr_date'),
                'auction_date' => Arr::get($this->data, 'data_mail.auction_date'),
                'auc_mail' => Arr::get($this->data, 'data_mail.auc_mail'),
                'status' => Arr::get($this->data, 'data_mail.status'),
                'note' => Arr::get($this->data, 'data_mail.note'),
                'date_of_time' => Arr::get($this->data, 'data_mail.date_of_time'),
                'tickitems_mail' => Arr::get($this->data, 'data_mail.tickitems_mail'),
                'plate_address_mail' => Arr::get($this->data, 'data_mail.plate_address_mail'),
            ]);
    }
}
