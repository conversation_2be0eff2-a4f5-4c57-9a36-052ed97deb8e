<?php

declare(strict_types=1);

namespace App\Services\Transport;

use App\Enums\Transport\TransIdDefault;
use App\Jobs\SendMailTransportFromBackend;
use App\Jobs\SendMailTransportStore;
use App\Models\sqlServer\User;
use App\Traits\CommonTrait;
use Illuminate\Support\Arr;

class SendMailStoreTransportService
{
    use CommonTrait;

    public function __construct(
        private User $userModel
    ) {
    }

    public function sendMail($data): void
    {
        $tickitEmails = '';

        if ($data['undrivable_flg']) {
            $tickitEmails .= trans('mail.lan_undrivable') . '<br/>';
        }
        if ($data['tall_flg']) {
            $tickitEmails .= trans('mail.lan_tall') . '<br/>';
        }
        if ($data['lowdown_flg']) {
            $tickitEmails .= trans('mail.lan_lowdown') . '<br/>';
        }
        if ($data['long_flg']) {
            $tickitEmails .= trans('mail.lan_long') . '<br/>';
        }
        if ($data['old_flg']) {
            $tickitEmails .= trans('mail.lan_old_car') . '<br/>';
        }
        if ($data['luxury_flg']) {
            $tickitEmails .= trans('mail.lan_luxury_car');
            if ('1' == $data['luxury_flg_insrance']) {
                $tickitEmails .= '(' . trans('mail.lan_luxyry_insurance1') . ')';
            } elseif ('2' == $data['luxury_flg_insrance']) {
                $tickitEmails .= '(' . trans('mail.lan_luxyry_insurance2') . ')';
            }
            $tickitEmails .= '<br/>';
        }
        if ('1' == $data['other_flg']) {
            $tickitEmails .= trans('mail.lan_other_car') . '(' . $data['other_flg_txt'] . ')' . '<br/>';
        }
        if ($data['tick_no_flg']) {
            $tickitEmails .= trans('mail.lan_gaitou_nasi') . '<br/>';
        }

        $aucMail = '';
        if ('1' == $data['auction_chk']) {
            $aucMail .= trans('mail.lan_aucBoth') . '<br/>';
        } else {
            $aucMail .= $data['auction_txt'] . '<br/>';
            if (!$data['auc_name'] && !$data['auc_addr'] && !$data['auc_tel']) {
                $aucMail .= trans('mail.lan_other_auc') . ': ' . $data['auc_addr'] . '<br/>';
                $aucMail .= trans('mail.lan_address') . ': ' . $data['auc_name'] . '<br/>';
                $aucMail .= trans('mail.lan_tel') . ': ' . $data['auc_tel'] . '<br/>';
            }
        }

        $plateAddressMail = '';
        if ('' != $data['plate_send_name']) {
            $plateAddressMail .= $data['plate_send_name'] . '<br/> ' . $data['plate_send_zipcd'] . '<br/> ' . $data['plate_send_address'] . '<br/> ' . $data['plate_send_tel'] . '<br/>';
        }

        $dateOfTime = '';
        if ('1' == $data['date_of_payment_time']) {
            $dateOfTime = '(' . trans('mail.lan_payment_time1') . ')';
        } elseif ('2' == $data['date_of_payment_time']) {
            $dateOfTime = '(' . trans('mail.lan_payment_time2') . ')';
        } elseif ('3' == $data['date_of_payment_time']) {
            $dateOfTime = '(' . trans('mail.lan_payment_time3') . ')';
        }

        $user = $this->userModel::where('id', $data['odr_customer_id'])->where('del_flg', 0)->first();

        $mailInfo = config('mail.mail_info.' . config('app.env'));
        $dataMail = [
            'mail_from' => [
                'mail' => $mailInfo['from'],
                'name' => config('mail.mail_name.from'),
            ],
            'mail_bcc' => $mailInfo['bcc'],
            'mail_cc' => '',
        ];
        $odrCustomerMails = [];
        $toAddress = [];
        if ($user) {
            $data['cus_name_jp'] = $user->cus_Name_JP;

            for ($i = 1; $i < 10; $i++) {
                if ('1' == $user->{'ch_trans_mail_flg' . $i}) {
                    if (1 == $i) {
                        $odrCustomerMails[] = $user->admin_email;
                        $toAddress[] = $user->admin_email;
                    } elseif (2 == $i) {
                        $odrCustomerMails[] = $user->shipping_prsn_email;
                        $toAddress[] = $user->shipping_prsn_email;
                    } elseif (3 == $i) {
                        $odrCustomerMails[] = $user->shipping_prsn_email;
                        $toAddress[] = $user->{'ch_mail_prsn_email' . $i};
                    }
                }
            }
        }

        if (count($toAddress) > 0) {
            SendMailTransportFromBackend::dispatchSync(
                $toAddress,
                [
                    ...$dataMail,
                    'data_info' => [
                        "id" => Arr::get($data, 'transport_id'),
                        "customer_name" => Arr::get($data, 'cus_name_jp'),
                        "fr_aa_place_id" => Arr::get($data, 'fr_aa_place_id'),
                        "fr_name" => Arr::get($data, 'fr_name'),
                        "fr_addr" => Arr::get($data, 'fr_addr'),
                        "fr_tel" => Arr::get($data, 'fr_tel'),
                        "auction_date" => Arr::get($data, 'auction_date'),
                        "auc_mail" => $aucMail,
                        "to_name" => Arr::get($data, 'to_name'),
                        "to_addr" => Arr::get($data, 'to_addr'),
                        "to_tel" => Arr::get($data, 'to_tel'),
                        "pos_no" => Arr::get($data, 'pos_no'),
                        "aa_no" => Arr::get($data, 'aa_no'),
                        "date_of_payment" => Arr::get($data, 'date_of_payment'),
                        "date_of_time" => $dateOfTime,
                        "car_name" => Arr::get($data, 'car_name'),
                        "car_no" => Arr::get($data, 'car_no'),
                        "tickitems_mail" => $tickitEmails,
                        "plate_cut_flg" => Arr::get($data, 'plate_cut_flg'),
                        "plate_no" => Arr::get($data, 'plate_no'),
                        "plate_address_mail" => $plateAddressMail,
                        "country" => Arr::get($data, 'country'),
                        "port" => Arr::get($data, 'port'),
                        "odr_date" => Arr::get($data, 'odr_date'),
                        "note" => Arr::get($data, 'note'),
                    ]
                ]
            );
        }

        // send mail administrator, land transport company
        $dataMail = [
            'mail_from' => [
                'mail' => $mailInfo['from'],
                'name' => config('mail.mail_name.from'),
            ],
            'mail_bcc' => $mailInfo['bcc'],
            'mail_cc' => '',
            'file_name_template' => '',
        ];
        $toAddress = [];

        if (TransIdDefault::TARGET->value == $data['m_trans_id']) {
            $toAddress[] = $mailInfo['mail_address_order'];
            $dataMail['mail_cc'] = $mailInfo['mail_address_order'];
            $dataMail['file_name_template'] = 'admin-cg-transport';
        } elseif (TransIdDefault::J_BRING->value == $data['m_trans_id']) {
            $toAddress[] = $mailInfo['mail_address_transport'];
            $dataMail['mail_cc'] = $mailInfo['mail_address_transport'];
            $dataMail['file_name_template'] = 'admin-jc-transport';
        } elseif (TransIdDefault::EIKO_SHOUNEN->value == $data['m_trans_id']) {
            $toAddress[] = $mailInfo['mail_address_yamashita'];
            $dataMail['mail_cc'] = $mailInfo['mail_address_yamashita'];
            $dataMail['file_name_template'] = 'admin-ei-transport';
        } elseif (TransIdDefault::SHIPPING_EAST_AND_WEST->value == $data['m_trans_id']) {
            $dataMail['mail_cc'] = "";
            $toAddress[] = $mailInfo['ptrans_mail'];
            $dataMail['file_name_template'] = 'admin-transport';
        } elseif (TransIdDefault::LOGICO->value == $data['m_trans_id']) {
            $dataMail['mail_cc'] = "";
            $toAddress[] = $mailInfo['ltrans_mail'];
            $dataMail['file_name_template'] = 'admin-lg-transport';
        }

        $user = $this->userModel::selectRaw("
            m_customer.id, m_customer.shipping_prsn_name, m_customer.ah_sales_id, m_sales_staff.e_mail, m_sales_staff.team, m_sales_staff.s_name,
            m_customer.ah_sales_mail_flg, m_customer.bk_trans_mail_flg, m_customer.ah_send_mailaddress
        ")->leftJoin('m_sales_staff', 'm_sales_staff.s_id', '=', 'm_customer.ah_sales_id')
            ->where('m_customer.id', $data['odr_customer_id'])->where('m_customer.del_flg', 0)
            ->where('m_customer.bk_trans_mail_flg', 1)->first();

        if ($user) {
            $shippingPrsnName = $user->shipping_prsn_name;
            $ahSalesId = $user->ah_sales_id;
            $ahStaffEmail = $user->e_mail;
            $ahStaffTeam = $user->team;
            $repAddress = trim($user->e_mail);
            $repAddressTemp = trim($user->e_mail);
            $ahStaffName = trim($user->s_name);
            $ahSalesMailFlg = $user->ah_sales_mail_flg;
            $ahSendMailAddress = $user->ah_send_mailaddress;
            $bkTransMailFlg = $user->bk_trans_mail_flg;


            if (1 == $ahSalesMailFlg && 1 == $bkTransMailFlg) {
                if ("" != $ahSendMailAddress && null !== $ahSendMailAddress) {
                    $ahSendMailAddresses = explode(';', $ahSendMailAddress);
                    $toAddress = array_merge($toAddress, $ahSendMailAddresses, [$repAddress]);
                }
            }
        }

        $toAddress[] = $mailInfo['trans_mail'];

        $dataInfo = [
            'ah_staff_name' => $ahStaffName,
            'id' => TransIdDefault::LOGICO->value == $data['m_trans_id'] ? Arr::get($data, 'transport_id') : Arr::get($data, 'tp_id'),
            'm_customer_id' => Arr::get($data, 'odr_customer_id'),
            'customer_name' => Arr::get($data, 'cus_name_jp'),
            'fr_aa_place_id' => Arr::get($data, 'fr_aa_place_id'),
            'fr_name' => Arr::get($data, 'fr_name'),
            'fr_addr' => Arr::get($data, 'fr_addr'),
            'fr_tel' => Arr::get($data, 'fr_tel'),
            'auction_date' => Arr::get($data, 'auction_date'),
            'auc_mail' => $aucMail,
            'to_name' => Arr::get($data, 'to_name'),
            'to_addr' => Arr::get($data, 'to_addr'),
            'to_tel' => Arr::get($data, 'to_tel'),
            'pos_no' => Arr::get($data, 'pos_no'),
            'aa_no' => Arr::get($data, 'aa_no'),
            'date_of_payment' => Arr::get($data, 'date_of_payment'),
            'date_of_time' => $dateOfTime,
            'car_name' => Arr::get($data, 'car_name'),
            'car_no' => Arr::get($data, 'car_no'),
            'tickitems_mail' => $tickitEmails,
            'plate_cut_flg' => Arr::get($data, 'plate_cut_flg'),
            'plate_no' => Arr::get($data, 'plate_no'),
            'plate_address_mail' => $plateAddressMail,
            'country' => Arr::get($data, 'country'),
            'port' => Arr::get($data, 'port'),
            'odr_date' => Arr::get($data, 'odr_date'),
            'note' => Arr::get($data, 'note'),
        ];

        SendMailTransportStore::dispatchSync(
            $toAddress,
            [
                ...$dataMail,
                'data_info' => $dataInfo
            ]
        );
    }
}
