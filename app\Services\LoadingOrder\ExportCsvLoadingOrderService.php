<?php

declare(strict_types=1);

namespace App\Services\LoadingOrder;

use App\Constants\ApiCodes;
use App\Enums\LoadingOrder\CsvHeaders;
use App\Enums\LoadingOrder\LoadingOrderType;
use App\Enums\LoadingOrder\Statuses;
use App\Exceptions\ApiException;
use App\Repositories\sqlServer\LoadingOrderRepository;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\StreamedResponse;

class ExportCsvLoadingOrderService
{
    private LoadingOrderRepository $loadingOrderRepository;

    public function __construct(LoadingOrderRepository $loadingOrderRepository)
    {
        $this->loadingOrderRepository = $loadingOrderRepository;
    }

    /**
     * @throws ApiException
     */
    public function call(array $params): StreamedResponse
    {
        $token = $params['token'];
        $this->checkToken($token);

        $timestamp = Carbon::now()->format('YmdHis');
        $filename = "loading{$timestamp}.txt";

        $headers = [
            'Content-Type' => 'text/plain; charset=UTF-8',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0'
        ];

        $callback = function () use ($params): void {
            $relations = [];
            $file = fopen('php://output', 'w');
            fwrite($file, "\xEF\xBB\xBF");

            $this->writeCsvHeader($file);

            $this->loadingOrderRepository->builderQueryExportCSV($params, $relations)
                ->chunk(1000, function ($loadingOrders) use ($file): void {
                    $this->processCsvChunk($loadingOrders, $file);
                });

            fclose($file);
        };

        return new StreamedResponse($callback, 200, $headers);
    }

    /**
     * @throws ApiException
     */
    private function checkToken(string $token): void
    {
        $cacheData = Cache::store('database')->pull($token);

        if (!$cacheData) {
            throw new ApiException((string)ApiCodes::INVALID_TOKEN);
        }
    }

    private function writeCsvHeader($file): void
    {
        $headers = CsvHeaders::getAllHeaders();
        fputcsv($file, $headers, "\t");
    }

    private function processCsvChunk(Collection $loadingOrders, $file): void
    {
        foreach ($loadingOrders as $loadingOrder) {
            $row = $this->mapToCsvRow($loadingOrder);
            fputcsv($file, $row, "\t");
        }
    }

    private function mapToCsvRow($loadingOrder): array
    {
        $partNote = $this->cleanNote($loadingOrder->part_note);
        $note = $this->cleanNote($loadingOrder->note);
        $specialNote = $this->cleanNote($loadingOrder->special_note);

        $exportInspection = $this->getExportInspection($loadingOrder->optn6_flg, $loadingOrder->optn6_sub_kbn);
        $marineInsurance = $this->convertOptn4Kbn($loadingOrder->optn4_sub_kbn);

        return [
            $loadingOrder->id,                                                           // 船積ID
            Carbon::parse($loadingOrder->odr_date)->format('n/j/Y h:i:s A'),        // 申込日時
            Statuses::getStatusName($loadingOrder->st_cd),                             // 状態
            $loadingOrder->ref_no,                                                       // AH整理番号
            $loadingOrder->ah_sales_name,                                               // 担当者
            $loadingOrder->m_customer_id,                                               // 会員ID
            $loadingOrder->cus_name_JP,                                                 // 申込会社
            $loadingOrder->agent_ref_no,                                                // AGENT整理番号
            $loadingOrder->car_year,                                                    // 年式
            $loadingOrder->car_no,                                                      // 車台番号
            $loadingOrder->car_name,                                                    // 車名
            $loadingOrder->mileage,                                                     // 走行距離
            $this->convertQuotation($loadingOrder->price_quotation),                    // 建値
            $this->convertCurrency($loadingOrder->price_currency),                      // 通貨
            $loadingOrder->fob_price,                                                   // 車両(FOB)金額
            $loadingOrder->to_name,                                                     // 乙仲名
            $loadingOrder->destination,                                                 // 仕向国
            $loadingOrder->port,                                                        // 港
            LoadingOrderType::getOrderTypeName($loadingOrder->odr_kbn),                 // 注文区分
            $loadingOrder->to_plan_date,                                               // 搬入予定日
            $loadingOrder->consignee_name,                                             // CONSIGNEE
            $partNote,                                                                  // リクエスト写真のご要望
            $note,                                                                      // その他備考
            $specialNote,                                                               // 特記事項
            $this->flagReplace($loadingOrder->optn7_flg),                              // 輸出抹消手続き
            $this->flagReplace($loadingOrder->optn17_flg),                             // 英文抹消作成
            $this->flagReplace($loadingOrder->optn8_flg),                              // 写真撮影（規定アングル）
            $this->flagReplace($loadingOrder->optn10_flg),                             // リクエスト写真撮影
            $this->flagReplace($loadingOrder->optn3_flg),                              // プレート外し
            $loadingOrder->optn3_sub_txt,                                              // プレート番号
            $this->flagReplace($loadingOrder->optn5_flg),                              // 軽作業
            $this->flagReplace($loadingOrder->optn21_flg),                             // コンディションチェック
            $this->flagReplace($loadingOrder->optn18_flg),                             // エアコンガス抜き
            $exportInspection,                                                          // 輸出検査
            $marineInsurance,                                                           // AH包括海上保険
            $this->flagReplace($loadingOrder->optn14_flg),                             // 通常の海上保険
            $this->flagReplace($loadingOrder->optn11_flg),                             // 海外書類発送
        ];
    }

    /**
     * Clean note text by replacing line breaks and <br> tags
     */
    private function cleanNote(?string $note): string
    {
        if (empty($note)) {
            return '';
        }

        $note = str_replace(["\r\n", "\n", "\r"], "　", $note);
        $note = str_replace("<br>", "　", $note);

        return $note;
    }

    /**
     * Convert flag values to Japanese text
     */
    private function flagReplace($flag): string
    {
        return '1' == $flag ? 'あり' : 'なし';
    }

    /**
     * Get export inspection text based on flag and sub code
     */
    private function getExportInspection($flag, $subKbn): string
    {
        if ('1' == $flag) {
            if (!empty($subKbn)) {
                return $this->convertOptn6Kbn($subKbn);
            }
            return 'あり';
        }
        return 'なし';
    }

    /**
     * Convert quotation code to text
     */
    private function convertQuotation($data): string
    {
        return match($data) {
            '0', '' => '',
            '1' => 'FOB',
            '2' => 'CIF',
            '3' => 'C&F',
            default => ''
        };
    }

    /**
     * Convert currency code to text
     */
    private function convertCurrency($data): string
    {
        return match($data) {
            '0', '' => '',
            '1' => 'JPY',
            '2' => 'NZD',
            '3' => 'USD',
            '4' => 'AUD',
            default => ''
        };
    }

    /**
     * Convert option 6 (inspection company) code to text
     */
    private function convertOptn6Kbn($data): string
    {
        return match($data) {
            '1' => 'JAAI',
            '2' => 'JEVIC',
            '3' => 'EAA',
            '4' => 'QISJ',
            '5' => 'JAAI（価格査定）',
            default => ''
        };
    }

    /**
     * Convert option 4 (marine insurance) code to text
     */
    private function convertOptn4Kbn($data): string
    {
        return match($data) {
            '1' => '証券あり',
            '0' => '証券なし',
            default => ''
        };
    }
}
