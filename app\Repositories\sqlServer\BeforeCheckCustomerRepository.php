<?php

declare(strict_types=1);

namespace App\Repositories\sqlServer;

use App\Models\sqlServer\BeforeCheckCustomer;
use App\Repositories\BaseRepository;
use Illuminate\Database\Eloquent\Collection;

class BeforeCheckCustomerRepository extends BaseRepository
{
    public function model(): mixed
    {
        return BeforeCheckCustomer::class;
    }

    public function listBeforeCheckCustomer(): Collection
    {
        $query = ($this->model)::query();

        $query->orderBy(($this->model)->getTable() . '.cus_Name_JP', 'ASC');

        return $query->get();
    }

    public function truncateBeforeCheckCustomer(): void
    {
        $query = ($this->model)::query();
        $query->truncate();
    }
}
