@php
    /**
     * @var string $enTitle
     * @var string $jaTitle
     * @var string $content
     */
    [$firstLetterTitle, $lastLetterTitle] = [$enTitle[0], substr($enTitle, 1)];
@endphp

<div class="2md:px-[15px] max-w-1/2 flex-none basis-1/2 px-[5px] md:max-w-1/4 md:basis-1/4">
    <div class="staff_box aos-init aos-animate" data-aos="flip-left" data-aos-delay="0" data-aos-duration="2000">
        <h2 class="2sm:text-22 text-xl leading-6 font-bold 2md:text-2xl lg:text-25">
            <span class="text-3xl 2sm:text-35 2md:text-38 lg:text-[45px] text-[#f00]">{{ $firstLetterTitle }}</span>{{ $lastLetterTitle }}<br>{{ $jaTitle }}
        </h2>
        <p class="2sm:text-15px 2md:text-base lg:text-17 text-13 mb-4 leading-5">{!! $content !!}</p>
    </div>
</div>
