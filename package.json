{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "prepare": "husky", "format:blade": "prettier --write resources/views"}, "devDependencies": {"axios": "^1.6.4", "husky": "^9.1.7", "laravel-vite-plugin": "^1.0", "prettier": "^3.5.3", "prettier-plugin-blade": "^2.1.21", "prettier-plugin-tailwindcss": "^0.6.11", "vite": "^5.0"}, "name": "base-be", "version": "1.0.0", "description": "<p align=\"center\"><a href=\"https://laravel.com\" target=\"_blank\"><img src=\"https://raw.githubusercontent.com/laravel/art/master/logo-lockup/5%20SVG/2%20CMYK/1%20Full%20Color/laravel-logolockup-cmyk-red.svg\" width=\"400\" alt=\"Laravel Logo\"></a></p>", "main": "vite.config.js", "directories": {"test": "tests"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@tailwindcss/vite": "^4.1.7", "tailwindcss": "^4.1.7"}}