<?php

declare(strict_types=1);

namespace App\Modules\SailingSchedule\Repositories;

use App\Models\as400\RouteSchedule;
use App\Repositories\BaseRepository;
use Illuminate\Support\Facades\DB;

class RouteScheduleRepository extends BaseRepository
{
    public function model(): mixed
    {
        return RouteSchedule::class;
    }

    public function getRouteSchedule(): array
    {
        $data = DB::connection('as400')->select('
            SELECT
                KRKRCD as "route_code",
                KRKRNM as "route_name"
            FROM
                HCDLIB.MKOROP
            ORDER BY
                KRKRNM ASC
        ');

        return $data;
    }

}
