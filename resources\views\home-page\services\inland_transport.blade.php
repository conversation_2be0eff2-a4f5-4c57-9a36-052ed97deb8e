@extends('app')

@section('title', __('home-page/inland_transport.page_title'))

@section('content')
    <x-services.container>
        <x-services.title-page
            titlePage="{{ __('home-page/inland_transport.title') }}"
            description1="{{ __('home-page/inland_transport.page_description1') }}"
            description2="{{ __('home-page/inland_transport.page_description2') }}"
        />
        <x-services.title subTitle="{{ __('home-page/inland_transport.service_descriptions') }}" />
        <div class="mx-auto my-6 w-full px-4">
            <div class="mx-[-15px] flex flex-wrap">
                <div class="2md:w-1/2 relative w-full px-[15px]">
                    <div
                        class="text-15 relative mx-auto mb-[1.5em] w-full border-t-2 border-b-2 border-sky-300 px-[2em] pt-[0.7em] pb-[0.5em] text-gray-700"
                    >
                        <div
                            class="absolute top-[-10px] left-[3px] h-[calc(100%+25px)] w-[10px] rounded-[10px] bg-sky-300 opacity-20 md:w-[15px]"
                        ></div>
                        <h4 class="font-medium break-all">
                            <span class="text-25 md:text-38 text-cyan-300 text-shadow-cyan-300">G</span>
                            <span class="md:text-28 text-lg text-lime-400 text-shadow-lime-400">o</span>
                            <span class="text-21 md:text-31 text-lime-400 text-shadow-lime-400">o</span>
                            <span class="text-23 text-lime-400 text-shadow-lime-400 md:text-4xl">d</span>
                            <span class="text-25 md:text-38 text-lime-400 text-shadow-lime-400">!</span>
                        </h4>
                        <div class="relative w-full px-[15px]">
                            <ul class="text-15 list-disc md:text-base">
                                <li class="my-[0.5em] ml-[1em]">
                                    {{ __('home-page/inland_transport.service_description_1') }}
                                </li>
                                <li class="my-[0.5em] ml-[1em]">
                                    {{ __('home-page/inland_transport.service_description_2') }}
                                </li>
                            </ul>
                        </div>
                        <div
                            class="absolute top-[-10px] right-[3px] h-[calc(100%+25px)] w-[10px] rounded-[10px] bg-sky-300 opacity-20 md:w-[15px]"
                        ></div>
                    </div>
                    <div
                        class="text-15 shadow-gray-220 mt-[2em] mb-[0.5em] bg-stone-50 pt-[1em] pr-[1em] pb-[0.5em] pl-[2em] md:text-base"
                    >
                        <ul class="list-decimal pl-[2em]">
                            <li class="my-[0.5em]">{{ __('home-page/inland_transport.service_description_3') }}</li>
                            <li class="my-[0.5em]">{{ __('home-page/inland_transport.service_description_4') }}</li>
                            <li class="my-[0.5em]">{{ __('home-page/inland_transport.service_description_5') }}</li>
                        </ul>
                    </div>
                </div>
                <div class="2md:w-1/2 relative w-full px-[15px]">
                    <img
                        src="{{ asset('images/services/ah_service_image04.png') }}"
                        class="2md:mt-0 mx-auto mt-[1.5em] w-full !max-w-max"
                        alt=""
                    />
                </div>
            </div>
            <x-services.content-item title="{{ __('common.how_to_use') }}">
                <div
                    class="2sm:py-[4em] 2sm:text-base text-15 mt-[1em] flex flex-col flex-wrap items-center justify-around px-[1em] py-[2em] md:flex-row"
                >
                    <x-services.login-hubnet />
                    <i
                        class="fas fa-caret-right 2sm:my-[0.2em] text-50 rotate-90 transform font-black text-orange-400 md:my-0 md:rotate-0 md:transform-none"
                    ></i>
                    <div class="2sm:w-1/2 2md:w-1/5 relative w-4/5 md:w-1/4">
                        <i
                            class="fas fa-hand-pointer text-50 absolute right-[10%] bottom-0 z-10 font-black text-white text-shadow-black"
                        ></i>
                        <div class="flex flex-wrap justify-between">
                            <div class="shadow-gray-150 mx-[1em] mb-[1em] w-full bg-white text-center">
                                <a
                                    href="{{ env('APP_URL_OLD', '') }}/hn/trans/?lan={{ app()->getLocale() }}"
                                    class="relative flex w-full flex-col items-center justify-center"
                                    target="_blank"
                                >
                                    <div
                                        class="absolute top-0 left-0 h-[30px] w-5"
                                        style="
                                            background: url('{{ asset('images/services/label_style08.png') }}')
                                                no-repeat;
                                            background-size: 20px 30px;
                                        "
                                    ></div>
                                    <img
                                        src="{{ asset('images/services/icon_menu_07.png') }}"
                                        alt="陸送発注"
                                        class="mt-[10px] w-15"
                                    />
                                    <p class="2sm:text-base text-red-450 pb-[1em] text-xs leading-normal font-bold">
                                        {!! __('home-page/inland_transport.click_transport_order') !!}
                                    </p>
                                </a>
                            </div>
                        </div>
                    </div>
                    <i
                        class="fas fa-caret-right 2sm:my-[0.2em] rotate-90 transform text-6xl font-black text-orange-400 md:my-0 md:rotate-0 md:transform-none"
                    ></i>
                    <div class="2sm:w-1/2 2md:w-1/5 relative w-4/5 md:w-1/4">
                        <div class="flex flex-wrap justify-between">
                            <div class="shadow-gray-100-big mx-[1em] mb-[1em] w-full rounded-sm bg-white">
                                <p
                                    class="2sm:text-base text-sky-350 p-[1em] text-center text-xs leading-normal font-bold"
                                >
                                    {!! __('home-page/inland_transport.transportation_request') !!}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </x-services.content-item>
            <x-services.content-item title="{{ __('common.functions') }}">
                <x-services.service-function-item
                    :index="1"
                    :content="__('home-page/inland_transport.function_title_1')"
                    icon="fas fa-globe"
                    :image="$isJa ? 'inland_transport_01.jpg' : 'inland_transport_01_en.jpg'"
                />
                <x-services.service-function-item
                    :index="2"
                    :content="__('home-page/inland_transport.function_title_2')"
                    icon="far fa-window-restore"
                    :image="$isJa ? 'inland_transport_02.jpg' : 'inland_transport_02_en.jpg'"
                />
                <x-services.service-function-item
                    :index="3"
                    :content="__('home-page/inland_transport.function_title_3')"
                    icon="fas fa-map-marked-alt"
                    image="inland_transport_03.jpg"
                />
            </x-services.content-item>
            <div class="mt-[5em] mb-[2em] flex items-center rounded-sm bg-sky-300 text-sm md:mt-[7em]">
                <i
                    class="fas fa-hand-holding-heart md:text-50 bg-white p-[0.5em] text-[35px] text-gray-100 shadow-[inset_#ccc_2px_2px_5px]"
                ></i>
                <p class="2sm:text-lg 2sm:m-0 2sm:ml-[1em] text-15 md:text-25 mx-[0.5em] font-bold text-white">
                    {{ __('home-page/inland_transport.sale_title') }}
                </p>
            </div>
        </div>
        <x-services.service-options
            :description="__('home-page/inland_transport.hubnet_order')"
            iconRight="icon_menu_07.png"
            :titleRight="__('home-page/inland_transport.land_transport_order')"
            :contentRight="__('home-page/inland_transport.land_transport_order_content')"
            linkRight="{{ env('APP_URL_OLD', '') }}/hn/trans/?lan={{ app()->getLocale() }}"
        />
    </x-services.container>
@endsection
