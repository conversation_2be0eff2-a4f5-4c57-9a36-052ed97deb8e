<?php

declare(strict_types=1);

namespace App\Repositories\sqlServer;

use App\Enums\CheckingRequest\DateMode;
use App\Enums\CheckingRequest\OrderOptions;
use App\Enums\CheckingRequest\Statuses;
use App\Enums\SortType;
use App\Models\sqlServer\BeforeCheck;
use App\Repositories\BaseRepository;
use App\Repositories\common\BuilderWhereCondition;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class CheckingRequestRepository extends BaseRepository
{
    public function model(): mixed
    {
        return BeforeCheck::class;
    }

    public function paginate($params = null, array $relations = [], $columns = ['*']): LengthAwarePaginator
    {
        $query = ($this->model)::query();

        $query->with($relations)
            ->leftJoinCustom('customer')
            ->leftJoinCustom('aaDate');

        $params = $params ?: request()->toArray();
        $limit = Arr::pull($params, 'limit', 20);
        $page = Arr::pull($params, 'page');

        $query = BuilderWhereCondition::call($query, false, $params);
        $this->applyCustomerNameFilters($query, $params);
        $this->applyDateFilters($query, $params);
        $query = $this->addSoftDeleteCondition($query);

        $this->buildSortQuery($query, $params);

        return $query->paginate($limit, $columns, 'page', $page);
    }

    public function getList($params = null, array $relations = [], $columns = ['*']): Collection
    {
        $query = ($this->model)::query();

        $query->with($relations)
            ->leftJoinCustom('customer')
            ->leftJoinCustom('aaDate');

        $query = BuilderWhereCondition::call($query, false, $params);
        $this->applyCustomerNameFilters($query, $params);
        $this->applyDateFilters($query, $params);
        $query = $this->addSoftDeleteCondition($query);

        $this->buildSortQuery($query, $params);

        return $query->get(is_array($columns) ? $columns : func_get_args());
    }

    public function findDetailById(int $id)
    {
        $query = ($this->model)::query();
        $query->with(['customer', 'aaDate.aaPlace', 'inspector']);
        $query = $this->addSoftDeleteCondition($query);

        return $query->findOrFail($id);
    }

    public function buildSortQuery(Builder $query, $params): void
    {
        $params = $params ?: request()->toArray();

        $orderOption = $params['orderOption'] ?? null;
        $sortType = $params['sortType'] ?? SortType::DESC->value;

        if ($orderOption) {
            $this->applySorting($query, $orderOption, $sortType);
        } else {
            $this->applyDefaultSorting($query, $sortType);
        }
    }

    public function buildSortQueryExportCSV(Builder $query, $params): void
    {
        $params = $params ?: request()->toArray();

        $orderOption = $params['orderOption'] ?? null;
        $sortType = $params['sortType'] ?? SortType::DESC->value;

        if ($orderOption) {
            $this->applySorting($query, $orderOption, $sortType);
        } else {
            $this->applyDefaultSortingExportCsv($query, $sortType);
        }
    }

    public function bulkUpdateStatus(array $ids, array $attributes): void
    {
        $query = ($this->model)::query();

        $query
            ->whereIn('id', $ids)
            ->update($attributes);
    }

    public function findByIds(array $ids): Collection
    {
        $query = ($this->model)::query();

        return $query->whereIn('id', $ids)->get();
    }

    public function builderQueryExportCSV($params = null, array $relations = []): Builder
    {
        $query = ($this->model)::query();

        $query->with($relations)
            ->leftJoinCustom('customer')
            ->leftJoinCustom('aaDate');

        $query = BuilderWhereCondition::call($query, false, $params);
        $this->applyCustomerNameFilters($query, $params);
        $this->applyDateFilters($query, $params);
        $query = $this->addSoftDeleteCondition($query);

        $this->buildSortQueryExportCSV($query, $params);

        return $query;
    }


    public function getDataForBeforeCheckCustomer(): Collection
    {
        $query = ($this->model)::query();

        $query->leftJoin('m_customer', 't_before_check.m_customer_id', '=', 'm_customer.id');
        $query->leftJoin('t_aa_date', 't_before_check.t_aa_date_id', '=', 't_aa_date.id');
        $query->leftJoin('t_aa_place', 't_aa_date.t_aa_place_id', '=', 't_aa_place.id');

        $query->whereIn('t_aa_place.id', ['45', '25', '77']);

        $endOpenDate = date('Y/m/d');
        $startOpenDate = date('Y/m/d', strtotime('-5 years'));

        $query->where('t_aa_date.open_date', '>=', $startOpenDate)
            ->where('t_aa_date.open_date', '<=', $endOpenDate);

        $query->where('t_before_check.m_customer_id', '<>', 1001);

        $query->orderBy('m_customer.cus_Name_JP', 'ASC');

        return $query->get([
            't_before_check.m_customer_id',
            'm_customer.cus_Name_JP',
            'm_customer.cus_Name_EN',
            't_before_check.odr_person',
            't_before_check.odr_tel',
            't_before_check.odr_mail'
        ]);
    }

    public function confirmCreateCheckingRequest(array $params): bool
    {
        $query = ($this->model)::query();

        $t_aa_date_id = $params['t_aa_date_id'];
        $m_customer_id = $params['m_customer_id'];
        $aa_no  = $params['aa_no'];

        return $query
            ->where('t_aa_date_id', $t_aa_date_id)
            ->where('m_customer_id', $m_customer_id)
            ->where('aa_no', $aa_no)
            ->where('st_cd', '<>', Statuses::CANCELED)
            ->where('del_flg', '=', 0)
            ->exists();
    }

    private function applySorting(Builder $query, string $orderOption, string $direction): void
    {
        $sortMapping = [
            OrderOptions::CUS_NAME_JP->value => fn ($query, $direction) => $query->orderBy('m_customer.cus_Name_JP', $direction),
            OrderOptions::AA_PLACE->value => fn ($query, $direction) => $query->orderBy('t_aa_date.t_aa_place_id', $direction),
            OrderOptions::AA_NO->value => fn ($query, $direction) => $query->orderBy('t_before_check.aa_no', $direction),
            OrderOptions::ORDER_DATE->value => fn ($query, $direction) => $query->orderBy('t_before_check.odr_date', $direction),
            OrderOptions::OPEN_DATE->value => fn ($query, $direction) => $query->orderBy('t_aa_date.open_date', $direction),
            OrderOptions::CAR_NAME->value => fn ($query, $direction) => $query->orderBy('t_before_check.car_name', $direction),
        ];

        if (isset($sortMapping[$orderOption])) {
            $sortMapping[$orderOption]($query, $direction);
        }
    }

    private function applyDefaultSorting(Builder $query, $sortType): void
    {
        $query->orderBy('t_before_check.st_cd', 'ASC')->orderBy('t_before_check.id', $sortType);
    }

    private function applyDefaultSortingExportCsv(Builder $query, $sortType): void
    {
        $query->orderBy('t_before_check.id', $sortType);
    }

    /**
     * Apply custom customer name filters based on customerName parameter
     *
     * @param Builder $query
     * @param array $params
     * @return void
     */
    private function applyCustomerNameFilters(Builder $query, array $params): void
    {
        $customerName = $params['customerName'] ?? null;

        if (!empty($customerName)) {
            $query->where(function (Builder $subQuery) use ($customerName): void {
                $subQuery->where('m_customer.cus_name_JP', 'like', '%' . $customerName . '%')
                    ->orWhere('m_customer.cus_name_EN', 'like', '%' . $customerName . '%');
            });
        }
    }

    /**
     * @param Builder $query
     * @param array $params
     * @return void
     */
    private function applyDateFilters(Builder $query, array $params): void
    {
        $dateMode = $params['date_mode'] ?? null;
        $sdate = $params['sdate'] ?? null;
        $edate = $params['edate'] ?? null;

        if (empty($dateMode) || (empty($sdate) && empty($edate))) {
            return;
        }

        $dateColumn = '';
        $stime = '';
        $etime = '';

        switch ($dateMode) {
            case DateMode::eventDate->value:
                $dateColumn = 't_aa_date.open_date';
                $stime = '';
                $etime = '';
                break;
            case DateMode::orderDate->value:
                $dateColumn = 't_before_check.odr_date';
                $stime = ' 00:00:00';
                $etime = ' 23:59:59';
                break;
            case DateMode::requestDate->value:
                $dateColumn = 't_before_check.check_req_date';
                $stime = ' 00:00:00';
                $etime = ' 23:59:59';
                break;
            default:
                return;
        }

        if (!empty($sdate)) {
            $startDateTime = $sdate . $stime;
            $query->where($dateColumn, '>=', $startDateTime);
        }

        if (!empty($edate)) {
            $endDateTime = $edate . $etime;
            $query->where($dateColumn, '<=', $endDateTime);
        }
    }
}
