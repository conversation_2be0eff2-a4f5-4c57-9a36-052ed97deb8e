<?php

declare(strict_types=1);

namespace App\Services\Inspector;

use App\Repositories\sqlServer\InspectorRepository;

class InspectorService
{
    private InspectorRepository $inspectorRepository;

    public function __construct(InspectorRepository $inspectorRepository)
    {
        $this->inspectorRepository = $inspectorRepository;
    }

    // Get all inspectors
    public function getList($params = [], $relations = [], $columns = ['*'], $withTrashed = false)
    {
        return $this->inspectorRepository->getList($params, $relations, $columns, $withTrashed);
    }
}
