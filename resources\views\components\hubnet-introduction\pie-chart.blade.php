@props([
    'id' => 'pie-chart',
    'percentage' => null,
    'percentageText' => null,
    'comments' => [],
    'commentsTitle' => 'comment',
    'wrapperClass' => '',
    'chartWrapperClass' => 'question-wrap3 2md:w-6/12 2md:max-w-1/2 2md:flex-none relative md:w-full',
    'commentsWrapperClass' => '2md:w-5/12 mb-0 md:w-full',
    'showComments' => true,
    'checkboxId' => null,
])

@php
    $checkboxId = $checkboxId ?? 'checkbox-' . $id;
@endphp

<div class="question-row {{ $wrapperClass }} flex flex-wrap items-center lg:justify-between xl:justify-start">
    <div class="{{ $chartWrapperClass }}">
        <canvas id="{{ $id }}"></canvas>
        @if ($percentage || $percentageText)
            <p data-aos="zoom-in" class="aos-init q_comments" data-aos-delay="300" data-aos-duration="300">
                @if ($percentageText)
                    {!! $percentageText !!}
                @endif

                @if ($percentage)
                    <span>&nbsp;{{ $percentage }}％&nbsp;</span>
                @endif
            </p>
        @endif
    </div>

    @if ($showComments && count($comments) > 0)
        <div class="{{ $commentsWrapperClass }}">
            <input type="checkbox" name="" id="{{ $checkboxId }}" />
            <label for="{{ $checkboxId }}">
                <p>
                    {{ $commentsTitle }}
                    <i class="fas fa-angle-right"></i>
                </p>
                <ul class="mb-0 list-disc">
                    @foreach ($comments as $comment)
                        <li>{{ $comment }}</li>
                    @endforeach
                </ul>
            </label>
        </div>
    @endif
</div>
